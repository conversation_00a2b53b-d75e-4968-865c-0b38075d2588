#!/bin/bash

# Database Schema Verification Script
# This script checks if your current database matches the schema files

echo "🔍 Database Schema Verification"
echo "================================"

# Check if we're in the right directory
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found. Please run this script from the databaseHub directory."
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed."
    exit 1
fi

# Check if required Python packages are available
echo "📦 Checking Python dependencies..."
python3 -c "import psycopg2, dotenv" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  Missing required Python packages. Installing..."
    pip3 install psycopg2-binary python-dotenv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install required packages. Please install manually:"
        echo "   pip3 install psycopg2-binary python-dotenv"
        exit 1
    fi
fi

# Run the verification script
echo "🚀 Running database verification..."
python3 scripts/verify_database_schema.py

# Capture the exit code
exit_code=$?

echo ""
if [ $exit_code -eq 0 ]; then
    echo "✅ Verification completed successfully!"
    echo "💡 Your database schema is up to date."
else
    echo "⚠️  Verification found issues."
    echo "💡 Consider running the schema files to update your database:"
    echo "   1. psql -d wizlop_db -f schema/00_create_extensions_and_schemas.sql"
    echo "   2. psql -d wizlop_db -f schema/backend_schema.sql"
    echo "   3. psql -d wizlop_db -f schema/spatial_schema.sql"
    echo "   4. psql -d wizlop_db -f schema/cross_schema.sql"
    echo "   5. psql -d wizlop_db -f schema/top_location.sql"
fi

exit $exit_code
