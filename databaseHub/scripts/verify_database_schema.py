#!/usr/bin/env python3
"""
Database Schema Verification Script
Compares the current database structure with the schema files to ensure they match.
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import json
from typing import Dict, List, Set, Tuple

# Load environment variables
load_dotenv()


class DatabaseSchemaVerifier:
    def __init__(self):
        self.db_config = {
            'dbname': os.getenv('DB_NAME', 'wizlop_db'),
            'user': os.getenv('DB_USER', 'wizlop_user'),
            'password': os.getenv('DB_PASSWORD', 'wizlop_pass'),
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', '5432')
        }
        self.conn = None
        self.issues = []

    def connect(self):
        """Connect to the database"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            print(f"✅ Connected to database: {self.db_config['dbname']}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False

    def get_database_tables(self) -> Dict[str, List[Dict]]:
        """Get all tables and their columns from the database"""
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Get all tables with their columns
            cur.execute("""
                SELECT 
                    t.table_schema,
                    t.table_name,
                    c.column_name,
                    c.data_type,
                    c.is_nullable,
                    c.column_default,
                    c.character_maximum_length,
                    c.numeric_precision,
                    c.numeric_scale
                FROM information_schema.tables t
                LEFT JOIN information_schema.columns c ON t.table_name = c.table_name 
                    AND t.table_schema = c.table_schema
                WHERE t.table_schema IN ('backend_schema', 'spatial_schema', 'public')
                    AND t.table_type = 'BASE TABLE'
                ORDER BY t.table_schema, t.table_name, c.ordinal_position;
            """)

            tables = {}
            for row in cur.fetchall():
                schema_table = f"{row['table_schema']}.{row['table_name']}"
                if schema_table not in tables:
                    tables[schema_table] = []
                if row['column_name']:  # Skip tables without columns
                    tables[schema_table].append(dict(row))

            return tables

    def get_database_indexes(self) -> Dict[str, List[str]]:
        """Get all indexes from the database"""
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE schemaname IN ('backend_schema', 'spatial_schema', 'public')
                    AND indexname NOT LIKE '%_pkey'  -- Exclude primary key indexes
                ORDER BY schemaname, tablename, indexname;
            """)

            indexes = {}
            for row in cur.fetchall():
                schema_table = f"{row['schemaname']}.{row['tablename']}"
                if schema_table not in indexes:
                    indexes[schema_table] = []
                indexes[schema_table].append(row['indexname'])

            return indexes

    def get_database_functions(self) -> List[str]:
        """Get all functions from the database"""
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    n.nspname as schema_name,
                    p.proname as function_name,
                    pg_get_function_identity_arguments(p.oid) as arguments
                FROM pg_proc p
                JOIN pg_namespace n ON p.pronamespace = n.oid
                WHERE n.nspname IN ('backend_schema', 'spatial_schema', 'public')
                    AND p.prokind = 'f'  -- Functions only, not procedures
                ORDER BY n.nspname, p.proname;
            """)

            functions = []
            for row in cur.fetchall():
                func_signature = f"{row['schema_name']}.{row['function_name']}({row['arguments']})"
                functions.append(func_signature)

            return functions

    def get_database_views(self) -> List[str]:
        """Get all views from the database"""
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    table_schema,
                    table_name
                FROM information_schema.views
                WHERE table_schema IN ('backend_schema', 'spatial_schema', 'public')
                ORDER BY table_schema, table_name;
            """)

            views = []
            for row in cur.fetchall():
                views.append(f"{row['table_schema']}.{row['table_name']}")

            return views

    def check_critical_tables(self, db_tables: Dict[str, List[Dict]]) -> bool:
        """Check if critical tables exist with expected structure"""
        critical_tables = {
            'backend_schema.nextauth_users': ['id', 'username', 'email', 'role'],
            'backend_schema.user_credits': ['id', 'user_id', 'credits_earned', 'credits_purchased', 'credits_used'],
            'backend_schema.credit_transactions': ['id', 'user_id', 'transaction_type', 'amount', 'reason'],
            'backend_schema.user_daily_limits': ['id', 'user_id', 'limit_date'],
            'spatial_schema.pois': ['id', 'name', 'category', 'latitude', 'longitude', 'country'],
            'spatial_schema.user_pois_temp': ['id', 'submitted_by_user_id', 'submission_type', 'target_poi_id'],
            'spatial_schema.user_pois_approved': ['id', 'submitted_by_user_id', 'final_poi_id', 'submission_type']
        }

        all_good = True
        print("\n🔍 Checking Critical Tables:")

        for table_name, expected_columns in critical_tables.items():
            if table_name not in db_tables:
                print(f"❌ Missing table: {table_name}")
                self.issues.append(f"Missing table: {table_name}")
                all_good = False
                continue

            db_columns = [col['column_name'] for col in db_tables[table_name]]
            missing_columns = set(expected_columns) - set(db_columns)

            if missing_columns:
                print(f"⚠️  {table_name} missing columns: {missing_columns}")
                self.issues.append(
                    f"{table_name} missing columns: {missing_columns}")
                all_good = False
            else:
                print(f"✅ {table_name}")

        return all_good

    def check_id_types(self, db_tables: Dict[str, List[Dict]]) -> bool:
        """Check if ID types are correct (INTEGER for spatial, UUID for backend)"""
        print("\n🔍 Checking ID Types:")
        all_good = True

        # Check spatial tables should have INTEGER IDs
        spatial_tables = [table for table in db_tables.keys(
        ) if table.startswith('spatial_schema.')]
        for table_name in spatial_tables:
            id_column = next(
                (col for col in db_tables[table_name] if col['column_name'] == 'id'), None)
            if id_column:
                if id_column['data_type'] != 'integer':
                    print(
                        f"❌ {table_name}.id should be INTEGER, found: {id_column['data_type']}")
                    self.issues.append(
                        f"{table_name}.id should be INTEGER, found: {id_column['data_type']}")
                    all_good = False
                else:
                    print(f"✅ {table_name}.id is INTEGER")

        # Check backend tables should have UUID IDs (except system tables)
        backend_tables = [table for table in db_tables.keys(
        ) if table.startswith('backend_schema.')]
        uuid_tables = ['nextauth_users', 'user_credits', 'credit_transactions', 'user_daily_limits',
                       'user_location_reviews', 'user_location_visits', 'user_favorites',
                       'user_added_locations', 'user_lists', 'user_interactions', 'user_location_photos']

        for table_name in backend_tables:
            table_short = table_name.split('.')[1]
            if table_short in uuid_tables:
                id_column = next(
                    (col for col in db_tables[table_name] if col['column_name'] == 'id'), None)
                if id_column:
                    if id_column['data_type'] != 'uuid':
                        print(
                            f"❌ {table_name}.id should be UUID, found: {id_column['data_type']}")
                        self.issues.append(
                            f"{table_name}.id should be UUID, found: {id_column['data_type']}")
                        all_good = False
                    else:
                        print(f"✅ {table_name}.id is UUID")

        return all_good

    def check_critical_functions(self, db_functions: List[str]) -> bool:
        """Check if critical functions exist"""
        critical_functions = [
            'backend_schema.user_has_credits',
            'backend_schema.deduct_credits',
            'backend_schema.award_credits',
            'backend_schema.check_daily_limit',
            'backend_schema.increment_daily_limit',
            'spatial_schema.get_top_locations',
            'spatial_schema.get_scoring_stats'
        ]

        print("\n🔍 Checking Critical Functions:")
        all_good = True

        for func_name in critical_functions:
            # Check if any function with this name exists (ignore parameters)
            exists = any(func.startswith(func_name + '(')
                         for func in db_functions)
            if exists:
                print(f"✅ {func_name}")
            else:
                print(f"❌ Missing function: {func_name}")
                self.issues.append(f"Missing function: {func_name}")
                all_good = False

        return all_good

    def check_critical_views(self, db_views: List[str]) -> bool:
        """Check if critical views exist"""
        critical_views = [
            'backend_schema.vw_user_locations',
            'spatial_schema.vw_user_pois_approved_details'
        ]

        print("\n🔍 Checking Critical Views:")
        all_good = True

        for view_name in critical_views:
            if view_name in db_views:
                print(f"✅ {view_name}")
            else:
                print(f"❌ Missing view: {view_name}")
                self.issues.append(f"Missing view: {view_name}")
                all_good = False

        return all_good

    def run_verification(self):
        """Run the complete verification process"""
        print("🔍 Database Schema Verification Starting...")
        print(
            f"📊 Target Database: {self.db_config['dbname']} @ {self.db_config['host']}:{self.db_config['port']}")

        if not self.connect():
            return False

        try:
            # Get database structure
            print("\n📋 Analyzing Database Structure...")
            db_tables = self.get_database_tables()
            db_indexes = self.get_database_indexes()
            db_functions = self.get_database_functions()
            db_views = self.get_database_views()

            print(
                f"Found: {len(db_tables)} tables, {len(db_functions)} functions, {len(db_views)} views")

            # Run checks
            tables_ok = self.check_critical_tables(db_tables)
            id_types_ok = self.check_id_types(db_tables)
            functions_ok = self.check_critical_functions(db_functions)
            views_ok = self.check_critical_views(db_views)

            # Summary
            print("\n" + "="*60)
            if tables_ok and id_types_ok and functions_ok and views_ok:
                print("🎉 DATABASE SCHEMA VERIFICATION: PASSED")
                print("✅ Your database matches the expected schema structure!")
            else:
                print("⚠️  DATABASE SCHEMA VERIFICATION: ISSUES FOUND")
                print(f"❌ Found {len(self.issues)} issues:")
                for issue in self.issues:
                    print(f"   • {issue}")
                print("\n💡 Consider running the schema files to update your database.")

            return len(self.issues) == 0

        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        finally:
            if self.conn:
                self.conn.close()


if __name__ == "__main__":
    verifier = DatabaseSchemaVerifier()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)
