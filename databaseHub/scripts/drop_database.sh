#!/bin/bash

# drop_database.sh - Simply drop the Wizlop database
# Usage: ./scripts/drop_database.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="wizlop_db"
SUPERUSER="saidmustafa"  # PostgreSQL superuser
DB_HOST="localhost"

echo -e "${BLUE}🗑️  Wizlop Database Drop Script${NC}"
echo "================================"

# Check if we're in the right directory
if [ ! -f "schema/00_create_extensions_and_schemas.sql" ]; then
    echo -e "${RED}❌ Error: Please run this script from the databaseHub directory${NC}"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Confirmation prompt
echo -e "${YELLOW}⚠️  WARNING: This will completely drop the database!${NC}"
echo "Database: $DB_NAME"
echo "Host: $DB_HOST"
echo ""
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}❌ Operation cancelled${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔄 Dropping database...${NC}"

# Drop existing database
echo -e "${YELLOW}Dropping database: $DB_NAME${NC}"
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" || {
    echo -e "${RED}❌ Failed to drop database. Make sure PostgreSQL is running and you have permissions.${NC}"
    exit 1
}

echo -e "${GREEN}✅ Database '$DB_NAME' dropped successfully!${NC}"
echo ""
echo -e "${BLUE}🚀 Next steps:${NC}"
echo "   1. Run: python scripts/spatial/setup/setup_portable.py"
echo "   2. Run schema files in order:"
echo "      psql -U wizlop_user -h localhost -d wizlop_db -f schema/00_create_extensions_and_schemas.sql"
echo "      psql -U wizlop_user -h localhost -d wizlop_db -f schema/backend_schema.sql"
echo "      psql -U wizlop_user -h localhost -d wizlop_db -f schema/spatial_schema.sql"
echo "      psql -U wizlop_user -h localhost -d wizlop_db -f schema/cross_schema.sql"
echo "      psql -U wizlop_user -h localhost -d wizlop_db -f schema/top_location.sql"
echo ""
echo -e "${GREEN}✅ Ready to recreate!${NC}"
