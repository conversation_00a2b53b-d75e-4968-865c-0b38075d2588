#!/usr/bin/env python3
"""
schema_diff.py: Compare current PostgreSQL DB schema to the canonical SQL files in databaseHub/schema/.
Outputs a summary of differences and optionally generates migration SQL.

Usage:
  python schema_diff.py --host HOST --port PORT --user USER --password PASSWORD --dbname DBNAME [--output migration.sql]
"""
import argparse
import psycopg2
import sqlparse
import os
from glob import glob
from dotenv import load_dotenv
from pathlib import Path

SCHEMA_DIR = os.path.join(os.path.dirname(__file__), '../schema')


def parse_args():
    parser = argparse.ArgumentParser(description="Compare DB schema to canonical SQL files.")
    parser.add_argument('--host')
    parser.add_argument('--port', type=int)
    parser.add_argument('--user')
    parser.add_argument('--password')
    parser.add_argument('--dbname')
    parser.add_argument('--output', help='Write migration SQL to this file (optional)')
    args = parser.parse_args()
    if not (args.host and args.user and args.password and args.dbname):
        env_path = Path(__file__).parent.parent / '.env'
        load_dotenv(dotenv_path=env_path)
        args.host = args.host or os.getenv('DB_HOST')
        args.port = args.port or int(os.getenv('DB_PORT', '5432'))
        args.user = args.user or os.getenv('DB_USER')
        args.password = args.password or os.getenv('DB_PASSWORD')
        args.dbname = args.dbname or os.getenv('DB_NAME')
    return args


def get_db_schema(conn):
    cur = conn.cursor()
    # Get tables
    cur.execute("""
        SELECT table_schema, table_name
        FROM information_schema.tables
        WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
        ORDER BY table_schema, table_name;
    """)
    tables = cur.fetchall()
    # Get columns
    cur.execute("""
        SELECT table_schema, table_name, column_name, data_type
        FROM information_schema.columns
        WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
        ORDER BY table_schema, table_name, ordinal_position;
    """)
    columns = cur.fetchall()
    # Get indexes
    cur.execute("""
        SELECT schemaname, tablename, indexname
        FROM pg_indexes
        WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
        ORDER BY schemaname, tablename, indexname;
    """)
    indexes = cur.fetchall()
    # Get triggers
    cur.execute("""
        SELECT event_object_schema, event_object_table, trigger_name
        FROM information_schema.triggers
        WHERE trigger_schema NOT IN ('pg_catalog', 'information_schema')
        ORDER BY event_object_schema, event_object_table, trigger_name;
    """)
    triggers = cur.fetchall()
    # Get functions
    cur.execute("""
        SELECT n.nspname as schema, p.proname as name
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname NOT IN ('pg_catalog', 'information_schema')
        ORDER BY n.nspname, p.proname;
    """)
    functions = cur.fetchall()
    return {
        'tables': tables,
        'columns': columns,
        'indexes': indexes,
        'triggers': triggers,
        'functions': functions,
    }


def get_sql_schema():
    sql_files = sorted(glob(os.path.join(SCHEMA_DIR, '*.sql')))
    schema = {'tables': set(), 'columns': set(), 'indexes': set(), 'triggers': set(), 'functions': set()}
    for path in sql_files:
        with open(path, 'r') as f:
            sql = f.read()
            statements = sqlparse.parse(sql)
            for stmt in statements:
                tokens = [t for t in stmt.tokens if not t.is_whitespace]
                if not tokens:
                    continue
                t0 = tokens[0].value.upper()
                if t0 == 'CREATE':
                    if 'TABLE' in stmt.value.upper():
                        # crude table name extraction
                        for i, t in enumerate(tokens):
                            if t.value.upper() == 'TABLE':
                                name = tokens[i+2].value.replace('IF NOT EXISTS', '').replace('(', '').strip()
                                schema['tables'].add(name)
                    elif 'INDEX' in stmt.value.upper():
                        for i, t in enumerate(tokens):
                            if t.value.upper() == 'INDEX':
                                name = tokens[i+2].value.replace('IF NOT EXISTS', '').replace('ON', '').strip()
                                schema['indexes'].add(name)
                    elif 'TRIGGER' in stmt.value.upper():
                        for i, t in enumerate(tokens):
                            if t.value.upper() == 'TRIGGER':
                                name = tokens[i+1].value.strip()
                                schema['triggers'].add(name)
                    elif 'FUNCTION' in stmt.value.upper():
                        for i, t in enumerate(tokens):
                            if t.value.upper() == 'FUNCTION':
                                name = tokens[i+2].value.split('(')[0].strip()
                                schema['functions'].add(name)
    return schema


def compare_schemas(db, sql):
    def diff(kind):
        db_set = set([x[-1] if kind != 'columns' else (x[1], x[2]) for x in db[kind]])
        sql_set = sql[kind]
        missing_in_db = sql_set - db_set
        extra_in_db = db_set - sql_set
        return missing_in_db, extra_in_db
    report = {}
    for kind in ['tables', 'indexes', 'triggers', 'functions']:
        missing, extra = diff(kind)
        report[kind] = {'missing': missing, 'extra': extra}
    return report


def main():
    args = parse_args()
    conn = psycopg2.connect(
        host=args.host, port=args.port, user=args.user, password=args.password, dbname=args.dbname
    )
    db_schema = get_db_schema(conn)
    sql_schema = get_sql_schema()
    report = compare_schemas(db_schema, sql_schema)
    print("=== SCHEMA DIFF REPORT ===")
    for kind in ['tables', 'indexes', 'triggers', 'functions']:
        print(f"\n{kind.upper()}:")
        print(f"  Missing in DB: {sorted(report[kind]['missing'])}")
        print(f"  Extra in DB:   {sorted(report[kind]['extra'])}")
    if args.output:
        with open(args.output, 'w') as f:
            f.write('-- Migration SQL generation not implemented in this version.\n')
    conn.close()

if __name__ == '__main__':
    main() 