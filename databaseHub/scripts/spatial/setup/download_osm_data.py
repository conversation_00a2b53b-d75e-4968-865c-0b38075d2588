#!/usr/bin/env python3
"""
Download the latest Turkey OSM PBF file for databaseHub.
"""
import os
from pathlib import Path
import requests

OSM_URL = "https://download.geofabrik.de/europe/turkey-latest.osm.pbf"
DATA_DIR = Path(__file__).parent.parent / 'data' / 'raw'
DATA_DIR.mkdir(parents=True, exist_ok=True)
OSM_FILE = DATA_DIR / 'turkey-latest.osm.pbf'


def download_osm():
    if OSM_FILE.exists():
        print(f"OSM file already exists: {OSM_FILE}")
        return
    print(f"Downloading OSM data from {OSM_URL} ...")
    with requests.get(OSM_URL, stream=True) as r:
        r.raise_for_status()
        with open(OSM_FILE, 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192):
                f.write(chunk)
    print(f"Downloaded OSM data to {OSM_FILE}")

if __name__ == '__main__':
    download_osm() 