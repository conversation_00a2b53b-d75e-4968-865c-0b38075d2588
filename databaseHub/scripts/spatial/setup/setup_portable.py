#!/usr/bin/env python3
"""
Portable PostgreSQL Setup Script for Wizlop DatabaseHub.

This script creates a portable PostgreSQL setup that works across different environments
by creating a dedicated database user instead of relying on system usernames.
"""

import os
import sys
import subprocess
import logging
import argparse
import getpass
import psycopg2
from psycopg2 import sql
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default configuration for portable setup
DEFAULT_CONFIG = {
    'dbname': 'wizlop_db',
    'user': 'wizlop_user',
    'password': 'wizlop_pass',
    'host': 'localhost',
    'port': '5432'
}


def parse_args():
    """Parse command line arguments and load DB config from .env only."""
    parser = argparse.ArgumentParser(
        description='Set up portable PostgreSQL database with PostGIS extension')
    parser.add_argument('--drop', action='store_true',
                        help='Drop database and user if they exist')
    args = parser.parse_args()
    # Load .env from databaseHub root
    env_path = Path(__file__).parent.parent.parent.parent / '.env'
    load_dotenv(dotenv_path=env_path)
    dbname = os.getenv('DB_NAME')
    user = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    host = os.getenv('DB_HOST')
    port = os.getenv('DB_PORT')
    missing = [k for k, v in [('DB_NAME', dbname), ('DB_USER', user), (
        'DB_PASSWORD', password), ('DB_HOST', host), ('DB_PORT', port)] if not v]
    if missing:
        print(f"Missing required DB config in .env: {', '.join(missing)}")
        sys.exit(1)
    args.dbname = dbname
    args.user = user
    args.password = password
    args.host = host
    args.port = port
    return args


def get_admin_connection(args):
    """Get admin connection using system user or postgres user."""
    # Try common superuser names in order of preference
    admin_users = ['postgres', getpass.getuser(), 'root']

    for admin_user in admin_users:
        try:
            logger.info(f"Attempting admin connection as: {admin_user}")
            conn = psycopg2.connect(
                dbname='postgres',
                user=admin_user,
                password='',
                host=args.host,
                port=args.port
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            logger.info(f"Successfully connected as admin user: {admin_user}")
            return conn, admin_user
        except Exception as e:
            logger.warning(f"Could not connect as {admin_user}: {e}")
            continue

    logger.error("Could not establish admin connection to PostgreSQL")
    logger.error(
        "Please ensure PostgreSQL is running and you have a superuser account")
    return None, None


def drop_existing_resources(args, conn):
    """Drop existing database and user if --drop flag is used."""
    if not args.drop:
        return True

    try:
        cursor = conn.cursor()

        # Drop database first (if it exists)
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s", [args.dbname])
        db_exists = cursor.fetchone()
        if db_exists:
            logger.info(f"Dropping existing database: {args.dbname}")
            cursor.execute(sql.SQL("DROP DATABASE {}").format(
                sql.Identifier(args.dbname)))

        # Then drop user (if it exists)
        cursor.execute(
            "SELECT 1 FROM pg_roles WHERE rolname = %s", [args.user])
        user_exists = cursor.fetchone()
        if user_exists:
            logger.info(f"Dropping existing user: {args.user}")
            cursor.execute(sql.SQL("DROP USER {}").format(
                sql.Identifier(args.user)))

        return True
    except Exception as e:
        logger.error(f"Error dropping existing resources: {e}")
        return False


def create_database_user(args, conn):
    """Create the database user if it doesn't exist."""
    try:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT 1 FROM pg_roles WHERE rolname = %s", [args.user])
        user_exists = cursor.fetchone()
        if user_exists:
            logger.info(f"Database user '{args.user}' already exists")
            return True

        logger.info(f"Creating database user: {args.user}")
        cursor.execute(
            sql.SQL("CREATE USER {} WITH SUPERUSER PASSWORD %s").format(
                sql.Identifier(args.user)
            ), [args.password]
        )
        logger.info(f"Database user '{args.user}' created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating database user: {e}")
        return False


def create_database(args, admin_user=None):
    """Create the database with PostGIS extension."""
    try:
        conn = psycopg2.connect(
            dbname='postgres',
            user=args.user,
            password=args.password,
            host=args.host,
            port=args.port
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s", [args.dbname])
        db_exists = cursor.fetchone()
        if db_exists:
            logger.info(f"Database '{args.dbname}' already exists")
            conn.close()
            return True
        logger.info(f"Creating database: {args.dbname}")
        cursor.execute(sql.SQL("CREATE DATABASE {}").format(
            sql.Identifier(args.dbname)))
        conn.close()

        # Create PostGIS extension using admin user if available
        if admin_user:
            try:
                logger.info(
                    "Creating PostGIS extension with admin privileges...")
                admin_conn = psycopg2.connect(
                    dbname=args.dbname,
                    user=admin_user,
                    password='',
                    host=args.host,
                    port=args.port
                )
                admin_conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
                admin_cursor = admin_conn.cursor()

                try:
                    logger.info("Creating extension: postgis (with admin)")
                    admin_cursor.execute(
                        "CREATE EXTENSION IF NOT EXISTS postgis")
                    logger.info("Extension 'postgis' created successfully")
                except psycopg2.Error as e:
                    logger.warning(f"Could not create PostGIS with admin: {e}")

                admin_conn.close()
            except Exception as e:
                logger.warning(f"Admin extension creation failed: {e}")

        # Create other extensions with regular user
        conn = psycopg2.connect(
            dbname=args.dbname,
            user=args.user,
            password=args.password,
            host=args.host,
            port=args.port
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Try remaining extensions (and PostGIS if admin failed)
        extensions = ['postgis', 'h3',
                      'hstore'] if not admin_user else ['h3', 'hstore']
        for ext in extensions:
            try:
                logger.info(f"Creating extension: {ext}")
                cursor.execute(f"CREATE EXTENSION IF NOT EXISTS {ext}")
                logger.info(f"Extension '{ext}' created successfully")
            except psycopg2.Error as e:
                logger.warning(f"Could not create extension '{ext}': {e}")
        conn.close()
        logger.info("Database setup completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


def verify_setup(args):
    """Verify the database setup."""
    try:
        conn = psycopg2.connect(
            dbname=args.dbname,
            user=args.user,
            password=args.password,
            host=args.host,
            port=args.port
        )
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT PostGIS_Version();")
            version = cursor.fetchone()
            logger.info(f"PostGIS version: {version[0]}")
        except:
            logger.warning("PostGIS not available")
        conn.close()
        logger.info("Verification complete")
        return True
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        return False


def main():
    args = parse_args()
    logger.info("Starting portable PostgreSQL setup for Wizlop DatabaseHub...")

    # Get admin connection and admin user name
    admin_conn, admin_user = get_admin_connection(args)
    if not admin_conn:
        logger.error("Admin connection failed. Exiting.")
        sys.exit(1)

    logger.info(f"Using admin user '{admin_user}' to create portable setup")

    # Drop existing resources if --drop flag is used
    if not drop_existing_resources(args, admin_conn):
        logger.error("Failed to drop existing resources. Exiting.")
        admin_conn.close()
        sys.exit(1)

    # Create the wizlop_user with superuser privileges
    if not create_database_user(args, admin_conn):
        logger.error("Failed to create database user. Exiting.")
        admin_conn.close()
        sys.exit(1)

    admin_conn.close()
    logger.info(f"Superuser '{args.user}' created successfully")

    # Now use the newly created superuser for database operations
    if not create_database(args, admin_user):
        logger.error("Failed to create database. Exiting.")
        sys.exit(1)
    if not verify_setup(args):
        logger.error("Database verification failed. Exiting.")
        sys.exit(1)
    logger.info(
        "Portable PostgreSQL setup for Wizlop DatabaseHub completed successfully!")


if __name__ == '__main__':
    main()
