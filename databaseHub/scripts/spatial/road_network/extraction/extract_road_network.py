#!/usr/bin/env python3
"""
Extract ALL Turkey road network from OSM PBF for databaseHub using Python osmium package.
"""
import os
import sys
import json
from pathlib import Path
import logging

# Try to import osmium and dependencies
try:
    import osmium
    import shapely.wkb as wkblib
    import shapely.geometry
except ImportError:
    print("This script requires the 'osmium' and 'shapely' Python packages. Please install them with 'pip install osmium shapely'.")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

RAW_DIR = Path(__file__).parent.parent.parent / 'data' / 'raw'
EXTRACTED_DIR = Path(__file__).parent.parent.parent / 'data' / 'extracted'
EXTRACTED_DIR.mkdir(parents=True, exist_ok=True)
OSM_FILE = RAW_DIR / 'turkey-latest.osm.pbf'
ROAD_NETWORK_FILE = EXTRACTED_DIR / 'road_network.geojson'

class RoadNetworkHandler(osmium.SimpleHandler):
    def __init__(self):
        super().__init__()
        self.roads = []
        self.wkbfab = osmium.geom.WKBFactory()
        self.count = 0

    def way(self, w):
        tags = {k: v for k, v in w.tags}
        if 'highway' in tags:
            try:
                wkb = self.wkbfab.create_linestring(w)
                geom = wkblib.loads(wkb)
                if not geom.is_valid or geom.is_empty:
                    return
                properties = {
                    'osm_id': w.id,
                    'highway': tags.get('highway'),
                    'name': tags.get('name', ''),
                    'name_en': tags.get('name:en', ''),
                    'name_tr': tags.get('name:tr', ''),
                    'oneway': tags.get('oneway', ''),
                    'maxspeed': tags.get('maxspeed', ''),
                    'surface': tags.get('surface', ''),
                    'lanes': tags.get('lanes', ''),
                }
                self.roads.append({
                    'type': 'Feature',
                    'geometry': shapely.geometry.mapping(geom),
                    'properties': properties
                })
                self.count += 1
            except Exception as e:
                logger.warning(f"Failed to process way {w.id}: {e}")

def extract_road_network():
    if not OSM_FILE.exists():
        logger.error(f"OSM file not found: {OSM_FILE}")
        sys.exit(1)
    logger.info("Extracting ALL Turkey road network using Python osmium...")
    handler = RoadNetworkHandler()
    handler.apply_file(str(OSM_FILE), locations=True)
    logger.info(f"Extracted {handler.count} roads. Writing to GeoJSON...")
    feature_collection = {
        'type': 'FeatureCollection',
        'features': handler.roads
    }
    with open(ROAD_NETWORK_FILE, 'w') as f:
        json.dump(feature_collection, f)
    logger.info(f"Extracted ALL road network to {ROAD_NETWORK_FILE}")

if __name__ == '__main__':
    extract_road_network() 