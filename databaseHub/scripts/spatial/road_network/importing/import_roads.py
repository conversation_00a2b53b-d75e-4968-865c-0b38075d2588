#!/usr/bin/env python3
"""
Import road network into the spatial database from extracted GeoJSON.

This script imports road network data from road_network.geojson into the spatial_schema.roads table.
It provides progress tracking and handles errors gracefully during the import process.
"""
import json
import logging
import os
from dotenv import load_dotenv
from pathlib import Path
import psycopg2

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Always load .env from the root of databaseHub
env_path = Path(__file__).resolve().parents[4] / '.env'
load_dotenv(dotenv_path=env_path)

DB_CONFIG = {
    'dbname': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'port': os.getenv('DB_PORT', '5432')
}

# Use the shared extracted data directory
DATA_DIR = Path(__file__).parent.parent.parent / 'data' / 'extracted'
ROAD_NETWORK_FILE = DATA_DIR / 'road_network.geojson'


def validate_geojson_file():
    """Validate that the GeoJSON file exists and has valid structure."""
    if not ROAD_NETWORK_FILE.exists():
        logger.error(f"Road network file not found: {ROAD_NETWORK_FILE}")
        return False

    try:
        with open(ROAD_NETWORK_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if 'features' not in data:
            logger.error("Invalid GeoJSON: missing 'features' key")
            return False

        feature_count = len(data.get('features', []))
        if feature_count == 0:
            logger.warning("GeoJSON file contains no features")
            return False

        logger.info(
            f"GeoJSON validation passed. Found {feature_count} road features.")
        return True

    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in road network file: {e}")
        return False
    except Exception as e:
        logger.error(f"Error validating GeoJSON file: {e}")
        return False


def import_roads():
    # Validate GeoJSON file first
    if not validate_geojson_file():
        return

    with open(ROAD_NETWORK_FILE, 'r', encoding='utf-8') as f:
        data = json.load(f)

    total_features = len(data.get('features', []))
    logger.info(f"Starting import of {total_features} roads...")

    conn = psycopg2.connect(**DB_CONFIG)
    conn.autocommit = True
    cursor = conn.cursor()

    imported = 0
    skipped = 0

    for i, feature in enumerate(data.get('features', []), 1):
        props = feature.get('properties', {})
        geom = feature.get('geometry', {})
        try:
            cursor.execute("""
                INSERT INTO spatial_schema.roads (
                    osm_id, highway, name, name_en, name_tr, oneway, maxspeed, surface, lanes, geom
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, ST_GeomFromGeoJSON(%s))
            """, (
                props.get('osm_id'),
                props.get('highway'),
                props.get('name'),
                props.get('name_en'),
                props.get('name_tr'),
                props.get('oneway'),
                props.get('maxspeed'),
                props.get('surface'),
                props.get('lanes'),
                json.dumps(geom)
            ))
            imported += 1

            # Progress tracking - show progress every 1000 roads or every 10%
            if imported % 1000 == 0 or (i % max(1, total_features // 10) == 0):
                progress = (i / total_features) * 100
                logger.info(
                    f"Progress: {progress:.1f}% - Imported {imported} roads, Skipped {skipped}")

        except Exception as e:
            logger.warning(
                f"Failed to import road {i} (OSM ID: {props.get('osm_id', 'unknown')}): {e}")
            skipped += 1
            continue

    logger.info(
        f"Import completed! Imported: {imported}, Skipped: {skipped}, Total processed: {imported + skipped}")
    cursor.close()
    conn.close()


if __name__ == '__main__':
    logger.info("Starting road network import process...")
    logger.info(f"GeoJSON file: {ROAD_NETWORK_FILE}")
    import_roads()
    logger.info("Road network import process completed.")
