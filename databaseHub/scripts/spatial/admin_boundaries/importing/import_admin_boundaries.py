#!/usr/bin/env python3
"""
Import Istanbul administrative boundaries into the spatial database (databaseHub version).
"""
import os
import sys
import logging
import json
from pathlib import Path
import psycopg2
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Always load .env from the root of databaseHub
env_path = Path(__file__).resolve().parents[4] / '.env'
load_dotenv(dotenv_path=env_path)

# Database configuration (edit as needed)
DB_CONFIG = {
    'dbname': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'port': os.getenv('DB_PORT', '5432')
}

# Use the shared extracted data directory
DATA_DIR = Path(__file__).parent.parent.parent / 'data' / 'extracted'
ADMIN_BOUNDARIES_PATH = DATA_DIR / 'admin_boundaries.geojson'


def import_admin_boundaries():
    if not ADMIN_BOUNDARIES_PATH.exists():
        logger.error(
            f"Admin boundaries file not found: {ADMIN_BOUNDARIES_PATH}")
        sys.exit(1)

    with open(ADMIN_BOUNDARIES_PATH, 'r') as f:
        data = json.load(f)

    total_features = len(data['features'])
    logger.info(f"Starting import of {total_features} admin boundaries...")

    conn = psycopg2.connect(**DB_CONFIG)
    conn.autocommit = True
    cursor = conn.cursor()

    imported = 0
    skipped = 0

    for i, feature in enumerate(data['features'], 1):
        props = feature['properties']
        geom = feature['geometry']
        latitude = props.get('latitude')
        longitude = props.get('longitude')
        try:
            cursor.execute("""
                INSERT INTO spatial_schema.admin_boundaries (
                    osm_id, admin_level, well_known_level, place_type, name, name_en, name_tr,
                    type, boundary, place, latitude, longitude, geom
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, ST_SetSRID(ST_GeomFromGeoJSON(%s), 4326))
            """, (
                props.get('osm_id'),
                props.get('admin_level'),
                props.get('well_known_level'),
                props.get('place_type'),
                props.get('name'),
                props.get('name:en'),
                props.get('name:tr'),
                props.get('type'),
                props.get('boundary'),
                props.get('place'),
                latitude,
                longitude,
                json.dumps(geom)
            ))
            imported += 1

            # Progress tracking
            if imported % 100 == 0:
                progress = (i / total_features) * 100
                logger.info(
                    f"Progress: {progress:.1f}% - Imported {imported} boundaries, Skipped {skipped}")

        except Exception as e:
            logger.warning(f"Failed to import feature {i}: {e}")
            skipped += 1
            continue

    logger.info(
        f"Import completed! Imported: {imported}, Skipped: {skipped}, Total processed: {imported + skipped}")
    cursor.close()
    conn.close()


if __name__ == '__main__':
    import_admin_boundaries()
