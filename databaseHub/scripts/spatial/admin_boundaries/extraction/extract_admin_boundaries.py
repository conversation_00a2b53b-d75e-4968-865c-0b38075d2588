#!/usr/bin/env python3
"""
Extract ALL Turkey administrative boundaries from OSM PBF for databaseHub using Python osmium package.
"""
import os
import sys
import json
from pathlib import Path
import logging

# Try to import osmium and dependencies
try:
    import osmium
    import shapely.wkb as wkblib
    import shapely.geometry
except ImportError:
    print("This script requires the 'osmium' and 'shapely' Python packages. Please install them with 'pip install osmium shapely'.")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

RAW_DIR = Path(__file__).parent.parent.parent / 'data' / 'raw'
EXTRACTED_DIR = Path(__file__).parent.parent.parent / 'data' / 'extracted'
EXTRACTED_DIR.mkdir(parents=True, exist_ok=True)
OSM_FILE = RAW_DIR / 'turkey-latest.osm.pbf'
ADMIN_BOUNDARIES_FILE = EXTRACTED_DIR / 'admin_boundaries.geojson'

class AdminBoundaryHandler(osmium.SimpleHandler):
    def __init__(self):
        super().__init__()
        self.boundaries = []
        self.wkbfab = osmium.geom.WKBFactory()
        self.count = 0

    def area(self, a):
        tags = {k: v for k, v in a.tags}
        if tags.get('boundary') == 'administrative' and 'admin_level' in tags:
            name = tags.get('name', '')
            if not name:
                return
            try:
                wkb = self.wkbfab.create_multipolygon(a)
                geom = wkblib.loads(wkb)
                if not geom.is_valid or geom.is_empty:
                    return
                properties = {
                    'osm_id': a.id,
                    'admin_level': tags.get('admin_level'),
                    'name': name,
                    'name:en': tags.get('name:en', ''),
                    'name:tr': tags.get('name:tr', ''),
                    'type': tags.get('type', ''),
                    'boundary': tags.get('boundary', ''),
                    'place': tags.get('place', ''),
                }
                centroid = geom.centroid
                properties['latitude'] = centroid.y
                properties['longitude'] = centroid.x
                self.boundaries.append({
                    'type': 'Feature',
                    'geometry': shapely.geometry.mapping(geom),
                    'properties': properties
                })
                self.count += 1
            except Exception as e:
                logger.warning(f"Failed to process area {a.id}: {e}")


def extract_admin_boundaries():
    if not OSM_FILE.exists():
        logger.error(f"OSM file not found: {OSM_FILE}")
        sys.exit(1)
    logger.info("Extracting ALL Turkey admin boundaries using Python osmium...")
    handler = AdminBoundaryHandler()
    handler.apply_file(str(OSM_FILE), locations=True)
    logger.info(f"Extracted {handler.count} admin boundaries. Writing to GeoJSON...")
    feature_collection = {
        'type': 'FeatureCollection',
        'features': handler.boundaries
    }
    with open(ADMIN_BOUNDARIES_FILE, 'w') as f:
        json.dump(feature_collection, f)
    logger.info(f"Extracted ALL admin boundaries to {ADMIN_BOUNDARIES_FILE}")

if __name__ == '__main__':
    extract_admin_boundaries() 