#!/bin/bash

# fix_schema_issues.sh - Fix common schema dependency and PostGIS issues
# Usage: ./scripts/fix_schema_issues.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="wizlop_db"
DB_USER="wizlop_user"
SUPERUSER="saidmustafa"
DB_HOST="localhost"

echo -e "${BLUE}🔧 Wizlop Schema Fix Script${NC}"
echo "============================"

# Check if we're in the right directory
if [ ! -f "schema/00_create_extensions_and_schemas.sql" ]; then
    echo -e "${RED}❌ Error: Please run this script from the databaseHub directory${NC}"
    echo "Current directory: $(pwd)"
    exit 1
fi

echo -e "${BLUE}🔍 Checking database status...${NC}"

# Step 1: Check if PostGIS is installed
echo -e "${YELLOW}1. Checking PostGIS installation...${NC}"
if psql -U $DB_USER -h $DB_HOST -d $DB_NAME -c "SELECT PostGIS_Version();" > /dev/null 2>&1; then
    echo -e "${GREEN}   ✅ PostGIS is installed${NC}"
else
    echo -e "${YELLOW}   ⚠️  PostGIS not found, installing with superuser...${NC}"
    psql -U $SUPERUSER -h $DB_HOST -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS postgis;" || {
        echo -e "${RED}❌ Failed to install PostGIS${NC}"
        exit 1
    }
    echo -e "${GREEN}   ✅ PostGIS installed${NC}"
fi

# Step 2: Check critical tables
echo -e "${YELLOW}2. Checking critical tables...${NC}"
MISSING_TABLES=()

# Check each critical table
TABLES=(
    "backend_schema.user_added_locations"
    "spatial_schema.pois"
    "spatial_schema.user_pois_temp"
    "spatial_schema.user_pois_approved"
)

for table in "${TABLES[@]}"; do
    if psql -U $DB_USER -h $DB_HOST -d $DB_NAME -c "\d $table" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Table exists: $table${NC}"
    else
        echo -e "${YELLOW}   ⚠️  Table missing: $table${NC}"
        MISSING_TABLES+=("$table")
    fi
done

# Step 3: Fix missing tables by re-running schemas
if [ ${#MISSING_TABLES[@]} -gt 0 ]; then
    echo -e "${YELLOW}3. Fixing missing tables by re-running schemas...${NC}"
    
    # Re-run backend schema (for user_added_locations)
    echo -e "${BLUE}   📄 Re-running backend_schema.sql${NC}"
    psql -U $DB_USER -h $DB_HOST -d $DB_NAME -f schema/backend_schema.sql || {
        echo -e "${RED}❌ Failed to run backend_schema.sql${NC}"
        exit 1
    }
    
    # Re-run spatial schema (for POI tables)
    echo -e "${BLUE}   📄 Re-running spatial_schema.sql${NC}"
    psql -U $DB_USER -h $DB_HOST -d $DB_NAME -f schema/spatial_schema.sql || {
        echo -e "${RED}❌ Failed to run spatial_schema.sql${NC}"
        exit 1
    }
    
    # Re-run cross schema
    echo -e "${BLUE}   📄 Re-running cross_schema.sql${NC}"
    psql -U $DB_USER -h $DB_HOST -d $DB_NAME -f schema/cross_schema.sql || {
        echo -e "${RED}❌ Failed to run cross_schema.sql${NC}"
        exit 1
    }
    
    echo -e "${GREEN}   ✅ Schemas re-applied${NC}"
else
    echo -e "${GREEN}3. All tables exist, no fixes needed${NC}"
fi

# Step 4: Final verification
echo -e "${YELLOW}4. Final verification...${NC}"
ALL_GOOD=true

for table in "${TABLES[@]}"; do
    if psql -U $DB_USER -h $DB_HOST -d $DB_NAME -c "\d $table" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Verified: $table${NC}"
    else
        echo -e "${RED}   ❌ Still missing: $table${NC}"
        ALL_GOOD=false
    fi
done

if [ "$ALL_GOOD" = true ]; then
    echo ""
    echo -e "${GREEN}🎉 All schema issues fixed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Summary:${NC}"
    echo "   • PostGIS extension: Installed"
    echo "   • Critical tables: All present"
    echo "   • Triggers and indexes: Applied"
    echo ""
    echo -e "${GREEN}✅ Database is ready to use!${NC}"
else
    echo ""
    echo -e "${RED}❌ Some issues remain. Please check the errors above.${NC}"
    exit 1
fi
