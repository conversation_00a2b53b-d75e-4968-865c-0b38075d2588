<!-- @format -->

# Superuser Bootstrap Guide

This guide explains how to create the first superuser in the Wizlop system, since superusers are needed to access the admin panel and promote other users.

## Overview

The Wizlop system has a 3-tier user role hierarchy:

1. **Superuser** (highest level)

   - Access to admin panel (`/admin/dashboard`)
   - Access to agent dashboard (`/agent/dashboard`)
   - Can promote users to agents or superusers
   - Full system administration privileges
   - Bypass credit and daily limits

2. **Agent** (middle level)

   - Access to agent dashboard (`/agent/dashboard`)
   - Can review and manage POI submissions
   - Bypass credit and daily limits
   - Cannot access admin panel or manage users

3. **User** (base level)
   - Standard user features only
   - Subject to credit and daily limits
   - Can sign up normally through the registration form

## Permission System

The system uses role-aware permissions:

- **`full_access`**: Complete access to all features (data + user management for superusers, data only for agents)
- **`user_management`**: Limited user management (superusers only - can promote/demote agents but not other superusers)

## The Bootstrap Problem

Since only superusers can access the admin panel to promote other users, you need to manually create the first superuser directly in the database.

## Prerequisites

1. A user must already exist in the system (signed up through normal registration)
2. Access to the PostgreSQL database
3. The database schema must be applied with all required functions
