# Security Implementation Guide

This document outlines the comprehensive security measures implemented to protect admin panel and agent dashboard routes.

## 🔒 **Multi-Layer Security Architecture**

### **Layer 1: Edge Middleware Protection**
- **File**: `application/middleware.ts`
- **Purpose**: Blocks unauthorized requests before they reach pages
- **Features**:
  - JWT token validation
  - Database role verification
  - IP address logging
  - User agent analysis
  - Automatic redirects for unauthorized access
  - Security headers injection

### **Layer 2: Server-Side Route Protection**
- **Files**: 
  - `application/app/admin/dashboard/page.tsx`
  - `application/app/agent/dashboard/page.tsx`
- **Purpose**: Server-side role checking before page rendering
- **Features**:
  - Session validation on server
  - Database role verification
  - Automatic redirects for unauthorized users
  - No client-side code exposure for unauthorized users

### **Layer 3: Client-Side Security Wrappers**
- **File**: `application/components/security/SecurityWrapper.tsx`
- **Purpose**: Additional client-side protection and UX
- **Features**:
  - Real-time permission checking
  - Graceful error handling
  - Custom loading and error components
  - Permission hooks for components

### **Layer 4: API Endpoint Hardening**
- **Files**: All `/api/admin/*` and `/api/agent/*` endpoints
- **Purpose**: Secure API access with comprehensive validation
- **Features**:
  - Enhanced middleware with security validation
  - Rate limiting detection
  - Suspicious activity monitoring
  - Comprehensive audit logging

## 🛡️ **Security Features**

### **Authentication & Authorization**
```typescript
// Role hierarchy (higher numbers have more access)
const roleHierarchy = {
  user: 0,      // Basic user access
  agent: 1,     // Agent dashboard + user features
  superuser: 2  // Admin panel + agent dashboard + user features
}
```

### **Protected Routes**
- `/admin/*` - Requires `superuser` role
- `/agent/*` - Requires `agent` or `superuser` role
- `/api/admin/*` - Requires `superuser` role
- `/api/agent/*` - Requires `agent` or `superuser` role

### **Security Headers**
All protected routes automatically receive:
```typescript
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: camera=(), microphone=(), geolocation=()
```

### **Audit Logging**
All security events are logged to `backend_schema.agent_activity_log`:
- Unauthorized access attempts
- Insufficient permissions
- Suspicious user agents
- Successful authentications
- Role changes and promotions

## 🚨 **Security Event Types**

### **Blocked Events**
- `unauthorized_access_attempt` - No authentication token
- `insufficient_permissions` - Wrong role or missing permissions
- `suspicious_user_agent` - Bot or crawler detected
- `middleware_error` - Security check failure

### **Allowed Events**
- `authorized_access` - Successful access granted
- `api_access_granted` - API endpoint access approved

## 🔧 **Implementation Examples**

### **Using Server-Side Protection**
```typescript
// app/admin/dashboard/page.tsx
export default async function AdminDashboard() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    redirect('/auth/signin?callbackUrl=/admin/dashboard&error=authentication_required')
  }
  
  const isUserSuperuser = await isSuperuser(session.user.id)
  
  if (!isUserSuperuser) {
    redirect('/?error=admin_access_denied')
  }
  
  return <AdminDashboardClient session={session} />
}
```

### **Using Security Wrapper**
```typescript
// For additional client-side protection
import { AdminSecurityWrapper } from '@/components/security/SecurityWrapper'

export default function ProtectedComponent() {
  return (
    <AdminSecurityWrapper>
      <SensitiveAdminContent />
    </AdminSecurityWrapper>
  )
}
```

### **Using Permission Hooks**
```typescript
// For conditional rendering based on permissions
import { usePermissions } from '@/components/security/SecurityWrapper'

export default function ConditionalComponent() {
  const { hasAccess, isLoading } = usePermissions('superuser', ['admin.full_access'])
  
  if (isLoading) return <LoadingSpinner />
  if (!hasAccess) return <AccessDenied />
  
  return <AdminFeature />
}
```

## 🔍 **Security Monitoring**

### **Real-Time Monitoring**
Query the activity log for security events:
```sql
-- Recent unauthorized access attempts
SELECT 
  user_id,
  activity_type,
  target_id,
  activity_data,
  ip_address,
  user_agent,
  created_at
FROM backend_schema.agent_activity_log 
WHERE activity_type IN (
  'unauthorized_access_attempt',
  'insufficient_permissions',
  'suspicious_user_agent'
)
ORDER BY created_at DESC 
LIMIT 50;
```

### **Security Metrics**
```sql
-- Security event summary
SELECT 
  activity_type,
  COUNT(*) as count,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(DISTINCT ip_address) as unique_ips
FROM backend_schema.agent_activity_log 
WHERE created_at >= NOW() - INTERVAL '24 hours'
  AND activity_type LIKE '%security%' OR activity_type LIKE '%unauthorized%'
GROUP BY activity_type
ORDER BY count DESC;
```

## ⚠️ **Security Considerations**

### **What's Protected**
✅ **Route Access** - Middleware blocks unauthorized requests  
✅ **Page Rendering** - Server-side checks prevent page load  
✅ **API Endpoints** - All admin/agent APIs are protected  
✅ **Client Components** - Security wrappers provide additional protection  
✅ **Database Access** - Role-based database functions  
✅ **Audit Trail** - Comprehensive logging of all security events  

### **Attack Vectors Mitigated**
✅ **Direct URL Access** - Middleware redirects unauthorized users  
✅ **Client-Side Bypassing** - Server-side validation prevents this  
✅ **Session Hijacking** - JWT validation and database role checks  
✅ **Privilege Escalation** - Role hierarchy strictly enforced  
✅ **API Abuse** - Enhanced validation and rate limiting detection  
✅ **Social Engineering** - Clear error messages without information leakage  

### **Best Practices Implemented**
✅ **Defense in Depth** - Multiple security layers  
✅ **Principle of Least Privilege** - Users get minimum required access  
✅ **Fail Secure** - Default to deny access on errors  
✅ **Audit Everything** - Comprehensive security event logging  
✅ **Zero Trust** - Verify every request at every layer  

## 🚀 **Testing Security**

### **Manual Testing**
1. **Unauthorized Access**: Try accessing `/admin/dashboard` without login
2. **Insufficient Role**: Login as regular user, try accessing admin panel
3. **Direct API Calls**: Use curl/Postman to test API endpoints
4. **Session Manipulation**: Modify JWT tokens and test access

### **Expected Behaviors**
- Unauthorized users → Redirect to login
- Insufficient role → Redirect to home with error
- Invalid tokens → Authentication required error
- Missing permissions → Access denied with specific error

## 📋 **Security Checklist**

- [x] Edge middleware protection implemented
- [x] Server-side route protection implemented  
- [x] Client-side security wrappers created
- [x] API endpoint hardening completed
- [x] Comprehensive audit logging active
- [x] Security headers configured
- [x] Role hierarchy enforced
- [x] Permission system implemented
- [x] Error handling secured (no info leakage)
- [x] Documentation completed

## 🔄 **Maintenance**

### **Regular Security Tasks**
1. **Review Audit Logs** - Check for suspicious activity weekly
2. **Update Dependencies** - Keep security packages current
3. **Test Access Controls** - Verify role restrictions monthly
4. **Monitor Performance** - Ensure security doesn't impact UX
5. **Review Permissions** - Audit user roles quarterly

### **Security Updates**
When adding new protected routes:
1. Add route to middleware matcher
2. Implement server-side checks
3. Add appropriate logging
4. Test all access scenarios
5. Update documentation

This security implementation provides enterprise-grade protection for your admin panel and agent dashboard while maintaining excellent user experience.
