<!-- @format -->

# Database Migrations - CLEAN STATE

All migrations have been applied to schema files and removed.

## Schema Files Status ✅

- **backend_schema.sql** - Complete with credit system, POI views, INTEGER refs
- **spatial_schema.sql** - Complete with INTEGER IDs, country fields, workflow improvements
- **cross_schema.sql** - Updated sync functions
- **top_location.sql** - Enhanced with country normalization

## Deployment

Run schema files in order:

1. `00_create_extensions_and_schemas.sql`
2. `backend_schema.sql`
3. `spatial_schema.sql`
4. `cross_schema.sql`
5. `top_location.sql`

## Recent Migrations

### 001_add_missing_poi_submission_columns.sql (2025-07-25)

- **Status**: ✅ Applied to database and schema files
- **Description**: Added missing columns to user POI submission tables
- **Changes**:
  - Added `submission_reason VARCHAR(50)` to both `user_pois_temp` and `user_pois_approved`
  - Added `original_poi_id INTEGER` to both tables
  - Added `original_poi_type VARCHAR(20)` to both tables
  - Created indexes for better query performance
  - Updated existing records with appropriate default values

### 002_add_missing_access_control_functions.sql (2025-07-25)

- **Status**: ✅ Applied to database and schema files
- **Description**: Added missing access control functions required by the application
- **Changes**:
  - Added `backend_schema.is_superuser(UUID)` function
  - Added `backend_schema.can_access_admin_panel(UUID)` function
  - Added `backend_schema.can_access_agent_dashboard(UUID)` function
  - Fixed permission mismatch: updated superuser permissions from `["admin.full_access"]` to `["full_access"]`

## Future Migrations

Add new migrations here, apply to database AND schema files, then remove.
