-- Migration: Add missing columns to user POI submission tables
-- Date: 2025-07-25
-- Description: Add submission_reason, original_poi_id, and original_poi_type columns
--              to both user_pois_temp and user_pois_approved tables

-- Start transaction
BEGIN;

-- Add missing columns to spatial_schema.user_pois_temp
ALTER TABLE spatial_schema.user_pois_temp 
ADD COLUMN IF NOT EXISTS submission_reason VARCHAR(50);

ALTER TABLE spatial_schema.user_pois_temp 
ADD COLUMN IF NOT EXISTS original_poi_id INTEGER;

ALTER TABLE spatial_schema.user_pois_temp 
ADD COLUMN IF NOT EXISTS original_poi_type VARCHAR(20);

-- Add missing columns to spatial_schema.user_pois_approved
ALTER TABLE spatial_schema.user_pois_approved 
ADD COLUMN IF NOT EXISTS submission_reason VARCHAR(50);

ALTER TABLE spatial_schema.user_pois_approved 
ADD COLUMN IF NOT EXISTS original_poi_id INTEGER;

ALTER TABLE spatial_schema.user_pois_approved 
ADD COLUMN IF NOT EXISTS original_poi_type VARCHAR(20);

-- Update existing records with default values where appropriate
-- Set submission_reason based on submission_type for existing records
UPDATE spatial_schema.user_pois_temp 
SET submission_reason = CASE 
    WHEN submission_type = 'new_poi' THEN 'new_poi'
    WHEN submission_type = 'info_update' THEN 'info_update'
    WHEN submission_type = 'closure_request' THEN 'closed_business'
    ELSE 'new_poi'
END
WHERE submission_reason IS NULL;

UPDATE spatial_schema.user_pois_approved 
SET submission_reason = CASE 
    WHEN submission_type = 'new_poi' THEN 'new_poi'
    WHEN submission_type = 'info_update' THEN 'info_update'
    WHEN submission_type = 'closure_request' THEN 'closed_business'
    ELSE 'new_poi'
END
WHERE submission_reason IS NULL;

-- Set original_poi_id from target_poi_id for existing records where applicable
UPDATE spatial_schema.user_pois_temp 
SET original_poi_id = target_poi_id
WHERE original_poi_id IS NULL AND target_poi_id IS NOT NULL;

-- Set original_poi_type to 'official' for existing records with original_poi_id
UPDATE spatial_schema.user_pois_temp 
SET original_poi_type = 'official'
WHERE original_poi_id IS NOT NULL AND original_poi_type IS NULL;

UPDATE spatial_schema.user_pois_approved 
SET original_poi_type = 'official'
WHERE original_poi_id IS NOT NULL AND original_poi_type IS NULL;

-- Add indexes for the new columns for better query performance
CREATE INDEX IF NOT EXISTS idx_user_pois_temp_submission_reason 
ON spatial_schema.user_pois_temp(submission_reason);

CREATE INDEX IF NOT EXISTS idx_user_pois_temp_original_poi_id 
ON spatial_schema.user_pois_temp(original_poi_id);

CREATE INDEX IF NOT EXISTS idx_user_pois_approved_submission_reason 
ON spatial_schema.user_pois_approved(submission_reason);

CREATE INDEX IF NOT EXISTS idx_user_pois_approved_original_poi_id 
ON spatial_schema.user_pois_approved(original_poi_id);

-- Commit transaction
COMMIT;

-- Verify the changes
SELECT 'Migration completed successfully. Verifying table structures...' as status;

-- Show the updated table structure for user_pois_temp
\d spatial_schema.user_pois_temp;

-- Show the updated table structure for user_pois_approved  
\d spatial_schema.user_pois_approved;
