-- Migration: Add missing access control functions
-- Date: 2025-07-25
-- Description: Add is_superuser, can_access_admin_panel, and can_access_agent_dashboard functions
--              that are required by the application but missing from the database

-- Start transaction
BEGIN;

-- Function to check if user is a superuser
CREATE OR REPLACE FUNCTION backend_schema.is_superuser(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users 
    WHERE id = user_id_param;
    
    RETURN user_role = 'superuser';
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can access admin panel
CREATE OR REPLACE FUNCTION backend_schema.can_access_admin_panel(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permissions 
    INTO user_role, user_permissions
    FROM backend_schema.nextauth_users 
    WHERE id = user_id_param;
    
    -- Only superusers with appropriate permissions can access admin panel
    IF user_role = 'superuser' THEN
        -- Allow if user has full_access or user_management
        RETURN user_permissions ? 'full_access' OR user_permissions ? 'user_management';
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can access agent dashboard
CREATE OR REPLACE FUNCTION backend_schema.can_access_agent_dashboard(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permissions 
    INTO user_role, user_permissions
    FROM backend_schema.nextauth_users 
    WHERE id = user_id_param;
    
    -- Agents with full_access can access agent dashboard
    IF user_role = 'agent' AND user_permissions ? 'full_access' THEN
        RETURN TRUE;
    END IF;
    
    -- Superusers with full_access can access agent dashboard
    IF user_role = 'superuser' AND user_permissions ? 'full_access' THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Commit transaction
COMMIT;

-- Verify the functions were created
SELECT 'Migration completed successfully. Verifying functions...' as status;

-- Test the functions exist
SELECT 
    'is_superuser function exists' as test,
    CASE WHEN EXISTS (
        SELECT 1 FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'backend_schema' AND p.proname = 'is_superuser'
    ) THEN 'PASS' ELSE 'FAIL' END as result;

SELECT 
    'can_access_admin_panel function exists' as test,
    CASE WHEN EXISTS (
        SELECT 1 FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'backend_schema' AND p.proname = 'can_access_admin_panel'
    ) THEN 'PASS' ELSE 'FAIL' END as result;

SELECT 
    'can_access_agent_dashboard function exists' as test,
    CASE WHEN EXISTS (
        SELECT 1 FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'backend_schema' AND p.proname = 'can_access_agent_dashboard'
    ) THEN 'PASS' ELSE 'FAIL' END as result;
