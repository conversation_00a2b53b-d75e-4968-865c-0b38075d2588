-- cross_schema.sql
-- Cross-schema logic for Wizlop

-- NOTE: No direct foreign key from user_added_locations to city_boundaries.
-- Spatial relationships should be handled via spatial queries (e.g., ST_Contains).
-- If a direct city_boundary_id FK is needed, add it to user_added_locations and reference city_boundaries(id).

-- Example: View joining users and their added locations
CREATE OR REPLACE VIEW backend_schema.vw_user_locations AS
SELECT u.id AS user_id, u.username, l.id AS location_id, l.name, l.latitude, l.longitude, l.geom
FROM backend_schema.nextauth_users u
JOIN backend_schema.user_added_locations l ON u.id = l.user_id;

-- Example: View joining approved user POIs with submitter and approver info
CREATE OR REPLACE VIEW spatial_schema.vw_user_pois_approved_details AS
SELECT a.id AS poi_id, a.name, a.city, a.category, a.created_at, a.updated_at,
       u.username AS submitted_by, approver.username AS approved_by
FROM spatial_schema.user_pois_approved a
LEFT JOIN backend_schema.nextauth_users u ON a.submitted_by_user_id = u.id
LEFT JOIN backend_schema.nextauth_users approver ON a.approved_by_admin_id = approver.id;

-- Add other cross-schema constraints, views, or logic as needed

-- Cross-schema synchronization triggers
CREATE OR REPLACE FUNCTION sync_backend_interactions_to_spatial()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.poi_type = 'official' THEN
      UPDATE spatial_schema.pois
      SET
        favorite_count = favorite_count + 1,
        popularity_score = popularity_score + 5,
        promoted_at = CASE WHEN promoted_at IS NULL THEN CURRENT_TIMESTAMP ELSE promoted_at END
      WHERE id = NEW.poi_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.poi_type = 'official' THEN
      UPDATE spatial_schema.pois
      SET
        favorite_count = favorite_count - 1,
        popularity_score = popularity_score - 5
      WHERE id = OLD.poi_id;
    END IF;
  END IF;
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS sync_favorites_to_spatial ON backend_schema.user_favorites;
CREATE TRIGGER sync_favorites_to_spatial
  AFTER INSERT OR DELETE ON backend_schema.user_favorites
  FOR EACH ROW EXECUTE FUNCTION sync_backend_interactions_to_spatial();

CREATE OR REPLACE FUNCTION sync_reviews_to_spatial()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.poi_type = 'official' THEN
      UPDATE spatial_schema.pois 
      SET 
        review_count = review_count + 1,
        popularity_score = popularity_score + 3,
        promoted_at = CASE WHEN promoted_at IS NULL THEN CURRENT_TIMESTAMP ELSE promoted_at END
      WHERE id = NEW.poi_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.poi_type = 'official' THEN
      UPDATE spatial_schema.pois 
      SET 
        review_count = review_count - 1,
        popularity_score = popularity_score - 3
      WHERE id = OLD.poi_id;
    END IF;
  END IF;
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS sync_reviews_to_spatial ON backend_schema.user_location_reviews;
CREATE TRIGGER sync_reviews_to_spatial
  AFTER INSERT OR DELETE ON backend_schema.user_location_reviews
  FOR EACH ROW EXECUTE FUNCTION sync_reviews_to_spatial();

CREATE OR REPLACE FUNCTION sync_visits_to_spatial()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.poi_type = 'official' THEN
      UPDATE spatial_schema.pois 
      SET 
        visit_count = visit_count + 1,
        popularity_score = popularity_score + 2,
        promoted_at = CASE WHEN promoted_at IS NULL THEN CURRENT_TIMESTAMP ELSE promoted_at END
      WHERE id = NEW.poi_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.poi_type = 'official' THEN
      UPDATE spatial_schema.pois 
      SET 
        visit_count = visit_count - 1,
        popularity_score = popularity_score - 2
      WHERE id = OLD.poi_id;
    END IF;
  END IF;
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS sync_visits_to_spatial ON backend_schema.user_location_visits;
CREATE TRIGGER sync_visits_to_spatial
  AFTER INSERT OR DELETE ON backend_schema.user_location_visits
  FOR EACH ROW EXECUTE FUNCTION sync_visits_to_spatial();

-- Add cross-schema triggers, functions, and indexes here as needed 