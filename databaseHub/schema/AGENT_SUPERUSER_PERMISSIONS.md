# Agent and Superuser Permissions System Documentation

## Overview
This document details the permissions system for the `agent` and `superuser` roles in the Wizlop backend database schema. It covers roles, permissions, role-aware permission logic, and best practices for safe assignment and checking.

---

## Table of Contents
1. [User Roles and Permissions Fields](#user-roles-and-permissions-fields)
2. [Permission Model](#permission-model)
3. [Role-Aware Permission Logic](#role-aware-permission-logic)
4. [Promotion and User Management](#promotion-and-user-management)
5. [Usage Examples](#usage-examples)
6. [Summary Table](#summary-table)
7. [Best Practices and Safeguards](#best-practices-and-safeguards)
8. [Notes and Warnings](#notes-and-warnings)

---

## 1. User Roles and Permissions Fields

- **Table:** `backend_schema.nextauth_users`
  - `role` (`VARCHAR(20)`): Can be `user`, `agent`, or `superuser`.
  - `permissions` (`JSONB`): Array of permission strings. Used for agents and superusers.

---

## 2. Permission Model

### Superuser
- **Role:** `superuser`
- **Permissions:**
  - `full_access`: Grants all data and user management powers, including promoting/demoting superusers.
  - `user_management`: Grants limited user management (can only promote users to agent and demote agents to user; cannot promote to superuser or demote superusers).
- **Summary:**
  - Superuser with `full_access` can do everything, including all user management actions.
  - Superuser with only `user_management` can only manage agents (not superusers).

### Agent
- **Role:** `agent`
- **Permissions:**
  - `full_access`: Grants all data-related access (e.g., POI review, edit, delete, analytics, etc.).
- **Summary:**
  - Agent with `full_access` can do everything data-related, but **cannot** access user management or admin features, even if mistakenly assigned `user_management`.

### Regular User
- **Role:** `user`
- **Permissions:**
  - No special permissions. Any permissions assigned are ignored.

---

## 3. Role-Aware Permission Logic

**Permissions are only effective if the user's role allows it.**

- If a user has the `full_access` permission **and** their role is `superuser`, they have all powers, including user management (promote/demote anyone, including superusers).
- If a user has the `user_management` permission **and** their role is `superuser`, they can only manage agents (promote to agent, demote agent to user), not superusers.
- If an agent is assigned `user_management`, it is **ignored**—agents cannot manage users, regardless of permissions.
- If a normal user is assigned `full_access` or `user_management`, these are **ignored**—users cannot manage data or users, regardless of permissions.
- **Permission checks must always consider both role and permission.**

**Example Logic (Pseudocode):**
```sql
-- Check if user can manage superusers
IF user.role = 'superuser' AND 'full_access' IN user.permissions THEN
    -- Allow all user management actions, including superuser management
ELSEIF user.role = 'superuser' AND 'user_management' IN user.permissions THEN
    -- Allow only agent management (promote/demote agents)
ELSE
    -- Deny user management actions
END IF

-- For agent
IF user.role = 'agent' AND 'user_management' IN user.permissions THEN
    -- DENY: agent cannot use user_management, treat as null
END IF
```

---

## 4. Promotion and User Management

- **Superuser with `full_access`** can:
  - Promote user → agent
  - Promote agent → superuser
  - Demote agent → user
  - Demote superuser
- **Superuser with only `user_management`** can:
  - Promote user → agent
  - Demote agent → user
  - **Cannot** promote to superuser or demote superusers
- **Agent** cannot access or perform any user management actions, even if assigned `user_management`.

---

## 5. Usage Examples

### Example: Checking Permissions
```sql
-- Check if user has full access
SELECT backend_schema.user_has_permission('user-uuid', 'full_access');

-- Check if user can manage users
SELECT backend_schema.user_has_permission('user-uuid', 'user_management');
```

### Example: Promoting a User to Agent
```sql
-- Only superuser with user_management or full_access can do this
SELECT backend_schema.promote_user_to_agent('user-uuid', 'superuser-uuid', '["full_access"]');
```

### Example: Promoting a User to Superuser
```sql
-- Only superuser with full_access can do this
SELECT backend_schema.promote_user_to_superuser('user-uuid', 'superuser-uuid', '["full_access"]');
```

---

## 6. Summary Table

| Role        | Permission         | Effective? | What Can They Do?                                 |
|-------------|-------------------|------------|---------------------------------------------------|
| superuser   | full_access       | Yes        | All powers, including superuser management         |
| superuser   | user_management   | Yes        | Only agent management (promote/demote agents)      |
| agent       | full_access       | Yes        | All data management, no user management            |
| agent       | user_management   | **No**     | Ignored, agent cannot manage users                 |
| user        | full_access       | **No**     | Ignored, user cannot manage data/users             |
| user        | user_management   | **No**     | Ignored, user cannot manage users                  |

---

## 7. Best Practices and Safeguards
- **Always check both role and permission** in your backend logic.
- Consider adding database triggers or application logic to prevent assigning `user_management` to agents or users, and `full_access` to users.
- Regularly audit user roles and permissions for correctness.
- Document the intended permission model for future developers.

---

## 8. Notes and Warnings
- **Permissions are only effective if the user's role allows it.**
- Assigning `user_management` to an agent or user, or `full_access` to a user, has no effect.
- Only superusers with `full_access` can manage (promote/demote) superusers.
- Only superusers with `user_management` (but not `full_access`) can manage agents, not superusers.
- Agents can never manage users, regardless of permissions.
- This model is simple and can be extended in the future if more granular permissions are needed. 