-- Enhanced get_top_locations function with country normalization support
CREATE OR REPLACE FUNCTION spatial_schema.get_top_locations(
    p_category VARCHAR DEFAULT NULL,
    p_subcategory VARCHAR DEFAULT NULL,
    p_city VARCHAR DEFAULT NULL,
    p_district VARCHAR DEFAULT NULL,
    p_neighborhood VARCHAR DEFAULT NULL,
    p_country VARCHAR DEFAULT NULL,  -- Can be 'Turkey' or 'Türkiye'
    p_min_rating DOUBLE PRECISION DEFAULT NULL,
    p_max_rating DOUBLE PRECISION DEFAULT NULL,
    p_admin_level INTEGER DEFAULT NULL, -- 10 (country), 8 (region), 6 (city)
    p_admin_name VARCHAR DEFAULT NULL,  -- e.g., 'Türkiye', 'Istanbul'
    p_min_lat DOUBLE PRECISION DEFAULT NULL, -- Optional bounding box
    p_max_lat DOUBLE PRECISION DEFAULT NULL,
    p_min_lng DOUBLE PRECISION DEFAULT NULL,
    p_max_lng DOUBLE PRECISION DEFAULT NULL,
    p_offset INTEGER DEFAULT 0,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
    id INTEGER,
    name VARCHAR(255),
    category VARCHAR(250),
    subcategory VARCHAR(250),
    cuisine VARCHAR(250),
    city VARCHAR(250),
    district VARCHAR(250),
    neighborhood VARCHAR(250),
    country VARCHAR(100),  -- NEW: Include country in return
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    user_rating_avg DOUBLE PRECISION,
    user_rating_count INTEGER,
    view_count INTEGER,
    favorite_count INTEGER,
    visit_count INTEGER,
    like_count INTEGER,
    review_count INTEGER,
    popularity_score INTEGER,
    trending_score INTEGER,
    final_score DOUBLE PRECISION,
    score_breakdown JSONB
) AS $$
DECLARE
    has_meaningful_data BOOLEAN := FALSE;
    rating_weight DOUBLE PRECISION := 0;
    engagement_weight DOUBLE PRECISION := 0;
    popularity_weight DOUBLE PRECISION := 0;
    trending_weight DOUBLE PRECISION := 0;
    total_weight DOUBLE PRECISION := 0;
    normalized_country VARCHAR(100);
BEGIN
    -- Normalize country name to handle both English and Turkish
    IF p_country IS NOT NULL THEN
        normalized_country := CASE
            WHEN LOWER(p_country) IN ('turkey', 'türkiye', 'turkiye') THEN 'Türkiye'
            ELSE p_country
        END;
    ELSE
        normalized_country := NULL;
    END IF;

    -- Check if we have meaningful data for adaptive scoring
    SELECT
        COUNT(*) > 0
    INTO has_meaningful_data
    FROM spatial_schema.pois p
    WHERE
        (p_category IS NULL OR p.category = p_category)
        AND (p_subcategory IS NULL OR p.subcategory = p_subcategory)
        AND (p_city IS NULL OR p.city = p_city)
        AND (p_district IS NULL OR p.district = p_district)
        AND (p_neighborhood IS NULL OR p.neighborhood = p_neighborhood)
        AND (normalized_country IS NULL OR p.country = normalized_country)
        AND p.status = 'active'
        AND (
            (p.user_rating_avg IS NOT NULL AND p.user_rating_avg > 0) OR
            (p.user_rating_count > 0) OR
            (p.view_count > 0) OR
            (p.favorite_count > 0) OR
            (p.visit_count > 0) OR
            (p.like_count > 0) OR
            (p.review_count > 0) OR
            (p.popularity_score > 0) OR
            (p.trending_score > 0)
        );

    -- Set weights based on data availability
    IF has_meaningful_data THEN
        rating_weight := 0.4;
        engagement_weight := 0.3;
        popularity_weight := 0.2;
        trending_weight := 0.1;
    ELSE
        -- Fallback to random scoring when no meaningful data
        rating_weight := 0;
        engagement_weight := 0;
        popularity_weight := 0;
        trending_weight := 0;
    END IF;

    total_weight := rating_weight + engagement_weight + popularity_weight + trending_weight;

    -- Main query with adaptive scoring
    RETURN QUERY
    WITH poi_scores AS (
        SELECT
            p.*,
            -- Rating score (0-1)
            CASE
                WHEN p.user_rating_avg IS NOT NULL AND p.user_rating_avg > 0 AND p.user_rating_count > 0 THEN
                    (p.user_rating_avg / 5.0) * (1 + LOG(GREATEST(p.user_rating_count, 1)) / 10.0)
                ELSE 0
            END AS rating_score,

            -- Engagement score (0-1)
            CASE
                WHEN (p.view_count + p.favorite_count + p.visit_count + p.like_count) > 0 THEN
                    LEAST(1.0, (p.view_count + p.favorite_count * 2 + p.visit_count * 3 + p.like_count * 1.5) / 100.0)
                ELSE 0
            END AS engagement_score,

            -- Normalized popularity score (0-1)
            CASE
                WHEN p.popularity_score > 0 THEN
                    LEAST(1.0, p.popularity_score / 100.0)
                ELSE 0
            END AS popularity_score_norm,

            -- Normalized trending score (0-1)
            CASE
                WHEN p.trending_score > 0 THEN
                    LEAST(1.0, p.trending_score / 100.0)
                ELSE 0
            END AS trending_score_norm,

            -- Random component for variety
            RANDOM() AS random_component

        FROM spatial_schema.pois p
        LEFT JOIN spatial_schema.admin_boundaries ab
            ON p.city = ab.name AND ab.admin_level = COALESCE(p_admin_level, ab.admin_level)
        WHERE
            (p_category IS NULL OR p.category = p_category)
            AND (p_subcategory IS NULL OR p.subcategory = p_subcategory)
            AND (p_city IS NULL OR p.city = p_city)
            AND (p_district IS NULL OR p.district = p_district)
            AND (p_neighborhood IS NULL OR p.neighborhood = p_neighborhood)
            AND (normalized_country IS NULL OR p.country = normalized_country)
            AND (p_min_rating IS NULL OR p.user_rating_avg >= p_min_rating)
            AND (p_max_rating IS NULL OR p.user_rating_avg <= p_max_rating)
            AND (p_admin_level IS NULL OR ab.admin_level = p_admin_level)
            AND (p_admin_name IS NULL OR ab.name = p_admin_name)
            AND p.status = 'active'
            AND (p_min_lat IS NULL OR p.latitude >= p_min_lat)
            AND (p_max_lat IS NULL OR p.latitude <= p_max_lat)
            AND (p_min_lng IS NULL OR p.longitude >= p_min_lng)
            AND (p_max_lng IS NULL OR p.longitude <= p_max_lng)
    ),
    scored_pois AS (
        SELECT 
            *,
            CASE 
                WHEN total_weight > 0 THEN
                    (rating_score * rating_weight + 
                     engagement_score * engagement_weight + 
                     popularity_score_norm * popularity_weight + 
                     trending_score_norm * trending_weight) / total_weight +
                    (random_component * 0.05)
                ELSE
                    random_component
            END AS final_score,
            
            jsonb_build_object(
                'has_meaningful_data', total_weight > 0,
                'rating_score', rating_score,
                'engagement_score', engagement_score,
                'popularity_score', popularity_score_norm,
                'trending_score', trending_score_norm,
                'weights', jsonb_build_object(
                    'rating', rating_weight,
                    'engagement', engagement_weight,
                    'popularity', popularity_weight,
                    'trending', trending_weight,
                    'total', total_weight
                ),
                'random_component', random_component
            ) AS score_breakdown
            
        FROM poi_scores
    )
    SELECT 
        scored_pois.id,
        scored_pois.name,
        scored_pois.category,
        scored_pois.subcategory,
        scored_pois.cuisine,
        scored_pois.city,
        scored_pois.district,
        scored_pois.neighborhood,
        scored_pois.country,  -- NEW: Include country in output
        scored_pois.latitude,
        scored_pois.longitude,
        scored_pois.user_rating_avg,
        scored_pois.user_rating_count,
        scored_pois.view_count,
        scored_pois.favorite_count,
        scored_pois.visit_count,
        scored_pois.like_count,
        scored_pois.review_count,
        scored_pois.popularity_score,
        scored_pois.trending_score,
        scored_pois.final_score,
        scored_pois.score_breakdown
    FROM scored_pois
    ORDER BY final_score DESC, id
    OFFSET p_offset
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Helper function to get scoring statistics with country normalization
CREATE OR REPLACE FUNCTION spatial_schema.get_scoring_stats(
    p_category VARCHAR DEFAULT NULL,
    p_city VARCHAR DEFAULT NULL,
    p_country VARCHAR DEFAULT NULL
)
RETURNS TABLE (
    total_pois INTEGER,
    pois_with_ratings INTEGER,
    pois_with_engagement INTEGER,
    pois_with_popularity INTEGER,
    pois_with_trending INTEGER,
    avg_rating DOUBLE PRECISION,
    avg_engagement_score DOUBLE PRECISION,
    scoring_mode TEXT
) AS $$
DECLARE
    normalized_country VARCHAR(100);
BEGIN
    -- Normalize country name to handle both English and Turkish
    IF p_country IS NOT NULL THEN
        normalized_country := CASE
            WHEN LOWER(p_country) IN ('turkey', 'türkiye', 'turkiye') THEN 'Türkiye'
            ELSE p_country
        END;
    ELSE
        normalized_country := NULL;
    END IF;

    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_pois,
        COUNT(*) FILTER (WHERE p.user_rating_avg IS NOT NULL AND p.user_rating_avg > 0 AND p.user_rating_count > 0)::INTEGER as pois_with_ratings,
        COUNT(*) FILTER (WHERE p.view_count > 0 OR p.favorite_count > 0 OR p.visit_count > 0 OR p.like_count > 0)::INTEGER as pois_with_engagement,
        COUNT(*) FILTER (WHERE p.popularity_score > 0)::INTEGER as pois_with_popularity,
        COUNT(*) FILTER (WHERE p.trending_score > 0)::INTEGER as pois_with_trending,
        AVG(p.user_rating_avg) FILTER (WHERE p.user_rating_avg IS NOT NULL AND p.user_rating_avg > 0) as avg_rating,
        AVG(p.view_count + p.favorite_count + p.visit_count + p.like_count) FILTER (WHERE p.view_count > 0 OR p.favorite_count > 0 OR p.visit_count > 0 OR p.like_count > 0) as avg_engagement_score,
        CASE
            WHEN COUNT(*) FILTER (WHERE
                (p.user_rating_avg IS NOT NULL AND p.user_rating_avg > 0) OR
                (p.user_rating_count > 0) OR
                (p.view_count > 0) OR
                (p.favorite_count > 0) OR
                (p.visit_count > 0) OR
                (p.like_count > 0) OR
                (p.review_count > 0) OR
                (p.popularity_score > 0) OR
                (p.trending_score > 0)
            ) > 0 THEN 'WEIGHTED_SCORING'
            ELSE 'RANDOM_SCORING'
        END as scoring_mode
    FROM spatial_schema.pois p
    WHERE
        (p_category IS NULL OR p.category = p_category)
        AND (p_city IS NULL OR p.city = p_city)
        AND (normalized_country IS NULL OR p.country = normalized_country)
        AND p.status = 'active';
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON FUNCTION spatial_schema.get_top_locations IS 'Enhanced function that supports both English (Turkey) and Turkish (Türkiye) country names for better API compatibility';
COMMENT ON FUNCTION spatial_schema.get_scoring_stats IS 'Scoring statistics function that supports both English and Turkish country names';
