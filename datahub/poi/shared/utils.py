import time
import logging
import requests
import pandas as pd
from .config import NAME_FIELDS, OVERPASS_URL

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("poi_utils")


def exponential_backoff(retries):
    """Calculate exponential backoff delay"""
    return min(60, (2 ** retries) + (0.1 * retries))


def has_any_name(row):
    """Check if row has any name field filled"""
    return any(str(row[field]).strip() for field in NAME_FIELDS if field in row)


def has_lat_lon(row):
    """Check if row has valid latitude and longitude"""
    try:
        lat = float(row['latitude'])
        lon = float(row['longitude'])
        return lat != 0 and lon != 0
    except (KeyError, ValueError, TypeError):
        return False


def get_country_from_coordinates(lat, lon, max_retries=3, delay=1):
    """
    Get country name from latitude and longitude using Nominatim reverse geocoding.
    For Turkey POI data, we use simple bounds checking first.
    """
    # Simple bounds check for Türkiye (approximate)
    # Türkiye bounds: lat 36-42, lon 26-45
    if 36 <= lat <= 42 and 26 <= lon <= 45:
        return 'Türkiye'

    # If coordinates are outside Turkey bounds, try reverse geocoding
    try:
        url = f"https://nominatim.openstreetmap.org/reverse?format=json&lat={lat}&lon={lon}&zoom=3"
        headers = {'User-Agent': 'Wizlop-POI-Processor/1.0'}

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if 'address' in data and 'country' in data['address']:
                        return data['address']['country']
                time.sleep(delay)
            except Exception as e:
                logger.warning(
                    f"Attempt {attempt + 1} failed for coordinates {lat}, {lon}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(delay * (attempt + 1))
    except Exception as e:
        logger.error(
            f"Error getting country for coordinates {lat}, {lon}: {e}")

    return 'Unknown'


def build_overpass_query(city, key, value):
    """Build Overpass API query for extracting POIs"""
    return f'''
    [out:json][timeout:60];
    area["name"="{city}"]->.searchArea;
    (
      node[{key}="{value}"](area.searchArea);
      way[{key}="{value}"](area.searchArea);
      relation[{key}="{value}"](area.searchArea);
    );
    out center meta;
    '''


def make_overpass_request(query, max_retries=3):
    """Make request to Overpass API with retry logic"""
    from config import OVERPASS_URL

    for attempt in range(max_retries):
        try:
            response = requests.post(OVERPASS_URL, data=query, timeout=120)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:  # Rate limited
                wait_time = exponential_backoff(attempt)
                logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error(f"HTTP {response.status_code}: {response.text}")
                return None
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout on attempt {attempt + 1}")
            if attempt < max_retries - 1:
                time.sleep(exponential_backoff(attempt))
        except Exception as e:
            logger.error(f"Request failed on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                time.sleep(exponential_backoff(attempt))

    return None


def clean_phone_number(phone):
    """Clean and standardize phone numbers"""
    if pd.isna(phone) or phone == '':
        return ''

    phone = str(phone).strip()
    # Remove common separators and spaces
    phone = phone.replace(' ', '').replace(
        '-', '').replace('(', '').replace(')', '')

    # Add Turkish country code if missing
    if phone.startswith('0') and len(phone) == 11:
        phone = '+90' + phone[1:]
    elif phone.startswith('90') and len(phone) == 12:
        phone = '+' + phone
    elif not phone.startswith('+') and len(phone) == 10:
        phone = '+90' + phone

    return phone


def validate_poi_data(df):
    """Validate POI dataframe and return cleaned version"""
    logger.info(f"Validating {len(df)} POI records...")

    # Remove rows without names or coordinates
    df_clean = df[df.apply(lambda row: has_any_name(row)
                           and has_lat_lon(row), axis=1)]

    # Clean phone numbers
    if 'phone_number' in df_clean.columns:
        df_clean['phone_number'] = df_clean['phone_number'].apply(
            clean_phone_number)

    # Remove duplicates based on coordinates (within 10 meters)
    df_clean = df_clean.drop_duplicates(subset=['latitude', 'longitude'])

    logger.info(
        f"Validation complete: {len(df)} -> {len(df_clean)} records kept")
    return df_clean


def get_cities_for_country(country_code, admin_levels=[4]):
    """
    Extract cities/administrative areas dynamically from OSM data for any country.

    This function is country-agnostic and can work with any ISO country code.

    Args:
        country_code (str): ISO 3166-1 alpha-2 country code (e.g., 'TR', 'US', 'DE')
        admin_levels (list): List of admin levels to extract (default: [4])
                           4 = provinces/states (main administrative divisions)

    Returns:
        list: List of city/administrative area names extracted from OSM data
    """
    try:
        # Build Overpass query - use a more direct approach
        if country_code == 'TR':
            # For Turkey, search for provinces (admin_level=4) only
            # Turkey has 81 provinces which are the main administrative divisions
            # Use a broader search within Turkey's bounding box
            overpass_query = """
            [out:json][timeout:120];
            (
              relation["admin_level"="4"](bbox:35.8,25.6,42.1,44.8);
            );
            out tags;
            """
        else:
            # For other countries, use the generic approach
            admin_level_queries = []
            for level in admin_levels:
                admin_level_queries.append(
                    f'relation["admin_level"="{level}"]["ISO3166-1"="{country_code}"];')

            overpass_query = f"""
            [out:json][timeout:120];
            (
              {chr(10).join(admin_level_queries)}
            );
            out tags;
            """

        logger.info(
            f"Extracting cities for country {country_code} from OSM data...")
        response = make_overpass_request(overpass_query)

        if response and 'elements' in response:
            cities = set()
            for element in response['elements']:
                tags = element.get('tags', {})

                # For Turkey, filter to only include Turkish provinces
                if country_code == 'TR':
                    # Check if this is actually a Turkish province
                    is_turkish = (
                        tags.get('is_in:country') == 'Turkey' or
                        tags.get('is_in:country') == 'TR' or
                        tags.get('country') == 'Turkey' or
                        tags.get('country') == 'TR' or
                        tags.get('ISO3166-1') == 'TR' or
                        'Turkey' in tags.get('is_in', '') or
                        'TR' in tags.get('is_in', '') or
                        tags.get('name:tr')  # Has Turkish name
                    )
                    if not is_turkish:
                        continue

                # Try different name fields, prefer local language names
                name = (tags.get(f'name:{country_code.lower()}') or
                        tags.get('name') or
                        tags.get('name:en'))
                if name and len(name.strip()) > 0:
                    cities.add(name.strip())

            city_list = sorted(list(cities))
            logger.info(
                f"Extracted {len(city_list)} cities for {country_code}")
            return city_list

    except Exception as e:
        logger.error(f"Error extracting cities for {country_code}: {e}")
        raise RuntimeError(
            f"Could not extract cities for {country_code}. Please check your internet connection and try again.")

    return []


def get_turkish_cities():
    """
    Convenience function to get Turkish cities.
    Returns the 81 Turkish provinces.

    For now, using a curated list of Turkish provinces since OSM queries
    are picking up too much noise from neighboring countries.
    """
    # The 81 Turkish provinces (İl)
    turkish_provinces = [
        'Adana', 'Adıyaman', 'Afyonkarahisar', 'Ağrı', 'Amasya', 'Ankara', 'Antalya',
        'Artvin', 'Aydın', 'Balıkesir', 'Bilecik', 'Bingöl', 'Bitlis', 'Bolu',
        'Burdur', 'Bursa', 'Çanakkale', 'Çankırı', 'Çorum', 'Denizli', 'Diyarbakır',
        'Edirne', 'Elazığ', 'Erzincan', 'Erzurum', 'Eskişehir', 'Gaziantep', 'Giresun',
        'Gümüşhane', 'Hakkâri', 'Hatay', 'Isparta', 'Mersin', 'İstanbul', 'İzmir',
        'Kars', 'Kastamonu', 'Kayseri', 'Kırklareli', 'Kırşehir', 'Kocaeli', 'Konya',
        'Kütahya', 'Malatya', 'Manisa', 'Kahramanmaraş', 'Mardin', 'Muğla', 'Muş',
        'Nevşehir', 'Niğde', 'Ordu', 'Rize', 'Sakarya', 'Samsun', 'Siirt', 'Sinop',
        'Sivas', 'Tekirdağ', 'Tokat', 'Trabzon', 'Tunceli', 'Şanlıurfa', 'Uşak',
        'Van', 'Yozgat', 'Zonguldak', 'Aksaray', 'Bayburt', 'Karaman', 'Kırıkkale',
        'Batman', 'Şırnak', 'Bartın', 'Ardahan', 'Iğdır', 'Yalova', 'Karabük',
        'Kilis', 'Osmaniye', 'Düzce'
    ]

    logger.info(
        f"Using curated list of {len(turkish_provinces)} Turkish provinces")
    return sorted(turkish_provinces)
