"""
Configuration for Cafe & Restaurant POI Extraction

This module contains specific configuration for extracting
cafe and restaurant POIs from OpenStreetMap data.
"""


from ..shared.utils import get_turkish_cities
from pathlib import Path
import sys
import os
import importlib

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
if str(shared_path) not in sys.path:
    sys.path.insert(0, str(shared_path))

# Dynamic import of shared configuration
shared_config = importlib.import_module('poi.shared.config')

ROOT_DIR = shared_config.ROOT_DIR
DATA_DIR = shared_config.DATA_DIR
RAW_DATA_DIR = shared_config.RAW_DATA_DIR
OUTPUT_DIR = shared_config.OUTPUT_DIR
TURKEY_OSM_PBF_PATH = shared_config.TURKEY_OSM_PBF_PATH
LANGUAGE_CODES = shared_config.LANGUAGE_CODES
# Import Turkish cities function from utils
TURKISH_CITIES = get_turkish_cities()
COMMON_COLUMNS = shared_config.COMMON_COLUMNS
NAME_FIELDS = shared_config.NAME_FIELDS
OVERPASS_URL = shared_config.OVERPASS_URL
DB_CONFIG = shared_config.DB_CONFIG
get_poi_output_dir = shared_config.get_poi_output_dir
sanitize_filename = shared_config.sanitize_filename

# POI Type specific configuration
POI_TYPE = "cafe_restaurant"
POI_OUTPUT_DIR = get_poi_output_dir(POI_TYPE)

# Specific amenities and shops for cafes and restaurants
CAFE_RESTAURANT_AMENITIES = {
    'amenity': ['restaurant', 'cafe'],
    'shop': ['restaurant', 'cafe']
}

# Output files for this POI type
RESTAURANTS_CSV = POI_OUTPUT_DIR / "restaurants.csv"
CAFES_CSV = POI_OUTPUT_DIR / "cafes.csv"

# Subcategory mapping
SUBCATEGORY_MAPPING = {
    'amenity': {
        'restaurant': 'Restaurant',
        'cafe': 'Cafe'
    },
    'shop': {
        'restaurant': 'Restaurant',
        'cafe': 'Cafe'
    }
}

# Category for all cafe/restaurant POIs
CATEGORY = "Food & Drink"


def get_amenities():
    """Get amenities configuration for cafe/restaurant extraction"""
    return CAFE_RESTAURANT_AMENITIES


def get_subcategory(osm_key, osm_value):
    """Get subcategory based on OSM key and value"""
    return SUBCATEGORY_MAPPING.get(osm_key, {}).get(osm_value, osm_value.title())
