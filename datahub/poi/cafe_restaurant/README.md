# Cafe & Restaurant POI Extraction Pipeline

This module extracts cafe and restaurant Points of Interest (POIs) from OpenStreetMap data for Turkish cities using a dynamic city extraction approach.

## 🏗️ Pipeline Architecture

The pipeline consists of 5 main steps that should be executed in order:

### Step 1: Download OSM Data
```bash
cd datahub
python3 poi/shared/download_osm_data.py
```
**What it does:** Downloads the latest Turkey OSM data file (~569MB) if not already present.

### Step 2: Extract POIs by City
```bash
cd datahub
python3 poi/cafe_restaurant/extract_osm_by_city.py
```
**What it does:** 
- Dynamically extracts 81 Turkish cities from OSM data
- Processes each city for cafe and restaurant POIs
- Shows progress bars for each city and overall progress
- Extracts 4 POI types per city: `amenity=cafe`, `amenity=restaurant`, `shop=cafe`, `shop=restaurant`
- Creates individual CSV files for each city/type combination

### Step 3: Clean Data
```bash
cd datahub
python3 poi/cafe_restaurant/clean_data.py
```
**What it does:**
- Validates and cleans the extracted POI data
- Removes invalid entries and duplicates
- Standardizes data formats
- Ensures data quality

### Step 4: Merge Data
```bash
cd datahub
python3 poi/cafe_restaurant/merge_data.py
```
**What it does:**
- Combines all individual city CSV files into a single dataset
- Creates `all_cities_merged.csv` with all POI data
- Maintains data structure and relationships

### Step 5: Spatial Database Enrichment (Optional)
```bash
cd datahub
python3 poi/shared/database_enrichment.py
```
**What it does:**
- Enriches POI data with additional spatial information
- Connects to PostgreSQL/PostGIS database
- Adds geographic context and administrative boundaries

## 🎯 Key Features

- **Dynamic City Extraction**: Uses live OSM data to get current Turkish provinces (81 cities)
- **Progress Monitoring**: Real-time progress bars showing city-by-city extraction
- **Modular Design**: Each step can be run independently
- **Error Handling**: Continues processing even if individual cities fail
- **Data Quality**: Built-in validation and cleaning processes

## 📊 Output Structure

```
datahub/data/output/cafe_restaurant/
├── Adana/
│   ├── amenity_cafe.csv
│   ├── amenity_restaurant.csv
│   ├── shop_cafe.csv
│   └── shop_restaurant.csv
├── Ankara/
│   └── ... (same structure)
├── ... (81 cities total)
└── all_cities_merged.csv (final combined dataset)
```

## 🔧 Configuration

- **Main Config**: `poi/cafe_restaurant/cafe_restaurant_config.py`
- **Shared Config**: `poi/shared/config.py`
- **City Extraction**: Uses `get_turkish_cities()` function from `poi/shared/utils.py`

## 📈 Expected Results

- **Cities Processed**: 81 Turkish provinces
- **POI Types**: 4 types per city (324 total combinations)
- **Output Files**: ~324 individual CSV files + 1 merged file
- **Processing Time**: ~2-3 hours depending on network speed

## 🚀 Quick Start (Full Pipeline)

To run all steps in sequence:

```bash
cd datahub

# Step 1: Download OSM data
python3 poi/shared/download_osm_data.py

# Step 2: Extract POIs (with progress bars)
python3 poi/cafe_restaurant/extract_osm_by_city.py

# Step 3: Clean data
python3 poi/cafe_restaurant/clean_data.py

# Step 4: Merge data
python3 poi/cafe_restaurant/merge_data.py

# Step 5: Optional enrichment
python3 poi/shared/database_enrichment.py
```

## 📋 Data Fields

Each POI record contains:
- `latitude, longitude` - Geographic coordinates
- `city, district, neighborhood, street` - Location details
- `phone_number` - Contact information
- `opening_hours` - Operating hours
- `cuisine` - Type of cuisine
- `name, name_en, name_tr, etc.` - Multilingual names
- `category` - Always "Food & Drink"
- `subcategory` - Specific type (Restaurant, Cafe)

## 🔧 Database Requirements (Optional Enrichment)

For spatial database enrichment:
- PostgreSQL with PostGIS extension
- Database credentials in environment variables

```bash
export DB_NAME=wizlop_db
export DB_USER=wizlop_user
export DB_PASSWORD=wizlop_pass
export DB_HOST=localhost
export DB_PORT=5432
```

## 📊 Performance Metrics

- **Total Cities**: 81 Turkish provinces
- **POI Types per City**: 4 (amenity=cafe, amenity=restaurant, shop=cafe, shop=restaurant)
- **Total Combinations**: 324
- **Expected POI Count**: 10,000-50,000 establishments
- **Output Size**: 50-100MB of CSV data

## 🎉 Success Indicators

When the pipeline completes successfully, you should see:
- ✅ All 81 cities processed
- 📁 324 individual CSV files created
- 📄 Final merged dataset: `all_cities_merged.csv`
- 📊 Progress bars showing 100% completion
- 🎉 "All cities completed!" message
