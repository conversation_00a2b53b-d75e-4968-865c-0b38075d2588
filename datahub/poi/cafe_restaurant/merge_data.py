#!/usr/bin/env python3
"""
Merge Cafe & Restaurant POI Data

Simple merging script that combines all cleaned city CSV files into a single dataset.
This script only merges - no cleaning is performed here.
All cleaning should be done first with clean_data.py
"""

import os
import glob
import pandas as pd
import sys
from pathlib import Path
import logging
from tqdm import tqdm

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_merger")

# Define output directory directly
POI_OUTPUT_DIR = Path(__file__).resolve().parent.parent.parent / "data" / "output" / "cafe_restaurant"


def merge_city_data():
    """Merge all cleaned city CSV files into a single dataset"""
    logger.info("Starting data merging...")
    logger.info("This script only merges - no cleaning is performed")
    
    if not POI_OUTPUT_DIR.exists():
        logger.error(f"Output directory not found: {POI_OUTPUT_DIR}")
        logger.error("Please run the extraction and cleaning scripts first")
        return False
    
    # Get all CSV files from all city directories
    all_csv_files = []
    city_dirs = [d for d in POI_OUTPUT_DIR.iterdir() if d.is_dir() and d.name != '__pycache__']
    
    for city_dir in city_dirs:
        csv_files = list(city_dir.glob("*.csv"))
        all_csv_files.extend(csv_files)
    
    if not all_csv_files:
        logger.error("No CSV files found to merge")
        return False
    
    logger.info(f"Found {len(all_csv_files)} CSV files from {len(city_dirs)} cities")
    
    # Read and combine all CSV files
    all_dataframes = []
    
    with tqdm(total=len(all_csv_files), desc="Reading CSV files", unit="file") as pbar:
        for csv_file in all_csv_files:
            try:
                df = pd.read_csv(csv_file)
                if len(df) > 0:
                    # Add source information for statistics only
                    df['_temp_source_city'] = csv_file.parent.name
                    all_dataframes.append(df)
                    pbar.set_description(f"Reading {csv_file.parent.name}/{csv_file.name}")
                else:
                    logger.debug(f"Skipping empty file: {csv_file}")
                pbar.update(1)
            except Exception as e:
                logger.error(f"Error reading {csv_file}: {e}")
                pbar.update(1)
    
    if not all_dataframes:
        logger.error("No valid data found to merge")
        return False
    
    # Combine all dataframes
    logger.info("Combining all data...")
    merged_df = pd.concat(all_dataframes, ignore_index=True)
    
    # Sort by city and name for better organization
    if '_temp_source_city' in merged_df.columns and 'name' in merged_df.columns:
        merged_df = merged_df.sort_values(['_temp_source_city', 'name'])
    
    # Print statistics before removing source columns
    if '_temp_source_city' in merged_df.columns:
        city_counts = merged_df['_temp_source_city'].value_counts()
        logger.info(f"Data from {len(city_counts)} cities")
        logger.info(f"Top 5 cities by POI count:")
        for city, count in city_counts.head().items():
            logger.info(f"  {city}: {count}")
    
    # Remove temporary source tracking column
    if '_temp_source_city' in merged_df.columns:
        merged_df = merged_df.drop(columns=['_temp_source_city'])
        logger.info("Removed temporary source tracking column from final output")
    
    # Save merged dataset
    output_file = POI_OUTPUT_DIR / 'all_cities_merged.csv'
    merged_df.to_csv(output_file, index=False, encoding='utf-8')
    
    logger.info(f"Merged dataset saved: {output_file}")
    logger.info(f"Total records: {len(merged_df)}")
    
    # Print summary statistics
    if 'subcategory' in merged_df.columns:
        logger.info("Subcategory breakdown:")
        subcategory_counts = merged_df['subcategory'].value_counts()
        for subcategory, count in subcategory_counts.items():
            logger.info(f"  {subcategory}: {count}")
    
    # Show final column structure
    logger.info(f"Final columns: {list(merged_df.columns)}")
    
    return True


def main():
    """Main merging function"""
    success = merge_city_data()
    
    if success:
        logger.info("Data merging completed successfully!")
        logger.info("You can now optionally run database enrichment")
        return 0
    else:
        logger.error("Data merging failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
