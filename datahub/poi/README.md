<!-- @format -->

# Modular POI Extraction System

A comprehensive, modular system for extracting Point of Interest (POI) data from OpenStreetMap for Turkey. This system is designed with a clean separation between shared components and POI-type-specific implementations.

## System Architecture

The system follows a modular design pattern that promotes code reuse and easy extension:

```
datahub/poi/
├── shared/                    # Shared components for all POI types
│   ├── config.py             # Common configuration (cities, columns, DB settings)
│   ├── utils.py              # Utility functions (validation, API requests, cleaning)
│   ├── download_osm_data.py  # OSM data downloader
│   └── database_enrichment.py # Spatial database enrichment
├── cafe_restaurant/          # Cafe & restaurant POI extraction
│   ├── config.py             # Specific configuration
│   ├── extract_osm_by_city.py # Extraction script
│   ├── clean_data.py         # Data cleaning script (clean only)
│   ├── merge_data.py         # Data merging script (merge only)
│   ├── enrich_osm_with_spatialdb.py # Enrichment script
│   ├── README.md             # Detailed documentation
│   └── QUICK_START.md        # Quick start guide
└── [future_poi_types]/       # Easy to add new POI types
```

## Design Principles

### 1. Structured Pipeline Approach

Instead of ad-hoc processing, the system follows a clear pipeline:

1. **Find Data**: Download OSM data
2. **Extract Data**: Extract POIs by city
3. **Clean Data**: Validate and clean extracted data
4. **Merge Data**: Combine all city data
5. **Enrich Data**: Enhance with spatial database

### 2. Shared Components

Common functionality is centralized in the `shared/` directory:

- **Configuration**: Common settings, city lists, column definitions
- **Utilities**: Data validation, API requests, cleaning functions
- **Data Download**: Shared OSM data downloader
- **Database Enrichment**: Spatial database integration

### 3. POI-Type Modularity

Each POI type (e.g., cafe_restaurant) has its own directory with:

- Specific configuration for that POI type
- Customized extraction logic
- Type-specific processing requirements
- Independent documentation and quick start guides

### 4. Easy Extension

Adding new POI types is straightforward:

1. Create new directory under `poi/`
2. Copy structure from existing POI type
3. Modify configuration for new amenities/shops
4. Shared components handle the rest

## Current POI Types

### Cafe & Restaurant (`cafe_restaurant/`)

- **Extracts**: Restaurants and cafes from OSM
- **Coverage**: All Turkish cities
- **Output**: ~10,000-50,000 establishments
- **Features**: Multilingual names, contact info, spatial enrichment

## Shared Features

All POI types benefit from:

- **Complete Turkey Coverage**: All 81 provinces
- **City-based Organization**: Data organized by Turkish cities
- **Multilingual Support**: Names in Turkish, English, German, Russian, Arabic, etc.
- **Data Quality**: Validation, deduplication, standardization
- **Spatial Enrichment**: Administrative boundaries and street information
- **No API Limits**: Works with downloaded data

## Quick Start

To get started with any POI type:

1. **Navigate to the datahub directory**:

   ```bash
   cd datahub
   ```

2. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

3. **Run the pipeline**:
   ```bash
   python run_cafe_restaurant_pipeline.py  # for cafe & restaurant POIs
   ```

## Adding New POI Types

### Example: Adding Hotels

1. **Create directory structure**:

   ```bash
   mkdir -p datahub/poi/hotels
   ```

2. **Copy base structure**:

   ```bash
   cp datahub/poi/cafe_restaurant/*.py datahub/poi/hotels/
   cp datahub/poi/cafe_restaurant/*.md datahub/poi/hotels/
   ```

3. **Modify configuration** (`hotels/config.py`):

   ```python
   POI_TYPE = "hotels"
   HOTEL_AMENITIES = {
       'tourism': ['hotel', 'motel', 'hostel', 'guest_house']
   }
   ```

4. **Update documentation** and run the pipeline

## Database Integration

The system supports optional spatial database enrichment:

### Requirements

- PostgreSQL with PostGIS
- Admin boundaries table
- Road network table

### Configuration

Create `.env` file in `datahub/`:

```
DB_NAME=wizlop_db
DB_USER=wizlop_user
DB_PASSWORD=wizlop_pass
DB_HOST=localhost
DB_PORT=5432
```

### Benefits

- Administrative province/city/district information
- Nearest street names
- Formatted addresses
- Enhanced data quality

## Data Output

Each POI type produces:

- **City CSV files**: Individual files per city and POI subtype
- **Merged dataset**: Single comprehensive CSV file
- **Enriched data**: Enhanced with spatial database (optional)

### Standard Fields

All POI types include:

- Geographic coordinates (latitude, longitude)
- Multilingual names (name, name_en, name_tr, etc.)
- Contact information (phone_number)
- Operational details (opening_hours, description)
- Category and subcategory classification
- Administrative information (province, city, district)
- Address details (street, full_address)

## Performance

Typical performance for Turkey-wide extraction:

- **Download**: 5-10 minutes
- **Extraction**: 15-30 minutes
- **Processing**: 5-10 minutes
- **Enrichment**: 10-20 minutes (optional)
- **Total**: 35-70 minutes

## Migration from Old System

The new modular system replaces the previous `data_hub/` structure:

### Old Structure Issues

- Monolithic scripts
- Mixed concerns
- Hard to extend
- Duplicate code

### New Structure Benefits

- Modular design
- Shared components
- Easy extension
- Clear separation of concerns
- Better documentation

### Migration Path

1. Test new system with existing data
2. Compare outputs for consistency
3. Switch to new system once validated
4. Remove old `data_hub/` directory

## Contributing

When adding new POI types or improving shared components:

1. **Follow the modular pattern**
2. **Use shared components** where possible
3. **Document thoroughly** (README + QUICK_START)
4. **Test with real data**
5. **Maintain backward compatibility** in shared components

## Future Enhancements

Planned improvements:

- **More POI types**: Hotels, gas stations, hospitals, etc.
- **Enhanced validation**: Better duplicate detection
- **Performance optimization**: Parallel processing
- **Data quality metrics**: Completeness scoring
- **API integration**: Real-time updates
- **Visualization tools**: Data exploration interfaces

This modular system provides a solid foundation for comprehensive POI data extraction and management for Turkey and can be easily extended to other countries or regions.
