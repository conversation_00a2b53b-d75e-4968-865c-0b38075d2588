import requests
import json


class Geocoder:
    def __init__(self, base_url="https://nominatim.openstreetmap.org"):
        self.base_url = base_url
        self.headers = {
            'User-Agent': 'MyGeocoderApp/1.0 (<EMAIL>)'
        }

    def search_all_results(self, query, limit=3):
        """
        Searches for a location and returns top results in JSON format.

        Args:
            query (str): The search query (e.g., city name, address).
            limit (int): Number of results to return. Default is 3.

        Returns:
            dict: JSON object containing the search results.
        """
        params = {
            'q': query,
            'format': 'json',
            'limit': limit,
            'addressdetails': 1,
            'extratags': 1,
            'namedetails': 1,
            'dedupe': 1,
            'polygon_geojson': 0,
        }

        try:
            response = requests.get(
                f"{self.base_url}/search", params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()

            if not data:
                print(f"No results found for '{query}'")
                return {}

            # Sort the results by importance
            sorted_data = sorted(data, key=lambda x: x.get(
                'importance', 0), reverse=True)
            return sorted_data

        except requests.RequestException as e:
            print(f"Request failed: {e}")
            return {}

    def print_results(self, query):
        """
        Prints the results for a given query in a readable format.

        Args:
            query (str): The search query (e.g., city name, address).
        """
        results = self.search_all_results(query)
        if results:
            print(f"Top results for '{query}' sorted by importance:")
            for place in results:
                print(f"- {place.get('display_name')} "
                      f"({place.get('class')}, {place.get('type')}) "
                      f"at (lat: {place.get('lat')}, lon: {place.get('lon')}), "
                      f"importance: {place.get('importance', 'N/A')}")
        else:
            print(f"No results to display for '{query}'.")


def geocode(query, limit=3):
    """
    Interface function to interact with the Geocoder class.

    Args:
        query (str): The search query (e.g., city name, address).
        limit (int): Number of results to return. Default is 3.

    Returns:
        dict: The search results in JSON format.
    """
    geocoder = Geocoder()
    results = json.dumps(geocoder.search_all_results(query, limit))

    # Print the results in JSON format
    # print("Results in JSON format:")
    # print(json.dumps(results, indent=4))

    # Print the results in human-readable format
    # geocoder.print_results(query)

    return results


if __name__ == "__main__":
    # Example query
    print(geocode("taksim"))
