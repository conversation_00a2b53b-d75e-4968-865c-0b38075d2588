import requests
import json
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

# Get API key from environment variable
api_key = os.getenv("OPENROUTER_API_KEY")

# Check if the key was loaded
if not api_key:
    raise ValueError("OPENROUTER_API_KEY not found in .env file")

# User query example for NER testing
question = "is there a restaurant close to Beşiktaş"

# OpenRouter API URL for chat completions
url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# Payload for the API call
payload = {
    "model": "qwen/qwen2.5-vl-32b-instruct:free",
    "messages": [
        {"role": "system", "content": "You are tasked with performing Named Entity Recognition (NER) on a natural language query. Your role is to extract the following entities:\n\nLocation: A geographical location (e.g., city, neighborhood, landmark).\nPOI Type: The type of Point of Interest (POI), such as 'cafe', 'museum', 'restaurant', etc.\nTime: A time-related entity, such as a specific time of day (e.g., 'morning', 'lunch', 'evening')."},
        {"role": "user", "content": question}
    ],
    "stream": True
}

buffer = ""
with requests.post(url, headers=headers, json=payload, stream=True) as r:
    for chunk in r.iter_content(chunk_size=1024, decode_unicode=True):
        buffer += chunk
        while True:
            try:
                # Find the next complete SSE line
                line_end = buffer.find('\n')
                if line_end == -1:
                    break

                line = buffer[:line_end].strip()
                buffer = buffer[line_end + 1:]

                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        break

                    try:
                        data_obj = json.loads(data)
                        content = data_obj["choices"][0]["delta"].get(
                            "content")
                        if content:
                            print(content, end="", flush=True)
                    except json.JSONDecodeError:
                        pass
            except Exception:
                break
