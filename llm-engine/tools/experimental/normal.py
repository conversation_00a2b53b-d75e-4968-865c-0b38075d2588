import requests
import json
import os
import pathlib
import re
from dotenv import load_dotenv

# Load .env from project root
current_file = pathlib.Path(__file__)
project_root = current_file.parent.parent
env_path = project_root / '.env'
load_dotenv(dotenv_path=env_path)

# Load API key
api_key = os.getenv('OPENROUTER_API_KEY')
print(f"Looking for .env file at: {env_path}")
print(f".env file exists: {env_path.exists()}")
if not api_key:
    raise ValueError("❌ Error: OPENROUTER_API_KEY not found in .env file.")
else:
    print(f"✅ API key loaded (starts with): {api_key[:4]}...")

# Example user query
question = "is there a restaurant close to Beşiktaş"

# API endpoint
url = "https://openrouter.ai/api/v1/chat/completions"

# NER task instructions
system_prompt = """
You are tasked with performing Named Entity Recognition (NER) on a natural language query. Your role is to extract the following entities:

Location: A geographical location (e.g., city, neighborhood, landmark).
POI Type: The type of Point of Interest (e.g., cafe, museum, restaurant).
Time: A time-related entity, such as a specific time of day (e.g., morning, lunch, evening).

Respond ONLY with a valid JSON object in this format:
```json
{
  "Location": "...",
  "POI Type": "...",
  "Time": "..."
}
```
If any field is not applicable, return an empty string for it.
"""

# Updated headers and payload format
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
    # Optional. Replace with your site URL if needed
    "HTTP-Referer": "https://wizlop.com",
    "X-Title": "NER Application"  # Optional. Replace with your site name if needed
}

# Payload with updated model and sampling settings
payload = {
    "model": "qwen/qwen2.5-vl-32b-instruct:free",
    "messages": [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": question}
    ],
    "max_tokens": 200,
    "temperature": 0.2,
    "top_p": 0.9,
    "response_format": {"type": "json_object"}
}

# Make request
try:
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    response.raise_for_status()
    data = response.json()

    # Extract model output
    result_text = data.get("choices", [{}])[0].get(
        "message", {}).get("content", "")

    # Extract JSON inside triple backticks (```json ... ```)
    match = re.search(r"```json\s*({.*?})\s*```", result_text, re.DOTALL)
    if match:
        result_text = match.group(1).strip()
    else:
        # If no triple backticks, try to use the content directly
        # (since response_format is set to json_object)
        if not result_text.strip().startswith("{"):
            raise ValueError(
                f"❌ Could not find valid JSON in model output.\nRaw Output:\n{result_text}")

    print("📤 Model Output:")
    print(result_text)

    # Parse JSON
    ner_result = json.loads(result_text)
    print("\n✅ Extracted Entities:")
    print(json.dumps(ner_result, indent=4))
except requests.exceptions.RequestException as e:
    print(f"❌ Request error: {e}")
except json.JSONDecodeError as e:
    print(f"❌ JSON decode error: {e}. Raw candidate:\n{result_text}")
except Exception as ex:
    print(f"❌ Unexpected error: {ex}")
