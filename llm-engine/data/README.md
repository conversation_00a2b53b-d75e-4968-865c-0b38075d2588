# Data Directory

This directory contains the main dataset file for the application.

## Files

- `dataset.csv`: Main dataset containing location data with the following required columns:
  - `latitude`: Latitude coordinate of the location
  - `longitude`: Longitude coordinate of the location
  - `category`: Category of the location (e.g., amenity, historic, leisure)
  - `subcategory`: Subcategory of the location (e.g., restaurant, cafe, museum)

## Configuration

The path to the dataset file is configured in `config.json` in the root directory:

```json
{
	"data_paths": {
		"dataset": "data/dataset.csv"
	}
}
```

The dataset path is independent of the storage_dir parameter, allowing you to store your datasets separately from application data.

## Customization

If you need to store your dataset in a different location, you can update the dataset path in the config.json file:

```json
{
	"data_paths": {
		"dataset": "/path/to/your/dataset.csv"
	}
}
```

You can use either relative or absolute paths for the dataset.
