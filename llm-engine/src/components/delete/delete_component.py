"""
Delete Component - Standalone component for session deletion.

Simple, minimal implementation following the established pattern.
"""

from typing import Dict, Any
from src.infrastructure.state.interface import IStateManager
from src.infrastructure.history.interface import IHistoryManager
from src.services.conversation.session_context import SessionContext


class DeleteComponent:
    """Standalone component for handling session deletion."""

    def __init__(self, state_manager: IStateManager, history_manager: IHistoryManager):
        self.state_manager = state_manager
        self.history_manager = history_manager

    def delete_session(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """Delete a session by marking files as removed."""
        session = SessionContext(user_id, session_id, self.state_manager, self.history_manager)
        session.delete()
        return {
            "status": "success",
            "message": f"Session {session_id} has been marked as removed"
        }
