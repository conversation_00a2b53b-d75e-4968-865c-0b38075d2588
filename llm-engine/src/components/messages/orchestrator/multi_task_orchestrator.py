"""
Multi-Task Orchestrator for handling complex user requests.

This module coordinates task decomposition, execution planning, and result aggregation
to handle multi-task requests like "cafes in Beyoğlu or Taksim" or "restaurant next to Kahve Dünyası".

Enhanced with Phase 4 capabilities:
- State management that accumulates results across requests
- History management with auto-summarization
- Memory management for insight extraction
"""

from typing import Dict, Any, List, Optional
from src.infrastructure.log.unified_logger import debug, info, warning, error
from src.infrastructure.state.interface import IStateManager
from src.infrastructure.history.interface import IHistoryManager
from .task_decomposer import TaskDecomposer
from .execution_planner import ExecutionPlanner
from .shared.response_formatter import format_unified_response, format_error_response
from .shared.task_utils import merge_task_results, extract_search_areas
from ..processes.shared import Task, TaskResult
from .state_management import StateManager, HistoryManager, MemoryManager
from ..shared.advice import AdviceGenerator, AdviceContext


class MultiTaskOrchestrator:
    """
    Main orchestrator class that coordinates multi-task execution.

    Handles:
    - Task decomposition from user requests
    - Execution planning (parallel/sequential)
    - Result aggregation and response generation
    - Backward compatibility with single-task requests
    - State, history, and memory management (Phase 4)
    """

    def __init__(self, base_state_manager: Optional[IStateManager] = None,
                 base_history_manager: Optional[IHistoryManager] = None):
        """
        Initialize the multi-task orchestrator with enhanced capabilities.

        Args:
            base_state_manager: Base state manager for persistence
            base_history_manager: Base history manager for persistence
        """
        self.task_decomposer = TaskDecomposer()
        self.execution_planner = ExecutionPlanner()

        # Phase 4: Enhanced state, history, and memory management
        if base_state_manager:
            self.state_manager = StateManager(base_state_manager)
        else:
            self.state_manager = None

        if base_history_manager:
            self.history_manager = HistoryManager(base_history_manager)
        else:
            self.history_manager = None

        self.memory_manager = MemoryManager()

        # Phase 5: Centralized advice generator
        self.advice_generator = AdviceGenerator()

    def process_request(self, user_request: str, latitude: float, longitude: float,
                        search_radius: int, num_candidates: int,
                        user_id: str, session_id: str,
                        history: str, llm_actions: list, streaming: bool = False,
                        session_context=None) -> Dict[str, Any]:
        """
        Main orchestrator method - handles all request types with Phase 4 enhancements.

        Args:
            user_request: The user's request text
            latitude: User's latitude
            longitude: User's longitude
            search_radius: Search radius in meters
            num_candidates: Number of candidates to return
            user_id: User ID (required)
            session_id: Session ID (required)
            history: Conversation history (required)
            llm_actions: List of actions/tools (dicts) as output by the LLM (required)
            streaming: Whether to generate streaming response
            session_context: Optional SessionContext for centralized session management

        Returns:
            Dict containing response and aggregated results with accumulated state

        Raises:
            ValueError if any required parameter is missing
        """
        # Check required parameters (history can be empty string for first message)
        required_params = [user_request, latitude, longitude,
                           search_radius, num_candidates, user_id, session_id, llm_actions]
        if not all(param is not None for param in required_params) or history is None:
            raise ValueError(
                "All parameters (user_request, latitude, longitude, search_radius, num_candidates, user_id, session_id, history, llm_actions) are required.")

        info(
            f"Processing multi-task request: '{user_request}' for session {session_id}")

        try:
            # Phase 4: Get enhanced context from state, history, and memory
            enhanced_context = self._get_enhanced_context(user_id, session_id)

            # 1. Task decomposition (strict LLM-driven)
            tasks = self.task_decomposer.decompose(
                llm_actions=llm_actions,
                user_request=user_request,
                latitude=latitude,
                longitude=longitude,
                radius=search_radius,
                num_candidates=num_candidates,
                user_id=user_id,
                session_id=session_id,
                state_manager=self.state_manager
            )

            if not tasks:
                warning("No tasks generated from request")
                return self._create_error_response("Unable to process request")

            # 2. Execution planning and execution
            execution_plan = self.execution_planner.plan(tasks)
            task_results = self.execution_planner.execute(execution_plan)

            # Phase 4: Update state, history, and memory with new results
            # NOTE: Only update internal state, not session history (handled by caller)
            self._update_enhanced_state(
                user_id, session_id, user_request, task_results)

            # 3. Result aggregation with enhanced context
            aggregated_result = self._aggregate_results(
                task_results, user_request, history, latitude, longitude,
                search_radius, num_candidates, enhanced_context, streaming, session_context
            )

            # Phase 4: Add accumulated candidates from previous searches
            if self.state_manager:
                accumulated_candidates = self.state_manager.get_accumulated_candidates(
                    user_id, session_id)
                aggregated_result['accumulated_candidates'] = accumulated_candidates
                aggregated_result['total_accumulated'] = len(
                    accumulated_candidates)

            # 4. Add execution metadata
            aggregated_result.update({
                "latitude": latitude,
                "longitude": longitude,
                "search_radius": search_radius,
                "tasks_executed": len(tasks),
                "successful_tasks": sum(1 for r in task_results if r.success),
                "execution_time": sum(r.execution_time for r in task_results)
            })

            # 5. Centralized session management (if session_context provided)
            if session_context:
                self._handle_centralized_session_update(
                    session_context, user_request, aggregated_result, latitude, longitude, search_radius)

            info(f"Multi-task processing completed: {len(tasks)} tasks, "
                 f"{aggregated_result['successful_tasks']} successful")
            return aggregated_result

        except Exception as e:
            error(f"Multi-task orchestrator error: {e}")
            return self._create_error_response(f"Processing error: {str(e)}")

    def _aggregate_results(self, task_results: List[TaskResult], user_request: str,
                           history: str = None, latitude: float = None,
                           longitude: float = None, search_radius: int = None,
                           num_candidates: int = None, enhanced_context: Dict[str, Any] = None,
                           streaming: bool = False, session_context=None) -> Dict[str, Any]:
        """
        Aggregate results from multiple tasks into a unified response using the advice agent.

        Args:
            task_results: List of TaskResult objects
            user_request: Original user request
            history: Conversation history
            latitude: User latitude
            longitude: User longitude
            search_radius: Search radius

        Returns:
            Dict containing aggregated response with advice agent generated content
        """
        debug(f"Aggregating results from {len(task_results)} tasks")

        # Collect all successful results
        successful_results = [r for r in task_results if r.success]

        if not successful_results:
            return self._create_error_response("No successful results found")

        # Use shared utility to merge task results
        merged_results = merge_task_results(task_results, num_candidates)
        top_candidates = merged_results["top_candidates"]
        # Try to extract available_subcategories and requested_subcategories from merged_results or task_results
        available_subcategories = merged_results.get(
            "available_subcategories", [])
        requested_subcategories = merged_results.get(
            "requested_subcategories", [])
        # Phase 5: Generate intelligent response using centralized advice generator
        try:
            debug("Generating response using centralized advice generator")
            debug(f"Task results count: {len(task_results)}")
            debug(f"Enhanced context: {enhanced_context}")

            # Create advice context with enhanced information
            advice_context = AdviceContext(
                state=enhanced_context.get(
                    'state') if enhanced_context else None,
                history=enhanced_context.get(
                    'history') if enhanced_context else None,
                memory=enhanced_context.get(
                    'memory') if enhanced_context else None,
                user_request=user_request,
                latitude=latitude,
                longitude=longitude,
                search_radius=search_radius
            )

            debug(f"Created advice context: {advice_context}")

            # Generate response using centralized advice generator
            debug("Calling advice_generator.generate")
            advice_result = self.advice_generator.generate(
                task_results, advice_context, num_candidates, streaming=streaming)

            debug(f"Advice generator returned: {advice_result}")

            # Extract response and update merged results with advice generator results
            response_text = advice_result.get(
                'response', 'No response generated')

            debug(f"Extracted response text: {response_text[:100]}...")

            # Update top_candidates with advice generator results (may be different due to deduplication)
            if advice_result.get('top_candidates'):
                top_candidates = advice_result['top_candidates']
                merged_results["total_candidates_found"] = advice_result.get('total_candidates_found',
                                                                             merged_results["total_candidates_found"])

        except Exception as e:
            # Fallback to basic formatter if centralized advice generator fails
            warning(
                f"Centralized advice generator failed: {e}, using fallback formatter")
            search_areas = extract_search_areas(successful_results)
            response_data = format_unified_response(
                task_results=successful_results,
                user_request=user_request,
                total_candidates=merged_results["total_candidates_found"],
                search_areas=search_areas
            )
            response_text = response_data["response"]

        # Generate session title for first message only
        session_title = ""
        if session_context and session_context.is_first_message():
            session_title = self._generate_session_title(
                user_request, task_results, top_candidates)
            if session_title:
                # Embed session title in response as hidden HTML tag
                response_text = self._embed_session_title_in_response(
                    response_text, session_title)

        return {
            "response": response_text,
            "top_candidates": top_candidates,
            "total_candidates_found": merged_results["total_candidates_found"],
            "successful_tasks": merged_results["successful_tasks"],
            "failed_tasks": merged_results["failed_tasks"],
            # Also include in response for centralized session management
            "session_title": session_title
        }

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """
        Create a standardized error response.

        Args:
            error_message: Error message to include

        Returns:
            Error response dictionary
        """
        return format_error_response(error_message)

    def _get_enhanced_context(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get enhanced context from state, history, and memory managers.

        Args:
            user_id: User ID
            session_id: Session ID

        Returns:
            Enhanced context dictionary
        """
        context = {}

        try:
            # Get state context
            if self.state_manager:
                enhanced_state = self.state_manager.get_enhanced_state(
                    user_id, session_id)
                context['state'] = {
                    'total_searches': enhanced_state.total_searches,
                    'last_search_location': enhanced_state.last_search_location,
                    'accumulated_candidates_count': len(enhanced_state.top_candidates),
                    'search_context': enhanced_state.search_context
                }

            # Get history context
            if self.history_manager:
                history_context = self.history_manager.get_context_for_response(
                    user_id, session_id)
                context['history'] = history_context

            # Get memory insights
            memory_insights = self.memory_manager.get_insights_for_response(
                user_id, session_id)
            context['memory'] = memory_insights

        except Exception as e:
            warning(f"Failed to get enhanced context: {e}")

        return context

    def _update_enhanced_state(self, user_id: str, session_id: str,
                               user_request: str, task_results: List[TaskResult]):
        """
        Update state, history, and memory with new task results.

        Args:
            user_id: User ID
            session_id: Session ID
            user_request: User request text
            task_results: Task results to process
        """
        try:
            # Update state (accumulate results)
            if self.state_manager:
                self.state_manager.update_session_state(
                    user_id, session_id, task_results)

            # Process memory insights
            self.memory_manager.process_task_results(
                user_id, session_id, user_request, task_results)

            # Note: History will be updated after response generation

        except Exception as e:
            warning(f"Failed to update enhanced state: {e}")

    def _handle_centralized_session_update(self, session_context, user_request: str,
                                           aggregated_result: Dict[str, Any],
                                           latitude: float, longitude: float, search_radius: int):
        """
        Handle centralized session state and history updates.

        This method consolidates all session-related updates in one place to prevent duplicates.

        Args:
            session_context: SessionContext for unified session management
            user_request: User's request text
            aggregated_result: Result from task execution
            latitude: User latitude
            longitude: User longitude
            search_radius: Search radius
        """
        try:
            # 1. Log user message to history (if not already logged)
            session_context.log_user_message(user_request, metadata={
                "latitude": latitude,
                "longitude": longitude,
                "search_radius": search_radius
            })

            # 2. Update session state with new top candidates
            new_top_candidates = aggregated_result.get("top_candidates", [])
            if new_top_candidates:
                session_context.save_top_candidates(new_top_candidates)

            # 3. Update session title if provided
            session_title = aggregated_result.get("session_title", "")
            if session_title:
                session_context.update_state_metadata(
                    {"session_title": session_title})

            # 4. Log assistant response to history
            response_text = aggregated_result.get("response", "")
            session_context.log_assistant_message(response_text, metadata={
                "top_candidates": new_top_candidates,
                "session_title": session_title,
                "tasks_executed": aggregated_result.get("tasks_executed", 0),
                "successful_tasks": aggregated_result.get("successful_tasks", 0)
            })

            debug("Centralized session update completed successfully")

        except Exception as e:
            warning(f"Failed to update session centrally: {e}")

    def update_history_with_response(self, user_id: str, session_id: str,
                                     user_request: str, task_results: List[TaskResult],
                                     assistant_response: str):
        """
        Update conversation history with the generated response.

        This should be called after response generation is complete.
        DEPRECATED: Use _handle_centralized_session_update instead.

        Args:
            user_id: User ID
            session_id: Session ID
            user_request: User request text
            task_results: Task results
            assistant_response: Generated assistant response
        """
        try:
            if self.history_manager:
                self.history_manager.add_interaction(
                    user_id, session_id, user_request, task_results, assistant_response
                )
        except Exception as e:
            warning(f"Failed to update history with response: {e}")

    def _generate_session_title(self, user_request: str, task_results: List, top_candidates: List) -> str:
        """
        Generate a creative and engaging session title based on the user request and results.

        Args:
            user_request: The user's original request
            task_results: Results from task execution
            top_candidates: Found location candidates

        Returns:
            Generated creative session title string
        """
        try:
            import random

            # Extract key information from the request and results
            request_lower = user_request.lower()

            # Creative title templates for different categories
            cafe_titles = [
                "☕ Coffee Quest", "Café Adventures", "Brew Discovery", "Coffee Hunt",
                "Caffeine Mission", "Espresso Expedition", "Coffee Corner Hunt",
                "Bean There, Done That", "Latte Locations", "Coffee Culture Tour"
            ]

            restaurant_titles = [
                "🍽️ Foodie Adventure", "Culinary Quest", "Taste Expedition", "Dining Discovery",
                "Food Safari", "Flavor Hunt", "Gourmet Journey", "Bite-Size Adventures",
                "Foodie's Paradise", "Culinary Exploration"
            ]

            pharmacy_titles = [
                "💊 Health Hub Hunt", "Wellness Quest", "Pharmacy Finder", "Health Stop Search",
                "Medicine Mission", "Wellness Journey", "Health Helper Hunt", "Care Corner Quest",
                "Pharmacy Patrol", "Health & Wellness Tour"
            ]

            shopping_titles = [
                "🛍️ Shopping Spree", "Retail Therapy", "Shopping Safari", "Mall Crawl",
                "Boutique Hunt", "Shopping Adventure", "Retail Quest", "Store Safari",
                "Shopping Expedition", "Retail Discovery"
            ]

            hotel_titles = [
                "🏨 Stay Quest", "Hotel Hunt", "Accommodation Adventure", "Sleep Safari",
                "Rest & Relax Hunt", "Hotel Hopping", "Stay Discovery", "Lodging Quest",
                "Comfort Corner Hunt", "Hotel Explorer"
            ]

            banking_titles = [
                "💰 Money Matters", "Banking Quest", "ATM Adventure", "Financial Hunt",
                "Cash Corner Quest", "Banking Safari", "Money Mission", "Financial Finder",
                "Banking Expedition", "Cash & Carry Hunt"
            ]

            healthcare_titles = [
                "🏥 Health Quest", "Medical Mission", "Healthcare Hunt", "Wellness Journey",
                "Health Safari", "Medical Explorer", "Care Quest", "Health Hub Hunt",
                "Medical Adventure", "Wellness Expedition"
            ]

            gas_titles = [
                "⛽ Fuel Quest", "Gas Hunt", "Fuel Stop Safari", "Energy Station Hunt",
                "Fuel Finder", "Gas Station Quest", "Fuel Mission", "Energy Explorer",
                "Fuel Adventure", "Gas & Go Hunt"
            ]

            # Determine primary search intent and get creative titles
            primary_intent = None
            intent_titles = []

            if any(word in request_lower for word in ['cafe', 'coffee', 'starbucks', 'espresso', 'latte', 'cappuccino']):
                primary_intent = "Cafe"
                intent_titles = cafe_titles
            elif any(word in request_lower for word in ['restaurant', 'food', 'eat', 'dining', 'meal', 'lunch', 'dinner']):
                primary_intent = "Restaurant"
                intent_titles = restaurant_titles
            elif any(word in request_lower for word in ['pharmacy', 'medicine', 'drug', 'health', 'medical']):
                primary_intent = "Pharmacy"
                intent_titles = pharmacy_titles
            elif any(word in request_lower for word in ['hotel', 'accommodation', 'stay', 'sleep', 'lodge']):
                primary_intent = "Hotel"
                intent_titles = hotel_titles
            elif any(word in request_lower for word in ['shop', 'store', 'shopping', 'mall', 'boutique', 'retail']):
                primary_intent = "Shopping"
                intent_titles = shopping_titles
            elif any(word in request_lower for word in ['gas', 'fuel', 'petrol', 'station']):
                primary_intent = "Gas Station"
                intent_titles = gas_titles
            elif any(word in request_lower for word in ['bank', 'atm', 'money', 'cash', 'financial']):
                primary_intent = "Banking"
                intent_titles = banking_titles
            elif any(word in request_lower for word in ['hospital', 'clinic', 'doctor', 'medical', 'health']):
                primary_intent = "Healthcare"
                intent_titles = healthcare_titles
            else:
                # Default creative titles for general searches
                intent_titles = [
                    "🗺️ Location Quest", "Place Hunter", "Discovery Mission", "Explorer's Journey",
                    "Adventure Awaits", "Hidden Gems Hunt", "Local Explorer", "City Safari",
                    "Urban Adventure", "Place Discovery"
                ]

            # Creative multi-category combinations
            multi_category_titles = {
                ("cafe", "pharmacy"): [
                    "☕💊 Coffee & Care", "Brew & Health Hub", "Caffeine & Wellness",
                    "Coffee & Medicine Run", "Latte & Life Essentials"
                ],
                ("cafe", "restaurant"): [
                    "☕🍽️ Food & Coffee Tour", "Dining & Coffee Quest", "Eat & Brew Adventure",
                    "Foodie's Coffee Trail", "Taste & Sip Journey"
                ],
                ("restaurant", "pharmacy"): [
                    "🍽️💊 Dine & Health Stop", "Food & Wellness Run", "Meal & Medicine Quest",
                    "Dining & Care Tour", "Feast & Health Hub"
                ],
                ("cafe", "shopping"): [
                    "☕🛍️ Shop & Sip", "Coffee & Shopping Spree", "Brew & Buy Adventure",
                    "Retail & Caffeine Fix", "Shopping & Coffee Break"
                ],
                ("restaurant", "shopping"): [
                    "🍽️🛍️ Dine & Shop", "Food & Fashion Hunt", "Eat & Buy Adventure",
                    "Culinary Shopping Tour", "Feast & Fashion Quest"
                ],
                ("hotel", "restaurant"): [
                    "🏨🍽️ Stay & Dine", "Hotel & Food Quest", "Rest & Feast Adventure",
                    "Accommodation & Dining", "Sleep & Eat Tour"
                ]
            }

            # Check for multi-category searches
            categories_detected = []
            if any(word in request_lower for word in ['cafe', 'coffee', 'starbucks', 'espresso']):
                categories_detected.append("cafe")
            if any(word in request_lower for word in ['restaurant', 'food', 'eat', 'dining', 'meal']):
                categories_detected.append("restaurant")
            if any(word in request_lower for word in ['pharmacy', 'pharmacies', 'medicine', 'drug', 'health']):
                categories_detected.append("pharmacy")
            if any(word in request_lower for word in ['shop', 'store', 'shopping', 'mall', 'retail']):
                categories_detected.append("shopping")
            if any(word in request_lower for word in ['hotel', 'accommodation', 'stay', 'lodge']):
                categories_detected.append("hotel")
            if any(word in request_lower for word in ['bank', 'atm', 'money', 'cash']):
                categories_detected.append("banking")

            # Generate creative title based on search pattern
            title = None

            if len(categories_detected) >= 2:
                # Multi-category search - use creative combinations
                category_pair = tuple(sorted(categories_detected[:2]))
                if category_pair in multi_category_titles:
                    title = random.choice(multi_category_titles[category_pair])
                else:
                    # Fallback for uncommon combinations
                    creative_combos = [
                        f"🗺️ {categories_detected[0].title()} & {categories_detected[1].title()} Quest",
                        f"Multi-Stop Adventure: {categories_detected[0].title()} + {categories_detected[1].title()}",
                        f"{categories_detected[0].title()} & {categories_detected[1].title()} Hunt",
                        f"Double Discovery: {categories_detected[0].title()} & {categories_detected[1].title()}"
                    ]
                    title = random.choice(creative_combos)

            elif len(categories_detected) == 1:
                # Single category - check if POI-based for extra creativity
                poi_name = None
                if any(word in request_lower for word in ['near', 'close to', 'around', 'close']):
                    # Extract POI name
                    poi_indicators = ['near', 'close to', 'around', 'close']
                    for indicator in poi_indicators:
                        if indicator in request_lower:
                            parts = request_lower.split(indicator)
                            if len(parts) > 1:
                                poi_part = parts[1].strip()
                                poi_words = []
                                for word in poi_part.split():
                                    if word not in ['and', 'also', 'find', 'the', 'a', 'an', 'in', 'same', 'area']:
                                        poi_words.append(word)
                                    if len(poi_words) >= 2:
                                        break
                                if poi_words:
                                    poi_name = ' '.join(poi_words).title()
                                    break

                if poi_name:
                    # POI-based creative titles
                    poi_templates = [
                        f"🎯 {primary_intent} Hunt near {poi_name}",
                        f"📍 {poi_name} Area {primary_intent} Quest",
                        f"🗺️ Exploring {primary_intent}s around {poi_name}",
                        f"🔍 {poi_name} Neighborhood {primary_intent} Safari",
                        f"📌 {primary_intent} Discovery: {poi_name} Edition"
                    ]
                    title = random.choice(poi_templates)
                else:
                    # Single category creative titles
                    if intent_titles:
                        title = random.choice(intent_titles)

            # Fallback to creative general titles
            if not title:
                general_creative_titles = [
                    "🗺️ Urban Explorer", "🔍 City Safari", "📍 Local Discovery",
                    "🎯 Place Hunter", "🌟 Hidden Gems Quest", "🚀 Adventure Awaits",
                    "🗺️ Neighborhood Navigator", "📍 Local Legends Hunt", "🎪 City Circus Tour",
                    "🎭 Urban Adventure Show"
                ]
                title = random.choice(general_creative_titles)

            # Ensure title is not too long (but allow more creative space)
            if len(title) > 35:
                title = title[:32] + "..."

            debug(
                f"Generated creative session title: '{title}' for request: '{user_request}'")
            return title

        except Exception as e:
            warning(f"Failed to generate session title: {e}")
            # Creative fallback titles
            fallback_titles = [
                "🗺️ Mystery Quest", "🔍 Discovery Mission", "📍 Adventure Time",
                "🎯 Explorer's Journey", "🌟 Local Legends"
            ]
            return random.choice(fallback_titles) if 'random' in locals() else "🗺️ Discovery Mission"

    def _embed_session_title_in_response(self, response_text: str, session_title: str) -> str:
        """
        Embed session title in response as hidden HTML tag.

        Args:
            response_text: The response text to embed title in
            session_title: The session title to embed

        Returns:
            Response text with embedded session title
        """
        try:
            # Create hidden HTML tag with session title
            hidden_title_tag = f'<span style="display:none;" data-session-title="{session_title}"></span>'

            # Embed at the beginning of the response
            embedded_response = hidden_title_tag + response_text

            debug(f"Embedded session title '{session_title}' in response")
            return embedded_response

        except Exception as e:
            warning(f"Failed to embed session title: {e}")
            return response_text  # Return original response if embedding fails
