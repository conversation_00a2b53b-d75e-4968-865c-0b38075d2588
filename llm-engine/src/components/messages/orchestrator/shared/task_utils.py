"""
Task utility functions for orchestrator components.

This module provides utility functions for task validation,
merging, and manipulation used across orchestrator components.
"""

from typing import Dict, Any, List, Optional
from src.infrastructure.log.unified_logger import debug, warning
from ...processes.shared.models import Task, TaskResult


def validate_task_parameters(task: Task) -> bool:
    """
    Validate that a task has all required parameters.

    Args:
        task: Task object to validate

    Returns:
        True if task is valid, False otherwise
    """
    if not task.tool_name:
        warning("Task missing tool_name")
        return False

    if not task.parameters:
        warning("Task missing parameters")
        return False

    # Check for required parameters based on tool type
    required_params = {
        'relative_search': ['latitude', 'longitude', 'radius'],
        'boundary_search': ['boundary_name'],
        'point_radius_search': ['latitude', 'longitude', 'radius'],
        'poi_based_search': ['poi_name']
    }

    if task.tool_name in required_params:
        missing_params = []
        for param in required_params[task.tool_name]:
            if param not in task.parameters:
                missing_params.append(param)

        if missing_params:
            warning(
                f"Task {task.tool_name} missing required parameters: {missing_params}")
            return False

    debug(f"Task {task.tool_name} validation passed")
    return True


def merge_task_results(results: List[TaskResult], num_candidates: int = None) -> Dict[str, Any]:
    """
    Merge multiple task results into a unified result.

    For multi-task scenarios:
    - Each task should contribute its full quota of candidates
    - Total candidates = num_candidates * number_of_successful_tasks
    - Example: 2 parallel tasks with num_candidates=2 should return 4 candidates total

    Args:
        results: List of TaskResult objects to merge
        num_candidates: Number of candidates per task (if None, return all)

    Returns:
        Merged result dictionary
    """
    all_candidates = []
    successful_results = []
    failed_results = []
    has_relative_search = False
    has_poi_based_search = False

    # Check what types of searches we have
    for result in results:
        if result.success:
            successful_results.append(result)
            if result.task.tool_name == "relative_search":
                has_relative_search = True
            elif result.task.tool_name == "poi_based_search":
                has_poi_based_search = True

            if result.result and 'top_candidates' in result.result:
                all_candidates.extend(result.result['top_candidates'])
        else:
            failed_results.append(result)

    # Remove duplicates based on POI ID
    unique_candidates = []
    seen_ids = set()

    for candidate in all_candidates:
        candidate_id = getattr(candidate, 'id', None) or candidate.get(
            'id') if isinstance(candidate, dict) else str(candidate)
        if candidate_id not in seen_ids:
            seen_ids.add(candidate_id)
            unique_candidates.append(candidate)

    # For multi-task scenarios, allow each task to contribute its full quota
    # Only apply limits for single-task scenarios or when explicitly needed
    if num_candidates is not None and len(successful_results) == 1:
        # Single task: apply the original limit logic
        if has_poi_based_search and not has_relative_search:
            # Pure POI-based search: allow reference POI + num_candidates nearby = num_candidates + 1 total
            limited_candidates = unique_candidates[:num_candidates + 1]
            debug(
                f"Single POI-based search: {len(unique_candidates)} unique candidates, limited to {num_candidates + 1}")
        else:
            # Regular single search: apply standard limit
            limited_candidates = unique_candidates[:num_candidates]
            debug(
                f"Single task search: {len(unique_candidates)} unique candidates, limited to {num_candidates}")
    else:
        # Multi-task scenario: allow each task to contribute its full quota
        # Calculate expected total: num_candidates * number_of_successful_tasks
        if num_candidates is not None:
            expected_total = num_candidates * len(successful_results)
            limited_candidates = unique_candidates[:expected_total]
            debug(
                f"Multi-task search: {len(successful_results)} tasks, {len(unique_candidates)} unique candidates, allowing up to {expected_total} ({num_candidates} per task)")
        else:
            limited_candidates = unique_candidates
            debug(
                f"Multi-task search: {len(successful_results)} tasks, {len(unique_candidates)} unique candidates, no limit applied")

    return {
        "top_candidates": limited_candidates,
        "total_candidates_found": len(unique_candidates),
        "successful_tasks": len(successful_results),
        "failed_tasks": len(failed_results),
        "all_candidates": unique_candidates
    }


def extract_search_areas(results: List[TaskResult]) -> List[str]:
    """
    Extract search areas from task results.

    Args:
        results: List of TaskResult objects

    Returns:
        List of search area names
    """
    areas = []

    for result in results:
        if not result.success:
            continue

        if result.task.tool_name == "boundary_search":
            boundary_name = result.task.parameters.get('boundary_name')
            if boundary_name:
                areas.append(boundary_name)
        elif result.task.tool_name == "relative_search":
            areas.append("Near you")
        elif result.task.tool_name == "point_radius_search":
            location_name = result.task.parameters.get(
                'location_name', 'Specific location')
            areas.append(location_name)
        elif result.task.tool_name == "poi_based_search":
            poi_name = result.task.parameters.get('poi_name')
            if poi_name:
                areas.append(f"Near {poi_name}")

    # Remove duplicates while preserving order
    unique_areas = []
    for area in areas:
        if area not in unique_areas:
            unique_areas.append(area)

    debug(f"Extracted {len(unique_areas)} unique search areas")
    return unique_areas


def calculate_task_priority(task: Task) -> int:
    """
    Calculate priority score for task execution ordering.

    Args:
        task: Task to calculate priority for

    Returns:
        Priority score (lower = higher priority)
    """
    # Base priorities from process metadata
    base_priorities = {
        'relative_search': 1,
        'point_radius_search': 2,
        'boundary_search': 3,
        'poi_based_search': 4
    }

    base_priority = base_priorities.get(task.tool_name, 5)

    # Adjust based on dependencies
    dependency_penalty = len(task.dependencies) * 10

    final_priority = base_priority + dependency_penalty

    debug(f"Task {task.tool_name} priority: {final_priority}")
    return final_priority
