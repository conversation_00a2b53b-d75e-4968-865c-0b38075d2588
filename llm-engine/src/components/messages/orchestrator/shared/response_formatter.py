"""
Response formatting utilities for orchestrator components.

This module provides standardized response formatting functions
used across different orchestrator components.
"""

from typing import Dict, Any, List, Optional
from src.infrastructure.log.unified_logger import debug, info


def format_unified_response(
    task_results: List[Any],
    user_request: str,
    total_candidates: int = 0,
    search_areas: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Format a unified response from multiple task results.
    
    Args:
        task_results: List of task results to aggregate
        user_request: Original user request
        total_candidates: Total number of candidates found
        search_areas: List of search areas covered
        
    Returns:
        Formatted response dictionary
    """
    if search_areas is None:
        search_areas = []
    
    # Count successful tasks
    successful_tasks = [r for r in task_results if getattr(r, 'success', True)]
    
    # Generate response text based on results
    if successful_tasks:
        if len(search_areas) > 1:
            areas_text = ", ".join(search_areas[:-1]) + f" and {search_areas[-1]}"
            response_text = f"I found {total_candidates} results across {areas_text}."
        elif len(search_areas) == 1:
            response_text = f"I found {total_candidates} results in {search_areas[0]}."
        else:
            response_text = f"I found {total_candidates} results for your request."
    else:
        response_text = "I couldn't find any results for your request."
    
    debug(f"Formatted unified response for {len(task_results)} tasks")
    
    return {
        "response": response_text,
        "total_candidates_found": total_candidates,
        "search_areas": search_areas,
        "tasks_executed": len(task_results),
        "successful_tasks": len(successful_tasks)
    }


def format_error_response(
    error_message: str,
    session_title: Optional[str] = None,
    status_code: int = 50000
) -> Dict[str, Any]:
    """
    Format a standardized error response.
    
    Args:
        error_message: Error message to include
        session_title: Optional session title
        status_code: Error status code
        
    Returns:
        Formatted error response dictionary
    """
    info(f"Formatting error response: {error_message}")
    
    return {
        "response": error_message,
        "top_candidates": [],
        "session_title": session_title or "",
        "status_code": status_code,
        "error": True
    }


def format_no_data_response(
    location_name: Optional[str] = None,
    session_title: Optional[str] = None
) -> Dict[str, Any]:
    """
    Format a response for when no data is available.
    
    Args:
        location_name: Name of location that had no data
        session_title: Optional session title
        
    Returns:
        Formatted no-data response dictionary
    """
    if location_name:
        message = f"I couldn't find any places in {location_name}. This area might not have data in our database yet."
    else:
        message = "I couldn't find any places in this area. This location might not have data in our database yet."
    
    return format_error_response(
        error_message=message,
        session_title=session_title,
        status_code=40002
    )
