"""
Orchestrator Package - Clean Architecture

This package contains the main orchestrator components organized for clarity:

Core Components:
- main_llm.py: Primary LLM interface and conversation handler (MAIN ENTRY POINT)
- multi_task_orchestrator.py: Multi-task coordination and execution
- task_decomposer.py: Request analysis and task creation
- execution_planner.py: Task execution planning and coordination
- interface.py: LLM interface definitions
- prompts/: System prompts and templates
"""

from .main_llm import MainLLM, get_main_llm
from .interface import LLMInterface
from .multi_task_orchestrator import MultiTaskOrchestrator
from .task_decomposer import TaskDecomposer
from .execution_planner import ExecutionPlanner

__all__ = [
    'MainLLM', 'get_main_llm', 'LLMInterface',  # Main LLM interface
    'MultiTaskOrchestrator',  # Multi-task coordination
    'TaskDecomposer', 'ExecutionPlanner'  # Task management
]
