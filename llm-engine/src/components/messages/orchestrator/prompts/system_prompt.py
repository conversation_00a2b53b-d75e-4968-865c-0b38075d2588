"""
Main LLM system prompt for orchestrator.
Optimized for token efficiency and maximum LLM understanding.
"""

from typing import Optional, List, Dict, Any
from ...processes.function_registry import get_function_registry

# --- Static Prompt Sections ---
INSTRUCTIONS = """PERSONALITY: Warm, helpful, enthusiastic about places within JSON responses.\n\nSEARCH BEHAVIOR: When user requests places → immediate search with JSON response.\n\nLOCATION CONTEXT PRESERVATION: Always preserve full user request including location names in prompt field.\n\nTOOL SELECTION: If the referenced POI is already known (see FOUND_LOCATIONS), use relative_search . Only use poi_lookup if you need to look up the POI’s coordinates.\n\nMULTI-STEP PLANS: For complex requests, output a list of actions in the 'actions' field (as a JSON array), each with its own parameters.\n\nALWAYS use the FOUND_LOCATIONS context for tool selection.\n"""

MULTI_STEP_PATTERNS = """MULTI-STEP DETECTION PATTERNS:
- Sequential: "find X and then Y", "find X then Y", "first find X, then Y"
- Parallel: "find X or Y", "find X and Y", "find X in A and Y in B"
- POI-relative: "find X next to Y", "find X near Y" (when Y is a specific POI)

When detected, use "actions" array instead of single "action" field.

POI-RELATIVE SEARCH RULE: For requests like "find X near [POI] and Y in the same area":
1. Check FOUND_LOCATIONS first - if POI is already known with coordinates, skip poi_lookup
2. If POI is NOT in FOUND_LOCATIONS, use poi_lookup to find the POI coordinates first
3. Use relative_search for subsequent searches around POI coordinates (from FOUND_LOCATIONS or poi_lookup)
4. Each subsequent search should use relative_search with appropriate radius
"""

RESPONSE_FORMATS_FIRST_MESSAGE_WITH_LOCATION = """RESPONSE FORMATS - FIRST MESSAGE ONLY (WITH LOCATION):
For single-step searches:
```json
{"action": "boundary_search", "prompt": "[full user request with location context]", "boundary_name": "Beyoğlu", "session_title": "descriptive title"}
```

For multi-step searches (use "actions" array):
```json
{
  "actions": [
    {"action": "boundary_search", "prompt": "find a restaurant in Besiktas", "boundary_name": "Beşiktaş"},
    {"action": "relative_search", "prompt": "find a cafe next to it", "location_name": "restaurant_location", "radius": 500}
  ],
  "session_title": "multi_step_search"
}
```

For parallel searches (multiple locations):
```json
{
  "actions": [
    {"action": "boundary_search", "prompt": "cafes in Beyoğlu", "boundary_name": "Beyoğlu"},
    {"action": "boundary_search", "prompt": "cafes in Taksim", "boundary_name": "Taksim"}
  ],
  "session_title": "parallel_location_search"
}
```

For POI-relative searches (sequential - ALWAYS use relative_search after poi_lookup):
```json
{
  "actions": [
    {"action": "poi_lookup", "prompt": "find Salt Bae", "poi_name": "Salt Bae"},
    {"action": "relative_search", "prompt": "find cafes close to it", "radius": 500},
    {"action": "relative_search", "prompt": "find pharmacies in the same area", "radius": 500}
  ],
  "session_title": "poi_relative_search"
}
```

IMPORTANT: For POI-relative searches, DO NOT include latitude/longitude in relative_search actions. The system will automatically use the POI coordinates from the poi_lookup result.

For non-search responses:
```json
{"response": "natural response", "session_title": "descriptive title"}
```"""

RESPONSE_FORMATS_FIRST_MESSAGE_NO_LOCATION = """RESPONSE FORMATS - FIRST MESSAGE ONLY (NO LOCATION):\n```json\n{"response": "I'd be happy to help you find places! However, I need your location to provide personalized recommendations. Please enable location services or provide coordinates to get started.", "session_title": "descriptive title"}\n```"""

RESPONSE_FORMATS_CONTINUING_WITH_LOCATION = """RESPONSE FORMATS - CONTINUING CHAT (WITH LOCATION):
For single-step searches:
```json
{"action": "boundary_search", "prompt": "[full user request with location context]", "boundary_name": "Beyoğlu"}
```

For multi-step searches (use "actions" array):
```json
{
  "actions": [
    {"action": "boundary_search", "prompt": "find a restaurant in Besiktas", "boundary_name": "Beşiktaş"},
    {"action": "relative_search", "prompt": "find a cafe next to it", "location_name": "restaurant_location", "radius": 500}
  ]
}
```

For parallel searches (multiple locations):
```json
{
  "actions": [
    {"action": "boundary_search", "prompt": "cafes in Beyoğlu", "boundary_name": "Beyoğlu"},
    {"action": "boundary_search", "prompt": "cafes in Taksim", "boundary_name": "Taksim"}
  ]
}
```

For POI-relative searches (sequential - check FOUND_LOCATIONS first, only use poi_lookup if POI not known):
```json
{
  "actions": [
    {"action": "poi_lookup", "prompt": "find Salt Bae", "poi_name": "Salt Bae"},
    {"action": "relative_search", "prompt": "find cafes close to it", "radius": 500},
    {"action": "relative_search", "prompt": "find pharmacies in the same area", "radius": 500}
  ]
}
```

IMPORTANT: For POI-relative searches, DO NOT include latitude/longitude in relative_search actions. The system will automatically use the POI coordinates from the poi_lookup result.

For POI-relative searches when POI is already in FOUND_LOCATIONS:
```json
{
  "action": "relative_search",
  "prompt": "find restaurants next to Kırmızı Kedi Café",
  "latitude": 41.041948,
  "longitude": 29.0048682,
  "radius": 500
}
```

For non-search responses:
```json
{"response": "natural response"}
```"""

RESPONSE_FORMATS_CONTINUING_NO_LOCATION = """RESPONSE FORMATS - CONTINUING CHAT (NO LOCATION):\n```json\n{"response": "I'd love to help you find places, but I still need your location to provide recommendations. Please enable location services or provide coordinates."}\n```"""

RULES_FIRST_MESSAGE = """CRITICAL RULES:\n- MANDATORY: All responses must be VALID JSON wrapped in ```json blocks\n- MANDATORY: Include session_title field on FIRST message only\n- MANDATORY: Always include opening { and closing }\n- MANDATORY: All strings must be in \"double quotes\"\n- NO additional text outside JSON blocks\n- LOCATION REQUIREMENT: NEVER use search actions when USER_LOCATION shows \"Not available\" - always respond with helpful message about needing location"""

RULES_CONTINUING = """CRITICAL RULES:\n- MANDATORY: All responses must be VALID JSON wrapped in ```json blocks\n- MANDATORY: NO session_title field after first message\n- MANDATORY: Always include opening { and closing }\n- MANDATORY: All strings must be in \"double quotes\"\n- NO additional text outside JSON blocks\n- MANDATORY: Do not greet again in continuing conversations - jump directly to helping\n- LOCATION REQUIREMENT: NEVER use search actions when USER_LOCATION shows \"Not available\" - always respond with helpful message about needing location"""

VALIDATION = "VALIDATION: Every response will be parsed as JSON. Invalid JSON will cause system failure."


def get_main_llm_system_prompt(
    history: Optional[str] = None,
    latitude: Optional[float] = None,
    longitude: Optional[float] = None,
    radius: Optional[int] = None,
    top_candidate: Optional[List[Dict[str, Any]]] = None
) -> str:
    """Generate main LLM system prompt with dynamic context."""

    # Get dynamic tools section from function registry
    registry = get_function_registry()
    dynamic_tools_section = registry.generate_dynamic_prompt()

    context_parts = []

    # User location context
    location_available = latitude is not None and longitude is not None
    if location_available:
        radius_text = f" ({radius}m)" if radius else ""
        context_parts.append(
            f"USER_LOCATION: {latitude},{longitude}{radius_text}")
    else:
        context_parts.append(
            "USER_LOCATION: Not available - location services disabled or coordinates not provided")

    # Found locations context - enhanced with comprehensive POI information
    if top_candidate and len(top_candidate) > 0:
        locations = _format_locations_comprehensive(top_candidate)
        if locations:
            context_parts.append(
                f"FOUND_LOCATIONS (all known POIs with coordinates - ALWAYS check this before choosing tools):\n{locations}")
        else:
            context_parts.append(
                "FOUND_LOCATIONS: None (no POIs found in previous searches)")
    else:
        context_parts.append(
            "FOUND_LOCATIONS: None (no previous searches or POIs)")

    # Conversation history - determine if this is first message
    is_first_message = not history or not history.strip()
    if history and history.strip():
        context_parts.append(f"HISTORY: {history}")

    context = "\n".join(context_parts)

    # Select static prompt sections
    if is_first_message:
        response_formats = RESPONSE_FORMATS_FIRST_MESSAGE_WITH_LOCATION if location_available else RESPONSE_FORMATS_FIRST_MESSAGE_NO_LOCATION
        rules = RULES_FIRST_MESSAGE
    else:
        response_formats = RESPONSE_FORMATS_CONTINUING_WITH_LOCATION if location_available else RESPONSE_FORMATS_CONTINUING_NO_LOCATION
        rules = RULES_CONTINUING

    return f"""Location Assistant - Enterprise JSON API Interface\n\n{context}\n\n{INSTRUCTIONS}\n\n{MULTI_STEP_PATTERNS}\n\n{dynamic_tools_section}\n\n{response_formats}\n\n{rules}\n\n{VALIDATION}"""


def _format_locations_compact(candidates: List[Dict[str, Any]]) -> str:
    """Format locations with all available fields, showing only non-null values."""
    if not candidates:
        return ""

    formatted = []

    for i, loc in enumerate(candidates, 1):
        # Extract all possible fields dynamically
        fields = _extract_location_fields(loc)
        if fields:
            formatted.append(f"{i}. {fields}")

    return "\n".join(formatted)


def _format_locations_comprehensive(candidates: List[Dict[str, Any]]) -> str:
    """Format locations with comprehensive information optimized for LLM tool selection."""
    if not candidates:
        return ""

    formatted = []

    # Group by unique POI names to avoid duplicates
    poi_map = {}
    for loc in candidates:
        name = loc.get('name', '').strip()
        if name:
            # Keep the most complete entry for each POI name
            if name not in poi_map or len(str(loc)) > len(str(poi_map[name])):
                poi_map[name] = loc

    # Format each unique POI with essential information for tool selection
    for i, (name, loc) in enumerate(poi_map.items(), 1):
        lat = loc.get('latitude')
        lon = loc.get('longitude')

        # Essential info for tool selection
        poi_info = [f"NAME: {name}"]

        if lat is not None and lon is not None:
            poi_info.append(f"COORDINATES: {lat},{lon}")

        # Add other useful context
        if loc.get('address'):
            poi_info.append(f"ADDRESS: {loc['address']}")
        if loc.get('district'):
            poi_info.append(f"DISTRICT: {loc['district']}")
        if loc.get('category'):
            poi_info.append(f"CATEGORY: {loc['category']}")
        if loc.get('subcategory'):
            poi_info.append(f"TYPE: {loc['subcategory']}")

        formatted.append(f"{i}. {' | '.join(poi_info)}")

    return "\n".join(formatted)


def _extract_location_fields(loc: Dict[str, Any]) -> str:
    """Extract all non-null location fields dynamically with proper Unicode handling."""
    if not loc:
        return ""

    field_parts = []

    # Process ALL fields dynamically
    for key, value in loc.items():
        # Skip null/empty values
        if value is None or (isinstance(value, str) and not value.strip()):
            continue

        # Clean up key name for compact display
        clean_key = key.replace('_', '').lower()

        # Ensure proper Unicode handling for string values
        if isinstance(value, str):
            # Decode any Unicode escapes that might be present
            try:
                value = value.encode('latin1').decode('unicode_escape')
            except (UnicodeDecodeError, UnicodeEncodeError):
                # If decoding fails, use the original value
                pass

        # Special formatting for common patterns
        if 'distance' in key and 'm' not in str(value):
            field_parts.append(f"{clean_key}:{value}m")
        elif key in ['latitude', 'longitude']:
            # Handle coordinates specially - combine lat,lon
            continue  # We'll handle this below
        else:
            field_parts.append(f"{clean_key}:{value}")

    # Handle coordinates as a pair if both exist
    lat = loc.get('latitude')
    lon = loc.get('longitude')
    if lat is not None and lon is not None:
        field_parts.append(f"coords:{lat},{lon}")

    return " | ".join(field_parts)
