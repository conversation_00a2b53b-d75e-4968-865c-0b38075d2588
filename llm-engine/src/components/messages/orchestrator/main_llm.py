# src/agents/llm/main_llm.py

"""
Main LLM implementation for centralized conversation handling.

This module provides the MainLLM class which serves as the central orchestrator
for all conversations, deciding when to use specialized tools and generating
natural language responses.
"""

from typing import Dict, Any, List
from src.models import LLMResponse
from .interface import LLMInterface
from .prompts.system_prompt import get_main_llm_system_prompt
from ..shared.llm_endpoints import create_agent_llm_adapter
from src.infrastructure.log.unified_logger import debug, error, info
# Removed process_router import - now using multi_task_orchestrator

# Main LLM status codes (9XXXX range)
LOCATION_SUCCESS = 20000


def is_success(status_code: int) -> bool:
    """Check if status code represents success."""
    return status_code == LOCATION_SUCCESS


class MainLLM(LLMInterface):
    """
    Central orchestrator for all conversations using LLM capabilities.
    """

    def __init__(self):
        self.llm_adapter = create_agent_llm_adapter("main")

    def call_api(self, prompt: str, **kwargs) -> LLMResponse:
        """
        Make a request to the LLM API (adapter only, config-driven).
        """
        # Prepare system prompt and API request
        history = kwargs.get("history")
        latitude = kwargs.get("latitude")
        longitude = kwargs.get("longitude")
        radius = kwargs.get("radius")
        top_candidate = kwargs.get("top_candidate", [])
        system_prompt = get_main_llm_system_prompt(
            history=history,
            latitude=latitude,
            longitude=longitude,
            radius=radius,
            top_candidate=top_candidate
        )
        api_request = {
            "system_prompt": system_prompt,
            "user_prompt": prompt
        }
        # Call the adapter (no fallback)
        raw_response = self.llm_adapter.call_api(prompt=prompt, **api_request)

        if not isinstance(raw_response, dict):
            error("Main LLM returned invalid response format")
            return LLMResponse({"response": "", "error": "Invalid response format", "top_candidates": []})
        return raw_response

    def extract_content(self, response: Dict[str, Any]) -> LLMResponse:
        """
        Extract the relevant content from the API response (adapter only).
        """
        return self.llm_adapter.extract_content(response)

    def process_message(self, session, message: str, latitude: float, longitude: float,
                        search_radius: int, num_candidates: int, streaming: bool = False) -> Dict[str, Any]:
        """
        Process a user message and generate an appropriate response.

        Args:
            session: SessionContext for unified state and history access
            message: User message to process
            latitude: User latitude
            longitude: User longitude
            search_radius: Search radius in meters
            num_candidates: Number of top candidates to return
        """
        # Get previous top_candidates from session
        previous_top_candidates = session.get_top_candidates()
        if previous_top_candidates is None:
            previous_top_candidates = []

        # Get formatted history BEFORE logging the current user message
        # This ensures the current message is not included in the history for this API call
        formatted_history = session.get_formatted_history()

        # Call the LLM API with context including previous top_candidates and history
        response = self.call_api(
            prompt=message,
            history=formatted_history,
            latitude=latitude,
            longitude=longitude,
            radius=search_radius,
            top_candidate=previous_top_candidates
        )

        # NOTE: User message logging is now handled centrally by the orchestrator
        # to prevent duplicates. Do not log here.

        # Parse the response to check for actions
        # Handle error responses
        if not response or not isinstance(response, dict):
            response_text = "I apologize, but I'm having trouble processing your request right now. Please try again."

            # Log assistant response to history
            session.log_assistant_message(response_text, metadata={
                "top_candidates": previous_top_candidates,
                "error": "Invalid LLM response format"
            })

            return {
                "response": response_text,
                "top_candidates": previous_top_candidates,
                "session_title": ""
            }

        # Handle error in response
        if "error" in response:
            response_text = "I apologize, but I'm having trouble processing your request right now. Please try again."

            # Log assistant response to history
            session.log_assistant_message(response_text, metadata={
                "top_candidates": previous_top_candidates,
                "error": response.get("error")
            })

            return {
                "response": response_text,
                "top_candidates": previous_top_candidates,
                "session_title": ""
            }

        # Enhanced: Support multi-action LLM outputs (multi-step plans)
        llm_actions = []
        if isinstance(response, dict):
            # Priority 1: If the response contains a list of actions (multi-step plan)
            if isinstance(response.get("actions"), list) and len(response["actions"]) > 0:
                llm_actions = response["actions"]
                debug(
                    f"Extracted {len(llm_actions)} actions from 'actions' field")
            # Priority 2: If the response contains a single action
            elif response.get("action"):
                llm_actions = [response]
                debug("Extracted single action from 'action' field")
            # Priority 3: If the response contains a JSON string with actions (fallback parsing)
            elif "response" in response and isinstance(response["response"], str):
                llm_actions = self._extract_actions_from_text(
                    response["response"])
                if llm_actions:
                    debug(
                        f"Extracted {len(llm_actions)} actions from text parsing")

        # Validate and clean extracted actions
        if llm_actions:
            llm_actions = self._validate_and_clean_actions(llm_actions)
            debug(f"Final validated actions count: {len(llm_actions)}")

        if llm_actions:
            # Use the multi-task orchestrator for handling all requests
            info("Processing request via multi-task orchestrator (multi-action support)")
            try:
                from .multi_task_orchestrator import MultiTaskOrchestrator
                # Initialize orchestrator with enhanced state and history managers
                orchestrator = MultiTaskOrchestrator(
                    base_state_manager=session.state_manager,
                    base_history_manager=session.history_manager
                )
                # Pass llm_actions to orchestrator and TaskDecomposer with session context
                location_result = orchestrator.process_request(
                    user_request=message,
                    latitude=latitude,
                    longitude=longitude,
                    search_radius=search_radius,
                    num_candidates=num_candidates,
                    history=formatted_history,
                    user_id=session.user_id,
                    session_id=session.session_id,
                    llm_actions=llm_actions,
                    streaming=streaming,
                    session_context=session  # Pass session context for centralized management
                )
                info(f"Multi-task orchestrator result: {location_result}")
            except Exception as e:
                error(f"Multi-task orchestrator error: {e}")
                # Fallback to error response
                location_result = {
                    "response": f"I encountered an error processing your request: {str(e)}",
                    "top_candidates": [],
                    "error": str(e)
                }
            # Check if location result has error status code
            status_code = location_result.get(
                "status_code", LOCATION_SUCCESS)
            if not is_success(status_code):
                # Replace error handler agent with a simple error response
                response_text = location_result.get(
                    "response", "I'm sorry, I encountered an issue while searching.")
                session_title = location_result.get(
                    "session_title", "") or response.get("session_title", "")

                # For error cases, we need to log manually since orchestrator may not have handled it
                session.log_user_message(message, metadata={
                    "latitude": latitude,
                    "longitude": longitude,
                    "search_radius": search_radius
                })
                session.log_assistant_message(response_text, metadata={
                    "top_candidates": [],
                    "session_title": session_title,
                    "status_code": location_result["status_code"]
                })

                return {
                    "response": response_text,
                    "top_candidates": [],
                    "session_title": session_title
                }

            # Session management is now handled centrally by the orchestrator
            # Return the result directly without duplicate logging
            return {
                "response": location_result.get("response", ""),
                "top_candidates": location_result.get("top_candidates", []),
                "session_title": location_result.get("session_title", "")
            }
        elif isinstance(response, dict):
            # Check if the response contains a search action directly
            search_actions = ["relative_search", "boundary_search",
                              "point_radius_search", "poi_based_search"]
            if response.get("action") in search_actions:
                # Validate that coordinates are available before calling location processes
                if latitude is None or longitude is None:
                    error(
                        f"Location process triggered but coordinates are null: lat={latitude}, lon={longitude}")
                    # Return error response instead of calling location processes
                    return {
                        "response": "I'd be happy to help you find places! However, I need your location to provide personalized recommendations. Please enable location services or provide coordinates to get started.",
                        "top_candidates": [],
                        "session_title": response.get("session_title", "") if session.is_first_message() else None
                    }

                try:
                    # Extract session title from main LLM response
                    main_llm_session_title = response.get("session_title", "")

                    # Use the multi-task orchestrator for handling all requests
                    info("Processing request via multi-task orchestrator")

                    try:
                        from .multi_task_orchestrator import MultiTaskOrchestrator
                        orchestrator = MultiTaskOrchestrator()

                        # Use LLM-provided coordinates if available, otherwise use user coordinates
                        llm_latitude = response.get("latitude", latitude)
                        llm_longitude = response.get("longitude", longitude)
                        llm_radius = response.get("radius", search_radius)

                        # Process the request using multi-task orchestrator
                        location_result = orchestrator.process_request(
                            user_request=response.get("prompt", message),
                            latitude=llm_latitude,
                            longitude=llm_longitude,
                            search_radius=llm_radius,
                            num_candidates=num_candidates,
                            history=formatted_history,
                            user_id=session.user_id,
                            session_id=session.session_id,
                            llm_actions=[response],
                            session_context=session  # Add centralized session management
                        )
                        info(
                            f"Multi-task orchestrator result: {location_result}")
                    except Exception as e:
                        error(f"Multi-task orchestrator error: {e}")
                        # Fallback to error response
                        location_result = {
                            "response": f"I encountered an error processing your request: {str(e)}",
                            "top_candidates": [],
                            "error": str(e)
                        }

                    # Check if location result has error status code
                    status_code = location_result.get(
                        "status_code", LOCATION_SUCCESS)
                    if not is_success(status_code):
                        # Replace error handler agent with a simple error response
                        response_text = location_result.get(
                            "response", "I'm sorry, I encountered an issue while searching.")
                        session_title = location_result.get(
                            "session_title", "") or main_llm_session_title
                        # Log assistant response to history
                        session.log_assistant_message(response_text, metadata={
                            "top_candidates": [],
                            "session_title": session_title,
                            "status_code": location_result["status_code"]
                        })
                        return {
                            "response": response_text,
                            "top_candidates": [],
                            "session_title": session_title
                        }

                    # Session management is now handled centrally by the orchestrator
                    # Return the result directly
                    return {
                        "response": location_result.get("response", ""),
                        "top_candidates": location_result.get("top_candidates", []),
                        "session_title": location_result.get("session_title", "")
                    }
                except Exception as e:
                    pass

            # Check if response field contains JSON string with action
            elif "response" in response:
                response_text = response["response"]
                if isinstance(response_text, str):
                    try:
                        import json
                        # Clean up markdown formatting from the response
                        cleaned_text = response_text.strip()

                        # Remove markdown code block formatting
                        if cleaned_text.startswith("json\n"):
                            cleaned_text = cleaned_text[5:]  # Remove "json\n"
                        if cleaned_text.endswith("\n```"):
                            cleaned_text = cleaned_text[:-4]  # Remove "\n```"
                        if cleaned_text.endswith("```"):
                            cleaned_text = cleaned_text[:-3]  # Remove "```"

                        # Fix malformed JSON that starts with 'response": ...'
                        if cleaned_text.startswith('response": '):
                            cleaned_text = "{\"" + cleaned_text

                        cleaned_text = cleaned_text.strip()

                        # Try to parse the cleaned text as JSON
                        if cleaned_text.startswith("{") and cleaned_text.endswith("}"):
                            parsed_action = json.loads(cleaned_text)

                            search_actions = [
                                "relative_search", "boundary_search", "point_radius_search", "poi_based_search"]

                            # Handle legacy action name mapping
                            action = parsed_action.get("action")
                            if action == "user_location_search":
                                debug(
                                    f"Legacy action name detected: {action} -> mapping to relative_search")
                                parsed_action["action"] = "relative_search"
                                action = "relative_search"

                            if action in search_actions:
                                # Validate that coordinates are available before calling location processes
                                if latitude is None or longitude is None:
                                    error(
                                        f"Location process triggered but coordinates are null: lat={latitude}, lon={longitude}")
                                    # Return error response instead of calling location processes
                                    response_text = "I'd be happy to help you find places! However, I need your location to provide personalized recommendations. Please enable location services or provide coordinates to get started."
                                    session_title = parsed_action.get(
                                        "session_title", "") if session.is_first_message() else ""

                                    # Log assistant response to history
                                    session.log_assistant_message(response_text, metadata={
                                        "top_candidates": [],
                                        "session_title": session_title
                                    })

                                    return {
                                        "response": response_text,
                                        "top_candidates": [],
                                        "session_title": session_title
                                    }

                                # Extract session title from main LLM response
                                main_llm_session_title = parsed_action.get(
                                    "session_title", "")

                                # Use the multi-task orchestrator for all requests
                                info(
                                    "Routing JSON action request to multi-task orchestrator")
                                try:
                                    from .multi_task_orchestrator import MultiTaskOrchestrator
                                    orchestrator = MultiTaskOrchestrator()

                                    # Process the request using multi-task orchestrator
                                    location_result = orchestrator.process_request(
                                        user_request=parsed_action.get(
                                            "prompt", message),
                                        latitude=latitude,
                                        longitude=longitude,
                                        search_radius=search_radius,
                                        num_candidates=num_candidates,
                                        history=formatted_history,
                                        user_id=session.user_id,
                                        session_id=session.session_id,
                                        llm_actions=[parsed_action],
                                        session_context=session  # Add centralized session management
                                    )
                                except Exception as e:
                                    error(
                                        f"Multi-task orchestrator failed: {e}")
                                    return {
                                        "response": "I apologize, but I encountered an error while processing your request. Please try again.",
                                        "top_candidates": [],
                                        "session_title": main_llm_session_title
                                    }

                                # Check if location result has error status code
                                status_code = location_result.get(
                                    "status_code", LOCATION_SUCCESS)
                                if not is_success(status_code):
                                    # Replace error handler agent with a simple error response
                                    response_text = location_result.get(
                                        "response", "I'm sorry, I encountered an issue while searching.")
                                    session_title = location_result.get(
                                        "session_title", "") or main_llm_session_title
                                    # Log assistant response to history
                                    session.log_assistant_message(response_text, metadata={
                                        "top_candidates": [],
                                        "session_title": session_title,
                                        "status_code": location_result["status_code"]
                                    })
                                    return {
                                        "response": response_text,
                                        "top_candidates": [],
                                        "session_title": session_title
                                    }

                                # Session management is now handled centrally by the orchestrator
                                # Return the result directly
                                return {
                                    "response": location_result.get("response", ""),
                                    "top_candidates": location_result.get("top_candidates", []),
                                    "session_title": location_result.get("session_title", "")
                                }
                            elif "response" in parsed_action:
                                # This is a regular conversational response in JSON format
                                response_text = parsed_action.get(
                                    "response", "")
                                session_title = parsed_action.get(
                                    "session_title", "")

                                # Log assistant response to history
                                session.log_assistant_message(response_text, metadata={
                                    "top_candidates": previous_top_candidates,
                                    "session_title": session_title
                                })

                                # Save session title to session state metadata if provided
                                if session_title:
                                    session.update_state_metadata(
                                        {"session_title": session_title})

                                return {
                                    "response": response_text,
                                    "top_candidates": previous_top_candidates,
                                    "session_title": session_title
                                }
                    except json.JSONDecodeError:
                        # Not valid JSON, treat as regular response
                        pass
                    except Exception as e:
                        pass

        # Return regular response with preserved top_candidates
        response_text = response.get("response", "")
        session_title = response.get("session_title", "")

        # Log assistant response to history
        session.log_assistant_message(response_text, metadata={
            "top_candidates": previous_top_candidates,
            "session_title": session_title
        })

        # Save session title to session state metadata if provided
        if session_title:
            session.update_state_metadata({"session_title": session_title})

        return {
            "response": response_text,
            "top_candidates": previous_top_candidates,
            "session_title": session_title
        }

    def _extract_actions_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract actions from JSON text with robust parsing."""
        import json

        actions = []
        try:
            # Clean up the text
            cleaned_text = text.strip()

            # Remove markdown code block markers
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            elif cleaned_text.startswith("json\n"):
                cleaned_text = cleaned_text[5:]
            elif cleaned_text.startswith("```"):
                cleaned_text = cleaned_text[3:]

            if cleaned_text.endswith("\n```"):
                cleaned_text = cleaned_text[:-4]
            elif cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]

            # Fix common JSON formatting issues
            if cleaned_text.startswith('response": '):
                cleaned_text = "{\"" + cleaned_text

            cleaned_text = cleaned_text.strip()

            # Try to parse as JSON
            if cleaned_text.startswith("[") and cleaned_text.endswith("]"):
                # Array of actions
                actions = json.loads(cleaned_text)
            elif cleaned_text.startswith("{") and cleaned_text.endswith("}"):
                # Single action or object with actions
                parsed = json.loads(cleaned_text)
                if isinstance(parsed, dict):
                    if "actions" in parsed and isinstance(parsed["actions"], list):
                        actions = parsed["actions"]
                    elif "action" in parsed:
                        actions = [parsed]
                    else:
                        # Treat the whole object as a single action if it has action-like properties
                        if any(key in parsed for key in ["action", "prompt", "latitude", "longitude", "boundary_name", "poi_name"]):
                            actions = [parsed]

        except (json.JSONDecodeError, ValueError) as e:
            debug(f"Failed to parse actions from text: {e}")

        return actions if isinstance(actions, list) else []

    def _validate_and_clean_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean extracted actions."""
        valid_actions = []

        for i, action in enumerate(actions):
            if not isinstance(action, dict):
                debug(f"Skipping non-dict action at index {i}")
                continue

            # Check if action has required fields
            if not action.get("action"):
                debug(f"Skipping action at index {i}: missing 'action' field")
                continue

            # Clean up the action
            cleaned_action = {}
            for key, value in action.items():
                # Skip None values and empty strings
                if value is not None and value != "":
                    cleaned_action[key] = value

            # Ensure required fields exist
            if "prompt" not in cleaned_action and "user_request" not in cleaned_action:
                # Add a default prompt if missing
                cleaned_action["prompt"] = "User request"

            valid_actions.append(cleaned_action)

        return valid_actions

# Factory function


def get_main_llm() -> LLMInterface:
    return MainLLM()
