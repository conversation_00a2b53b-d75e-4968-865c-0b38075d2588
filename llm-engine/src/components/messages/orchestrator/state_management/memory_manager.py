"""
Memory Manager for Insight Extraction

This module provides memory management that extracts insights from tool results
to enhance future responses with learned user preferences and patterns.
"""

from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass, field
from collections import Counter
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ...processes.shared import TaskResult


@dataclass
class SessionMemory:
    """
    Session memory that stores extracted insights and patterns.
    """
    user_id: str
    session_id: str
    
    # User preferences learned from interactions
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    # Location patterns and frequently searched areas
    location_patterns: List[str] = field(default_factory=list)
    frequent_locations: Dict[str, int] = field(default_factory=dict)
    
    # POI interests and category preferences
    poi_interests: List[str] = field(default_factory=list)
    category_preferences: Dict[str, int] = field(default_factory=dict)
    
    # Search insights and result summaries
    search_insights: Dict[str, Any] = field(default_factory=dict)
    successful_search_patterns: List[str] = field(default_factory=list)
    
    # Temporal patterns
    search_times: List[datetime] = field(default_factory=list)
    preferred_search_radius: Optional[int] = None
    
    # Memory metadata
    insights_count: int = 0
    last_updated: Optional[datetime] = None


class MemoryManager:
    """
    Memory manager that extracts insights from tool results.
    
    Key features:
    - Learns user preferences from search patterns
    - Identifies frequently searched locations and categories
    - Extracts successful search patterns
    - Provides insights for enhanced response generation
    """
    
    def __init__(self):
        """Initialize the memory manager."""
        self.session_memories: Dict[str, SessionMemory] = {}
        
    def get_session_memory(self, user_id: str, session_id: str) -> SessionMemory:
        """
        Get session memory, creating if not exists.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            SessionMemory object
        """
        cache_key = f"{user_id}:{session_id}"
        
        if cache_key not in self.session_memories:
            self.session_memories[cache_key] = SessionMemory(
                user_id=user_id,
                session_id=session_id,
                last_updated=datetime.now()
            )
            
        return self.session_memories[cache_key]
        
    def process_task_results(self, user_id: str, session_id: str, 
                           user_request: str, task_results: List[TaskResult]) -> bool:
        """
        Process task results to extract insights and update memory.
        
        Args:
            user_id: User ID
            session_id: Session ID
            user_request: Original user request
            task_results: Task results to analyze
            
        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_session_memory(user_id, session_id)
            
            # Extract insights from successful results
            successful_results = [r for r in task_results if r.success]
            
            if successful_results:
                self._extract_location_patterns(memory, user_request, successful_results)
                self._extract_category_preferences(memory, user_request, successful_results)
                self._extract_search_patterns(memory, user_request, successful_results)
                self._extract_temporal_patterns(memory, task_results)
                
                memory.insights_count += 1
                memory.last_updated = datetime.now()
                
                debug(f"Extracted insights from {len(successful_results)} successful results")
                
            return True
            
        except Exception as e:
            error(f"Failed to process task results for memory: {e}")
            return False
            
    def _extract_location_patterns(self, memory: SessionMemory, user_request: str, 
                                 task_results: List[TaskResult]):
        """
        Extract location patterns from task results.
        
        Args:
            memory: Session memory to update
            user_request: User request text
            task_results: Successful task results
        """
        # Extract mentioned locations from request
        request_lower = user_request.lower()
        
        # Common Turkish district/neighborhood names
        turkish_locations = [
            'beyoğlu', 'beşiktaş', 'kadıköy', 'üsküdar', 'fatih', 'şişli',
            'bakırköy', 'maltepe', 'pendik', 'kartal', 'ataşehir', 'büyükçekmece',
            'taksim', 'galata', 'karaköy', 'ortaköy', 'bebek', 'etiler'
        ]
        
        for location in turkish_locations:
            if location in request_lower:
                memory.frequent_locations[location] = memory.frequent_locations.get(location, 0) + 1
                if location not in memory.location_patterns:
                    memory.location_patterns.append(location)
                    
        # Extract locations from task results
        for result in task_results:
            if result.result and result.result.get('top_candidates'):
                for candidate in result.result['top_candidates']:
                    district = candidate.get('district', '').lower()
                    if district and district not in memory.location_patterns:
                        memory.location_patterns.append(district)
                        memory.frequent_locations[district] = memory.frequent_locations.get(district, 0) + 1
                        
    def _extract_category_preferences(self, memory: SessionMemory, user_request: str,
                                    task_results: List[TaskResult]):
        """
        Extract category preferences from task results.
        
        Args:
            memory: Session memory to update
            user_request: User request text
            task_results: Successful task results
        """
        # Extract categories from request
        request_lower = user_request.lower()
        
        # Common categories
        categories = [
            'cafe', 'restaurant', 'market', 'shop', 'bank', 'pharmacy', 'hospital',
            'hotel', 'gas_station', 'atm', 'bakery', 'bar', 'gym', 'school'
        ]
        
        for category in categories:
            if category in request_lower or category.replace('_', ' ') in request_lower:
                memory.category_preferences[category] = memory.category_preferences.get(category, 0) + 1
                if category not in memory.poi_interests:
                    memory.poi_interests.append(category)
                    
        # Extract categories from results
        for result in task_results:
            if result.result and result.result.get('top_candidates'):
                for candidate in result.result['top_candidates']:
                    category = candidate.get('category', '').lower()
                    subcategory = candidate.get('subcategory', '').lower()
                    
                    if category:
                        memory.category_preferences[category] = memory.category_preferences.get(category, 0) + 1
                        if category not in memory.poi_interests:
                            memory.poi_interests.append(category)
                            
                    if subcategory:
                        memory.category_preferences[subcategory] = memory.category_preferences.get(subcategory, 0) + 1
                        if subcategory not in memory.poi_interests:
                            memory.poi_interests.append(subcategory)
                            
    def _extract_search_patterns(self, memory: SessionMemory, user_request: str,
                               task_results: List[TaskResult]):
        """
        Extract successful search patterns.
        
        Args:
            memory: Session memory to update
            user_request: User request text
            task_results: Successful task results
        """
        # Identify successful search patterns
        total_candidates = sum(
            len(result.result.get('top_candidates', [])) 
            for result in task_results 
            if result.result
        )
        
        if total_candidates > 0:
            # This was a successful search pattern
            pattern = self._normalize_search_pattern(user_request)
            if pattern not in memory.successful_search_patterns:
                memory.successful_search_patterns.append(pattern)
                
            # Store search insights
            memory.search_insights[pattern] = {
                'total_results': total_candidates,
                'tools_used': [result.task.tool_name for result in task_results],
                'success_rate': len(task_results) / len(task_results) if task_results else 0,
                'last_used': datetime.now().isoformat()
            }
            
    def _extract_temporal_patterns(self, memory: SessionMemory, task_results: List[TaskResult]):
        """
        Extract temporal patterns from searches.
        
        Args:
            memory: Session memory to update
            task_results: Task results
        """
        memory.search_times.append(datetime.now())
        
        # Extract preferred search radius
        for result in task_results:
            if hasattr(result.task, 'parameters') and result.task.parameters.get('radius'):
                radius = result.task.parameters['radius']
                if memory.preferred_search_radius is None:
                    memory.preferred_search_radius = radius
                else:
                    # Average with existing preference
                    memory.preferred_search_radius = (memory.preferred_search_radius + radius) // 2
                    
    def _normalize_search_pattern(self, user_request: str) -> str:
        """
        Normalize user request to identify patterns.
        
        Args:
            user_request: User request text
            
        Returns:
            Normalized pattern string
        """
        # Simple normalization - remove specific names and keep structure
        normalized = user_request.lower()
        
        # Replace specific location names with placeholder
        turkish_locations = [
            'beyoğlu', 'beşiktaş', 'kadıköy', 'üsküdar', 'fatih', 'şişli',
            'bakırköy', 'maltepe', 'pendik', 'kartal', 'ataşehir', 'büyükçekmece'
        ]
        
        for location in turkish_locations:
            if location in normalized:
                normalized = normalized.replace(location, '[LOCATION]')
                
        return normalized[:100]  # Limit length
        
    def get_insights_for_response(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get memory insights for enhanced response generation.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            Dictionary of insights for response enhancement
        """
        memory = self.get_session_memory(user_id, session_id)
        
        # Get top preferences
        top_categories = Counter(memory.category_preferences).most_common(3)
        top_locations = Counter(memory.frequent_locations).most_common(3)
        
        insights = {
            'user_preferences': {
                'favorite_categories': [cat for cat, _ in top_categories],
                'frequent_locations': [loc for loc, _ in top_locations],
                'preferred_radius': memory.preferred_search_radius,
                'search_count': memory.insights_count
            },
            'patterns': {
                'successful_patterns': memory.successful_search_patterns[-3:],  # Last 3
                'location_patterns': memory.location_patterns[-5:],  # Last 5
                'poi_interests': memory.poi_interests[-5:]  # Last 5
            },
            'context': {
                'total_searches': len(memory.search_times),
                'recent_activity': len([t for t in memory.search_times if (datetime.now() - t).days < 1]),
                'last_updated': memory.last_updated.isoformat() if memory.last_updated else None
            }
        }
        
        return insights
        
    def clear_session_memory(self, user_id: str, session_id: str) -> bool:
        """
        Clear session memory (for testing or reset).
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            True if successful
        """
        cache_key = f"{user_id}:{session_id}"
        if cache_key in self.session_memories:
            del self.session_memories[cache_key]
        return True
