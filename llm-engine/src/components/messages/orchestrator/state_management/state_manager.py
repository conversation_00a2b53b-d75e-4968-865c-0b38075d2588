"""
Enhanced State Manager for Multi-Task Orchestrator

This module provides state management that accumulates results across requests
instead of overwriting them, maintaining conversation context and search history.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from src.infrastructure.log.unified_logger import debug, info, warning, error
from src.infrastructure.state.interface import IStateManager
from src.models import SessionState
from ..shared.task_utils import merge_task_results
from ...processes.shared import TaskResult


@dataclass
class EnhancedSessionState:
    """
    Enhanced session state that accumulates data across requests.

    This extends the basic SessionState with accumulative capabilities
    for top_candidates, search_context, and user preferences.
    """
    # Basic session info
    user_id: str
    session_id: str

    # Accumulated data (never overwritten, only extended)
    top_candidates: List[Dict[str, Any]] = field(default_factory=list)
    search_context: Dict[str, Any] = field(default_factory=dict)
    active_filters: Dict[str, Any] = field(default_factory=dict)

    # Location tracking
    last_search_location: Optional[Tuple[float, float]] = None
    last_search_time: Optional[datetime] = None

    # Session metadata
    total_searches: int = 0
    session_created: Optional[datetime] = None
    last_updated: Optional[datetime] = None


class StateManager:
    """
    Enhanced state manager that accumulates results across requests.

    Key features:
    - Accumulates top_candidates instead of overwriting
    - Maintains search context across requests
    - Tracks user preferences and patterns
    - Deduplicates results intelligently
    """

    def __init__(self, base_state_manager: IStateManager):
        """
        Initialize with the base state manager for persistence.

        Args:
            base_state_manager: The underlying state manager for persistence
        """
        self.base_state_manager = base_state_manager
        self.session_cache: Dict[str, EnhancedSessionState] = {}

    def get_enhanced_state(self, user_id: str, session_id: str) -> EnhancedSessionState:
        """
        Get enhanced session state, creating if not exists.

        Args:
            user_id: User ID
            session_id: Session ID

        Returns:
            EnhancedSessionState object
        """
        cache_key = f"{user_id}:{session_id}"

        # Check cache first
        if cache_key in self.session_cache:
            return self.session_cache[cache_key]

        # Try to load from base state manager
        base_state = self.base_state_manager.get_session(user_id, session_id)

        if base_state and base_state.get('metadata', {}).get('enhanced_state'):
            # Load existing enhanced state
            enhanced_data = base_state['metadata']['enhanced_state']
            enhanced_state = EnhancedSessionState(
                user_id=user_id,
                session_id=session_id,
                top_candidates=enhanced_data.get('top_candidates', []),
                search_context=enhanced_data.get('search_context', {}),
                active_filters=enhanced_data.get('active_filters', {}),
                last_search_location=enhanced_data.get('last_search_location'),
                last_search_time=datetime.fromisoformat(
                    enhanced_data['last_search_time']) if enhanced_data.get('last_search_time') else None,
                total_searches=enhanced_data.get('total_searches', 0),
                session_created=datetime.fromisoformat(
                    enhanced_data['session_created']) if enhanced_data.get('session_created') else None,
                last_updated=datetime.fromisoformat(
                    enhanced_data['last_updated']) if enhanced_data.get('last_updated') else None
            )
        else:
            # Create new enhanced state
            enhanced_state = EnhancedSessionState(
                user_id=user_id,
                session_id=session_id,
                session_created=datetime.now(),
                last_updated=datetime.now()
            )

        # Cache and return
        self.session_cache[cache_key] = enhanced_state
        return enhanced_state

    def update_session_state(self, user_id: str, session_id: str,
                             task_results: List[TaskResult]) -> bool:
        """
        Update session state with new task results (accumulative).

        Args:
            user_id: User ID
            session_id: Session ID
            task_results: New task results to accumulate

        Returns:
            True if successful, False otherwise
        """
        try:
            enhanced_state = self.get_enhanced_state(user_id, session_id)

            # Accumulate top_candidates from successful results
            new_candidates = []
            for result in task_results:
                if result.success and result.result.get('top_candidates'):
                    new_candidates.extend(result.result['top_candidates'])

            if new_candidates:
                # Summarize existing candidates to save memory/tokens
                summarized_existing = self._summarize_existing_candidates(
                    enhanced_state.top_candidates
                )

                # Add new candidates with full details
                all_candidates = summarized_existing + new_candidates

                # Deduplicate and limit size
                enhanced_state.top_candidates = self._deduplicate_candidates(
                    all_candidates
                )[:50]  # Keep max 50 candidates

            # Update search context from successful results
            for result in task_results:
                if result.success and result.result.get('search_context'):
                    enhanced_state.search_context.update(
                        result.result['search_context'])

            # Update location tracking
            if task_results and hasattr(task_results[0].task, 'parameters'):
                params = task_results[0].task.parameters
                if params.get('latitude') and params.get('longitude'):
                    enhanced_state.last_search_location = (
                        params['latitude'], params['longitude']
                    )

            # Update metadata
            enhanced_state.total_searches += 1
            enhanced_state.last_search_time = datetime.now()
            enhanced_state.last_updated = datetime.now()

            # Persist to base state manager
            return self._persist_enhanced_state(enhanced_state)

        except Exception as e:
            error(f"Failed to update session state: {e}")
            return False

    def _deduplicate_candidates(self, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate POIs based on ID or name+location.

        Args:
            candidates: List of candidate POIs

        Returns:
            Deduplicated list of candidates
        """
        seen_ids = set()
        seen_locations = set()
        unique_candidates = []

        for candidate in candidates:
            # Try ID-based deduplication first
            candidate_id = candidate.get('id')
            if candidate_id and candidate_id not in seen_ids:
                seen_ids.add(candidate_id)
                unique_candidates.append(candidate)
                continue

            # Fallback to name+location deduplication
            name = candidate.get('name', '').lower()
            lat = candidate.get('latitude')
            lon = candidate.get('longitude')

            if name and lat and lon:
                location_key = f"{name}:{lat:.6f}:{lon:.6f}"
                if location_key not in seen_locations:
                    seen_locations.add(location_key)
                    unique_candidates.append(candidate)

        return unique_candidates

    def _summarize_existing_candidates(self, existing_candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Summarize existing candidates to keep only essential fields for token efficiency.

        Keeps only: name, country, city, coordinates, tags/category, and id for reference.

        Args:
            existing_candidates: List of existing candidate dictionaries

        Returns:
            List of summarized candidates with essential fields only
        """
        summarized = []

        for candidate in existing_candidates:
            # Extract essential fields only
            essential_fields = {
                'id': candidate.get('id'),
                'name': candidate.get('name'),
                # Fallback to city if no country
                'country': candidate.get('country', candidate.get('city', '')),
                # Fallback to district if no city
                'city': candidate.get('city', candidate.get('district', '')),
                'latitude': candidate.get('latitude'),
                'longitude': candidate.get('longitude'),
                'category': candidate.get('category'),
                'subcategory': candidate.get('subcategory'),
                'tags': candidate.get('tags', [candidate.get('category', ''), candidate.get('subcategory', '')]),
                'distance_m': candidate.get('distance_m'),
                'is_summarized': True  # Mark as summarized for identification
            }

            # Only include candidates with essential data
            if essential_fields['name'] and essential_fields['latitude'] and essential_fields['longitude']:
                summarized.append(essential_fields)

        return summarized

    def _persist_enhanced_state(self, enhanced_state: EnhancedSessionState) -> bool:
        """
        Persist enhanced state to base state manager.

        Args:
            enhanced_state: Enhanced state to persist

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert to base SessionState format
            base_state: SessionState = {
                'user_id': enhanced_state.user_id,
                'session_id': enhanced_state.session_id,
                'current_state': 'active',
                'latitude': enhanced_state.last_search_location[0] if enhanced_state.last_search_location else None,
                'longitude': enhanced_state.last_search_location[1] if enhanced_state.last_search_location else None,
                'last_updated': enhanced_state.last_updated.isoformat() if enhanced_state.last_updated else None,
                'metadata': {
                    'enhanced_state': {
                        'top_candidates': enhanced_state.top_candidates,
                        'search_context': enhanced_state.search_context,
                        'active_filters': enhanced_state.active_filters,
                        'last_search_location': enhanced_state.last_search_location,
                        'last_search_time': enhanced_state.last_search_time.isoformat() if enhanced_state.last_search_time else None,
                        'total_searches': enhanced_state.total_searches,
                        'session_created': enhanced_state.session_created.isoformat() if enhanced_state.session_created else None,
                        'last_updated': enhanced_state.last_updated.isoformat() if enhanced_state.last_updated else None
                    }
                }
            }

            return self.base_state_manager.save_session(
                enhanced_state.user_id,
                enhanced_state.session_id,
                base_state
            )

        except Exception as e:
            error(f"Failed to persist enhanced state: {e}")
            return False

    def get_accumulated_candidates(self, user_id: str, session_id: str) -> List[Dict[str, Any]]:
        """
        Get all accumulated candidates for a session.

        Args:
            user_id: User ID
            session_id: Session ID

        Returns:
            List of accumulated candidates
        """
        enhanced_state = self.get_enhanced_state(user_id, session_id)
        return enhanced_state.top_candidates

    def clear_session_state(self, user_id: str, session_id: str) -> bool:
        """
        Clear session state (for testing or reset).

        Args:
            user_id: User ID
            session_id: Session ID

        Returns:
            True if successful, False otherwise
        """
        cache_key = f"{user_id}:{session_id}"
        if cache_key in self.session_cache:
            del self.session_cache[cache_key]

        return self.base_state_manager.delete_session(user_id, session_id)
