"""
Enhanced History Manager with Auto-Summarization

This module provides conversation history management with automatic summarization
when token limits are exceeded, maintaining conversation context efficiently.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, field
from src.infrastructure.log.unified_logger import debug, info, warning, error
from src.infrastructure.history.interface import IHistoryManager
from src.models import ConversationHistory, ConversationMessage
from ...processes.shared import TaskResult


@dataclass
class Interaction:
    """
    Represents a single user-assistant interaction.
    """
    user_request: str
    task_results: List[TaskResult]
    assistant_response: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EnhancedConversationHistory:
    """
    Enhanced conversation history with summarization capabilities.
    """
    user_id: str
    session_id: str
    interactions: List[Interaction] = field(default_factory=list)
    summary: Optional[str] = None
    summary_created_at: Optional[datetime] = None
    total_interactions: int = 0
    token_count: int = 0
    
    @property
    def has_summary(self) -> bool:
        """Check if conversation has a summary."""
        return self.summary is not None
        
    def add_interaction(self, interaction: Interaction):
        """Add a new interaction to history."""
        self.interactions.append(interaction)
        self.total_interactions += 1
        # Rough token estimation (4 chars per token)
        self.token_count += len(interaction.user_request + interaction.assistant_response) // 4


class HistoryManager:
    """
    Enhanced history manager with auto-summarization capabilities.
    
    Key features:
    - Tracks full conversation interactions
    - Auto-summarization when token limits exceeded
    - Maintains recent interactions + summary for context
    - Background summarization to avoid blocking responses
    """
    
    def __init__(self, base_history_manager: IHistoryManager, max_tokens: int = 8000):
        """
        Initialize with base history manager and token limit.
        
        Args:
            base_history_manager: The underlying history manager for persistence
            max_tokens: Maximum tokens before triggering summarization
        """
        self.base_history_manager = base_history_manager
        self.max_tokens = max_tokens
        self.session_cache: Dict[str, EnhancedConversationHistory] = {}
        
    def get_enhanced_history(self, user_id: str, session_id: str) -> EnhancedConversationHistory:
        """
        Get enhanced conversation history, creating if not exists.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            EnhancedConversationHistory object
        """
        cache_key = f"{user_id}:{session_id}"
        
        # Check cache first
        if cache_key in self.session_cache:
            return self.session_cache[cache_key]
            
        # Try to load from base history manager
        base_history = self.base_history_manager.get_conversation(user_id, session_id)
        
        if base_history and base_history.get('metadata', {}).get('enhanced_history'):
            # Load existing enhanced history
            enhanced_data = base_history['metadata']['enhanced_history']
            enhanced_history = EnhancedConversationHistory(
                user_id=user_id,
                session_id=session_id,
                summary=enhanced_data.get('summary'),
                summary_created_at=datetime.fromisoformat(enhanced_data['summary_created_at']) if enhanced_data.get('summary_created_at') else None,
                total_interactions=enhanced_data.get('total_interactions', 0),
                token_count=enhanced_data.get('token_count', 0)
            )
            
            # Load recent interactions (last 3 for context)
            recent_messages = base_history.get('messages', [])[-6:]  # Last 3 interactions (user + assistant)
            interactions = []
            for i in range(0, len(recent_messages), 2):
                if i + 1 < len(recent_messages):
                    user_msg = recent_messages[i]
                    assistant_msg = recent_messages[i + 1]
                    interaction = Interaction(
                        user_request=user_msg['content'],
                        task_results=[],  # Not stored in base history
                        assistant_response=assistant_msg['content'],
                        timestamp=datetime.fromisoformat(user_msg['timestamp']),
                        metadata=user_msg.get('metadata')
                    )
                    interactions.append(interaction)
            enhanced_history.interactions = interactions
        else:
            # Create new enhanced history
            enhanced_history = EnhancedConversationHistory(
                user_id=user_id,
                session_id=session_id
            )
            
        # Cache and return
        self.session_cache[cache_key] = enhanced_history
        return enhanced_history
        
    def add_interaction(self, user_id: str, session_id: str, user_request: str,
                       task_results: List[TaskResult], assistant_response: str) -> bool:
        """
        Add new interaction to conversation history.
        
        Args:
            user_id: User ID
            session_id: Session ID
            user_request: User's request text
            task_results: Results from task execution
            assistant_response: Generated assistant response
            
        Returns:
            True if successful, False otherwise
        """
        try:
            enhanced_history = self.get_enhanced_history(user_id, session_id)
            
            # Create new interaction
            interaction = Interaction(
                user_request=user_request,
                task_results=task_results,
                assistant_response=assistant_response,
                timestamp=datetime.now()
            )
            
            # Add to enhanced history
            enhanced_history.add_interaction(interaction)
            
            # Check if summarization needed
            if enhanced_history.token_count > self.max_tokens and not enhanced_history.has_summary:
                info(f"Token limit exceeded ({enhanced_history.token_count} > {self.max_tokens}), scheduling summarization")
                self._schedule_summarization(user_id, session_id)
            elif enhanced_history.token_count > self.max_tokens * 1.5 and enhanced_history.has_summary:
                info(f"Token limit exceeded again, updating summary")
                self._schedule_summarization(user_id, session_id)
                
            # Persist to base history manager
            return self._persist_enhanced_history(enhanced_history, user_request, assistant_response)
            
        except Exception as e:
            error(f"Failed to add interaction: {e}")
            return False
            
    def get_context_for_response(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get conversation context for response generation.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            Context dictionary with summary and recent interactions
        """
        enhanced_history = self.get_enhanced_history(user_id, session_id)
        
        context = {
            'total_interactions': enhanced_history.total_interactions,
            'token_count': enhanced_history.token_count
        }
        
        if enhanced_history.has_summary:
            context['summary'] = enhanced_history.summary
            context['recent_interactions'] = enhanced_history.interactions[-2:]  # Last 2 interactions
        else:
            context['full_history'] = enhanced_history.interactions
            
        return context
        
    def _schedule_summarization(self, user_id: str, session_id: str):
        """
        Schedule background summarization (placeholder for now).
        
        In a full implementation, this would trigger background summarization.
        For now, we'll do inline summarization.
        
        Args:
            user_id: User ID
            session_id: Session ID
        """
        try:
            enhanced_history = self.get_enhanced_history(user_id, session_id)
            
            # Simple summarization for now (in production, use LLM)
            interactions_to_summarize = enhanced_history.interactions[:-2]  # All but last 2
            
            if interactions_to_summarize:
                summary_parts = []
                for interaction in interactions_to_summarize:
                    summary_parts.append(f"User searched for: {interaction.user_request[:100]}...")
                    
                enhanced_history.summary = "Previous conversation summary:\n" + "\n".join(summary_parts)
                enhanced_history.summary_created_at = datetime.now()
                
                # Keep only recent interactions
                enhanced_history.interactions = enhanced_history.interactions[-2:]
                
                # Update token count (rough estimate)
                enhanced_history.token_count = len(enhanced_history.summary) // 4
                for interaction in enhanced_history.interactions:
                    enhanced_history.token_count += len(interaction.user_request + interaction.assistant_response) // 4
                    
                info(f"Created conversation summary, reduced token count to {enhanced_history.token_count}")
                
        except Exception as e:
            error(f"Failed to create summary: {e}")
            
    def _persist_enhanced_history(self, enhanced_history: EnhancedConversationHistory,
                                user_request: str, assistant_response: str) -> bool:
        """
        Persist enhanced history to base history manager.
        
        Args:
            enhanced_history: Enhanced history to persist
            user_request: Latest user request
            assistant_response: Latest assistant response
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Log the latest interaction to base history
            self.base_history_manager.log_user_message(
                enhanced_history.user_id, 
                enhanced_history.session_id, 
                user_request
            )
            self.base_history_manager.log_assistant_message(
                enhanced_history.user_id, 
                enhanced_history.session_id, 
                assistant_response
            )
            
            # Update conversation metadata with enhanced history data
            base_conversation = self.base_history_manager.get_conversation(
                enhanced_history.user_id, 
                enhanced_history.session_id
            )
            
            if base_conversation:
                if 'metadata' not in base_conversation:
                    base_conversation['metadata'] = {}
                    
                base_conversation['metadata']['enhanced_history'] = {
                    'summary': enhanced_history.summary,
                    'summary_created_at': enhanced_history.summary_created_at.isoformat() if enhanced_history.summary_created_at else None,
                    'total_interactions': enhanced_history.total_interactions,
                    'token_count': enhanced_history.token_count
                }
                
                return self.base_history_manager.save_conversation(
                    enhanced_history.user_id,
                    enhanced_history.session_id,
                    base_conversation
                )
                
            return True
            
        except Exception as e:
            error(f"Failed to persist enhanced history: {e}")
            return False
