"""
Background Summarizer for Conversation History

This module provides background summarization of conversation history
to maintain context while staying within token limits.
"""

import threading
import time
from queue import Queue, Empty
from typing import Dict, Any, List, Optional
from datetime import datetime
from src.infrastructure.log.unified_logger import debug, info, warning, error
from .history_manager import EnhancedConversationHistory, Interaction


class BackgroundSummarizer:
    """
    Background worker that processes conversation summarization.
    
    Key features:
    - Non-blocking summarization in background thread
    - Queue-based processing to handle multiple sessions
    - Simple rule-based summarization (can be enhanced with LLM)
    - Automatic cleanup of old interactions after summarization
    """
    
    def __init__(self, max_queue_size: int = 100):
        """
        Initialize the background summarizer.
        
        Args:
            max_queue_size: Maximum number of sessions in summarization queue
        """
        self.summarization_queue = Queue(maxsize=max_queue_size)
        self.is_running = False
        self.worker_thread: Optional[threading.Thread] = None
        
    def start(self):
        """Start the background summarization worker."""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(
                target=self._process_queue, 
                daemon=True,
                name="BackgroundSummarizer"
            )
            self.worker_thread.start()
            info("Background summarizer started")
            
    def stop(self):
        """Stop the background summarization worker."""
        if self.is_running:
            self.is_running = False
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5.0)
            info("Background summarizer stopped")
            
    def schedule_summarization(self, user_id: str, session_id: str, 
                             enhanced_history: EnhancedConversationHistory):
        """
        Schedule a session for background summarization.
        
        Args:
            user_id: User ID
            session_id: Session ID
            enhanced_history: Enhanced conversation history to summarize
        """
        try:
            summarization_request = {
                'user_id': user_id,
                'session_id': session_id,
                'enhanced_history': enhanced_history,
                'timestamp': datetime.now()
            }
            
            # Non-blocking put with timeout
            self.summarization_queue.put(summarization_request, timeout=1.0)
            debug(f"Scheduled summarization for session {session_id}")
            
        except Exception as e:
            warning(f"Failed to schedule summarization for {session_id}: {e}")
            
    def _process_queue(self):
        """Background worker that processes the summarization queue."""
        info("Background summarization worker started")
        
        while self.is_running:
            try:
                # Get next summarization request with timeout
                request = self.summarization_queue.get(timeout=1.0)
                
                # Process the summarization
                self._summarize_conversation(
                    request['user_id'],
                    request['session_id'], 
                    request['enhanced_history']
                )
                
                # Mark task as done
                self.summarization_queue.task_done()
                
            except Empty:
                # No requests in queue, continue
                continue
            except Exception as e:
                error(f"Error in background summarization: {e}")
                
        info("Background summarization worker stopped")
        
    def _summarize_conversation(self, user_id: str, session_id: str,
                              enhanced_history: EnhancedConversationHistory):
        """
        Create summary of conversation interactions.
        
        Args:
            user_id: User ID
            session_id: Session ID
            enhanced_history: Enhanced conversation history to summarize
        """
        try:
            debug(f"Creating summary for session {session_id}")
            
            # Get interactions to summarize (all but last 2)
            interactions_to_summarize = enhanced_history.interactions[:-2]
            
            if not interactions_to_summarize:
                debug(f"No interactions to summarize for session {session_id}")
                return
                
            # Create summary using rule-based approach
            summary = self._create_rule_based_summary(interactions_to_summarize)
            
            # Update enhanced history
            enhanced_history.summary = summary
            enhanced_history.summary_created_at = datetime.now()
            
            # Keep only recent interactions (last 2)
            enhanced_history.interactions = enhanced_history.interactions[-2:]
            
            # Update token count estimate
            enhanced_history.token_count = len(summary) // 4  # Rough estimate
            for interaction in enhanced_history.interactions:
                enhanced_history.token_count += len(
                    interaction.user_request + interaction.assistant_response
                ) // 4
                
            info(f"Created summary for session {session_id}, "
                 f"reduced token count to {enhanced_history.token_count}")
                 
        except Exception as e:
            error(f"Failed to summarize conversation for {session_id}: {e}")
            
    def _create_rule_based_summary(self, interactions: List[Interaction]) -> str:
        """
        Create a rule-based summary of interactions.
        
        This is a simple implementation. In production, you might want to use
        an LLM for more sophisticated summarization.
        
        Args:
            interactions: List of interactions to summarize
            
        Returns:
            Summary string
        """
        if not interactions:
            return ""
            
        summary_parts = []
        
        # Extract key patterns
        search_locations = set()
        search_categories = set()
        successful_searches = 0
        
        for interaction in interactions:
            # Extract locations mentioned
            request_lower = interaction.user_request.lower()
            
            # Common Turkish locations
            locations = [
                'beyoğlu', 'beşiktaş', 'kadıköy', 'üsküdar', 'fatih', 'şişli',
                'bakırköy', 'maltepe', 'pendik', 'kartal', 'ataşehir', 'büyükçekmece',
                'taksim', 'galata', 'karaköy', 'ortaköy', 'bebek', 'etiler'
            ]
            
            for location in locations:
                if location in request_lower:
                    search_locations.add(location.title())
                    
            # Extract categories
            categories = [
                'cafe', 'restaurant', 'market', 'shop', 'bank', 'pharmacy', 
                'hospital', 'hotel', 'gas station', 'atm', 'bakery', 'bar'
            ]
            
            for category in categories:
                if category in request_lower:
                    search_categories.add(category)
                    
            # Count successful searches (rough heuristic)
            if len(interaction.assistant_response) > 100:  # Assume longer responses = success
                successful_searches += 1
                
        # Build summary
        summary_parts.append(f"Conversation Summary ({len(interactions)} interactions):")
        
        if search_locations:
            summary_parts.append(f"• Searched locations: {', '.join(sorted(search_locations))}")
            
        if search_categories:
            summary_parts.append(f"• Searched categories: {', '.join(sorted(search_categories))}")
            
        summary_parts.append(f"• Successful searches: {successful_searches}/{len(interactions)}")
        
        # Add recent search patterns
        if len(interactions) >= 2:
            recent_requests = [i.user_request[:50] + "..." for i in interactions[-2:]]
            summary_parts.append(f"• Recent requests: {'; '.join(recent_requests)}")
            
        return "\n".join(summary_parts)
        
    def get_queue_size(self) -> int:
        """Get current queue size for monitoring."""
        return self.summarization_queue.qsize()
        
    def is_worker_running(self) -> bool:
        """Check if background worker is running."""
        return self.is_running and self.worker_thread and self.worker_thread.is_alive()


# Global instance for the application
_background_summarizer: Optional[BackgroundSummarizer] = None


def get_background_summarizer() -> BackgroundSummarizer:
    """
    Get the global background summarizer instance.
    
    Returns:
        BackgroundSummarizer instance
    """
    global _background_summarizer
    
    if _background_summarizer is None:
        _background_summarizer = BackgroundSummarizer()
        _background_summarizer.start()
        
    return _background_summarizer


def shutdown_background_summarizer():
    """Shutdown the global background summarizer."""
    global _background_summarizer
    
    if _background_summarizer is not None:
        _background_summarizer.stop()
        _background_summarizer = None
