# src/agents/llm/interface.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

# Import the standardized data models
from src.models import LLMResponse
from src.models.result import ResultType


class LLMInterface(ABC):
    """
    Abstract interface for LLM API calls.
    This interface allows for different LLM providers to be used interchangeably.
    """

    @abstractmethod
    def call_api(self, prompt: str, flow=None, **kwargs) -> LLMResponse:
        """
        Make a request to the LLM API.

        Args:
            prompt: The input prompt to send to the LLM
            flow: Optional flow context for tracking
            **kwargs: Additional parameters specific to the implementation

        Returns:
            LLMResponse: Structured response from the LLM
        """
        pass

    @abstractmethod
    def extract_content(self, response: Dict[str, Any]) -> Any:
        """
        Extract the relevant content from the API response.

        Args:
            response: The raw API response

        Returns:
            The extracted content
        """
        pass
