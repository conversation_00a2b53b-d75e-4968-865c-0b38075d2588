"""
Task Decomposer for Multi-Task Orchestrator.

This module analyzes user requests and breaks them into executable sub-tasks
for parallel or sequential execution.
"""

import re
from typing import List, Dict, Any, Optional
from uuid import uuid4
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ..processes.shared import Task


class TaskDecomposer:
    """
    Pure LLM-driven task manager. This class only creates tasks based on explicit LLM output.
    No internal knowledge of locations or categories is maintained.
    """

    def __init__(self):
        pass

    def decompose(self, llm_actions: list, user_request: str, latitude: float, longitude: float,
                  radius: int, num_candidates: int, user_id: str, session_id: str,
                  state_manager=None) -> List[Task]:
        """
        Create tasks strictly based on the LLM's chosen actions/tools.
        All parameters are required. This class does not perform any decision making or fallback.
        Args:
            llm_actions: List of actions/tools (dicts) as output by the LLM (each with 'action' and parameters)
            user_request: The user's request text
            latitude: User's latitude
            longitude: User's longitude
            radius: Search radius in meters
            num_candidates: Number of candidates to return
            user_id: User ID (required)
            session_id: Session ID (required)
        Returns:
            List of Task objects to execute, in the order provided by the LLM
        Raises:
            ValueError if llm_actions is not provided
        """
        if not llm_actions:
            raise ValueError(
                "llm_actions must be provided for strict LLM-driven task decomposition.")
        debug(f"Decomposing request strictly from LLM actions: {llm_actions}")

        # Detect if this is a sequential request
        is_sequential = self._is_sequential_request(user_request)
        debug(f"Sequential request detected: {is_sequential}")

        tasks = []
        is_multi_task = len(llm_actions) > 1

        for i, action in enumerate(llm_actions):
            tool_name = action.get('action')
            parameters = action.copy()
            parameters.pop('action', None)
            # Add user/session info if not present
            parameters.setdefault('user_id', user_id)
            parameters.setdefault('session_id', session_id)
            parameters.setdefault('prompt', user_request)

            # Ensure numeric parameters are properly typed (LLM may return strings)
            # Always override with correct types to prevent comparison errors
            # But skip conversion for coordinate variables that reference other tasks
            lat_param = parameters.get('latitude', latitude)
            lon_param = parameters.get('longitude', longitude)

            # Check if latitude/longitude are variable references (e.g., "AKM_Sanat_Cafe_latitude")
            # These will be resolved later by the execution planner
            if isinstance(lat_param, str) and ('_latitude' in lat_param or '_longitude' in lat_param):
                # Keep as string - will be resolved during execution
                parameters['latitude'] = lat_param
            else:
                parameters['latitude'] = self._convert_to_float(
                    lat_param, latitude)

            if isinstance(lon_param, str) and ('_latitude' in lon_param or '_longitude' in lon_param):
                # Keep as string - will be resolved during execution
                parameters['longitude'] = lon_param
            else:
                parameters['longitude'] = self._convert_to_float(
                    lon_param, longitude)
            parameters['radius'] = self._convert_to_int(
                parameters.get('radius', radius), radius)

            # Adjust num_candidates for POI lookup in multi-task scenarios
            if tool_name == 'poi_lookup' and is_multi_task:
                # POI lookup only returns 1 POI (the reference POI), so we set num_candidates to 1
                parameters['num_candidates'] = 1
                debug(
                    f"Multi-task POI lookup: set num_candidates to 1 for POI reference")
            else:
                parameters['num_candidates'] = self._convert_to_int(
                    parameters.get('num_candidates', num_candidates), num_candidates)

            # Add state manager for processes that need it (like poi_detail_retrieval)
            if tool_name == 'poi_detail_retrieval' and state_manager:
                parameters['state_manager'] = state_manager

            # Create dependencies for sequential tasks
            dependencies = []
            if is_sequential and i > 0:
                # Each task depends on the previous task
                dependencies = [tasks[i-1].task_id]
                debug(f"Task {i+1} depends on task {i}: {dependencies}")

            task = Task(
                task_id=str(uuid4()),
                tool_name=tool_name,
                parameters=parameters,
                dependencies=dependencies,
                sub_request=user_request
            )
            tasks.append(task)
        info(
            f"Decomposed into {len(tasks)} tasks from LLM actions: {[t.tool_name for t in tasks]}")
        return tasks

    def _is_sequential_request(self, user_request: str) -> bool:
        """
        Detect if the user request indicates sequential execution.

        Args:
            user_request: The user's request text

        Returns:
            True if the request indicates sequential execution
        """
        sequential_patterns = [
            r'\band then\b',
            r'\bthen\b',
            r'\bafter that\b',
            r'\bnext to it\b',
            r'\bclose to it\b',
            r'\bnear it\b',
            r'\bfirst.*then\b',
            r'\bfind.*and then\b',
            # POI-relative patterns
            r'\bclose to\s+\w+.*and\b',  # "close to [POI] and [something]"
            r'\bnear\s+\w+.*and\b',      # "near [POI] and [something]"
            r'\bnext to\s+\w+',          # "next to [POI]"
            r'\bclose to\s+\w+',         # "close to [POI]"
            r'\bnear\s+\w+',             # "near [POI]"
            r'\bin the same area\b',      # "in the same area"
            r'\bin that area\b',          # "in that area"
            r'\baround there\b',          # "around there"
            # "also find [something] in [same/that] area"
            r'\balso find.*in.*area\b',
        ]

        request_lower = user_request.lower()
        for pattern in sequential_patterns:
            if re.search(pattern, request_lower):
                debug(f"Sequential pattern detected: {pattern}")
                return True

        return False

    def _convert_to_float(self, value, fallback: float) -> float:
        """
        Convert a value to float, with fallback if conversion fails.

        Args:
            value: Value to convert (may be string, int, float, or None)
            fallback: Fallback value if conversion fails

        Returns:
            Float value
        """
        if value is None:
            return fallback
        try:
            return float(value)
        except (ValueError, TypeError):
            warning(
                f"Failed to convert {value} to float, using fallback {fallback}")
            return fallback

    def _convert_to_int(self, value, fallback: int) -> int:
        """
        Convert a value to int, with fallback if conversion fails.

        Args:
            value: Value to convert (may be string, int, float, or None)
            fallback: Fallback value if conversion fails

        Returns:
            Integer value
        """
        if value is None:
            return fallback
        try:
            return int(value)
        except (ValueError, TypeError):
            warning(
                f"Failed to convert {value} to int, using fallback {fallback}")
            return fallback
