"""
Interface for location resolver agent.

This module defines the interface that location resolver implementations
must follow for consistent behavior.
"""

from abc import ABC, abstractmethod
from typing import Optional
from ...processes.shared.models import LocationContext


class ILocationResolver(ABC):
    """
    Interface for location resolver implementations.
    
    This interface defines the contract that all location resolver implementations
    must follow for consistent location resolution behavior.
    """
    
    @abstractmethod
    def resolve_location(self, location_name: str) -> LocationContext:
        """
        Resolve a location name to coordinates and search method.
        
        Args:
            location_name: Name of the location to resolve
            
        Returns:
            LocationContext with resolved information
        """
        pass
    
    @abstractmethod
    def is_valid_location(self, location_name: str) -> bool:
        """
        Check if a location name can be resolved.
        
        Args:
            location_name: Name of the location to check
            
        Returns:
            True if the location can be resolved, False otherwise
        """
        pass
    
    @abstractmethod
    def get_location_suggestions(self, partial_name: str, limit: int = 5) -> list:
        """
        Get location suggestions for a partial name.
        
        Args:
            partial_name: Partial location name
            limit: Maximum number of suggestions to return
            
        Returns:
            List of location suggestions
        """
        pass
