"""
Agents Package

Contains all specialized agents for message processing.
"""

from typing import Dict, Any, Optional


def find_location(
    prompt: str,
    latitude: float,
    longitude: float,
    radius: int,
    num_candidates: int,
    history: Optional[str] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    session_title: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Main entry point for location finding functionality.

    This function routes location requests to the multi-task orchestrator
    for intelligent processing and coordination.

    Args:
        prompt: The user's prompt/query
        latitude: User's latitude
        longitude: User's longitude
        radius: Search radius in meters
        num_candidates: Number of top candidates to return
        history: Optional conversation history
        user_id: Optional user ID for state management
        session_id: Optional session ID for state management
        session_title: Optional title for the session
        **kwargs: Additional parameters

    Returns:
        Dictionary containing the response with status, data, and metadata
    """
    # Import here to avoid circular dependency
    from ..orchestrator.multi_task_orchestrator import MultiTaskOrchestrator

    orchestrator = MultiTaskOrchestrator()
    return orchestrator.process_request(
        user_request=prompt,
        latitude=latitude,
        longitude=longitude,
        search_radius=radius,
        num_candidates=num_candidates,
        user_id=user_id,
        session_id=session_id,
        history=history,
        session_title=session_title,
        **kwargs
    )


__all__ = ['find_location']
