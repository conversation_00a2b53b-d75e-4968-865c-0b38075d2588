# src/agents/location_finder/llm/location_advice.py

from src.utils import timing_decorator
from src.models import TopCandidates, LocationAdviceResponse, LLMResponse
from .prompts.system_prompt import get_location_system_prompt
from src.infrastructure.config.config import ConfigManager
from .interface import LLMInterface
from ...shared.llm_endpoints import create_agent_llm_adapter


class LocationAdviceRequest(LLMInterface):
    """
    Implementation of LLMInterface for location advice requests.
    """

    def __init__(self):
        self.config = ConfigManager()
        self.unified_cache = self.config.get_cache_manager()
        self.llm_cache = self.unified_cache.get_llm_cache()
        self.llm_caching_enabled = self.config.is_llm_caching_enabled()
        self.llm_adapter = create_agent_llm_adapter("advice")

    def extract_content(self, response):
        if self.llm_adapter:
            return self.llm_adapter.extract_content(response)
        return None

    def format_top_candidates(self, top_candidates: TopCandidates) -> str:
        if top_candidates is None or not top_candidates:
            return "No top candidates available."
        descriptions = []
        for poi in top_candidates:
            name = None
            for name_field in ["name", "name_en", "name_tr", "name_de", "name_uk", "name_ru", "name_ar"]:
                if poi.get(name_field):
                    name = poi.get(name_field)
                    break
            if not name:
                name = "Unnamed place"
            modes = []
            if poi.get("walk_route_distance_m") is not None:
                modes.append("walk")
            if poi.get("drive_route_distance_m") is not None:
                modes.append("drive")
            modes_str = ", ".join(modes) if modes else "unknown mode"
            drive_dist = poi.get("drive_route_distance_m")
            walk_dist = poi.get("walk_route_distance_m")
            poi_id = poi.get("id", "unknown")
            sentence = [
                f"**{name}** (ID: {poi_id}) is accessible by {modes_str}"]
            excluded_keys = {"name", "name_en", "name_tr", "name_de", "name_uk", "name_ru", "name_ar",
                             "drive_route_distance_m", "walk_route_distance_m"}
            extra_info = []
            for key, value in poi.items():
                if key in excluded_keys or value is None or value == "":
                    continue
                if isinstance(value, float) and (value != value):
                    continue
                readable_key = key.replace("_", " ").capitalize()
                extra_info.append(f"{readable_key}: {value}")
            if extra_info:
                sentence.append(". " + "; ".join(extra_info))
            dist_parts = []
            if drive_dist is not None:
                dist_parts.append(f"{round(float(drive_dist))}m drive")
            if walk_dist is not None:
                dist_parts.append(f"{round(float(walk_dist))}m walk")
            if dist_parts:
                sentence.append(f". Distance: {', '.join(dist_parts)}.")
            descriptions.append("".join(sentence))
        result = "\n\n".join(
            descriptions) if descriptions else "No top candidates available."
        return result

    @timing_decorator
    def call_api(self, prompt: str, **kwargs) -> LLMResponse:
        if self.llm_caching_enabled:
            result = self.llm_cache.cached_llm_call(
                self._make_api_request, prompt, **kwargs
            )
        else:
            result = self._make_api_request(prompt, **kwargs)
        return result

    def _make_api_request(self, prompt: str, **kwargs) -> LLMResponse:
        top_candidates = kwargs.get('top_candidates', [])
        history = kwargs.get('history', None)
        latitude = kwargs.get('latitude', None)
        longitude = kwargs.get('longitude', None)
        search_radius = kwargs.get('search_radius', None)
        system_prompt = get_location_system_prompt(
            top_candidate=self.format_top_candidates(top_candidates),
            history=history,
            latitude=latitude,
            longitude=longitude,
            search_radius=search_radius
        )
        request_params = {
            "system_prompt": system_prompt,
            "user_prompt": prompt
        }
        if self.llm_adapter:
            return self.llm_adapter.call_api(
                prompt=prompt,
                **request_params
            )
        else:
            raise RuntimeError("No LLM adapter available.")

# Factory function to get an instance of the LLM interface


def get_location_advice_interface() -> LLMInterface:
    return LocationAdviceRequest()

# Minimal API for compatibility


def get_location_advice(prompt, history, top_candidates: TopCandidates,
                        latitude, longitude, search_radius,
                        available_subcategories=None, requested_subcategories=None) -> LocationAdviceResponse:
    """
    Patched: Prevent hallucination for unsupported categories and empty results.
    If top_candidates is empty, return a clear 'no results found' message.
    """
    # Defensive: default to empty lists if not provided
    available_subcategories = available_subcategories or []
    requested_subcategories = requested_subcategories or []

    # If no candidates found, return a clear "no results" message
    if not top_candidates:
        if requested_subcategories:
            requested = ', '.join(sorted(requested_subcategories))
            response = (
                f"<p>I couldn't find any <strong>{requested}</strong> in your area within the search radius. "
                "You might want to try expanding your search area or looking for different types of places.</p>"
            )
        else:
            response = (
                "<p>I couldn't find any places matching your request in the current search area. "
                "You might want to try expanding your search area or being more specific about what you're looking for.</p>"
            )
        return LocationAdviceResponse({"response": response, "top_candidates": [], "status_code": 200})

    # If no candidates and a requested subcategory is not available, return a clear message
    if (not top_candidates and requested_subcategories and
            not any(sub in available_subcategories for sub in requested_subcategories)):
        supported = ', '.join(sorted(available_subcategories)
                              ) if available_subcategories else 'None'
        requested = ', '.join(sorted(requested_subcategories))
        response = (
            f"<p>Sorry, <strong>{requested}</strong> is not currently available in the application. "
            f"Only the following categories are supported right now: <strong>{supported}</strong>. "
            "If you want, you can search for one of the supported categories instead.</p>"
        )
        return LocationAdviceResponse({"response": response, "top_candidates": [], "status_code": 200})

    # Otherwise, use the normal LLM advice logic
    llm = get_location_advice_interface()
    llm_response = llm.call_api(prompt, history=history, top_candidates=top_candidates,
                                latitude=latitude, longitude=longitude, search_radius=search_radius)

    # Convert LLMResponse to LocationAdviceResponse
    return LocationAdviceResponse({
        "response": llm_response.get('response', 'No response generated'),
        "top_candidates": top_candidates,
        "status_code": 200
    })
