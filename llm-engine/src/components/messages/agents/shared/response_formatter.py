"""
Common response formatting utilities for agents.

This module provides utilities for formatting agent responses
consistently across different agent implementations.
"""

from typing import Dict, Any, Optional, Union
from src.infrastructure.log.unified_logger import debug, warning


def format_agent_response(
    success: bool,
    data: Any = None,
    error_message: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Format a standardized agent response.
    
    Args:
        success: Whether the operation was successful
        data: The response data (if successful)
        error_message: Error message (if unsuccessful)
        metadata: Optional metadata about the operation
        
    Returns:
        Standardized response dictionary
    """
    response = {
        "success": success,
        "data": data if success else None,
        "error": error_message if not success else None
    }
    
    if metadata:
        response["metadata"] = metadata
    
    return response


def extract_response_text(response: Union[Dict[str, Any], str]) -> str:
    """
    Extract response text from various response formats.
    
    Args:
        response: The response in various formats
        
    Returns:
        Extracted response text
    """
    if isinstance(response, dict):
        # Check for common response text fields
        for field in ["response", "text", "content", "message"]:
            if field in response:
                return str(response[field])
        
        # If no text field found, convert the whole dict to string
        warning("No text field found in response dict, converting to string")
        return str(response)
    
    elif isinstance(response, str):
        return response
    
    else:
        # Convert other types to string
        return str(response)


def validate_response_format(response: Any, expected_fields: list) -> bool:
    """
    Validate that a response has the expected format.
    
    Args:
        response: The response to validate
        expected_fields: List of expected field names
        
    Returns:
        True if the response has the expected format, False otherwise
    """
    if not isinstance(response, dict):
        return False
    
    for field in expected_fields:
        if field not in response:
            debug(f"Missing expected field '{field}' in response")
            return False
    
    return True


def normalize_llm_response(llm_response: Union[Dict[str, Any], str]) -> Dict[str, Any]:
    """
    Normalize LLM response to a standard format.
    
    Args:
        llm_response: The LLM response in various formats
        
    Returns:
        Normalized response dictionary
    """
    if isinstance(llm_response, dict):
        return llm_response
    
    elif isinstance(llm_response, str):
        return {"response": llm_response}
    
    else:
        return {"response": str(llm_response)}


def create_error_response(error_message: str, error_code: Optional[str] = None) -> Dict[str, Any]:
    """
    Create a standardized error response.
    
    Args:
        error_message: The error message
        error_code: Optional error code
        
    Returns:
        Standardized error response
    """
    response = {
        "success": False,
        "error": error_message,
        "data": None
    }
    
    if error_code:
        response["error_code"] = error_code
    
    return response
