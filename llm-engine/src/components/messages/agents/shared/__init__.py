"""
Shared utilities for agents.

This package contains common utilities and base classes used
across different agent implementations.
"""

from .base_agent import BaseAgent
from .response_formatter import (
    format_agent_response, extract_response_text, validate_response_format,
    normalize_llm_response, create_error_response
)

__all__ = [
    'BaseAgent',
    'format_agent_response',
    'extract_response_text',
    'validate_response_format',
    'normalize_llm_response',
    'create_error_response',
]
