"""
Base agent class for reusable agent tools.

This module provides a base class that defines common patterns
and utilities for agent implementations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from src.infrastructure.log.unified_logger import debug, info, warning, error


class BaseAgent(ABC):
    """
    Base class for all agent implementations.
    
    This class provides common functionality and patterns that
    all agents should follow for consistency.
    """
    
    def __init__(self, agent_name: str):
        """
        Initialize the base agent.
        
        Args:
            agent_name: Name of the agent for logging purposes
        """
        self.agent_name = agent_name
        self._initialized = False
    
    def initialize(self, **kwargs) -> None:
        """
        Initialize the agent with configuration.
        
        Args:
            **kwargs: Agent-specific configuration parameters
        """
        if self._initialized:
            debug(f"{self.agent_name} already initialized")
            return
        
        self._do_initialize(**kwargs)
        self._initialized = True
        info(f"{self.agent_name} initialized successfully")
    
    @abstractmethod
    def _do_initialize(self, **kwargs) -> None:
        """
        Perform agent-specific initialization.
        
        Args:
            **kwargs: Agent-specific configuration parameters
        """
        pass
    
    def is_initialized(self) -> bool:
        """
        Check if the agent is initialized.
        
        Returns:
            True if the agent is initialized, False otherwise
        """
        return self._initialized
    
    def log_request(self, operation: str, **kwargs) -> None:
        """
        Log an agent request for debugging.
        
        Args:
            operation: The operation being performed
            **kwargs: Request parameters
        """
        debug(f"{self.agent_name} {operation} request: {kwargs}")
    
    def log_response(self, operation: str, response: Any) -> None:
        """
        Log an agent response for debugging.
        
        Args:
            operation: The operation that was performed
            response: The response from the operation
        """
        debug(f"{self.agent_name} {operation} response: {response}")
    
    def log_error(self, operation: str, error_msg: str) -> None:
        """
        Log an agent error.
        
        Args:
            operation: The operation that failed
            error_msg: The error message
        """
        error(f"{self.agent_name} {operation} error: {error_msg}")
    
    def validate_required_params(self, params: Dict[str, Any], required: list) -> None:
        """
        Validate that required parameters are present.
        
        Args:
            params: Dictionary of parameters
            required: List of required parameter names
            
        Raises:
            ValueError: If any required parameter is missing
        """
        missing = [param for param in required if param not in params or params[param] is None]
        if missing:
            raise ValueError(f"{self.agent_name}: Missing required parameters: {missing}")
    
    @abstractmethod
    def get_agent_info(self) -> Dict[str, Any]:
        """
        Get information about this agent.
        
        Returns:
            Dictionary containing agent metadata
        """
        pass
