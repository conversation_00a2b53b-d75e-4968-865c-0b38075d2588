"""
Classification system prompt for location categorization.
Optimized for token efficiency and maximum LLM understanding with Turkish location normalization.
"""
from typing import Optional

# --- Static Prompt Sections ---
TASK = """# TASK\nExtract relevant subcategories and location name from user input. The system will automatically determine the best search method based on what's found in the database."""

EXTRACTION_RULES = """# EXTRACTION RULES\n- Match to existing subcategories only (if none match, return empty array)\n- Extract location name when mentioned (any place name: districts, neighborhoods, landmarks, etc.)\n- **EXTRACT DESCRIPTIVE TAGS**: Extract descriptive words/phrases that describe what the user wants\n- **NORMALIZE TURKISH LOCATIONS**: Convert romanized/anglicized Turkish place names to proper Turkish orthography\n- Don't classify location types - let the database determine what it is\n- Return best match even if uncertain"""

TAG_EXTRACTION = """# TAG EXTRACTION\nExtract descriptive tags from user requests that describe qualities, features, or characteristics:\n- \"good view\" → tag: \"good view\"\n- \"romantic atmosphere\" → tag: \"romantic\"\n- \"outdoor seating\" → tag: \"outdoor\"\n- \"live music\" → tag: \"live music\"\n- \"family friendly\" → tag: \"family friendly\"\n- \"cheap\" or \"budget\" → tag: \"budget\"\n- \"luxury\" or \"upscale\" → tag: \"luxury\"\n- \"quiet\" → tag: \"quiet\"\n- \"cozy\" → tag: \"cozy\"\n- Extract ANY descriptive words that help describe what the user is looking for"""

TURKISH_NORMALIZATION = """# TURKISH LOCATION NORMALIZATION\nWhen extracting Turkish location names, convert common romanized versions to proper Turkish spelling:\n- Apply Turkish character rules: ç, ğ, ı, ö, ş, ü\n- Common patterns to normalize:\n  - \"besiktas\" → \"Beşiktaş\"\n  - \"beyoglu\" → \"Beyoğlu\" \n  - \"uskudar\" → \"Üsküdar\"\n  - \"kadikoy\" → \"Kadıköy\"\n  - \"galatasaray\" → \"Galatasaray\"\n  - \"sultanahmet\" → \"Sultanahmet\"\n  - \"ortakoy\" → \"Ortaköy\"\n  - \"bebek\" → \"Bebek\"\n  - \"taksim\" → \"Taksim\"\n- Capitalize first letters of each word in location names\n- Preserve original spelling if already in proper Turkish orthography"""

OUTPUT_FORMAT = """# OUTPUT FORMAT\n```json\n{{\n  \"categories\": [],\n  \"subcategories\": [\"restaurant\", \"cafe\"],\n  \"location_name\": \"normalized_location_name_or_null\",\n  \"tags\": [\"tag1\", \"tag2\"]\n}}\n```"""

NOTE = """Note: Extract categories if you can identify them from the request, otherwise leave empty. Tags should be a simple array of descriptive terms."""

EXAMPLES = """# EXAMPLES\n- \"restaurants in beyoglu\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"restaurant\"], \"location_name\": \"Beyoğlu\", \"tags\": []}}\n- \"cafes near galata tower\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"cafe\"], \"location_name\": \"Galata Tower\", \"tags\": []}}\n- \"places with good view\" → {{\"categories\": [], \"subcategories\": [], \"location_name\": null, \"tags\": [\"good view\"]}}\n- \"romantic restaurants\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"restaurant\"], \"location_name\": null, \"tags\": [\"romantic\"]}}\n- \"quiet cafes with outdoor seating\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"cafe\"], \"location_name\": null, \"tags\": [\"quiet\", \"outdoor seating\"]}}\n- \"budget restaurants in besiktas\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"restaurant\"], \"location_name\": \"Beşiktaş\", \"tags\": [\"budget\"]}}\n- \"find restaurants\" → {{\"categories\": [\"Food & Drink\"], \"subcategories\": [\"restaurant\"], \"location_name\": null, \"tags\": []}}"""

RETURN_ONLY = "Return classification only. No conversation."


def get_classification_system_prompt(
    existing_subcategories: str,
    history: Optional[str] = None
) -> str:
    """
    Generate a minimal classification system prompt for location NER/classification.
    Only the current user prompt and available subcategories are needed. No conversation context/history.
    """
    return (
        f"Extract subcategories (from: {existing_subcategories}), location name, and tags from user input. "
        "Normalize Turkish locations. Output as JSON: "
        '{"categories": [], "subcategories": [...], "location_name": "...", "tags": [...]}\n'
        "Examples: "
        '\n- "cafes near galata tower" → {"subcategories": ["cafe"], "location_name": "Galata Tower", "tags": []}'
        '\n- "places with good view" → {"subcategories": [], "location_name": null, "tags": ["good view"]}'
        '\nReturn only the JSON. No conversation.'
    )
