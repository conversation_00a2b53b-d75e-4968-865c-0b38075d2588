# src/agents/location_finder/llm/request.py
from typing import Dict, Any
import json

from src.utils import timing_decorator
from .prompts.system_prompt import get_classification_system_prompt
from src.infrastructure.config.config import ConfigManager
from .interface import LLMInterface
from src.models import LLMResponse
from src.models.result import ResultType
from ...shared.llm_endpoints import create_agent_llm_adapter


class LlamaRequest(LLMInterface):
    """
    Implementation of LLMInterface for the Llama API using the centralized LLM endpoints system.
    """

    def __init__(self):
        self.config = ConfigManager()
        self.unified_cache = self.config.get_cache_manager()
        self.llm_cache = self.unified_cache.get_llm_cache()
        self.llm_caching_enabled = self.config.is_llm_caching_enabled()
        self.llm_adapter = create_agent_llm_adapter("request")

    def extract_content(self, response: Dict[str, Any]) -> Any:
        if self.llm_adapter:
            return self.llm_adapter.extract_content(response)
        return None

    @timing_decorator
    def call_api(self, prompt: str, **kwargs) -> LLMResponse:
        if self.llm_caching_enabled:
            result = self.llm_cache.cached_llm_call(
                self._make_api_request, prompt, **kwargs
            )
        else:
            result = self._make_api_request(prompt, **kwargs)
        return result

    def _make_api_request(self, prompt: str, **kwargs) -> LLMResponse:
        subcategories = kwargs.get('subcategories', [])
        history = kwargs.get('history', None)

        # Convert subcategories list to string format expected by prompt
        if isinstance(subcategories, list):
            subcategories_str = ", ".join(subcategories)
        else:
            subcategories_str = str(subcategories)

        system_prompt = get_classification_system_prompt(
            subcategories_str, history)
        request_params = {
            "system_prompt": system_prompt,
            "user_prompt": f"Classify this request: '{prompt}'"
        }
        if self.llm_adapter:
            return self.llm_adapter.call_api(
                prompt=prompt,
                **request_params
            )
        else:
            raise RuntimeError("No LLM adapter available.")

# Factory function to get an instance of the LLM interface


def get_llm_interface() -> LLMInterface:
    return LlamaRequest()

# Minimal API for compatibility


def llm_api(prompt: str, subcategories, history=None) -> LLMResponse:
    llm = get_llm_interface()
    return llm.call_api(prompt, subcategories=subcategories, history=history)
