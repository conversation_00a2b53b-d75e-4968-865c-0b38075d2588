"""
Shared utilities for the messages component.

This package contains shared infrastructure and utilities used across
the messages component, including LLM endpoints, data access, and common utilities.
"""

# Re-export key components for convenience
from .llm_endpoints import get_llm_provider, LLMProviderInterface
from .data_access import data_service, create_data_provider
from .advice import AdviceGenerator, ResponseTemplate, AdviceContext

__all__ = [
    'get_llm_provider',
    'LLMProviderInterface',
    'data_service',
    'create_data_provider',
    'AdviceGenerator',
    'ResponseTemplate',
    'AdviceContext',
]
