# src/infrastructure/data_access/providers/csv/csv_provider.py

"""
CSV implementation of the data provider interface.

This module provides a CSV-based implementation of the data provider interface,
with support for memory-mapped files, caching, and asynchronous loading.
"""

import os
import pandas as pd
import numpy as np
import threading
import time
from typing import Any, Dict, List, Optional, Tuple, Union, cast
import re
import math

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical
from src.infrastructure.data_access.core.interfaces import (
    IDataProvider, IDataQuery, IDataResult,
    QueryFilter, GeospatialFilter
)
from src.infrastructure.data_access.core.query import DataQuery
from src.infrastructure.data_access.core.result import DataResult
from src.infrastructure.cache.factory import CacheFactory
from src.infrastructure.data_access.providers.csv.partitioning import CSVPartitioner


class CSVDataProvider(IDataProvider):
    """
    CSV implementation of the data provider interface.

    Features:
    - Memory-mapped file loading for efficient memory usage
    - Query result caching
    - Asynchronous background loading
    - Geographic partitioning support
    """

    def __init__(self,
                 file_path: str,
                 cache_enabled: bool = True,
                 cache_ttl_seconds: int = 300,
                 cache_max_size: int = 200,
                 use_memory_map: bool = True,
                 auto_initialize: bool = True,
                 use_partitioning: bool = False,
                 partition_dir: Optional[str] = None,
                 grid_size: int = 4,
                 min_partition_size: int = 500):
        """
        Initialize the CSV data provider.

        Args:
            file_path: Path to the CSV file
            cache_enabled: Whether to enable query result caching
            cache_ttl_seconds: Time-to-live for cached results in seconds
            cache_max_size: Maximum number of cached results
            use_memory_map: Whether to use memory-mapped file loading
            auto_initialize: Whether to initialize automatically
            use_partitioning: Whether to use dataset partitioning
            partition_dir: Directory for partitioned data (defaults to file_path + "_partitions")
            grid_size: Number of grid cells per dimension for partitioning (smaller = fewer partitions)
            min_partition_size: Minimum number of records per partition
        """
        self.file_path = file_path
        self.use_memory_map = use_memory_map
        # Using direct logging functions instead of logger instance

        # Partitioning configuration
        self.use_partitioning = use_partitioning
        self.partition_dir = partition_dir or f"{os.path.splitext(file_path)[0]}_partitions"

        # Create partitioner with the configured settings
        self.partitioner = CSVPartitioner(
            file_path=file_path,
            partition_dir=partition_dir,
            grid_size=grid_size,
            min_partition_size=min_partition_size,

        )

        # DataFrame and initialization state
        self.df: Optional[pd.DataFrame] = None
        self._initialized = False
        self._initializing = False
        self._initialization_error = None
        self._initialization_lock = threading.RLock()
        self._loading_thread: Optional[threading.Thread] = None

        # Cache configuration
        self.cache_enabled = cache_enabled
        self.cache_ttl_seconds = cache_ttl_seconds
        self.cache_max_size = cache_max_size

        # Use specialized data cache
        self.data_cache = CacheFactory.create_data_cache()

        # Schema information
        self._schema: Dict[str, str] = {}

        # Start initialization if requested
        if auto_initialize:
            self._start_initialization()

    def _start_initialization(self) -> None:
        """Start asynchronous initialization in a background thread."""
        with self._initialization_lock:
            if not self._initialized and not self._initializing:
                self._initializing = True
                self._loading_thread = threading.Thread(
                    target=self._initialize_thread)
                self._loading_thread.daemon = True
                self._loading_thread.start()
                info(
                    f"Started background initialization of dataset: {self.file_path}")

    def _initialize_thread(self) -> None:
        """Background thread function for initialization."""
        try:
            success = self._load_data()
            with self._initialization_lock:
                self._initialized = success
                self._initializing = False
                if success:
                    info(
                        f"Successfully initialized dataset: {self.file_path}")
                else:
                    error(
                        f"Failed to initialize dataset: {self.file_path}")
        except Exception as e:
            with self._initialization_lock:
                self._initialization_error = str(e)
                self._initializing = False
                self._initialized = False
            error(
                f"Error initializing dataset {self.file_path}: {e}")

    def _wait_for_initialization(self, timeout_seconds: float = 10.0) -> bool:
        """
        Wait for initialization to complete.

        Args:
            timeout_seconds: Maximum time to wait in seconds

        Returns:
            True if initialization completed successfully, False otherwise
        """
        info(
            f"Waiting up to {timeout_seconds} seconds for dataset initialization...")
        start_time = time.time()
        last_log_time = start_time

        # First, check if we're already initialized
        with self._initialization_lock:
            if self._initialized:
                info("Dataset already initialized")
                return True

            # If we're using partitioning and have metadata, we can consider it initialized
            if self.use_partitioning and self.partitioner.get_partitions():
                self._initialized = True
                info(
                    "Dataset considered initialized due to available partitions")
                return True

        while time.time() - start_time < timeout_seconds:
            with self._initialization_lock:
                if self._initialized:
                    info(
                        "Dataset initialization completed successfully")
                    return True
                if not self._initializing:
                    warning(
                        "Dataset initialization stopped without completing")
                    return False

            # Log progress every 5 seconds
            current_time = time.time()
            if current_time - last_log_time > 5.0:
                elapsed = current_time - start_time
                info(
                    f"Still waiting for dataset initialization... ({elapsed:.1f}s elapsed)")
                last_log_time = current_time

            time.sleep(0.1)

        # If we timed out but have partition metadata, consider it initialized
        if self.use_partitioning and self.partitioner.get_partitions():
            with self._initialization_lock:
                self._initialized = True
                info(
                    "Dataset considered initialized due to available partitions despite timeout")
                return True

        warning(
            f"Timed out after {timeout_seconds} seconds waiting for dataset initialization")
        return False

    def initialize(self) -> bool:
        """
        Initialize the data provider.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        with self._initialization_lock:
            if self._initialized:
                return True

            # If we're using partitioning and have metadata, we can consider it initialized
            if self.use_partitioning and self.partitioner.get_partitions():
                self._initialized = True
                info(
                    "Dataset considered initialized due to available partitions")
                return True

            if self._initializing:
                # Use a shorter timeout (5 seconds) for initialization
                # This is enough time for most datasets to load
                return self._wait_for_initialization(timeout_seconds=5.0)

        # If not initializing, load the data directly
        info("Starting direct initialization of dataset")
        return self._load_data()

    def is_initialized(self) -> bool:
        """
        Check if the provider is initialized and ready to use.

        Returns:
            bool: True if the provider is initialized, False otherwise
        """
        with self._initialization_lock:
            return self._initialized

    def _load_data(self) -> bool:
        """
        Load the CSV data into a pandas DataFrame.

        Returns:
            bool: True if loading was successful, False otherwise
        """
        try:
            if not os.path.exists(self.file_path):
                error(f"Dataset file not found: {self.file_path}")
                return False

            # Check if we can use partitioning without loading the full dataset
            if self.use_partitioning:
                # Try to load partition metadata first
                if self.partitioner.load_partition_metadata():
                    info(
                        "Loaded partition metadata, deferring full dataset load")

                    # Load just a small sample to extract schema
                    sample_df = pd.read_csv(
                        self.file_path,
                        memory_map=self.use_memory_map,
                        nrows=100  # Only load 100 rows for schema extraction
                    )

                    # Set the main dataframe to None - we'll load partitions on demand
                    self.df = None

                    # Extract schema from the sample
                    self._extract_schema(sample_df)

                    info("Initialized with lazy loading enabled")
                    return True
                else:
                    info(
                        "No partition metadata found, loading full dataset")

            # If we reach here, we need to load the full dataset
            info("Loading full dataset...")

            # Load the CSV file with memory mapping if enabled
            self.df = pd.read_csv(
                self.file_path,
                memory_map=self.use_memory_map
            )

            # Extract schema information
            self._extract_schema(self.df)

            # Create partitions if needed
            if self.use_partitioning and not self.partitioner.get_partitions() and self.df is not None:
                info("Creating partitions from full dataset")
                self.partitioner.create_partitions(self.df)

            if self.df is not None:
                info(
                    f"Loaded dataset with {len(self.df)} rows and {len(self.df.columns)} columns")
            return True
        except Exception as e:
            error(
                f"Error loading dataset from {self.file_path}: {e}")
            self.df = pd.DataFrame()
            return False

    def _extract_schema(self, df: Optional[pd.DataFrame] = None) -> None:
        """
        Extract schema information from the DataFrame.

        Args:
            df: DataFrame to extract schema from (uses self.df if None)
        """
        # Use provided DataFrame or fall back to self.df
        source_df = df if df is not None else self.df

        if source_df is None:
            return

        self._schema = {}
        for column in source_df.columns:
            dtype = source_df[column].dtype
            if pd.api.types.is_integer_dtype(dtype):
                self._schema[column] = "integer"
            elif pd.api.types.is_float_dtype(dtype):
                self._schema[column] = "float"
            elif pd.api.types.is_bool_dtype(dtype):
                self._schema[column] = "boolean"
            elif pd.api.types.is_datetime64_dtype(dtype):
                self._schema[column] = "datetime"
            else:
                self._schema[column] = "string"

    def _load_partition(self, partition_name: str) -> Optional[pd.DataFrame]:
        """
        Load a partition from disk or cache.

        Args:
            partition_name: Name of the partition file

        Returns:
            DataFrame containing the partition data or None if loading fails
        """
        # Check if the partition is in cache
        hit, cached_df = self.data_cache.get_partition(partition_name)
        if hit:
            debug(f"Partition cache hit: {partition_name}")
            return cached_df

        # Cache miss, load from disk
        debug(f"Partition cache miss: {partition_name}")

        partition_path = os.path.join(self.partition_dir, partition_name)
        if not os.path.exists(partition_path):
            info(f"Partition file not found: {partition_path}")
            return None

        try:
            # Load the partition with memory mapping if enabled
            df = pd.read_csv(
                partition_path,
                memory_map=self.use_memory_map
            )

            # Cache the partition
            self.data_cache.cache_partition(partition_name, df)

            return df
        except Exception as e:
            error(f"Error loading partition {partition_name}: {e}")
            return None

    def _generate_cache_key(self, query: IDataQuery) -> str:
        """
        Generate a cache key for a query.

        Args:
            query: Query to generate a key for

        Returns:
            Cache key string
        """
        # Convert filters to a string representation
        filter_strs = []
        for f in query.filters:
            filter_strs.append(f"{f.field}:{f.operator}:{str(f.value)}")

        # Convert geo filter to a string representation
        geo_str = ""
        if query.geo_filter:
            geo = query.geo_filter
            geo_str = f"geo:{geo.center_lat},{geo.center_lon},{geo.radius_m},{geo.lat_field},{geo.lon_field}"

        # Convert options to a string representation
        opt = query.options
        opt_str = f"opt:{opt.limit},{opt.offset},{opt.sort_by},{opt.sort_order}"

        # Combine all parts
        key_parts = [
            ",".join(filter_strs),
            geo_str,
            opt_str
        ]

        return "|".join(key_parts)

    def _apply_filter(self, df: pd.DataFrame, filter_obj: QueryFilter) -> pd.DataFrame:
        """
        Apply a filter to a DataFrame.

        Args:
            df: DataFrame to filter
            filter_obj: Filter to apply

        Returns:
            Filtered DataFrame
        """
        if filter_obj.field not in df.columns:
            warning(
                f"Filter field not found in dataset: {filter_obj.field}")
            return df

        if filter_obj.operator == 'eq':
            return df[df[filter_obj.field] == filter_obj.value]
        elif filter_obj.operator == 'neq':
            return df[df[filter_obj.field] != filter_obj.value]
        elif filter_obj.operator == 'gt':
            return df[df[filter_obj.field] > filter_obj.value]
        elif filter_obj.operator == 'gte':
            return df[df[filter_obj.field] >= filter_obj.value]
        elif filter_obj.operator == 'lt':
            return df[df[filter_obj.field] < filter_obj.value]
        elif filter_obj.operator == 'lte':
            return df[df[filter_obj.field] <= filter_obj.value]
        elif filter_obj.operator == 'in':
            return df[df[filter_obj.field].isin(filter_obj.value)]
        elif filter_obj.operator == 'contains':
            if isinstance(filter_obj.value, str):
                return df[df[filter_obj.field].str.contains(
                    filter_obj.value, case=False, na=False)]
            else:
                warning(
                    f"Contains operator requires string value, got {type(filter_obj.value)}")
                return df
        else:
            warning(f"Unknown operator: {filter_obj.operator}")
            return df

    def _apply_geo_filter(self, df: pd.DataFrame, geo_filter: GeospatialFilter) -> pd.DataFrame:
        """
        Apply a geospatial filter to a DataFrame.

        Args:
            df: DataFrame to filter
            geo_filter: Geospatial filter to apply

        Returns:
            Filtered DataFrame
        """
        if geo_filter.lat_field not in df.columns or geo_filter.lon_field not in df.columns:
            warning(
                f"Geospatial filter fields not found in dataset: {geo_filter.lat_field}, {geo_filter.lon_field}")
            return df

        # Convert radius to degrees (approximate)
        earth_radius_m = 6371000
        lat_radius_deg = (geo_filter.radius_m /
                          earth_radius_m) * (180 / math.pi)
        lon_radius_deg = lat_radius_deg / \
            math.cos(math.radians(geo_filter.center_lat))

        # Calculate bounding box
        min_lat = geo_filter.center_lat - lat_radius_deg
        max_lat = geo_filter.center_lat + lat_radius_deg
        min_lon = geo_filter.center_lon - lon_radius_deg
        max_lon = geo_filter.center_lon + lon_radius_deg

        # Filter by bounding box
        filtered_df = df[
            (df[geo_filter.lat_field] >= min_lat) &
            (df[geo_filter.lat_field] <= max_lat) &
            (df[geo_filter.lon_field] >= min_lon) &
            (df[geo_filter.lon_field] <= max_lon)
        ]

        # Calculate haversine distance for each point
        lat1 = np.radians(geo_filter.center_lat)
        lon1 = np.radians(geo_filter.center_lon)
        lat2 = np.radians(filtered_df[geo_filter.lat_field])
        lon2 = np.radians(filtered_df[geo_filter.lon_field])

        dlon = lon2 - lon1
        dlat = lat2 - lat1

        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
        distance = earth_radius_m * c

        # Filter by radius
        return filtered_df[distance <= geo_filter.radius_m]

    def _apply_sorting(self, df: pd.DataFrame, sort_by: str, sort_order: str) -> pd.DataFrame:
        """
        Apply sorting to a DataFrame.

        Args:
            df: DataFrame to sort
            sort_by: Field to sort by
            sort_order: Sort order ('asc' or 'desc')

        Returns:
            Sorted DataFrame
        """
        if sort_by not in df.columns:
            warning(f"Sort field not found in dataset: {sort_by}")
            return df

        ascending = sort_order.lower() == 'asc'
        return df.sort_values(by=sort_by, ascending=ascending)

    def _apply_pagination(self, df: pd.DataFrame, limit: Optional[int], offset: Optional[int]) -> pd.DataFrame:
        """
        Apply pagination to a DataFrame.

        Args:
            df: DataFrame to paginate
            limit: Maximum number of items to return
            offset: Number of items to skip

        Returns:
            Paginated DataFrame
        """
        if offset is not None and offset > 0:
            df = df.iloc[offset:]

        if limit is not None and limit > 0:
            df = df.iloc[:limit]

        return df

    def query(self, query: IDataQuery) -> IDataResult:
        """
        Query the dataset with the given parameters.

        Args:
            query: Query parameters

        Returns:
            Query results
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for query")
                return DataResult([], 0)

        # Check if the result is in cache
        cache_key = self._generate_cache_key(query)
        hit, cached_result = self.data_cache.get_query_result(cache_key)
        if hit:
            debug(f"Cache hit for query: {cache_key}")
            return cached_result

        # Cache miss, execute the query
        debug(f"Cache miss for query: {cache_key}")

        # Check if we can use partitioned data
        matching_partitions = []
        use_partitions = False

        # Try to use partitions for geo queries or if we're in lazy loading mode
        if self.use_partitioning:
            if query.geo_filter:
                matching_partitions = self.partitioner.find_matching_partitions(
                    center_lat=query.geo_filter.center_lat,
                    center_lon=query.geo_filter.center_lon,
                    radius_m=query.geo_filter.radius_m
                )
                use_partitions = len(matching_partitions) > 0
            elif self.df is None:
                # We're in lazy loading mode, need to use all partitions
                info(
                    "Using all partitions for query (lazy loading mode)")
                matching_partitions = self.partitioner.find_matching_partitions()
                use_partitions = len(matching_partitions) > 0

        if use_partitions:
            info(
                f"Using {len(matching_partitions)} partitions for query")

            # Load and combine matching partitions
            partition_dfs = []
            for partition_name in matching_partitions:
                df = self._load_partition(partition_name)
                if df is not None:
                    partition_dfs.append(df)

            if partition_dfs:
                # Combine partitions
                result_df = pd.concat(partition_dfs, ignore_index=True)
            else:
                # Fallback to full dataset if no partitions could be loaded
                if self.df is None:
                    info("Lazy loading full dataset as fallback")
                    try:
                        self.df = pd.read_csv(
                            self.file_path,
                            memory_map=self.use_memory_map
                        )
                    except Exception as e:
                        error(f"Error loading full dataset: {e}")

                if self.df is None:
                    error(
                        "DataFrame is None, cannot execute query")
                    return DataResult([], 0)
                result_df = self.df.copy()
        else:
            # Use the full dataset
            if self.df is None:
                info("Lazy loading full dataset for query")
                try:
                    self.df = pd.read_csv(
                        self.file_path,
                        memory_map=self.use_memory_map
                    )
                except Exception as e:
                    error(f"Error loading full dataset: {e}")

            if self.df is None:
                error("DataFrame is None, cannot execute query")
                return DataResult([], 0)
            result_df = self.df.copy()

        # Apply filters
        for filter_obj in query.filters:
            result_df = self._apply_filter(result_df, filter_obj)

        # Apply geospatial filter if present
        if query.geo_filter:
            result_df = self._apply_geo_filter(result_df, query.geo_filter)

        # Get total count before pagination
        total_count = len(result_df)

        # Apply sorting if requested
        if query.options.sort_by:
            result_df = self._apply_sorting(
                result_df, query.options.sort_by, query.options.sort_order or 'asc')

        # Calculate page number
        page = 1
        if query.options.offset and query.options.limit and query.options.limit > 0:
            page = (query.options.offset // query.options.limit) + 1

        # Apply pagination
        result_df = self._apply_pagination(
            result_df, query.options.limit, query.options.offset)

        # Convert to list of dictionaries
        items = result_df.to_dict(orient='records')

        # Create result
        result = DataResult(
            items=items,
            total=total_count,
            page=page,
            page_size=query.options.limit or len(items)
        )

        # Cache the result
        self.data_cache.cache_query_result(cache_key, result)

        return result

    def get_by_id(self, id_field: str, id_value: Any) -> Optional[Dict[str, Any]]:
        """
        Get a single item by its ID.

        Args:
            id_field: Name of the ID field
            id_value: Value of the ID

        Returns:
            Item data or None if not found
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for get_by_id")
                return None

        if self.df is None:
            error("DataFrame is None, cannot execute get_by_id")
            return None

        if id_field not in self.df.columns:
            error(f"ID field not found in dataset: {id_field}")
            return None

        # Find the row with the matching ID
        matching_rows = self.df[self.df[id_field] == id_value]

        if len(matching_rows) == 0:
            return None

        # Return the first matching row as a dictionary
        return matching_rows.iloc[0].to_dict()

    def get_dataframe(self) -> pd.DataFrame:
        """
        Get the entire dataset as a pandas DataFrame.

        Returns:
            DataFrame containing the dataset
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error(
                    "Failed to initialize dataset for get_dataframe")
                return pd.DataFrame()

        if self.df is None:
            error(
                "DataFrame is None, cannot execute get_dataframe")
            return pd.DataFrame()

        return self.df.copy()

    def get_schema(self) -> Dict[str, str]:
        """
        Get the schema of the dataset.

        Returns:
            Dictionary mapping field names to their types
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error(
                    "Failed to initialize dataset for get_schema")
                return {}

        return self._schema.copy()

    def get_unique_values(self, field: str) -> List[Any]:
        """
        Get all unique values for a field.

        Args:
            field: Field name

        Returns:
            List of unique values
        """
        # Check if the result is in cache
        hit, cached_values = self.data_cache.get_unique_values(field)
        if hit:
            return cached_values

        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error(
                    "Failed to initialize dataset for get_unique_values")
                return []

        if self.df is None:
            error(
                "DataFrame is None, cannot execute get_unique_values")
            return []

        if field not in self.df.columns:
            error(f"Field not found in dataset: {field}")
            return []

        # Get unique values
        unique_values = self.df[field].dropna().unique().tolist()

        # Cache the result
        self.data_cache.cache_unique_values(field, unique_values)

        return unique_values

    def count(self, query: Optional[IDataQuery] = None) -> int:
        """
        Count items matching the query.

        Args:
            query: Optional query parameters

        Returns:
            Count of matching items
        """
        # If no query, return total count
        if query is None:
            # Ensure the dataset is initialized
            if not self.is_initialized():
                success = self.initialize()
                if not success:
                    error("Failed to initialize dataset for count")
                    return 0

            if self.df is None:
                error("DataFrame is None, cannot execute count")
                return 0

            return len(self.df)

        # Check if the result is in cache
        cache_key = f"count:{self._generate_cache_key(query)}"
        hit, cached_count = self.data_cache.get_count(cache_key)
        if hit:
            return cached_count

        # Execute the query and get the total count
        result = self.query(query)
        count = result.total

        # Cache the result
        self.data_cache.cache_count(cache_key, count)

        return count

    def refresh(self) -> bool:
        """
        Refresh the dataset from its source.

        Returns:
            True if refresh was successful, False otherwise
        """
        with self._initialization_lock:
            self._initialized = False
            self._initializing = False
            self._initialization_error = None

        # Clear caches
        self.data_cache.clear()

        # Reload the data
        return self.initialize()
