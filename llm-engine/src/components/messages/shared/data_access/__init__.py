# src/infrastructure/data_access/__init__.py

"""
Data access infrastructure package.

This package provides a flexible infrastructure for accessing data from
different storage backends.
"""

from .core.interfaces import (
    IDataProvider, IDataQuery, IDataResult,
    QueryFilter, GeospatialFilter, QueryOptions
)
from .core.query import DataQuery
from .core.result import DataResult
from .core.factory import (
    create_data_provider, switch_data_provider
)
from .config import DataAccessConfig

# Microservice entry point
from .data_service import data_service

__all__ = [
    'IDataProvider', 'IDataQuery', 'IDataResult',
    'QueryFilter', 'GeospatialFilter', 'QueryOptions',
    'DataQuery', 'DataResult',
    'create_data_provider', 'switch_data_provider',
    'DataAccessConfig',
    # Microservice
    'data_service'
]
