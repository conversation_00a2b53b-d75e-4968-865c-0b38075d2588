# src/infrastructure/data_access/core/query.py

"""
Query building and execution abstractions.

This module provides implementations for building and executing queries
against data providers.
"""

from typing import Any, List, Optional
from src.infrastructure.data_access.core.interfaces import (
    QueryFilter, GeospatialFilter, QueryOptions, IDataQuery
)


class DataQuery:
    """Implementation of IDataQuery."""

    def __init__(self):
        self.filters: List[QueryFilter] = []
        self.geo_filter: Optional[GeospatialFilter] = None
        self.options: QueryOptions = QueryOptions()

    def add_filter(self, field: str, operator: str, value: Any) -> 'DataQuery':
        """Add a filter to the query."""
        self.filters.append(QueryFilter(field, operator, value))
        return self

    def set_geospatial_filter(self, lat: float, lon: float, radius_m: int,
                              lat_field: str = 'latitude', lon_field: str = 'longitude') -> 'DataQuery':
        """Set a geospatial filter."""
        self.geo_filter = GeospatialFilter(
            lat, lon, radius_m, lat_field, lon_field)
        return self

    def set_options(self, limit: Optional[int] = None, offset: Optional[int] = None,
                    sort_by: Optional[str] = None, sort_order: Optional[str] = 'asc') -> 'DataQuery':
        """Set query options."""
        self.options = QueryOptions(limit, offset, sort_by, sort_order)
        return self
