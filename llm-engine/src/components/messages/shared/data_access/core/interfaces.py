# src/infrastructure/data_access/core/interfaces.py

"""
Core interfaces for data access providers and related components.

This module defines the core interfaces for the data access infrastructure,
allowing for different implementations of data storage and access.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, TypeVar, Generic, Union
from dataclasses import dataclass
import pandas as pd


# Type variable for generic result types
T = TypeVar('T')


@dataclass
class QueryFilter:
    """Filter parameters for data queries."""
    field: str
    operator: str  # 'eq', 'gt', 'lt', 'in', 'contains', etc.
    value: Any


@dataclass
class GeospatialFilter:
    """Geospatial filter for location-based queries."""
    center_lat: float
    center_lon: float
    radius_m: int
    lat_field: str = 'latitude'
    lon_field: str = 'longitude'


@dataclass
class QueryOptions:
    """Options for data queries."""
    limit: Optional[int] = None
    offset: Optional[int] = None
    sort_by: Optional[str] = None
    sort_order: Optional[str] = 'asc'  # 'asc' or 'desc'


class IDataQuery(Protocol):
    """Interface for data query parameters."""
    filters: List[QueryFilter]
    geo_filter: Optional[GeospatialFilter]
    options: QueryOptions


class IDataResult(Generic[T], Protocol):
    """Interface for data query results."""
    items: List[T]
    total: int
    page: int
    page_size: int
    has_more: bool


class IDataProvider(ABC):
    """Interface for data providers."""

    @abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the data provider.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        pass

    @abstractmethod
    def is_initialized(self) -> bool:
        """
        Check if the provider is initialized and ready to use.

        Returns:
            bool: True if the provider is initialized, False otherwise
        """
        pass

    @abstractmethod
    def query(self, query: IDataQuery) -> IDataResult:
        """
        Query the data with the given parameters.

        Args:
            query: Query parameters

        Returns:
            Query results
        """
        pass

    @abstractmethod
    def get_by_id(self, id_field: str, id_value: Any) -> Optional[Dict[str, Any]]:
        """
        Get a single item by its ID.

        Args:
            id_field: Name of the ID field
            id_value: Value of the ID

        Returns:
            Item data or None if not found
        """
        pass

    @abstractmethod
    def get_dataframe(self) -> pd.DataFrame:
        """
        Get the entire dataset as a pandas DataFrame.

        Returns:
            DataFrame containing the dataset
        """
        pass

    @abstractmethod
    def get_schema(self) -> Dict[str, str]:
        """
        Get the schema of the dataset.

        Returns:
            Dictionary mapping field names to their types
        """
        pass

    @abstractmethod
    def get_unique_values(self, field: str) -> List[Any]:
        """
        Get all unique values for a field.

        Args:
            field: Field name

        Returns:
            List of unique values
        """
        pass

    @abstractmethod
    def count(self, query: Optional[IDataQuery] = None) -> int:
        """
        Count items matching the query.

        Args:
            query: Optional query parameters

        Returns:
            Count of matching items
        """
        pass

    @abstractmethod
    def refresh(self) -> bool:
        """
        Refresh the dataset from its source.

        Returns:
            True if refresh was successful, False otherwise
        """
        pass
