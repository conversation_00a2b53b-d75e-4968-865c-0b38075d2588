# src/infrastructure/data_access/core/factory.py

"""
Factory functions for creating data providers.

This module provides factory functions for creating data providers
based on configuration settings.
"""

import os
import importlib
import inspect
from typing import Dict, Optional, Type, Any

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical
from .interfaces import IDataProvider
from src.infrastructure.config.config import ConfigManager


# Registry of data provider implementations
_provider_registry: Dict[str, Type[IDataProvider]] = {}

# Cache for provider instances (singleton pattern)
_provider_instances: Dict[str, IDataProvider] = {}


def _discover_providers():
    """
    Dynamically discover provider implementations in the providers directory.
    """
    providers_dir = os.path.join(os.path.dirname(
        os.path.dirname(__file__)), "providers")

    if not os.path.exists(providers_dir):
        warning(f"Providers directory not found: {providers_dir}")
        return

    # Scan provider directories
    for provider_type in os.listdir(providers_dir):
        provider_dir = os.path.join(providers_dir, provider_type)
        if not os.path.isdir(provider_dir):
            continue

        # Look for provider implementation files
        for filename in os.listdir(provider_dir):
            if not filename.endswith("_provider.py"):
                continue

            module_name = f"src.components.messages.shared.data_access.providers.{provider_type}.{filename[:-3]}"
            try:
                module = importlib.import_module(module_name)

                # Find provider classes in the module
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                        issubclass(obj, IDataProvider) and
                            obj != IDataProvider):

                        _provider_registry[provider_type] = obj
                        info(
                            f"Discovered provider: {provider_type} -> {obj.__name__}")
            except Exception as e:
                error(f"Error loading provider module {module_name}: {e}")


def create_data_provider(
    provider_type: Optional[str] = None,
    data_path: Optional[str] = None,
    cache_enabled: Optional[bool] = None,
    cache_ttl_seconds: Optional[int] = None,
    cache_max_size: Optional[int] = None,
    force_new_instance: bool = False,
    **provider_options
) -> IDataProvider:
    """
    Create a data provider based on configuration.

    Args:
        provider_type: Type of provider to create (defaults to config value)
        data_path: Path to the data source (defaults to config value)
        cache_enabled: Whether to enable caching (defaults to config value)
        cache_ttl_seconds: Cache TTL in seconds (defaults to config value)
        cache_max_size: Maximum cache size (defaults to config value)
        force_new_instance: If True, always create a new instance instead of using cached one
        **provider_options: Additional provider-specific options

    Returns:
        Data provider instance
    """
    config = ConfigManager()

    # Ensure providers are discovered
    if not _provider_registry:
        _discover_providers()

    # Use provided values or get from config
    provider_type = provider_type or config.get_config_value(
        "data_provider_type", "csv")
    data_path = data_path or config.get_config_value(
        "data_paths", {}).get("dataset", "data/dataset.csv")
    cache_enabled = cache_enabled if cache_enabled is not None else config.get_config_value(
        "data_cache_enabled", True)
    cache_ttl_seconds = cache_ttl_seconds or config.get_config_value(
        "data_cache_ttl", 300)
    cache_max_size = cache_max_size or config.get_config_value(
        "data_cache_max_size", 1000)

    # Create a cache key based on provider type and data path
    cache_key = f"{provider_type}:{data_path}"

    # Check if we already have an instance for this configuration
    if not force_new_instance and cache_key in _provider_instances:
        debug(f"Reusing existing data provider instance for {cache_key}")
        return _provider_instances[cache_key]

    info(f"Creating data provider of type: {provider_type}")

    # Check if provider type exists in registry
    if provider_type not in _provider_registry:
        error(
            f"Unknown data provider type: {provider_type}, falling back to CSV")
        provider_type = "csv"

        # If CSV is not available, raise an error
        if "csv" not in _provider_registry:
            raise ValueError("CSV provider not found in registry")

    provider_class = _provider_registry[provider_type]

    # Get provider-specific configuration
    provider_config = _get_provider_config(
        provider_type, config, provider_options)

    # Common configuration for all providers
    common_config = {
        "cache_enabled": cache_enabled,
        "cache_ttl_seconds": cache_ttl_seconds,
        "cache_max_size": cache_max_size,
    }

    # Create the provider instance
    try:
        provider = provider_class(
            **common_config,
            **provider_config
        )

        # Store in cache if not forcing a new instance
        if not force_new_instance:
            _provider_instances[cache_key] = provider

        return provider
    except Exception as e:
        error(f"Error creating provider of type {provider_type}: {e}")
        raise


def _get_provider_config(provider_type: str, config: ConfigManager, provider_options: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get provider-specific configuration.

    Args:
        provider_type: Type of provider
        config: ConfigManager instance
        provider_options: Additional provider options

    Returns:
        Provider-specific configuration
    """
    if provider_type == "csv":
        return {
            "file_path": provider_options.get("data_path") or config.get_config_value(
                "data_paths", {}).get("dataset", "data/dataset.csv"),
            "use_memory_map": provider_options.get("use_memory_map") or config.get_config_value(
                "csv_use_memory_map", True),
            "use_partitioning": provider_options.get("use_partitioning") or config.get_config_value(
                "csv_use_partitioning", True),
            "partition_dir": provider_options.get("partition_dir") or config.get_config_value(
                "csv_partition_dir", None),
            "auto_initialize": provider_options.get("auto_initialize", True)
        }
    elif provider_type == "postgres":
        return {
            "connection_string": provider_options.get("connection_string") or config.get_config_value(
                "postgres_connection_string", "postgresql://user:password@localhost:5432/database"),
            "table_name": provider_options.get("table_name") or config.get_config_value(
                "postgres_table_name", "dataset"),
            "schema_name": provider_options.get("schema_name") or config.get_config_value(
                "postgres_schema_name", "public"),
            "auto_initialize": provider_options.get("auto_initialize", True)
        }
    else:
        return provider_options


def switch_data_provider(provider_type: str, **provider_options) -> IDataProvider:
    """
    Switch to a different data provider type.

    Args:
        provider_type: Type of provider to switch to
        **provider_options: Provider-specific options

    Returns:
        New data provider instance
    """
    config = ConfigManager()

    # Update configuration
    config_updates = {
        "data_provider_type": provider_type
    }

    # Add provider-specific configuration
    if provider_type == "csv":
        if "file_path" in provider_options:
            config_updates["data_paths"] = {
                "dataset": provider_options["file_path"]}
        if "use_memory_map" in provider_options:
            config_updates["csv_use_memory_map"] = provider_options["use_memory_map"]
        if "use_partitioning" in provider_options:
            config_updates["csv_use_partitioning"] = provider_options["use_partitioning"]
        if "partition_dir" in provider_options:
            config_updates["csv_partition_dir"] = provider_options["partition_dir"]
    elif provider_type == "postgres":
        if "connection_string" in provider_options:
            config_updates["postgres_connection_string"] = provider_options["connection_string"]
        if "table_name" in provider_options:
            config_updates["postgres_table_name"] = provider_options["table_name"]
        if "schema_name" in provider_options:
            config_updates["postgres_schema_name"] = provider_options["schema_name"]

    # Update configuration
    config.update_config(config_updates)

    # Clear the provider instances cache
    global _provider_instances
    _provider_instances = {}
    info("Cleared provider instances cache due to provider switch")

    # Create and return the new provider
    return create_data_provider(provider_type, **provider_options)
