# src/infrastructure/data_access/core/__init__.py

"""
Core components for data access infrastructure.

This package provides the core interfaces and abstractions for the
data access infrastructure.
"""

from .interfaces import (
    IDataProvider, IDataQuery, IDataResult,
    QueryFilter, GeospatialFilter, QueryOptions
)
from .query import DataQuery
from .result import DataResult
from .factory import (
    create_data_provider, switch_data_provider
)

__all__ = [
    'IDataProvider', 'IDataQuery', 'IDataResult',
    'QueryFilter', 'GeospatialFilter', 'QueryOptions',
    'DataQuery', 'DataResult',
    'create_data_provider', 'switch_data_provider'
]
