"""
Session management helpers for the messages component.

This module provides utilities for managing session IDs, user IDs,
and logging context across different processes.
"""

import uuid
import time
from typing import Optional, Tuple
from src.infrastructure.log.unified_logger import start_session


def generate_session_id() -> str:
    """
    Generate a unique session ID.
    
    Returns:
        A unique session ID string
    """
    return f"{int(time.time())}_{uuid.uuid4().hex[:8]}"


def normalize_user_and_session_ids(
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
) -> Tuple[str, str]:
    """
    Normalize and ensure valid user_id and session_id.
    
    Args:
        user_id: Optional user ID
        session_id: Optional session ID
        
    Returns:
        Tuple of (user_id, session_id) with guaranteed valid values
    """
    # Generate a session ID if not provided
    if session_id is None:
        session_id = generate_session_id()
    
    # Use session_id as user_id if user_id is not provided or is "unknown"
    if user_id is None or user_id == "unknown":
        user_id = session_id
    
    return user_id, session_id


def setup_session_logging(user_id: str, session_id: str) -> None:
    """
    Set up logging session context for operations.
    
    This ensures all logs from operations go to the user's session log
    instead of system.log.
    
    Args:
        user_id: The user ID for the session
        session_id: The session ID for the session
    """
    if user_id and session_id:
        start_session(user_id, session_id)


def validate_coordinates(latitude: Optional[float], longitude: Optional[float]) -> bool:
    """
    Validate that coordinates are provided and valid.
    
    Args:
        latitude: The latitude coordinate
        longitude: The longitude coordinate
        
    Returns:
        True if coordinates are valid, False otherwise
    """
    return (
        latitude is not None and 
        longitude is not None and
        -90 <= latitude <= 90 and
        -180 <= longitude <= 180
    )


def create_coordinate_error_response(
    latitude: Optional[float],
    longitude: Optional[float],
    session_title: Optional[str] = None
) -> dict:
    """
    Create a standardized error response for invalid coordinates.
    
    Args:
        latitude: The provided latitude
        longitude: The provided longitude
        session_title: Optional session title
        
    Returns:
        Error response dictionary
    """
    from .error_codes import CLIENT_INVALID_COORDINATES
    from .response_parser import format_response_with_session
    
    return format_response_with_session(
        status_code=CLIENT_INVALID_COORDINATES,
        response_text="Location services are required to find places. Please enable location services or provide coordinates.",
        top_candidates=[],
        session_title=session_title
    )
