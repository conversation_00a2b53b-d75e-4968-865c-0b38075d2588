"""
Common utilities for the messages component.

This package contains shared utility functions and classes used across
the messages component processes and agents.
"""

from .error_codes import (
    LOCATION_SUCCESS, CLASSIFICATION_NO_SUBCATEGORIES, LOCATION_NO_POIS,
    CANDIDATES_NO_RESULTS, LOCATION_TIMEOUT, CLIENT_INVALID_COORDINATES,
    get_error_description, is_success_code, is_client_error, is_server_error
)
from .response_parser import (
    extract_json_from_response, parse_classification_response,
    parse_available_categories, format_response_with_session
)
from .session_helpers import (
    generate_session_id, normalize_user_and_session_ids, setup_session_logging,
    validate_coordinates, create_coordinate_error_response
)

__all__ = [
    # Error codes
    'LOCATION_SUCCESS', 'CLASSIFICATION_NO_SUBCATEGORIES', 'LOCATION_NO_POIS',
    'CANDIDATES_NO_RESULTS', 'LOCATION_TIMEOUT', 'CLIENT_INVALID_COORDINATES',
    'get_error_description', 'is_success_code', 'is_client_error', 'is_server_error',
    # Response parsing
    'extract_json_from_response', 'parse_classification_response',
    'parse_available_categories', 'format_response_with_session',
    # Session helpers
    'generate_session_id', 'normalize_user_and_session_ids', 'setup_session_logging',
    'validate_coordinates', 'create_coordinate_error_response',
]
