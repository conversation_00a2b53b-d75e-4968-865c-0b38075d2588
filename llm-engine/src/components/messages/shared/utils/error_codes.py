"""
Centralized error codes for the messages component.

This module contains all status codes used across the messages component
processes and agents for consistent error handling and response formatting.
"""

# Success codes
LOCATION_SUCCESS = 20000

# Location agent status codes (2XXXX range)
CLASSIFICATION_NO_SUBCATEGORIES = 30020
LOCATION_NO_POIS = 20020
CANDIDATES_NO_RESULTS = 22020
LOCATION_TIMEOUT = 20080

# Client error codes (4XXXX range)
CLIENT_INVALID_COORDINATES = 40000

# Data access status codes (5XXXX range)
DATA_SUCCESS = 50000
DATA_QUERY_SUCCESS = 50500
DATA_GET_SUCCESS = 50500
DATA_SCHEMA_SUCCESS = 50500
DATA_COUNT_SUCCESS = 50500
DATA_REFRESH_SUCCESS = 50400
DATA_MISSING_PARAMS = 50022
DATA_INVALID_TYPE = 50021
DATA_UNKNOWN_OPERATION = 50021
DATA_NOT_FOUND = 50520
DATA_QUERY_ERROR = 50082
DATA_SYSTEM_ERROR = 50084

# Error code descriptions for debugging
ERROR_DESCRIPTIONS = {
    LOCATION_SUCCESS: "Location search completed successfully",
    CLASSIFICATION_NO_SUBCATEGORIES: "No subcategories found by classification",
    LOCATION_NO_POIS: "No POIs found for the given criteria",
    CANDIDATES_NO_RESULTS: "No top candidates found",
    LOCATION_TIMEOUT: "Location search timed out or failed",
    CLIENT_INVALID_COORDINATES: "Invalid or missing coordinates provided",
    DATA_SUCCESS: "Data operation completed successfully",
    DATA_QUERY_SUCCESS: "Data query completed successfully",
    DATA_GET_SUCCESS: "Data retrieval completed successfully",
    DATA_SCHEMA_SUCCESS: "Schema operation completed successfully",
    DATA_COUNT_SUCCESS: "Count operation completed successfully",
    DATA_REFRESH_SUCCESS: "Data refresh completed successfully",
    DATA_MISSING_PARAMS: "Missing required parameters",
    DATA_INVALID_TYPE: "Invalid data type provided",
    DATA_UNKNOWN_OPERATION: "Unknown operation requested",
    DATA_NOT_FOUND: "Requested data not found",
    DATA_QUERY_ERROR: "Error executing data query",
    DATA_SYSTEM_ERROR: "System error during data operation",
}


def get_error_description(status_code: int) -> str:
    """
    Get a human-readable description for a status code.
    
    Args:
        status_code: The status code to describe
        
    Returns:
        Human-readable description of the status code
    """
    return ERROR_DESCRIPTIONS.get(status_code, f"Unknown status code: {status_code}")


def is_success_code(status_code: int) -> bool:
    """
    Check if a status code represents a successful operation.
    
    Args:
        status_code: The status code to check
        
    Returns:
        True if the status code represents success, False otherwise
    """
    return status_code in [
        LOCATION_SUCCESS,
        DATA_SUCCESS,
        DATA_QUERY_SUCCESS,
        DATA_GET_SUCCESS,
        DATA_SCHEMA_SUCCESS,
        DATA_COUNT_SUCCESS,
        DATA_REFRESH_SUCCESS,
    ]


def is_client_error(status_code: int) -> bool:
    """
    Check if a status code represents a client error.
    
    Args:
        status_code: The status code to check
        
    Returns:
        True if the status code represents a client error, False otherwise
    """
    return 40000 <= status_code < 50000


def is_server_error(status_code: int) -> bool:
    """
    Check if a status code represents a server error.
    
    Args:
        status_code: The status code to check
        
    Returns:
        True if the status code represents a server error, False otherwise
    """
    return status_code >= 50000
