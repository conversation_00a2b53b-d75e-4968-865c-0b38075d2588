"""
Centralized advice generation for the multi-task orchestrator.

This module provides centralized advice generation that can handle
multi-task results and integrate state, history, and memory for
intelligent, context-aware responses.
"""

from .advice_generator import AdviceGenerator, ResponseTemplate, AdviceContext

__all__ = [
    'AdviceGenerator',
    'ResponseTemplate',
    'AdviceContext',
]
