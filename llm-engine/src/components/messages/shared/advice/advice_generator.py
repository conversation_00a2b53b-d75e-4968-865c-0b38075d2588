"""
Centralized advice generator for multi-task orchestrator.

This module provides centralized advice generation that can handle
multi-task results and integrate state, history, and memory for
intelligent, context-aware responses.
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import logging

from src.models import POIData
from ...processes.shared.models import TaskResult
from ...agents.advice import get_location_advice

logger = logging.getLogger(__name__)


class ResponseTemplate(Enum):
    """Response templates for different task scenarios."""
    MULTI_BOUNDARY_SEARCH = "multi_boundary_search"
    MULTI_CATEGORY_SEARCH = "multi_category_search"
    MIXED_TASK_SEARCH = "mixed_task_search"
    POI_SEARCH_RESULT = "poi_search_result"
    BOUNDARY_SEARCH_RESULT = "boundary_search_result"
    LOCATION_SEARCH_RESULT = "location_search_result"
    NO_RESULTS_FOUND = "no_results_found"


@dataclass
class AdviceContext:
    """Context information for advice generation."""
    state: Optional[Dict[str, Any]] = None
    history: Optional[Dict[str, Any]] = None
    memory: Optional[Dict[str, Any]] = None
    user_request: str = ""
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    search_radius: Optional[int] = None


class AdviceGenerator:
    """
    Centralized advice generator that handles multi-task results
    and integrates state, history, and memory for intelligent responses.
    """

    def __init__(self):
        """Initialize the advice generator."""
        self.logger = logging.getLogger(__name__)

    def generate(self, task_results: List[TaskResult], context: AdviceContext,
                 num_candidates: int, streaming: bool = False) -> Dict[str, Any]:
        """
        Generate advice using task results and session context.

        Args:
            task_results: Results from executed tasks
            context: Combined state, history, and memory context
            num_candidates: Number of top candidates to return
            streaming: Whether to generate streaming response

        Returns:
            Dict containing:
            - response: Generated advice text (or generator for streaming)
            - top_candidates: Combined candidates from all tasks
            - total_candidates_found: Total number of unique candidates
            - tasks_executed: Number of tasks executed
            - successful_tasks: Number of successful tasks
            - is_streaming: Boolean indicating if response is streaming
        """
        self.logger.debug(
            f"Generating advice for {len(task_results)} task results")

        try:
            self.logger.debug(
                f"Starting advice generation for {len(task_results)} task results")

            # Combine all successful results
            all_candidates = []
            successful_tasks = []

            for result in task_results:
                if result.success and result.result and result.result.get('top_candidates'):
                    all_candidates.extend(result.result['top_candidates'])
                    successful_tasks.append(result)

            self.logger.debug(
                f"Found {len(successful_tasks)} successful tasks with {len(all_candidates)} total candidates")

            # Remove duplicates and rank
            unique_candidates = self._deduplicate_and_rank(all_candidates)

            # Check if we have POI-based searches that need special handling
            has_poi_based_search = any(
                result.task.tool_name in ["poi_based_search", "poi_lookup"]
                for result in successful_tasks
            )

            # For multi-task scenarios, allow each task to contribute its full quota
            # For single-task scenarios, apply the original logic
            # Special case: POI-relative searches (poi_lookup + relative_search) should be treated as POI-based
            if has_poi_based_search:
                # POI-based search: allow num_candidates + 1 (reference POI + requested candidates)
                top_candidates = unique_candidates[:num_candidates + 1]
                self.logger.debug(
                    f"POI-based search detected: allowing {num_candidates + 1} candidates")
            elif len(successful_tasks) > 1:
                # Multi-task: allow num_candidates * number_of_tasks
                expected_total = num_candidates * len(successful_tasks)
                top_candidates = unique_candidates[:expected_total]
                self.logger.debug(
                    f"Multi-task search: {len(successful_tasks)} tasks, allowing up to {expected_total} candidates ({num_candidates} per task)")
            else:
                # Single regular search: use num_candidates exactly
                top_candidates = unique_candidates[:num_candidates]
                self.logger.debug(
                    f"Single regular search: limiting to {num_candidates} candidates")

            self.logger.debug(
                f"After deduplication: {len(unique_candidates)} unique, {len(top_candidates)} top candidates")

            # Create token-efficient version for advice generation
            efficient_candidates = self._create_efficient_candidates(
                top_candidates)

            self.logger.debug(
                f"Created {len(efficient_candidates)} efficient candidates")

            # Select appropriate response template
            template = self._select_response_template(
                successful_tasks, context)

            self.logger.debug(f"Selected template: {template}")

            # Generate response using existing advice agent with efficient candidates
            if streaming:
                response_text = self._generate_streaming_response(
                    template=template,
                    candidates=efficient_candidates,  # Use efficient version for advice
                    full_candidates=top_candidates,   # Keep full version for response
                    task_results=successful_tasks,
                    context=context
                )
            else:
                response_text = self._generate_response(
                    template=template,
                    candidates=efficient_candidates,  # Use efficient version for advice
                    full_candidates=top_candidates,   # Keep full version for response
                    task_results=successful_tasks,
                    context=context
                )

            return {
                "response": response_text,
                "top_candidates": top_candidates,
                "total_candidates_found": len(unique_candidates),
                "tasks_executed": len(task_results),
                "successful_tasks": len(successful_tasks),
                "is_streaming": streaming
            }

        except Exception as e:
            import traceback
            self.logger.error(f"Error generating advice: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "response": "I apologize, but I encountered an error generating the response. Please try again.",
                "top_candidates": [],
                "total_candidates_found": 0,
                "tasks_executed": len(task_results),
                "successful_tasks": 0,
                "error": str(e)
            }

    def _deduplicate_and_rank(self, candidates: List[POIData]) -> List[POIData]:
        """
        Remove duplicate POIs and rank them by relevance.

        Args:
            candidates: List of POI candidates

        Returns:
            List of unique, ranked POI candidates
        """
        if not candidates:
            return []

        # Use a dictionary to track unique POIs by ID or name+location
        unique_pois = {}

        for poi in candidates:
            # Handle both dictionary and object formats
            if isinstance(poi, dict):
                poi_id = poi.get('id')
                poi_name = poi.get('name', 'Unknown')
                poi_lat = poi.get('latitude', 0)
                poi_lon = poi.get('longitude', 0)
            else:
                poi_id = getattr(poi, 'id', None)
                poi_name = getattr(poi, 'name', 'Unknown')
                poi_lat = getattr(poi, 'latitude', 0)
                poi_lon = getattr(poi, 'longitude', 0)

            # Create a unique key based on ID or name+coordinates
            if poi_id:
                key = f"id_{poi_id}"
            else:
                # Fallback to name + approximate location
                key = f"name_{poi_name}_{round(poi_lat, 4)}_{round(poi_lon, 4)}"

            # Keep the first occurrence (assuming they're already ranked)
            if key not in unique_pois:
                unique_pois[key] = poi

        # Return as list, maintaining original order
        return list(unique_pois.values())

    def _select_response_template(self, tasks: List[TaskResult], context: AdviceContext) -> ResponseTemplate:
        """
        Select appropriate response template based on task types.

        Args:
            tasks: List of successful task results
            context: Advice generation context

        Returns:
            ResponseTemplate enum value
        """
        if not tasks:
            return ResponseTemplate.NO_RESULTS_FOUND

        task_types = [task.task.tool_name for task in tasks]

        # Multi-task templates
        if len(tasks) > 1:
            if all(t == "boundary_search" for t in task_types):
                return ResponseTemplate.MULTI_BOUNDARY_SEARCH
            elif all(t == "relative_search" for t in task_types):
                return ResponseTemplate.MULTI_CATEGORY_SEARCH
            else:
                return ResponseTemplate.MIXED_TASK_SEARCH

        # Single task templates
        elif len(tasks) == 1:
            task_type = task_types[0]
            if task_type == "poi_based_search":
                return ResponseTemplate.POI_SEARCH_RESULT
            elif task_type == "boundary_search":
                return ResponseTemplate.BOUNDARY_SEARCH_RESULT
            else:
                return ResponseTemplate.LOCATION_SEARCH_RESULT

        return ResponseTemplate.NO_RESULTS_FOUND

    def _generate_response(self, template: ResponseTemplate, candidates: List[POIData],
                           task_results: List[TaskResult], context: AdviceContext,
                           full_candidates: List[POIData] = None) -> str:
        """
        Generate response using existing advice agent with enhanced context.

        Args:
            template: Selected response template
            candidates: Token-efficient POI candidates for advice generation
            task_results: Successful task results
            context: Advice generation context
            full_candidates: Full POI candidates for response (optional)

        Returns:
            Generated response text
        """
        try:
            # Extract additional context from task results
            available_subcategories = []
            requested_subcategories = []

            for result in task_results:
                if result.result:
                    available_subcategories.extend(
                        result.result.get('available_subcategories', []))
                    requested_subcategories.extend(
                        result.result.get('requested_subcategories', []))

            # Remove duplicates
            available_subcategories = list(set(available_subcategories))
            requested_subcategories = list(set(requested_subcategories))

            # Build enhanced history with template-specific context
            enhanced_history = self._build_enhanced_history_with_template(
                context, template, task_results)

            # Use existing advice agent with enhanced context
            self.logger.debug(
                f"Calling get_location_advice with {len(candidates)} candidates")
            advice_response = get_location_advice(
                prompt=context.user_request,
                history=enhanced_history,
                top_candidates=candidates,
                latitude=context.latitude,
                longitude=context.longitude,
                search_radius=context.search_radius,
                available_subcategories=available_subcategories,
                requested_subcategories=requested_subcategories
            )

            self.logger.debug(
                f"get_location_advice returned: {type(advice_response)}")
            self.logger.debug(f"advice_response content: {advice_response}")

            if hasattr(advice_response, 'get'):
                response_text = advice_response.get(
                    'response', 'No response generated')
            else:
                response_text = str(advice_response)

            self.logger.debug(
                f"Extracted response text: {response_text[:100]}...")
            return response_text

        except Exception as e:
            self.logger.error(f"Error in response generation: {e}")
            return self._generate_fallback_response(template, candidates, task_results)

    def _build_enhanced_history(self, context: AdviceContext) -> str:
        """
        Build enhanced history string with state, history, and memory context.

        Args:
            context: Advice generation context

        Returns:
            Enhanced history string
        """
        history_parts = []

        # Add original history if available
        if context.history and isinstance(context.history, str):
            history_parts.append(context.history)
        elif context.history and isinstance(context.history, dict):
            if context.history.get('summary'):
                history_parts.append(
                    f"Previous conversation summary: {context.history['summary']}")

        # Add memory insights if available
        if context.memory and context.memory.get('user_preferences'):
            prefs = context.memory['user_preferences']
            if prefs:
                history_parts.append(f"User preferences: {prefs}")

        # Add state context if available
        if context.state and context.state.get('search_context'):
            search_ctx = context.state['search_context']
            if search_ctx:
                history_parts.append(f"Recent search context: {search_ctx}")

        return " | ".join(history_parts) if history_parts else ""

    def _build_enhanced_history_with_template(self, context: AdviceContext,
                                              template: ResponseTemplate,
                                              task_results: List[TaskResult]) -> str:
        """
        Build enhanced history string with template-specific context information.

        Args:
            context: Advice generation context
            template: Selected response template
            task_results: Task results for additional context

        Returns:
            Enhanced history string with template-specific information
        """
        history_parts = []

        # Add original history
        base_history = self._build_enhanced_history(context)
        if base_history:
            history_parts.append(base_history)

        # Add template-specific context
        template_context = self._get_template_context(template, task_results)
        if template_context:
            history_parts.append(f"Search context: {template_context}")

        return " | ".join(history_parts) if history_parts else ""

    def _get_template_context(self, template: ResponseTemplate, task_results: List[TaskResult]) -> str:
        """
        Get template-specific context information.

        Args:
            template: Selected response template
            task_results: Task results for context

        Returns:
            Template-specific context string
        """
        if template == ResponseTemplate.MULTI_BOUNDARY_SEARCH:
            boundaries = []
            for result in task_results:
                if result.task.parameters.get('boundary_name'):
                    boundaries.append(result.task.parameters['boundary_name'])
            return f"Searched multiple areas: {', '.join(boundaries)}" if boundaries else ""

        elif template == ResponseTemplate.MULTI_CATEGORY_SEARCH:
            categories = []
            for result in task_results:
                if result.result and result.result.get('requested_subcategories'):
                    categories.extend(result.result['requested_subcategories'])
            unique_categories = list(set(categories))
            return f"Searched multiple categories: {', '.join(unique_categories)}" if unique_categories else ""

        elif template == ResponseTemplate.POI_SEARCH_RESULT:
            for result in task_results:
                if result.task.tool_name == "poi_based_search" and result.task.parameters.get('poi_name'):
                    poi_name = result.task.parameters['poi_name']
                    return f"POI-based search for: {poi_name}"

        elif template == ResponseTemplate.MIXED_TASK_SEARCH:
            task_types = [result.task.tool_name for result in task_results]
            return f"Mixed search using: {', '.join(set(task_types))}"

        return ""

    def _generate_fallback_response(self, template: ResponseTemplate,
                                    candidates: List[POIData],
                                    task_results: List[TaskResult]) -> str:
        """
        Generate a fallback response when the main advice generation fails.

        Args:
            template: Selected response template
            candidates: POI candidates
            task_results: Task results

        Returns:
            Fallback response string
        """
        if not candidates:
            return "I apologize, but I couldn't find any places matching your request. Please try a different search or expand your search area."

        # Basic response based on template
        if template == ResponseTemplate.MULTI_BOUNDARY_SEARCH:
            return f"I found {len(candidates)} places across multiple areas. Here are the top results for your search."

        elif template == ResponseTemplate.MULTI_CATEGORY_SEARCH:
            return f"I found {len(candidates)} places in different categories. Here are the top results for your search."

        elif template == ResponseTemplate.POI_SEARCH_RESULT:
            return f"I found {len(candidates)} places related to your POI search. Here are the top results."

        elif template == ResponseTemplate.MIXED_TASK_SEARCH:
            return f"I found {len(candidates)} places using multiple search methods. Here are the top results."

        else:
            return f"I found {len(candidates)} places for you. Here are the top results for your search."

    def _create_efficient_candidates(self, candidates: List[POIData]) -> List[Dict[str, Any]]:
        """
        Create token-efficient version of candidates for advice generation.

        Keeps only essential fields: name, country, city, coordinates, tags.
        This saves significant tokens while maintaining functionality.

        Args:
            candidates: Full POI candidates

        Returns:
            List of token-efficient candidate dictionaries
        """
        efficient = []

        for candidate in candidates:
            # Convert POIData to dict if needed
            if hasattr(candidate, 'to_dict'):
                candidate_dict = candidate.to_dict()
            elif isinstance(candidate, dict):
                candidate_dict = candidate
            else:
                # Skip if we can't convert
                continue

            # Extract only essential fields
            essential = {
                'id': candidate_dict.get('id'),
                'name': candidate_dict.get('name'),
                'country': candidate_dict.get('country', candidate_dict.get('city', '')),
                'city': candidate_dict.get('city', candidate_dict.get('district', '')),
                'latitude': candidate_dict.get('latitude'),
                'longitude': candidate_dict.get('longitude'),
                'tags': [
                    candidate_dict.get('category', ''),
                    candidate_dict.get('subcategory', ''),
                    candidate_dict.get('cuisine', '')
                ],
                'distance_m': candidate_dict.get('distance_m')
            }

            # Only include if we have essential data
            if essential['name'] and essential['latitude'] and essential['longitude']:
                # Clean up tags (remove empty strings)
                essential['tags'] = [tag for tag in essential['tags'] if tag]
                efficient.append(essential)

        return efficient

    def _generate_streaming_response(self, template: ResponseTemplate, candidates: List[POIData],
                                     task_results: List[TaskResult], context: AdviceContext,
                                     full_candidates: List[POIData] = None):
        """
        Generate streaming response using existing advice agent with enhanced context.

        This method returns a generator that yields response chunks for streaming.

        Args:
            template: Selected response template
            candidates: Token-efficient POI candidates for advice generation
            task_results: Successful task results
            context: Advice generation context
            full_candidates: Full POI candidates for response (optional)

        Returns:
            Generator that yields response chunks
        """
        try:
            # First generate the full response
            full_response = self._generate_response(
                template=template,
                candidates=candidates,
                task_results=task_results,
                context=context,
                full_candidates=full_candidates
            )

            # Create a streaming generator that yields words
            def response_generator():
                words = full_response.split()
                for i, word in enumerate(words):
                    yield {
                        "type": "text_chunk",
                        "content": word + (" " if i < len(words) - 1 else ""),
                        "chunk_index": i
                    }

                # Final completion signal
                yield {
                    "type": "complete",
                    "status": "success",
                    "total_chunks": len(words)
                }

            return response_generator()

        except Exception as e:
            self.logger.error(f"Error in streaming response generation: {e}")

            # Fallback streaming response
            def fallback_generator():
                fallback_text = self._generate_fallback_response(
                    template, candidates, task_results)
                words = fallback_text.split()
                for i, word in enumerate(words):
                    yield {
                        "type": "text_chunk",
                        "content": word + (" " if i < len(words) - 1 else ""),
                        "chunk_index": i
                    }
                yield {
                    "type": "complete",
                    "status": "success",
                    "total_chunks": len(words)
                }

            return fallback_generator()
