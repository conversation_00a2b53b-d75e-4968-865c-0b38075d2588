"""
OpenRouter API provider implementation.

This module implements the LLMProviderInterface for the OpenRouter API.
"""

import json
import os
from typing import Dict, Any, Optional, List, Union

from openai import OpenAI

from src.models import LLMResponse
from ..interface import LLMProviderInterface
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical


class OpenRouterProvider(LLMProviderInterface):
    """
    Implementation of LLMProviderInterface for the OpenRouter API.
    """

    def __init__(self):
        """Initialize the OpenRouterProvider."""
        self.client = None
        self.api_key = None
        self.site_url = "https://wizlop.com"  # Will be set from config
        self.site_name = "Wizlop"  # Will be set from config

    def initialize(self, api_key: Optional[str] = None, **kwargs) -> None:
        """
        Initialize the OpenRouter API client with the given API key.

        Args:
            api_key: Optional API key for the OpenRouter API. If not provided, will attempt
                    to use the API key from the configuration.
            **kwargs: Additional provider-specific parameters.
        """
        # Get site information from kwargs or use defaults
        self.site_url = kwargs.get("site_url", self.site_url)
        self.site_name = kwargs.get("site_name", self.site_name)

        # Try to get site information from configuration if not provided in kwargs
        if "site_url" not in kwargs or "site_name" not in kwargs:
            try:
                from src.infrastructure.config.config import ConfigManager
                config = ConfigManager()
                provider_config = config.config.get(
                    "llm_providers", {}).get("openrouter", {})

                if "site_url" not in kwargs and "site_url" in provider_config:
                    self.site_url = provider_config["site_url"]

                if "site_name" not in kwargs and "site_name" in provider_config:
                    self.site_name = provider_config["site_name"]
            except Exception as e:
                pass

        if api_key:
            self.api_key = api_key
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=api_key
            )
        else:
            # Try to get the API key from the configuration
            try:
                from src.infrastructure.config.config import ConfigManager
                config = ConfigManager()
                self.api_key = config.get_api_key("openrouter")
            except Exception:
                # Fall back to environment variable
                self.api_key = os.getenv("OPENROUTER_API_KEY")

            if self.api_key:
                self.client = OpenAI(
                    base_url="https://openrouter.ai/api/v1",
                    api_key=self.api_key
                )
            else:
                raise ValueError("No API key provided for OpenRouter API")

    def call_api(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Make a request to the OpenRouter API.

        Args:
            prompt: The input prompt to send to the LLM.
            model: Optional model name to use. If not provided, will use the default model.
            system_prompt: Optional system prompt to use.
            temperature: Optional temperature parameter for the model.
            max_tokens: Optional maximum number of tokens to generate.
            response_format: Optional response format specification.
            **kwargs: Additional provider-specific parameters.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        if not self.client:
            self.initialize()

        # Check if this is a request from the prompt builder
        if "user_prompt" in kwargs and "hyperparameters" in kwargs:
            # Extract parameters from the prompt builder format
            user_prompt = kwargs.get("user_prompt", prompt)
            hyperparameters = kwargs.get("hyperparameters", {})
            system_prompt = kwargs.get("system_prompt", system_prompt)

            # Extract hyperparameters
            temperature = hyperparameters.get("temperature", temperature)
            max_tokens = hyperparameters.get("max_tokens", max_tokens)
            response_format = hyperparameters.get(
                "response_format", response_format)

            # Use the user_prompt instead of the original prompt
            prompt = user_prompt

        # Model must be provided - no defaults
        if not model:
            raise ValueError(
                "Model must be specified - no default model configured")
        model_id = model

        # Get default values from config if not provided
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()

        # Try to get provider configuration
        provider_config = config.config.get(
            "llm_providers", {}).get("openrouter", {})

        # Set default values if not provided
        if temperature is None:
            temperature = 0.7
        if max_tokens is None:
            max_tokens = 1000

        # Build the messages array
        messages = []

        # Add system prompt if provided
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })

        # Add user prompt
        messages.append({
            "role": "user",
            "content": prompt
        })

        # Prepare extra headers for OpenRouter
        extra_headers = {
            "HTTP-Referer": self.site_url,
            "X-Title": self.site_name
        }

        # Log the full request for debugging
        request_data = {
            "model": model_id,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "response_format": response_format,
            "extra_headers": extra_headers,
            "extra_body": {}
        }
        try:
            request_json = json.dumps(request_data, default=str)
            debug(f"OpenRouter API Request: {request_json}")
        except Exception as e:
            error(f"Failed to log OpenRouter API request: {str(e)}")

        try:
            # Call the OpenRouter API
            completion = self.client.chat.completions.create(
                extra_headers=extra_headers,
                extra_body={},
                model=model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                response_format=response_format
            )

            # Log the full response for debugging
            try:
                # Convert the completion object to a dictionary for better logging
                completion_dict = {
                    "id": completion.id,
                    "model": completion.model,
                    "created": completion.created,
                    "choices": [{
                        "index": choice.index,
                        "message": {
                            "role": choice.message.role,
                            "content": choice.message.content
                        },
                        "finish_reason": choice.finish_reason
                    } for choice in completion.choices],
                    "usage": {
                        "prompt_tokens": completion.usage.prompt_tokens,
                        "completion_tokens": completion.usage.completion_tokens,
                        "total_tokens": completion.usage.total_tokens
                    }
                }
                response_json = json.dumps(completion_dict, default=str)
                debug(f"OpenRouter API Response: {response_json}")

                # Log token usage information
                info(f"OpenRouter API Usage: model={completion.model}, " +
                     f"prompt_tokens={completion.usage.prompt_tokens}, " +
                     f"completion_tokens={completion.usage.completion_tokens}, " +
                     f"total_tokens={completion.usage.total_tokens}")
            except Exception as e:
                error(f"Failed to log OpenRouter API response: {str(e)}")

            # Extract the content from the response
            return self.extract_content(completion)

        except Exception as e:
            error_msg = f"Failed to call OpenRouter API: {str(e)}"
            error(error_msg)
            return LLMResponse({"error": error_msg})

    def extract_content(self, response: Any) -> LLMResponse:
        """
        Extract the relevant content from the OpenRouter API response.

        Args:
            response: The raw API response.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        try:
            # Extract the content from the response
            content = response.choices[0].message.content

            # Remove any markdown code block formatting
            if "```" in content:
                import re
                # Remove markdown code blocks but keep the content inside
                content = re.sub(r'```(?:json)?\s*(.*?)\s*```',
                                 r'\1', content, flags=re.DOTALL)
                content = content.strip()

            # Try to parse the content as JSON if it looks like JSON
            if content.strip().startswith("{") and content.strip().endswith("}"):
                try:
                    content_json = json.loads(content)
                    return LLMResponse(content_json)
                except json.JSONDecodeError:
                    # If it's not valid JSON, return the raw content
                    return LLMResponse({"response": content})
            else:
                # Return the raw content
                return LLMResponse({"response": content})

        except Exception as e:
            error_msg = f"Failed to extract content from OpenRouter API response: {str(e)}"
            error(error_msg)
            return LLMResponse({"error": error_msg})

    def get_available_models(self) -> List[str]:
        """
        Get a list of available models from your config.json tested_models.

        Returns:
            List[str]: A list of model identifiers.
        """
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()
        provider_config = config.config.get(
            "llm_providers", {}).get("openrouter", {})
        return provider_config.get("tested_models", [])

    def get_default_model(self) -> str:
        """
        Get the first model from tested_models - agents should specify their model explicitly.

        Returns:
            str: The first tested model identifier.
        """
        tested_models = self.get_available_models()
        if not tested_models:
            raise ValueError(
                "No tested models configured for OpenRouter provider")
        return tested_models[0]

    def validate_api_key(self, api_key: str) -> bool:
        """
        Validate the given API key.

        Args:
            api_key: The API key to validate.

        Returns:
            bool: True if the API key is valid, False otherwise.
        """
        try:
            # Create a temporary client with the given API key
            temp_client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=api_key
            )

            # Try to make a simple request to validate the API key
            # Use the first tested model for validation
            test_model = self.get_default_model()
            temp_client.chat.completions.create(
                model=test_model,
                messages=[
                    {"role": "user", "content": "Hello"}
                ],
                max_tokens=10
            )

            # If we get a successful response without exceptions, the API key is valid
            return True

        except Exception as e:
            return False
