"""
Factory for creating LLM providers.

This module provides a factory for creating LLM providers based on the configuration.
"""

from typing import Dict, Any, Optional, Type

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical
from .interface import LLMProviderInterface
from .providers.llama import LlamaProvider
from .providers.openrouter import OpenRouterProvider


# Registry of available providers
PROVIDER_REGISTRY: Dict[str, Type[LLMProviderInterface]] = {
    "llama": LlamaProvider,
    "openrouter": OpenRouterProvider,
    # Add more providers here as they are implemented
}


class LLMProviderFactory:
    """
    Factory for creating LLM providers.

    This class provides methods for creating and managing LLM providers.
    """

    _instance = None
    _providers = {}

    def __new__(cls):
        """Implement singleton pattern for LLMProviderFactory."""
        if cls._instance is None:
            cls._instance = super(LLMProviderFactory, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the LLMProviderFactory."""
        if not getattr(self, "_initialized", False):
            self._providers = {}
            self._initialized = True

    def get_provider(self, provider_id: str, initialize: bool = True, **kwargs) -> Optional[LLMProviderInterface]:
        """
        Get a provider instance by ID.

        Args:
            provider_id: The provider ID.
            initialize: Whether to initialize the provider.
            **kwargs: Additional parameters to pass to the provider's initialize method.

        Returns:
            Optional[LLMProviderInterface]: The provider instance, or None if not found.
        """
        # Check if the provider is already instantiated
        if provider_id in self._providers:
            return self._providers[provider_id]

        # Check if the provider is registered
        if provider_id not in PROVIDER_REGISTRY:
            error(
                f"Provider {provider_id} not found. Available providers: {list(PROVIDER_REGISTRY.keys())}")
            return None

        # Create a new provider instance
        provider_class = PROVIDER_REGISTRY[provider_id]
        provider = provider_class()

        # Initialize the provider if requested
        if initialize:
            try:
                provider.initialize(**kwargs)
            except Exception as e:
                error(f"Error initializing provider {provider_id}: {e}")
                return None

        # Store the provider instance
        self._providers[provider_id] = provider

        return provider

    def register_provider(self, provider_id: str, provider_class: Type[LLMProviderInterface]) -> None:
        """
        Register a new provider class.

        Args:
            provider_id: The provider ID.
            provider_class: The provider class.
        """
        PROVIDER_REGISTRY[provider_id] = provider_class
        info(f"Registered provider {provider_id}")

    def get_default_provider(self, initialize: bool = True, **kwargs) -> Optional[LLMProviderInterface]:
        """
        Get the default provider from the first agent configuration.

        Args:
            initialize: Whether to initialize the provider.
            **kwargs: Additional parameters to pass to the provider's initialize method.

        Returns:
            Optional[LLMProviderInterface]: The default provider instance, or None if not found.
        """
        # Get the configuration
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()

        # Get the agents configuration - this is your single source of truth
        agents_config = config.config.get("agents", {})

        # Use the first agent's provider
        if agents_config:
            first_agent_config = next(iter(agents_config.values()))
            provider_id = first_agent_config.get("provider")

            if provider_id and provider_id in PROVIDER_REGISTRY:
                return self.get_provider(provider_id, initialize, **kwargs)

        # Fallback to openrouter if no agents configured
        if "openrouter" in PROVIDER_REGISTRY:
            return self.get_provider("openrouter", initialize, **kwargs)

        # If no providers are registered, log an error and return None
        error("No providers registered")
        return None

    def get_available_providers(self) -> Dict[str, Type[LLMProviderInterface]]:
        """
        Get all available providers.

        Returns:
            Dict[str, Type[LLMProviderInterface]]: A dictionary of provider IDs to provider classes.
        """
        return PROVIDER_REGISTRY.copy()

    def get_enabled_providers(self) -> Dict[str, Type[LLMProviderInterface]]:
        """
        Get all available providers.

        Returns:
            Dict[str, Type[LLMProviderInterface]]: A dictionary of provider IDs to provider classes.
        """
        # Simply return all registered providers
        return PROVIDER_REGISTRY.copy()


# Convenience functions
def get_enabled_providers() -> Dict[str, Type[LLMProviderInterface]]:
    """
    Get all enabled providers based on configuration.

    Returns:
        Dict[str, Type[LLMProviderInterface]]: A dictionary of enabled provider IDs to provider classes.
    """
    factory = LLMProviderFactory()
    return factory.get_enabled_providers()


def get_llm_provider(provider_id: Optional[str] = None, initialize: bool = True, **kwargs) -> Optional[LLMProviderInterface]:
    """
    Get a provider instance by ID. Simple and config-driven.

    Args:
        provider_id: The provider ID. If None, returns the default provider.
        initialize: Whether to initialize the provider.
        **kwargs: Additional parameters to pass to the provider's initialize method.

    Returns:
        Optional[LLMProviderInterface]: The provider instance, or None if not found.
    """
    factory = LLMProviderFactory()

    # Get configuration from your config.json
    if initialize and not kwargs.get("skip_config", False):
        try:
            from src.infrastructure.config.config import ConfigManager
            config = ConfigManager()

            # If no provider ID specified, get from first agent
            if provider_id is None:
                agents_config = config.config.get("agents", {})
                if agents_config:
                    first_agent_config = next(iter(agents_config.values()))
                    provider_id = first_agent_config.get(
                        "provider", "openrouter")
                else:
                    provider_id = "openrouter"

            # Get provider config from your llm_providers section
            providers_config = config.config.get("llm_providers", {})
            if provider_id in providers_config:
                provider_config = providers_config[provider_id]

                # Get API key
                api_key = config.get_api_key(provider_id)
                if api_key and "api_key" not in kwargs:
                    kwargs["api_key"] = api_key

                # Add provider-specific config (site_url, site_name, etc.)
                for key, value in provider_config.items():
                    if key not in ["api_key_env_var", "tested_models"] and key not in kwargs:
                        kwargs[key] = value

        except Exception as e:
            warning(f"Error getting provider configuration: {e}")

    # Remove the skip_config flag if it exists
    if "skip_config" in kwargs:
        del kwargs["skip_config"]

    if provider_id:
        return factory.get_provider(provider_id, initialize, **kwargs)
    else:
        return factory.get_default_provider(initialize, **kwargs)
