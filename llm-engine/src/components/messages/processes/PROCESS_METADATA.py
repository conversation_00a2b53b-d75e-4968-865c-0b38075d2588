"""
Centralized Process Metadata Registry.

This module contains all process metadata in one place, making it easy to add new processes
without touching the function registry. Simply add a new entry here and the process will
be automatically discovered and registered.
"""

from typing import Dict, List

# Centralized process metadata registry
PROCESS_METADATA = {
    "relative_search": {
        "action": "relative_search",
        "description": "Search around given coordinates (user location or POI coordinates)",
        "when_to_use": "For queries like 'cafe near me', 'restaurant close by', 'market around here' when searching around user's current position, OR when searching around POI coordinates obtained from poi_lookup. This process uses provided coordinates as the center point for search.",
        "parameters": ["prompt", "latitude", "longitude", "radius", "num_candidates"],
        "processor_function": "process_relative_search",
        "priority": 1,
        "enabled": True
    },

    "boundary_search": {
        "action": "boundary_search",
        "description": "Search within administrative boundaries like countries, cities, districts, neighborhoods",
        "when_to_use": "When user mentions administrative areas like 'cafes in Beşiktaş', 'restaurants in Istanbul', 'shops in Kadıköy', 'markets in Turkey' - when searching within a defined administrative boundary (country/city/district/neighborhood)",
        "parameters": ["prompt", "boundary_name", "latitude", "longitude", "radius", "num_candidates"],
        "processor_function": "process_boundary_search",
        "priority": 3,
        "enabled": True
    },

    "poi_lookup": {
        "action": "poi_lookup",
        "description": "Find specific POI coordinates by name for use in subsequent searches",
        "when_to_use": "When you need to find the coordinates of a specific POI (like 'Kırmızı Kedi Café', 'Starbucks', 'McDonald's') to use as reference point for subsequent relative_search operations. This process ONLY returns POI coordinates and basic info - it does NOT search for nearby places. IMPORTANT: Use proper Turkish city names (İstanbul not Istanbul, İzmir not Izmir).",
        "parameters": ["poi_name", "boundary_name", "latitude", "longitude", "radius", "num_candidates"],
        "processor_function": "process_poi_lookup",
        "priority": 4,
        "enabled": True
    },

    "poi_detail_retrieval": {
        "action": "poi_detail_retrieval",
        "description": "Retrieve detailed information about specific POIs from accumulated state based on user descriptions",
        "when_to_use": "For queries asking for more details about previously found places, like 'give me more info about the place with nice view', 'tell me more about that cafe in Beyoğlu', 'what are the opening hours for the restaurant you mentioned?'",
        "parameters": ["prompt", "user_id", "session_id", "latitude", "longitude", "radius", "num_candidates"],
        "processor_function": "process_poi_detail_retrieval",
        "priority": 5,
        "enabled": True
    }
}


def get_process_metadata(process_name: str) -> Dict:
    """
    Get metadata for a specific process.

    Args:
        process_name: Name of the process

    Returns:
        Process metadata dictionary or empty dict if not found
    """
    return PROCESS_METADATA.get(process_name, {})


def get_all_process_metadata() -> Dict[str, Dict]:
    """
    Get all process metadata.

    Returns:
        Dictionary of all process metadata
    """
    return PROCESS_METADATA.copy()


def get_enabled_processes() -> Dict[str, Dict]:
    """
    Get only enabled process metadata.

    Returns:
        Dictionary of enabled process metadata
    """
    return {name: metadata for name, metadata in PROCESS_METADATA.items()
            if metadata.get("enabled", True)}


def add_process_metadata(process_name: str, metadata: Dict) -> None:
    """
    Add new process metadata.

    Args:
        process_name: Name of the process
        metadata: Process metadata dictionary
    """
    PROCESS_METADATA[process_name] = metadata


def disable_process(process_name: str) -> bool:
    """
    Disable a process.

    Args:
        process_name: Name of the process to disable

    Returns:
        True if process was found and disabled, False otherwise
    """
    if process_name in PROCESS_METADATA:
        PROCESS_METADATA[process_name]["enabled"] = False
        return True
    return False


def enable_process(process_name: str) -> bool:
    """
    Enable a process.

    Args:
        process_name: Name of the process to enable

    Returns:
        True if process was found and enabled, False otherwise
    """
    if process_name in PROCESS_METADATA:
        PROCESS_METADATA[process_name]["enabled"] = True
        return True
    return False
