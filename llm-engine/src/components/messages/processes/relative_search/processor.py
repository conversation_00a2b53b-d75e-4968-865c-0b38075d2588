"""
Relative Search Processor.

This process handles searches relative to previously found locations or user's current location.
It follows the standardized 7-step workflow for location-based searches.
"""

from typing import Dict, Any, Optional, List
from src.infrastructure.log.unified_logger import debug, info, error
from ..shared.base_processor import BaseProcessor
from ..shared.models import LocationContext, POIData, TopCandidates
from ..shared import (
    process_classification_response, find_top_candidates,
    get_poi_by_subcategories_with_user_distances
)
from .pre_classifier import get_available_categories_for_relative_search
from ...agents.classification import llm_api


class RelativeSearchProcessor(BaseProcessor):
    """
    Processor for relative search workflow.

    This processor handles searches relative to previously found locations or user's current coordinates,
    following the standardized 7-step process workflow.
    """

    def __init__(self):
        """Initialize the relative search processor."""
        super().__init__("RelativeSearch")

    def _do_initialize(self, **kwargs) -> None:
        """Perform relative search specific initialization."""
        # No specific initialization needed for relative search
        pass

    def get_process_info(self) -> Dict[str, Any]:
        """Get information about this process."""
        return {
            "name": "RelativeSearch",
            "description": "Search for places relative to previously found locations or user's current location",
            "workflow_steps": [
                "pre_classification",
                "llm_classification",
                "post_classification",
                "location_resolution",
                "poi_search",
                "top_candidates",
                "advice_generation"
            ],
            "search_method": "relative_search",
            "version": "2.0.0"
        }

    def _execute_workflow(
        self,
        prompt: str,
        latitude: float,
        longitude: float,
        radius: int,
        num_candidates: int,
        history: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the relative search workflow.

        Workflow Steps:
        1. Pre-Classifier: Get available categories around relative coordinates
        2. Classification Agent: LLM classification with available subcategories
        3. Post-Classifier: Parse response, handle errors
        4. Location Resolution: Skip (using provided coordinates)
        5. POI Search: Use provided coordinates with classified subcategories
        6. Top Candidates: Rank results by distance from coordinates
        7. Advice: Generate response with relative location context
        """

        # Step 1: Pre-Classifier - Get available categories around user coordinates
        debug("Step 1: Getting available categories around user coordinates")
        try:
            available_categories_str = get_available_categories_for_relative_search(
                latitude, longitude, radius
            )
        except ValueError as e:
            if str(e) == "no_categories_in_database":
                # No categories found in database - end process gracefully to trigger advice agent
                warning(
                    f"No categories found in database for relative search at ({latitude}, {longitude})")
                return {
                    "response": "",
                    "top_candidates": [],
                    "session_title": session_title or "",
                    "status_code": 40002,  # No data available status
                    "trigger_advice": True
                }
            else:
                raise  # Re-raise other ValueError types

        # Parse available subcategories
        from ...shared.utils import parse_available_categories
        available_subcategories = parse_available_categories(
            available_categories_str)

        # Step 2: Classification Agent - LLM classification using available subcategories
        debug(
            f"Step 2: LLM classification with {len(available_subcategories)} available subcategories")
        llm_response = llm_api(
            prompt, available_subcategories)
        debug(f"LLM classification response received")

        # Step 3: Post-Classifier - Parse classification response and handle errors
        debug("Step 3: Processing classification response")
        classification_result = process_classification_response(
            llm_response, prompt, available_subcategories, radius, session_title
        )

        # Check if classification failed
        if not classification_result.get("success", False):
            return classification_result  # Return error response

        # Check if no categories were found in database
        if classification_result.get("no_categories_found", False):
            debug(
                "No categories found in database - returning empty results for advice agent")
            # Return successful response with empty candidates to trigger advice agent
            return self._create_success_response(
                response_text="",  # Empty - orchestrator will call advice agent
                top_candidates=[],
                session_title=session_title,
                search_method="user_location",
                search_area=f"({latitude}, {longitude})",
                subcategories_found=[],
                poi_count=0,
                fallback_stage=0,
                search_params={"subcategories": [],
                               "tags": [], "radius": radius},
                original_subcategories=[],
                original_tags=[],
                no_categories_found=True  # Flag for orchestrator
            )

        classification_data = classification_result["classification_data"]
        categories = classification_data.get("categories", [])
        subcategories = classification_data["subcategories"]
        tags = classification_data.get("tags", [])
        use_fallback = classification_result.get("use_fallback", False)

        # Step 4: Location Resolution - Use provided coordinates (could be user or POI coordinates)
        debug(
            f"Step 4: Using provided coordinates for search: ({latitude}, {longitude})")
        # Use the coordinates passed to this process (could be user location or POI coordinates from previous task)
        search_latitude, search_longitude = latitude, longitude
        # For distance calculations, always use user coordinates from kwargs if available
        user_latitude = kwargs.get('user_latitude', latitude)
        user_longitude = kwargs.get('user_longitude', longitude)
        debug(
            f"Search coordinates: ({search_latitude}, {search_longitude}), User coordinates for distance: ({user_latitude}, {user_longitude})")

        # Step 5: POI Search - Use new fallback system with subcategories and tags
        debug(
            f"Step 5: Searching POIs around search location ({search_latitude}, {search_longitude})")
        debug(
            f"Search parameters: categories={list(categories)}, subcategories={list(subcategories)}, tags={list(tags)}, use_fallback={use_fallback}")

        # Use the new fallback system
        from ..shared.poi_manager import get_poi_by_subcategories_with_tags
        search_result = get_poi_by_subcategories_with_tags(
            user_lat=search_latitude,
            user_lon=search_longitude,
            radius_m=radius,
            search_subcategories=subcategories,
            tags=tags
        )

        poi_data = search_result['pois']
        stage_used = search_result['stage_used']
        search_params = search_result['search_params']

        search_description = f"at search location ({search_latitude}, {search_longitude}) using fallback stage {stage_used}: {search_params['description']}"
        info(
            f"POI search completed: {len(poi_data)} POIs found {search_description}")
        info(
            f"Final search parameters: subcategories={search_params['subcategories']}, tags={search_params['tags']}, radius={search_params['radius']}")

        # Step 6: Top Candidates - Find and rank POIs by distance from user
        debug(
            f"Step 6: Finding top {num_candidates} candidates from {len(poi_data)} POIs")
        top_candidates: TopCandidates = find_top_candidates(
            poi_data, user_latitude, user_longitude, radius, num_candidates
        ) if poi_data else []

        info(f"Top candidates found: {len(top_candidates)}")

        # Step 7: Advice - Generate final response with user location context
        debug(
            f"Step 7: Generating advice for {len(top_candidates)} candidates")
        # Import here to avoid circular dependency
        from ...agents.advice import get_location_advice
        advice_result = get_location_advice(
            prompt, history, top_candidates, latitude, longitude, radius,
            available_subcategories=available_subcategories,
            requested_subcategories=subcategories
        )

        # Extract response text from advice result
        from ...agents.shared import extract_response_text
        response_text = extract_response_text(advice_result)

        # Create success response with fallback information
        search_method = "poi_relative" if search_latitude != user_latitude or search_longitude != user_longitude else "user_location"
        return self._create_success_response(
            response_text=response_text,
            top_candidates=top_candidates,
            session_title=session_title,
            search_method=search_method,
            search_area=f"({search_latitude}, {search_longitude})",
            subcategories_found=search_params['subcategories'],
            poi_count=len(poi_data),
            fallback_stage=stage_used,
            search_params=search_params,
            original_subcategories=subcategories,
            original_tags=tags
        )


# Convenience function for direct usage
def process_relative_search(
    prompt: str,
    latitude: float,
    longitude: float,
    radius: int,
    num_candidates: int,
    history: Optional[str] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    session_title: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process relative search request.

    Args:
        prompt: The user's prompt/query
        latitude: User's latitude
        longitude: User's longitude
        radius: Search radius in meters
        num_candidates: Number of top candidates to return
        history: Optional conversation history
        user_id: Optional user ID for state management
        session_id: Optional session ID for state management
        session_title: Optional title for the session
        **kwargs: Additional parameters

    Returns:
        Dict containing response and any additional information
    """
    processor = RelativeSearchProcessor()
    return processor.process_request(
        prompt=prompt,
        latitude=latitude,
        longitude=longitude,
        radius=radius,
        num_candidates=num_candidates,
        history=history,
        user_id=user_id,
        session_id=session_id,
        session_title=session_title,
        **kwargs
    )
