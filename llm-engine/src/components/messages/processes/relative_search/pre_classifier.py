"""
Pre-classifier for relative search process.

This module handles getting available categories around relative coordinates
for the relative search process.
"""

from typing import List
from src.infrastructure.log.unified_logger import debug, info, warning
from ..shared.poi_manager import get_available_categories


def get_available_categories_for_relative_search(
    latitude: float,
    longitude: float,
    radius: int
) -> str:
    """
    Get available categories around relative coordinates.

    This is the process-specific pre-classification step for relative search.
    It retrieves categories available within the specified radius around the provided coordinates.

    Args:
        latitude: Relative latitude coordinate
        longitude: Relative longitude coordinate
        radius: Search radius in meters

    Returns:
        String containing available categories formatted for LLM classification

    Raises:
        ValueError: If no categories are found in the database for this location
    """
    debug(
        f"Getting available categories for relative location: ({latitude}, {longitude}) radius {radius}m")

    # Use the shared POI manager to get categories around user coordinates
    available_categories_str = get_available_categories(
        latitude, longitude, radius)

    # Check if any categories were found
    if not available_categories_str or available_categories_str.strip() == "":
        warning(
            f"No categories found in database for relative search at ({latitude}, {longitude}) radius {radius}m")
        raise ValueError("no_categories_in_database")

    info(
        f"Found categories for relative search at ({latitude}, {longitude})")

    return available_categories_str


def parse_available_categories_for_relative_search(categories_str: str) -> List[str]:
    """
    Parse available categories string into a list of subcategories for relative search.

    Args:
        categories_str: String containing available categories

    Returns:
        List of available subcategories
    """
    from ...shared.utils import parse_available_categories

    subcategories = parse_available_categories(categories_str)

    debug(f"Parsed {len(subcategories)} subcategories for relative search")

    return subcategories
