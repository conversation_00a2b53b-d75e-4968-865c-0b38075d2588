"""
Function Registry for Dynamic Process Discovery and Routing.

This module serves as the central discovery and routing hub for all processes
in the messages component. It automatically discovers processes, extracts metadata,
and provides dynamic registration for the orchestrator.
"""

import importlib
from typing import Dict, Optional, List, Callable
from dataclasses import dataclass
from src.infrastructure.log.unified_logger import debug, info, error
from .PROCESS_METADATA import get_all_process_metadata


@dataclass
class ProcessMetadata:
    """Metadata for a registered process."""
    action: str
    description: str
    when_to_use: str
    parameters: List[str]
    processor_module: str
    processor_function: str
    priority: int = 1
    enabled: bool = True


class FunctionRegistry:
    """
    Central registry for dynamic function discovery and routing.

    This class automatically discovers all processes in the processes/ directory,
    extracts metadata, and provides dynamic registration for the orchestrator.
    """

    _instance = None
    _registered_functions: Dict[str, ProcessMetadata] = {}
    _loaded_processors: Dict[str, Callable] = {}

    def __new__(cls):
        """Implement singleton pattern for FunctionRegistry."""
        if cls._instance is None:
            cls._instance = super(FunctionRegistry, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the function registry."""
        if not getattr(self, "_initialized", False):
            self._registered_functions = {}
            self._loaded_processors = {}
            self._initialized = True
            self._discover_processes()

    def _discover_processes(self) -> None:
        """
        Discover all processes using centralized metadata.
        """
        info("Discovering processes from centralized metadata")

        # Get all process metadata from the centralized registry
        all_metadata = get_all_process_metadata()

        for process_name, metadata in all_metadata.items():
            if metadata.get("enabled", True):
                self._register_process_from_metadata(process_name, metadata)
            else:
                debug(f"Skipping disabled process: {process_name}")

    def _register_process_from_metadata(self, process_name: str, metadata: Dict) -> None:
        """
        Register a process from centralized metadata.

        Args:
            process_name: Name of the process
            metadata: Process metadata dictionary
        """
        try:
            # Convert dict metadata to ProcessMetadata object
            process_metadata = ProcessMetadata(
                action=metadata["action"],
                description=metadata["description"],
                when_to_use=metadata["when_to_use"],
                parameters=metadata["parameters"],
                processor_module=f"src.components.messages.processes.{process_name}.processor",
                processor_function=metadata["processor_function"],
                priority=metadata.get("priority", 1),
                enabled=metadata.get("enabled", True)
            )

            self._registered_functions[process_name] = process_metadata
            info(f"Registered process: {process_name}")

        except Exception as e:
            error(f"Error registering process {process_name}: {e}")

    def get_registered_functions(self) -> Dict[str, ProcessMetadata]:
        """
        Get all registered functions.

        Returns:
            Dictionary of process names to metadata
        """
        return self._registered_functions.copy()

    def get_enabled_functions(self) -> Dict[str, ProcessMetadata]:
        """
        Get all enabled functions.

        Returns:
            Dictionary of enabled process names to metadata
        """
        return {
            name: metadata for name, metadata in self._registered_functions.items()
            if metadata.enabled
        }

    def get_function_metadata(self, function_name: str) -> Optional[ProcessMetadata]:
        """
        Get metadata for a specific function.

        Args:
            function_name: Name of the function

        Returns:
            ProcessMetadata if found, None otherwise
        """
        return self._registered_functions.get(function_name)

    def load_processor(self, function_name: str) -> Optional[Callable]:
        """
        Load and return a processor function.

        Args:
            function_name: Name of the function to load

        Returns:
            The processor function if found, None otherwise
        """
        # Check if already loaded
        if function_name in self._loaded_processors:
            return self._loaded_processors[function_name]

        # Get metadata
        metadata = self.get_function_metadata(function_name)
        if not metadata:
            error(f"No metadata found for function: {function_name}")
            return None

        try:
            # Import the module
            module = importlib.import_module(metadata.processor_module)

            # Get the function
            if hasattr(module, metadata.processor_function):
                processor_func = getattr(module, metadata.processor_function)
                self._loaded_processors[function_name] = processor_func
                debug(f"Loaded processor: {function_name}")
                return processor_func
            else:
                error(
                    f"Function {metadata.processor_function} not found in {metadata.processor_module}")
                return None

        except Exception as e:
            error(f"Error loading processor {function_name}: {e}")
            return None

    def register_function(self, name: str, metadata: ProcessMetadata) -> None:
        """
        Manually register a function.

        Args:
            name: Name of the function
            metadata: Process metadata
        """
        self._registered_functions[name] = metadata
        info(f"Manually registered function: {name}")

    def unregister_function(self, name: str) -> bool:
        """
        Unregister a function.

        Args:
            name: Name of the function to unregister

        Returns:
            True if unregistered, False if not found
        """
        if name in self._registered_functions:
            del self._registered_functions[name]
            if name in self._loaded_processors:
                del self._loaded_processors[name]
            info(f"Unregistered function: {name}")
            return True
        return False

    def generate_dynamic_prompt(self) -> str:
        """
        Generate dynamic prompt for orchestrator based on registered tools.

        Returns:
            String containing tool descriptions for LLM prompt
        """
        enabled_functions = self.get_enabled_functions()

        if not enabled_functions:
            return "No tools available."

        prompt_sections = []
        prompt_sections.append("AVAILABLE TOOLS:")

        # Sort by priority (higher priority first)
        sorted_functions = sorted(
            enabled_functions.items(),
            key=lambda x: x[1].priority,
            reverse=True
        )

        for tool_name, metadata in sorted_functions:
            prompt_sections.append(f"""
Tool: {tool_name}
Description: {metadata.description}
When to use: {metadata.when_to_use}
Parameters: {', '.join(metadata.parameters)}
Priority: {metadata.priority}
""")

        return "\n".join(prompt_sections)

    def get_tool_selection_guidance(self) -> str:
        """
        Generate guidance for tool selection based on registered tools.

        Returns:
            String containing tool selection guidance
        """
        enabled_functions = self.get_enabled_functions()

        guidance_sections = []
        guidance_sections.append("TOOL SELECTION GUIDANCE:")

        for tool_name, metadata in enabled_functions.items():
            guidance_sections.append(
                f"- Use {tool_name}: {metadata.when_to_use}")

        return "\n".join(guidance_sections)

    def reload_registry(self) -> None:
        """
        Reload the entire registry by rediscovering processes.
        """
        info("Reloading function registry")
        self._registered_functions.clear()
        self._loaded_processors.clear()
        self._discover_processes()


# Global registry instance
_registry = None


def get_function_registry() -> FunctionRegistry:
    """
    Get the global function registry instance.

    Returns:
        The function registry singleton
    """
    global _registry
    if _registry is None:
        _registry = FunctionRegistry()
    return _registry


def get_available_functions() -> Dict[str, ProcessMetadata]:
    """
    Get all available functions from the registry.

    Returns:
        Dictionary of available functions
    """
    registry = get_function_registry()
    return registry.get_enabled_functions()


def load_function(function_name: str) -> Optional[Callable]:
    """
    Load a function from the registry.

    Args:
        function_name: Name of the function to load

    Returns:
        The loaded function or None if not found
    """
    registry = get_function_registry()
    return registry.load_processor(function_name)
