"""
Pre-classifier for boundary search process.

This module handles getting available categories within administrative boundaries
for the boundary search process.
"""

from typing import List, Optional
from src.infrastructure.log.unified_logger import debug, info, warning
from ..shared.poi_manager import get_available_categories_by_location_context
from ..shared.models import LocationContext


def get_available_categories_for_boundary(
    location_context: LocationContext,
    radius: int
) -> str:
    """
    Get available categories within administrative boundaries.

    This is the process-specific pre-classification step for boundary search.
    It retrieves categories available within the boundary geometry of the resolved location.

    Args:
        location_context: Resolved location context with boundary information
        radius: Search radius in meters (used as fallback)

    Returns:
        String containing available categories formatted for LLM classification

    Raises:
        ValueError: If no categories are found in the database for this boundary
    """
    if not location_context.boundary_geom and not location_context.coordinates:
        warning(
            "No boundary geometry or coordinates available in location context for boundary search")
        raise ValueError("no_boundary_data")

    debug(f"Getting available categories for boundary search: "
          f"location='{location_context.name}' type='{location_context.type}'")

    # Use the shared POI manager to get categories within boundary
    available_categories_str = get_available_categories_by_location_context(
        location_context, radius
    )

    # Check if any categories were found
    if not available_categories_str or available_categories_str.strip() == "":
        warning(
            f"No categories found in database for boundary '{location_context.name}'")
        raise ValueError("no_categories_in_database")

    info(f"Found categories for boundary search within '{location_context.name}' "
         f"({location_context.type})")

    return available_categories_str


def parse_available_categories_for_boundary(categories_str: str) -> List[str]:
    """
    Parse available categories string into a list of subcategories for boundary search.

    Args:
        categories_str: String containing available categories

    Returns:
        List of available subcategories
    """
    from ...shared.utils import parse_available_categories

    subcategories = parse_available_categories(categories_str)

    debug(f"Parsed {len(subcategories)} subcategories for boundary search")

    return subcategories
