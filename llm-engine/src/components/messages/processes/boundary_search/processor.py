"""
Boundary Search Processor.

This process handles searches within administrative boundaries like districts or neighborhoods.
It follows the standardized 7-step workflow for location-based searches.
"""

from typing import Dict, Any, Optional, List
from src.infrastructure.log.unified_logger import debug, info, error, warning
from ..shared.base_processor import BaseProcessor
from ..shared.models import LocationContext, POIData, TopCandidates
from ..shared import (
    process_classification_response, find_top_candidates,
    get_poi_by_location_context, resolve_location
)
from .pre_classifier import get_available_categories_for_boundary
from ...agents.classification import llm_api


class BoundarySearchProcessor(BaseProcessor):
    """
    Processor for boundary search workflow.

    This processor handles searches within administrative boundaries,
    following the standardized 7-step process workflow.
    """

    def __init__(self):
        """Initialize the boundary search processor."""
        super().__init__("BoundarySearch")

    def _do_initialize(self, **kwargs) -> None:
        """Perform boundary search specific initialization."""
        # No specific initialization needed for boundary search
        pass

    def get_process_info(self) -> Dict[str, Any]:
        """Get information about this process."""
        return {
            "name": "BoundarySearch",
            "description": "Search for places within administrative boundaries",
            "workflow_steps": [
                "pre_classification",
                "llm_classification",
                "post_classification",
                "location_resolution",
                "poi_search",
                "top_candidates",
                "advice_generation"
            ],
            "search_method": "boundary",
            "version": "2.0.0"
        }

    def _execute_workflow(
        self,
        prompt: str,
        latitude: float,
        longitude: float,
        radius: int,
        num_candidates: int,
        history: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the boundary search workflow.

        Workflow Steps:
        1. Pre-Classifier: Get available categories within boundary area
        2. Classification Agent: LLM classification with available subcategories
        3. Post-Classifier: Parse response, handle errors
        4. Location Resolution: Resolve boundary name to geometry
        5. POI Search: Use boundary geometry for search
        6. Top Candidates: Rank results by distance from user
        7. Advice: Generate response with boundary context
        """

        # Extract boundary_name from kwargs
        boundary_name = kwargs.get('boundary_name')
        if not boundary_name:
            error("BoundarySearch error: boundary_name parameter is required")
            return {
                "response": "I need a location name to search within. Please specify a district, neighborhood, or area name.",
                "top_candidates": [],
                "session_title": session_title or "",
                "status_code": 40001
            }

        # Preserve original user coordinates for distance calculations
        user_latitude, user_longitude = latitude, longitude

        # Step 1: Pre-Classifier - Get available categories within boundary
        debug("Step 1: Getting available categories within boundary")
        # For boundary search, we need to resolve the boundary first to get proper categories
        from ..shared.location_resolver import resolve_location
        location_context = resolve_location(boundary_name)

        if not location_context or not location_context.boundary_geom:
            # If boundary resolution fails, this process cannot continue
            warning(f"Could not resolve boundary: {boundary_name}")
            return {
                "response": f"I couldn't find the boundary '{boundary_name}'. Please check the location name and try again.",
                "top_candidates": [],
                "session_title": session_title or "",
                "status_code": 40001
            }

        try:
            available_categories_str = get_available_categories_for_boundary(
                location_context, radius
            )
        except ValueError as e:
            if str(e) == "no_categories_in_database":
                # No categories found in database - end process gracefully to trigger advice agent
                warning(
                    f"No categories found in database for boundary '{boundary_name}'")
                return {
                    "response": "",
                    "top_candidates": [],
                    "session_title": session_title or "",
                    "status_code": 40002,  # No data available status
                    "trigger_advice": True
                }
            elif str(e) == "no_boundary_data":
                # No boundary data available
                warning(f"No boundary data available for '{boundary_name}'")
                return {
                    "response": f"I couldn't find boundary data for '{boundary_name}'. Please check the location name and try again.",
                    "top_candidates": [],
                    "session_title": session_title or "",
                    "status_code": 40001
                }
            else:
                raise  # Re-raise other ValueError types

        # Parse available subcategories
        from ...shared.utils import parse_available_categories
        available_subcategories = parse_available_categories(
            available_categories_str)

        # Step 2: Classification Agent - LLM classification using available subcategories
        debug(
            f"Step 2: LLM classification with {len(available_subcategories)} available subcategories")
        llm_response = llm_api(
            prompt, available_subcategories)
        debug(f"LLM classification response received")

        # Step 3: Post-Classifier - Parse classification response and handle errors
        debug("Step 3: Processing classification response")
        classification_result = process_classification_response(
            llm_response, prompt, available_subcategories, radius, session_title
        )

        # Check if classification failed
        if not classification_result.get("success", False):
            return classification_result  # Return error response

        classification_data = classification_result["classification_data"]
        subcategories = classification_data["subcategories"]
        location_name = classification_data.get("location_name")
        search_method = classification_data.get("search_method", "boundary")

        # PRIORITY: Use boundary_name from main LLM if provided
        if kwargs.get("boundary_name"):
            location_name = kwargs["boundary_name"]
            search_method = "boundary"  # Force boundary search when boundary_name is provided
            debug(f"Using boundary_name from main LLM: '{location_name}'")

        # Step 4: Location Resolution - Resolve boundary name to geometry
        location_result: Optional[LocationContext] = None
        # Default to user coordinates
        search_latitude, search_longitude = latitude, longitude

        if (search_method == "location_search" or search_method == "boundary") and location_name:
            debug(f"Step 4: Resolving boundary location '{location_name}'")
            location_result = resolve_location(location_name)

            # Update search method and coordinates based on resolution
            search_method = location_result.search_method
            location_type = location_result.type

            if location_result.coordinates:
                # Use resolved coordinates for search area
                search_latitude, search_longitude = location_result.coordinates
                debug(f"Boundary resolved to coordinates: ({search_latitude}, {search_longitude}), "
                      f"search_method: {search_method}, type: {location_type}")

                # Update available categories based on resolved boundary
                if search_method == "boundary" and location_result.boundary_geom:
                    debug("Updating available categories for resolved boundary")
                    available_categories_str = get_available_categories_for_boundary(
                        location_result, radius
                    )
                elif search_method == "point_radius":
                    debug("Using point radius fallback for boundary search")
                    available_categories_str = get_available_categories_for_boundary_fallback(
                        search_latitude, search_longitude, radius, location_name
                    )
            else:
                debug(
                    f"Boundary resolution failed, using original coordinates: ({latitude}, {longitude})")
                location_type = "none"
                search_method = "boundary"  # Keep boundary method but use fallback
        else:
            debug("Step 4: No boundary resolution needed, using user coordinates")
            location_type = "none"
            search_method = "boundary"

        # Step 5: POI Search - Use boundary geometry or fallback to coordinate search
        debug(f"Step 5: Searching POIs using {search_method} method")

        if location_name and location_result and search_method == "boundary" and location_result.boundary_geom:
            # Use boundary geometry-based search
            poi_data: List[POIData] = get_poi_by_location_context(
                location_result, subcategories, radius
            )
            search_description = f"within {location_name} boundary ({location_type})"
        else:
            # Use coordinate-based search as fallback
            from ..shared import get_poi_by_subcategories_with_user_distances
            poi_data: List[POIData] = get_poi_by_subcategories_with_user_distances(
                search_lat=search_latitude,
                search_lon=search_longitude,
                user_lat=user_latitude,
                user_lon=user_longitude,
                radius_m=radius,
                search_subcategories=subcategories
            )
            boundary_desc = f"near {location_name}" if location_name else "boundary area"
            search_description = f"around {boundary_desc} at ({search_latitude}, {search_longitude}) radius {radius}m"

        info(
            f"POI search completed: {len(poi_data)} POIs found for subcategories {subcategories} {search_description}")

        # Step 6: Top Candidates - Find and rank POIs by distance from user
        debug(
            f"Step 6: Finding top {num_candidates} candidates from {len(poi_data)} POIs")
        top_candidates: TopCandidates = find_top_candidates(
            poi_data, user_latitude, user_longitude, radius, num_candidates
        ) if poi_data else []

        info(f"Top candidates found: {len(top_candidates)}")

        # Step 7: Advice - Generate final response with boundary context
        debug(
            f"Step 7: Generating advice for {len(top_candidates)} candidates")
        # Import here to avoid circular dependency
        from ...agents.advice import get_location_advice
        advice_result = get_location_advice(
            prompt, history, top_candidates, search_latitude, search_longitude, radius,
            available_subcategories=available_subcategories,
            requested_subcategories=subcategories
        )

        # Extract response text from advice result
        from ...agents.shared import extract_response_text
        response_text = extract_response_text(advice_result)

        # Create success response
        return self._create_success_response(
            response_text=response_text,
            top_candidates=top_candidates,
            session_title=session_title,
            search_method=search_method,
            search_area=f"({search_latitude}, {search_longitude})",
            boundary_name=location_name,
            boundary_type=location_type if location_result else "none",
            subcategories_found=subcategories,
            poi_count=len(poi_data)
        )


# Convenience function for direct usage
def process_boundary_search(
    prompt: str,
    latitude: float,
    longitude: float,
    radius: int,
    num_candidates: int,
    boundary_name: Optional[str] = None,
    history: Optional[str] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    session_title: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process boundary search request.

    Args:
        prompt: The user's prompt/query
        latitude: User's latitude (for distance calculations)
        longitude: User's longitude (for distance calculations)
        radius: Search radius in meters
        num_candidates: Number of top candidates to return
        boundary_name: Optional boundary name to search within
        history: Optional conversation history
        user_id: Optional user ID for state management
        session_id: Optional session ID for state management
        session_title: Optional title for the session
        **kwargs: Additional parameters

    Returns:
        Dict containing response and any additional information
    """
    processor = BoundarySearchProcessor()
    return processor.process_request(
        prompt=prompt,
        latitude=latitude,
        longitude=longitude,
        radius=radius,
        num_candidates=num_candidates,
        history=history,
        user_id=user_id,
        session_id=session_id,
        session_title=session_title,
        boundary_name=boundary_name,
        **kwargs
    )
