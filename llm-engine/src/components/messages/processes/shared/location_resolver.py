"""
Location Resolver Service

This module provides location name resolution functionality for Phase 2 of the spatial database integration.
It converts location names (districts, neighborhoods, POIs) to coordinates and determines the appropriate search method.
"""

from typing import Optional, Tuple
from psycopg2.extras import RealDictCursor

from src.spatial_db.utils.db_connection import get_spatial_db_connection
from src.infrastructure.log.unified_logger import info, warning, error
from src.utils import timing_decorator
from .models import LocationContext


def normalize_turkish_location(location_name: str) -> str:
    """
    Normalize Turkish location names by converting ASCII to Turkish characters.

    Args:
        location_name: Location name that might have ASCII characters

    Returns:
        Normalized location name with proper Turkish characters
    """
    if not location_name:
        return location_name

    # Common Turkish location normalizations
    normalizations = {
        'buyukcekmece': 'Büyükçekmece',
        'besiktas': 'Beşiktaş',
        'beyoglu': 'Beyoğlu',
        'uskudar': 'Üsküdar',
        'kadikoy': 'Kadıköy',
        'ortakoy': 'Ortaköy',
        'arnavutkoy': 'Arnavutköy',
        'sariyer': 'Sar<PERSON>yer',
        'sisli': '<PERSON><PERSON><PERSON><PERSON>',
        'nisantasi': 'Nişantaşı',
        'galatasaray': 'Galatasaray',
        'sultanahmet': 'Sultanahmet',
        'taksim': 'Taksim',
        'galata': 'Galata',
        'bebek': 'Bebek',
        'levent': 'Levent',
        'maslak': 'Maslak',
        'etiler': 'Etiler',
        'fatih': 'Fatih'
    }

    location_lower = location_name.lower().strip()

    # Check for exact matches first
    if location_lower in normalizations:
        return normalizations[location_lower]

    # If no exact match, return title case version
    return location_name.title()


class LocationResolver:
    """
    Location Resolver Service for converting location names to coordinates and search methods.

    This service supports Phase 2 location name search functionality by:
    1. Resolving district/neighborhood names using admin_boundaries table
    2. Resolving POI/landmark names using pois table
    3. Falling back to external geocoding services when needed
    4. Determining appropriate search method (boundary vs point_radius)
    """

    def __init__(self):
        """Initialize the location resolver."""
        pass

    @timing_decorator
    def resolve_location(self, location_name: Optional[str]) -> LocationContext:
        """
        Resolve a location name to coordinates and search method using simplified approach.

        This new approach searches across ALL locations (admin boundaries + well-known places)
        without filtering by type, letting the database return the best match.

        Args:
            location_name: Name of the location to resolve

        Returns:
            LocationContext with resolved coordinates and search method
        """
        if not location_name:
            return LocationContext(
                type="none",
                name=None,
                search_method="relative_search"
            )

        # Normalize Turkish characters before searching
        normalized_location = normalize_turkish_location(location_name)
        if normalized_location != location_name:
            info(
                f"Normalized location name: '{location_name}' -> '{normalized_location}'")

        # Try to resolve from our spatial database first (unified search)
        return self._resolve_unified_location(normalized_location)

    def _resolve_unified_location(self, location_name: str) -> LocationContext:
        """
        Resolve location using unified search across admin boundaries, POIs, and address parsing.

        This method searches across ALL entries without filtering by level or type,
        letting the database return the best match.

        Args:
            location_name: Name of the location to resolve

        Returns:
            LocationContext with resolved coordinates and search method
        """
        # First try admin boundaries (districts, neighborhoods)
        admin_result = self._try_admin_boundaries_search(location_name)
        if admin_result:
            return admin_result

        # Then try POI search (for specific places like cafes, restaurants)
        poi_result = self._try_poi_search(location_name)
        if poi_result:
            return poi_result

        # If both fail, fallback to user location
        warning(f"Location not found in spatial database: {location_name}")
        return self._fallback_to_user_location(location_name)

    def _try_admin_boundaries_search(self, location_name: str) -> Optional[LocationContext]:
        """Try to find location in admin_boundaries table with improved matching."""
        try:
            conn = get_spatial_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Enhanced search with both exact and partial matching
            # Priority: exact match first, then partial match
            query = """
                SELECT id, name, name_en, name_tr, admin_level, well_known_level, place_type,
                       latitude, longitude, ST_AsGeoJSON(geom) as geometry_json
                FROM spatial_schema.admin_boundaries
                WHERE
                    LOWER(name) = LOWER(%s) OR
                    LOWER(name_en) = LOWER(%s) OR
                    LOWER(name_tr) = LOWER(%s) OR
                    LOWER(name) LIKE LOWER(%s) OR
                    LOWER(name_en) LIKE LOWER(%s) OR
                    LOWER(name_tr) LIKE LOWER(%s)
                ORDER BY
                    CASE
                        WHEN LOWER(name) = LOWER(%s) THEN 1
                        WHEN LOWER(name_en) = LOWER(%s) THEN 1
                        WHEN LOWER(name_tr) = LOWER(%s) THEN 1
                        ELSE 2
                    END,
                    admin_level DESC
                LIMIT 1
            """

            # Parameters: exact matches (3x), partial matches (3x), priority sorting (3x)
            params = [
                location_name, location_name, location_name,  # exact matches
                # partial matches
                f'%{location_name}%', f'%{location_name}%', f'%{location_name}%',
                location_name, location_name, location_name  # priority sorting
            ]

            cursor.execute(query, params)
            result = cursor.fetchone()

            cursor.close()
            conn.close()

            if result:
                lat = float(result['latitude'])
                lon = float(result['longitude'])

                # Simplified search method determination: use boundary if geometry exists
                if result['geometry_json']:
                    search_method = "boundary"
                    boundary_geom = result['geometry_json']
                else:
                    search_method = "point_radius"
                    boundary_geom = None

                # Determine location type
                if result['well_known_level'] is not None:
                    location_type = "well_known_place"
                elif result['admin_level'] is not None and result['admin_level'] in [4, 6]:
                    location_type = "district"
                else:
                    location_type = "neighborhood"

                info(
                    f"Admin boundary resolution successful for {location_name}: found '{result['name']}' at ({lat}, {lon}) - {location_type}, using {search_method}")

                return LocationContext(
                    type=location_type,
                    # Use the actual found name, not the search term
                    name=result['name'],
                    search_method=search_method,
                    coordinates=(lat, lon),
                    boundary_geom=boundary_geom
                )

            return None

        except Exception as e:
            error(f"Error in admin boundaries search for {location_name}: {e}")
            return None

    def _try_poi_search(self, location_name: str) -> Optional[LocationContext]:
        """Try to find location in POIs table, including parsing complex address strings."""
        try:
            conn = get_spatial_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Extract POI name from complex address strings
            # e.g., "Dilek Pastane&Cafe, Mustafa Kemal Bulvarı, ..." -> "Dilek Pastane&Cafe"
            poi_name = location_name.split(',')[0].strip()

            # Search POIs table for exact match first, then partial match
            query = """
                SELECT id, name, name_en, name_tr, latitude, longitude, category, subcategory
                FROM spatial_schema.pois
                WHERE
                    LOWER(name) = LOWER(%s) OR
                    LOWER(name_en) = LOWER(%s) OR
                    LOWER(name_tr) = LOWER(%s) OR
                    LOWER(name) LIKE LOWER(%s) OR
                    LOWER(name_en) LIKE LOWER(%s) OR
                    LOWER(name_tr) LIKE LOWER(%s)
                ORDER BY
                    CASE
                        WHEN LOWER(name) = LOWER(%s) THEN 1
                        WHEN LOWER(name_en) = LOWER(%s) THEN 1
                        WHEN LOWER(name_tr) = LOWER(%s) THEN 1
                        ELSE 2
                    END
                LIMIT 1
            """

            like_pattern = f"%{poi_name}%"
            cursor.execute(query, (
                poi_name, poi_name, poi_name,  # Exact matches
                like_pattern, like_pattern, like_pattern,  # Partial matches
                poi_name, poi_name, poi_name  # For ordering
            ))
            result = cursor.fetchone()

            cursor.close()
            conn.close()

            if result:
                lat = float(result['latitude'])
                lon = float(result['longitude'])

                info(
                    f"POI resolution successful for {location_name}: Found {result['name']} at ({lat}, {lon})")

                return LocationContext(
                    type="poi",
                    name=result['name'],
                    search_method="point_radius",
                    coordinates=(lat, lon)
                )

            return None

        except Exception as e:
            error(f"Error in POI search for {location_name}: {e}")
            return None

    def _resolve_address(self, address: str) -> LocationContext:
        """
        Resolve address (not supported in database-only mode).

        Args:
            address: Address string to resolve

        Returns:
            LocationContext with fallback to user location
        """
        # Addresses are not supported in database-only mode
        warning(
            f"Address resolution not supported in database-only mode: {address}")
        return self._fallback_to_user_location(address)

    def _fallback_to_user_location(self, location_name: str) -> LocationContext:
        """
        Final fallback to user location when all resolution methods fail.

        Args:
            location_name: Name of the location that couldn't be resolved

        Returns:
            LocationContext indicating to use user location
        """
        warning(
            f"Could not resolve location '{location_name}', falling back to user location")
        return LocationContext(
            type="none",
            name=None,
            search_method="user_location"
        )


# Convenience function for direct usage
@timing_decorator
def resolve_location(location_name: Optional[str]) -> LocationContext:
    """
    Convenience function to resolve a location.

    Args:
        location_name: Name of the location to resolve

    Returns:
        LocationContext with resolved information
    """
    resolver = LocationResolver()
    return resolver.resolve_location(location_name)
