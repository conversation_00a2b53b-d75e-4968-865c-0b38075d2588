"""
Shared data models for the messages component processes.

This module contains data models used across different processes,
including location context and POI data structures.
"""

from dataclasses import dataclass, field
from typing import Optional, Tuple, Dict, Any, List
from uuid import uuid4
from datetime import datetime
from src.models import POIData, TopCandidates


@dataclass
class LocationContext:
    """Data class for location context information."""
    type: str  # district, neighborhood, poi, address, none
    name: Optional[str]
    search_method: str  # boundary, point_radius, user_location
    coordinates: Optional[Tuple[float, float]] = None  # (lat, lon)
    boundary_geom: Optional[str] = None  # GeoJSON for boundary searches


@dataclass
class Task:
    """Represents a single task in the multi-task orchestrator."""
    task_id: str
    # "boundary_search", "relative_search", etc.
    tool_name: str
    parameters: Dict[str, Any]        # Tool-specific parameters
    # List of task_ids this task depends on
    dependencies: List[str] = field(default_factory=list)
    sub_request: str = ""             # Original sub-request text
    created_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """Ensure task_id is set if not provided."""
        if not self.task_id:
            self.task_id = str(uuid4())


@dataclass
class TaskResult:
    """Represents the result of executing a task."""
    task: Task
    result: Optional[Dict[str, Any]] = None     # Tool execution result
    error: Optional[str] = None                 # Error message if failed
    success: bool = True                        # Execution success status
    execution_time: float = 0.0                 # Execution time in seconds
    started_at: Optional[datetime] = None       # When execution started
    completed_at: Optional[datetime] = None     # When execution completed

    @property
    def top_candidates(self) -> List[POIData]:
        """Extract top_candidates from result if available."""
        if self.success and self.result and 'top_candidates' in self.result:
            return self.result['top_candidates']
        return []


@dataclass
class ExecutionPlan:
    """Represents an execution plan for multiple tasks."""
    parallel_groups: List[List[Task]] = field(
        default_factory=list)  # Groups of tasks that can run in parallel
    # Chains of tasks that must run sequentially
    sequential_chains: List[List[Task]] = field(default_factory=list)
    total_tasks: int = 0
    estimated_execution_time: float = 0.0

    def __post_init__(self):
        """Calculate total tasks."""
        self.total_tasks = sum(len(group) for group in self.parallel_groups) + \
            sum(len(chain) for chain in self.sequential_chains)


# Re-export commonly used models from src.models for convenience
__all__ = [
    'LocationContext',
    'POIData',
    'TopCandidates',
    'Task',
    'TaskResult',
    'ExecutionPlan',
]
