#!/usr/bin/env python3
"""
Top Candidates Finder Interface.

This module defines the interface for top candidates finder implementations.
"""

from abc import ABC, abstractmethod
from typing import List
from src.models import POIData, TopCandidates


class ITopCandidatesFinder(ABC):
    """
    Interface for Top Candidates Finder implementations.
    
    This interface defines the contract that all top candidates finder implementations
    must follow for consistent behavior across different routing engines.
    """
    
    @abstractmethod
    def find_top_candidates(self, candidates: List[POIData], user_lat: float, user_lon: float,
                            radius_m: int, n: int) -> TopCandidates:
        """
        Finds top POIs within radius by computing true route distances for drive and walk.

        Args:
            candidates: List of POI data
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters
            n: Number of top candidates to return

        Returns:
            A list of top candidates with NaN values removed
        """
        pass
