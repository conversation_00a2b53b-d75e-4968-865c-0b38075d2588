#!/usr/bin/env python3
"""
POI Manager Interface.

This module defines the interface for POI (Point of Interest) managers.
"""

from abc import ABC, abstractmethod
from typing import List
from src.models import POIData


class IPOIManager(ABC):
    """
    Interface for POI Manager implementations.
    
    This interface defines the contract that all POI manager implementations
    must follow for consistent behavior across different data sources.
    """
    
    @abstractmethod
    def get_poi_by_subcategories(self, user_lat: float, user_lon: float, radius_m: int,
                                 search_subcategories: List[str]) -> List[POIData]:
        """
        Finds and returns POIs (Points of Interest) that match the specified subcategories
        and are located within a geographic radius from the user's coordinates.

        Args:
            user_lat (float): User's latitude.
            user_lon (float): User's longitude.
            radius_m (int): Search radius in meters.
            search_subcategories (List[str]): Subcategories to filter by (e.g., ['cafe', 'museum']).

        Returns:
            List[POIData]: List of matching POIs as POIData objects.
        """
        pass
    
    @abstractmethod
    def get_available_categories(self, user_lat: float, user_lon: float, radius_m: int) -> str:
        """
        Scans all POIs within a specified radius from the user's location and summarizes
        which categories and subcategories are available.

        Args:
            user_lat (float): User's latitude.
            user_lon (float): User's longitude.
            radius_m (int): Search radius in meters.

        Returns:
            str: A multi-line string mapping categories to subcategories (e.g., "Food: Cafe, Bakery").
        """
        pass
