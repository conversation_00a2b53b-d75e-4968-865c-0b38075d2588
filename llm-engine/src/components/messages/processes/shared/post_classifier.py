"""
Shared post-classification utilities for processes.

This module provides shared utilities for parsing and validating
classification responses from the LLM, handling errors consistently
across all processes.
"""

from typing import Dict, Any, List, Optional, Union
from src.infrastructure.log.unified_logger import info, warning, error
from ...shared.utils import (
    parse_classification_response, format_response_with_session,
    CLASSIFICATION_NO_SUBCATEGORIES
)


def process_classification_response(
    llm_response: Union[Dict[str, Any], str],
    prompt: str,
    available_subcategories: List[str],
    radius: int,
    session_title: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process and validate classification response from LLM with fallback support.

    Args:
        llm_response: The raw LLM response
        prompt: The original user prompt
        available_subcategories: List of available subcategories for the area
        radius: Search radius for error context
        session_title: Optional session title

    Returns:
        Either parsed classification data or error response
    """
    # Parse the classification response
    classification_data = parse_classification_response(llm_response)

    # Check if parsing failed
    if "error" in classification_data:
        error(
            f"Failed to parse classification response: {classification_data['error']}")
        return {
            "success": False,
            "response": "",
            "top_candidates": [],
            "session_title": session_title or "",
            "status_code": 40002,  # No data available status
            "trigger_advice": True,
            "error": "classification_parse_error",
            "message": "Failed to parse classification response"
        }

    # Extract tags, categories, and location name from classification data
    tags = extract_tags(classification_data)
    categories = extract_categories(classification_data)
    location_name = extract_location_name(classification_data)

    # Check if subcategories were found
    subcategories = classification_data.get("subcategories", [])

    # Check if we have valid classification results
    if not subcategories and not tags:
        info(
            f"[LOCATION_NO_SUBCATEGORIES_OR_TAGS] No subcategories or tags found by LLM for prompt: {prompt}")
        # Return SUCCESS response with empty candidates to trigger advice agent
        return {
            "success": True,  # Mark as success so orchestrator processes it
            "subcategories": [],
            "tags": [],
            "location_name": location_name,
            "no_categories_found": True,  # Flag for processors to handle
            "message": "No valid categories or tags found for this request."
        }

    # Validate subcategories against available ones (if any provided)
    valid_subcategories = []
    if subcategories:
        # Create a case-insensitive mapping of available subcategories
        available_subcategories_lower = {
            subcat.lower(): subcat for subcat in available_subcategories}

        for subcat in subcategories:
            # Try exact match first
            if subcat in available_subcategories:
                valid_subcategories.append(subcat)
            # Then try case-insensitive match
            elif subcat.lower() in available_subcategories_lower:
                # Use the correctly cased version from the database
                correct_case_subcat = available_subcategories_lower[subcat.lower(
                )]
                valid_subcategories.append(correct_case_subcat)
                info(
                    f"Matched subcategory '{subcat}' to '{correct_case_subcat}' (case-insensitive)")
            else:
                warning(
                    f"Invalid subcategory '{subcat}' not in available list")

    # Update classification data with validated subcategories, tags, and categories
    classification_data["categories"] = categories
    classification_data["subcategories"] = valid_subcategories
    classification_data["tags"] = tags

    # Determine if we should use fallback
    # Use fallback if no valid subcategories
    use_fallback = not valid_subcategories

    return {
        "success": True,
        "classification_data": classification_data,
        "use_fallback": use_fallback
    }


def _create_classification_error_response(
    prompt: str,
    available_subcategories: List[str],
    radius: int,
    session_title: Optional[str],
    error_message: str
) -> Dict[str, Any]:
    """
    Create a standardized error response for classification failures.

    Args:
        prompt: The original user prompt
        available_subcategories: List of available subcategories
        radius: Search radius
        session_title: Optional session title
        error_message: Specific error message

    Returns:
        Standardized error response
    """
    error_context = {
        "available_categories": available_subcategories,
        "user_request": prompt,
        "radius": radius,
        "error_message": error_message
    }

    return format_response_with_session(
        status_code=CLASSIFICATION_NO_SUBCATEGORIES,
        response_text="",  # Will be generated by error handler
        top_candidates=[],
        session_title=session_title,
        error_context=error_context
    )


def validate_search_method(search_method: str) -> str:
    """
    Validate and normalize search method.

    Args:
        search_method: The search method from classification

    Returns:
        Validated search method
    """
    valid_methods = ["user_location",
                     "location_search", "boundary", "point_radius"]

    if search_method not in valid_methods:
        warning(
            f"Invalid search method '{search_method}', defaulting to 'user_location'")
        return "user_location"

    return search_method


def extract_location_name(classification_data: Dict[str, Any]) -> Optional[str]:
    """
    Extract and validate location name from classification data.

    Args:
        classification_data: Parsed classification data

    Returns:
        Validated location name or None
    """
    location_name = classification_data.get("location_name")

    if location_name and isinstance(location_name, str):
        location_name = location_name.strip()
        if location_name:
            return location_name

    return None


def extract_tags(classification_data: Dict[str, Any]) -> List[str]:
    """
    Extract and validate tags from classification data.

    Args:
        classification_data: Parsed classification data

    Returns:
        List of validated tags
    """
    # Extract from tags field (now a simple array)
    tags_data = classification_data.get("tags", [])

    # Handle both old format (dict) and new format (list)
    if isinstance(tags_data, dict):
        # Old format: {"existed": [...], "new": [...]}
        tags = []
        existed_tags = tags_data.get("existed", [])
        new_tags = tags_data.get("new", [])

        if isinstance(existed_tags, list):
            tags.extend(existed_tags)
        if isinstance(new_tags, list):
            tags.extend(new_tags)
    elif isinstance(tags_data, list):
        # New format: ["tag1", "tag2", ...]
        tags = tags_data
    else:
        tags = []

    # Clean and validate tags
    clean_tags = []
    for tag in tags:
        if isinstance(tag, str):
            tag = tag.strip()
            if tag and len(tag) > 1:  # Ignore single character tags
                clean_tags.append(tag)

    return clean_tags


def extract_categories(classification_data: Dict[str, Any]) -> List[str]:
    """
    Extract and validate categories from classification data.

    Args:
        classification_data: Parsed classification data

    Returns:
        List of validated categories
    """
    categories_data = classification_data.get("categories", [])

    if not isinstance(categories_data, list):
        return []

    # Clean and validate categories
    clean_categories = []
    for category in categories_data:
        if isinstance(category, str):
            category = category.strip()
            if category:
                clean_categories.append(category)

    return clean_categories
