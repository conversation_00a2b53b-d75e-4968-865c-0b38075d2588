"""
Base processor framework for standardized process workflows.

This module provides a base class and common patterns that all processes
should follow for consistency, error handling, and response standardization.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ...shared.utils import (
    normalize_user_and_session_ids, setup_session_logging, validate_coordinates,
    create_coordinate_error_response, LOCATION_SUCCESS, LOCATION_TIMEOUT
)


class BaseProcessor(ABC):
    """
    Base class for all process implementations.

    This class provides common functionality and standardized workflow patterns
    that all processes should follow for consistency.
    """

    def __init__(self, process_name: str):
        """
        Initialize the base processor.

        Args:
            process_name: Name of the process for logging purposes
        """
        self.process_name = process_name
        self._initialized = False

    def initialize(self, **kwargs) -> None:
        """
        Initialize the processor with configuration.

        Args:
            **kwargs: Process-specific configuration parameters
        """
        if self._initialized:
            debug(f"{self.process_name} already initialized")
            return

        self._do_initialize(**kwargs)
        self._initialized = True
        info(f"{self.process_name} initialized successfully")

    @abstractmethod
    def _do_initialize(self, **kwargs) -> None:
        """
        Perform process-specific initialization.

        Args:
            **kwargs: Process-specific configuration parameters
        """
        pass

    def process_request(
        self,
        prompt: str,
        latitude: Optional[float],
        longitude: Optional[float],
        radius: int,
        num_candidates: int,
        history: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a request using the standardized workflow.

        Args:
            prompt: The user's prompt/query
            latitude: User's latitude (can be None)
            longitude: User's longitude (can be None)
            radius: Search radius in meters
            num_candidates: Number of top candidates to return
            history: Optional conversation history
            user_id: Optional user ID for state management
            session_id: Optional session ID for state management
            session_title: Optional title for the session
            **kwargs: Additional process-specific parameters

        Returns:
            Dict containing response and any additional information
        """
        try:
            # Step 1: Validate session information (don't generate new IDs if already provided)
            if not user_id or not session_id:
                # Only normalize if missing - this should rarely happen in normal API flow
                user_id, session_id = normalize_user_and_session_ids(
                    user_id, session_id)
                debug(
                    f"Generated session info: user_id={user_id}, session_id={session_id}")
            else:
                debug(
                    f"Using provided session info: user_id={user_id}, session_id={session_id}")

            # Step 2: Set up logging context
            setup_session_logging(user_id, session_id)

            # Step 3: Validate coordinates
            if not validate_coordinates(latitude, longitude):
                error(
                    f"{self.process_name} called with invalid coordinates: lat={latitude}, lon={longitude}")
                return create_coordinate_error_response(latitude, longitude, session_title)

            # Step 4: Log the request
            self.log_request(prompt, latitude, longitude,
                             radius, num_candidates, **kwargs)

            # Step 5: Execute the process-specific workflow
            result = self._execute_workflow(
                prompt=prompt,
                latitude=latitude,
                longitude=longitude,
                radius=radius,
                num_candidates=num_candidates,
                history=history,
                user_id=user_id,
                session_id=session_id,
                session_title=session_title,
                **kwargs
            )

            # Step 6: Log the response
            self.log_response(result)

            return result

        except Exception as e:
            error(f"{self.process_name} error: {str(e)}")
            return self._create_error_response(str(e), session_title)

    @abstractmethod
    def _execute_workflow(
        self,
        prompt: str,
        latitude: float,
        longitude: float,
        radius: int,
        num_candidates: int,
        history: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the process-specific workflow.

        Args:
            prompt: The user's prompt/query
            latitude: User's latitude
            longitude: User's longitude
            radius: Search radius in meters
            num_candidates: Number of top candidates to return
            history: Optional conversation history
            user_id: User ID for state management
            session_id: Session ID for state management
            session_title: Optional title for the session
            **kwargs: Additional process-specific parameters

        Returns:
            Dict containing response and any additional information
        """
        pass

    def log_request(self, prompt: str, latitude: float, longitude: float,
                    radius: int, num_candidates: int, **kwargs) -> None:
        """
        Log a process request for debugging.

        Args:
            prompt: The user's prompt
            latitude: User's latitude
            longitude: User's longitude
            radius: Search radius
            num_candidates: Number of candidates requested
            **kwargs: Additional parameters
        """
        debug(f"{self.process_name} request: prompt='{prompt}', "
              f"coords=({latitude}, {longitude}), radius={radius}, "
              f"candidates={num_candidates}, kwargs={kwargs}")

    def log_response(self, response: Dict[str, Any]) -> None:
        """
        Log a process response for debugging.

        Args:
            response: The response dictionary
        """
        status_code = response.get('status_code', 'unknown')
        candidates_count = len(response.get('top_candidates', []))
        debug(f"{self.process_name} response: status={status_code}, "
              f"candidates={candidates_count}")

    def _create_error_response(self, error_message: str, session_title: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a standardized error response.

        Args:
            error_message: The error message
            session_title: Optional session title

        Returns:
            Standardized error response
        """
        from ...shared.utils import format_response_with_session

        return format_response_with_session(
            status_code=LOCATION_TIMEOUT,
            response_text=f"{self.process_name} failed: {error_message}",
            top_candidates=[],
            session_title=session_title
        )

    def _create_success_response(
        self,
        response_text: str,
        top_candidates: List[Any],
        session_title: Optional[str] = None,
        **metadata
    ) -> Dict[str, Any]:
        """
        Create a standardized success response.

        Args:
            response_text: The response text
            top_candidates: List of top candidates
            session_title: Optional session title
            **metadata: Additional metadata to include

        Returns:
            Standardized success response
        """
        from ...shared.utils import format_response_with_session

        response = format_response_with_session(
            status_code=LOCATION_SUCCESS,
            response_text=response_text,
            top_candidates=top_candidates,
            session_title=session_title
        )

        # Add any additional metadata
        if metadata:
            response.update(metadata)

        return response

    @abstractmethod
    def get_process_info(self) -> Dict[str, Any]:
        """
        Get information about this process.

        Returns:
            Dictionary containing process metadata
        """
        pass

    def is_initialized(self) -> bool:
        """
        Check if the processor is initialized.

        Returns:
            True if the processor is initialized, False otherwise
        """
        return self._initialized
