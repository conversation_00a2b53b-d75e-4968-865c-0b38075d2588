#!/usr/bin/env python3
"""
Spatial Top Candidates Finder for Location Agent.

This module provides a spatial database implementation of top candidates finding
that replaces the original top_candidates.py functionality with sub-second performance.
"""

from typing import List, Dict
from src.models import POIData, TopCandidates
from src.infrastructure.log.unified_logger import info, warning
from src.utils import timing_decorator


class SpatialTopCandidatesFinder:
    """
    Spatial Top Candidates Finder that uses the spatial database for efficient route calculations.

    This class replaces the functionality of the original top_candidates.py module
    with spatial database queries for improved performance.
    """

    def __init__(self):
        """Initialize the spatial top candidates finder."""
        pass

    @staticmethod
    def calculate_euclidean_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate euclidean distance between two points."""
        import math
        # Rough conversion to meters
        return math.sqrt((lat2 - lat1)**2 + (lon2 - lon1)**2) * 111000

    @timing_decorator
    def find_top_candidates(self, candidates: List[POIData], user_lat: float, user_lon: float,
                            radius_m: int, n: int) -> TopCandidates:
        """
        Find top candidates using spatial database.

        Args:
            candidates: List of POI candidates
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters
            n: Number of top candidates to return

        Returns:
            List of top candidates with route distances
        """
        info(
            f"Finding top {n} candidates from {len(candidates)} POIs within {radius_m}m")

        # Convert POIData objects to raw dicts if they have .raw attribute
        raw_candidates: List[Dict] = []
        for candidate in candidates:
            if hasattr(candidate, 'raw'):
                raw_candidates.append(candidate.raw)
            elif isinstance(candidate, dict):
                raw_candidates.append(candidate)
            else:
                # Convert POIData to dict
                raw_candidates.append({
                    'id': getattr(candidate, 'id', None),
                    'name': getattr(candidate, 'name', ''),
                    'name_en': getattr(candidate, 'name_en', ''),
                    'name_tr': getattr(candidate, 'name_tr', ''),
                    'category': getattr(candidate, 'category', ''),
                    'subcategory': getattr(candidate, 'subcategory', ''),
                    'cuisine': getattr(candidate, 'cuisine', ''),
                    'city': getattr(candidate, 'city', ''),
                    'district': getattr(candidate, 'district', ''),
                    'street': getattr(candidate, 'street', ''),
                    'phone_number': getattr(candidate, 'phone_number', ''),
                    'opening_hours': getattr(candidate, 'opening_hours', ''),
                    'description': getattr(candidate, 'description', '')
                })

        if not raw_candidates:
            warning("No candidates provided to find_top_candidates")
            return []

        # For now, use euclidean distance as a simple approximation
        # In a full implementation, this would use road network routing
        candidates_with_distances = []
        for candidate in raw_candidates:
            try:
                lat = candidate.get('latitude')
                lon = candidate.get('longitude')

                if lat is None or lon is None:
                    continue

                lat = float(lat)
                lon = float(lon)

                # Calculate euclidean distance as approximation
                distance = self.calculate_euclidean_distance(
                    user_lat, user_lon, lat, lon)

                # Add distance information
                candidate_copy = candidate.copy()
                candidate_copy['walk_route_distance_m'] = distance
                candidate_copy['drive_route_distance_m'] = distance

                candidates_with_distances.append(candidate_copy)

            except (ValueError, TypeError) as e:
                warning(f"Skipping candidate with invalid coordinates: {e}")
                continue

        # Sort by minimum distance (walk or drive)
        def get_min_distance(poi):
            walk_dist = poi.get('walk_route_distance_m', float('inf'))
            drive_dist = poi.get('drive_route_distance_m', float('inf'))

            # Ensure distances are numeric (handle string values)
            try:
                walk_dist = float(
                    walk_dist) if walk_dist is not None else float('inf')
            except (ValueError, TypeError):
                walk_dist = float('inf')

            try:
                drive_dist = float(
                    drive_dist) if drive_dist is not None else float('inf')
            except (ValueError, TypeError):
                drive_dist = float('inf')

            return min(walk_dist, drive_dist)

        candidates_with_distances.sort(key=get_min_distance)

        # Take the top n candidates
        top_candidates = candidates_with_distances[:n]

        info(f"Found {len(top_candidates)} top candidates")
        return top_candidates


# Replacement function that maintains the same interface as top_candidates.py
def find_top_candidates(
    candidates: List[POIData],
    user_lat: float, user_lon: float,
    radius_m: int, n: int
) -> TopCandidates:
    """
    Find top candidates - replacement for top_candidates.py function.

    Args:
        candidates: List of POI data
        user_lat: User's latitude
        user_lon: User's longitude
        radius_m: Search radius in meters
        n: Number of top candidates to return

    Returns:
        List of top candidates with route distances
    """
    finder = SpatialTopCandidatesFinder()
    return finder.find_top_candidates(candidates, user_lat, user_lon, radius_m, n)
