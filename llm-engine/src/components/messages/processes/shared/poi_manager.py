#!/usr/bin/env python3
"""
Spatial POI Manager for Location Agent.

This module provides a spatial database implementation of POI filtering
that replaces the original filter.py functionality with sub-second performance.
"""

from psycopg2.extras import RealDictCursor
from typing import List, Optional, Dict, Any
import json
from src.models import POIData
from src.infrastructure.log.unified_logger import info, error, warning
from src.utils import timing_decorator
from src.spatial_db.utils.db_connection import get_spatial_db_connection
from .models import LocationContext
from .location_resolver import resolve_location


class SpatialPOIManager:
    """
    Spatial POI Manager that uses the spatial database for efficient POI filtering.

    This class replaces the functionality of the original filter.py module
    with spatial database queries for improved performance.
    """

    def __init__(self):
        """Initialize the spatial POI manager."""
        pass

    @timing_decorator
    def get_poi_by_subcategories(self, user_lat: float, user_lon: float, radius_m: int,
                                 search_subcategories: List[str]) -> List[POIData]:
        """
        Get POIs by subcategories using spatial database.

        Args:
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters
            search_subcategories: List of subcategories to search for

        Returns:
            List of POIData objects matching the criteria
        """
        try:
            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Create SQL query with subcategory filter
                query = """
                    SELECT
                        p.id,
                        p.name,
                        p.name_en,
                        p.name_tr,
                        p.category,
                        p.subcategory,
                        p.cuisine,
                        p.city,
                        p.district,
                        p.street,
                        p.latitude,
                        p.longitude,
                        p.phone_number,
                        p.opening_hours,
                        p.description,
                        ST_Distance(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                        ) AS distance_m
                    FROM
                        spatial_schema.pois p
                    WHERE
                        ST_DWithin(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography,
                            %s
                        )
                """

                params = [user_lon, user_lat, user_lon, user_lat, radius_m]

                # Add subcategory filter if provided
                if search_subcategories:
                    subcategory_conditions = []
                    for subcategory in search_subcategories:
                        subcategory_conditions.append("p.subcategory ILIKE %s")
                        params.append(f'%{subcategory}%')

                    query += f" AND ({' OR '.join(subcategory_conditions)})"

                # Add order by distance
                query += " ORDER BY distance_m"

                cursor.execute(query, params)
                results = cursor.fetchall()

                # Convert results to POIData objects
                poi_data_list = []
                for row in results:
                    poi_dict = dict(row)
                    # Validate required fields
                    if poi_dict.get('latitude') and poi_dict.get('longitude') and poi_dict.get('subcategory'):
                        poi_data_list.append(POIData(poi_dict))

                info(f"Found {len(poi_data_list)} POIs matching the criteria")
                conn.close()
                return poi_data_list

        except Exception as e:
            error(f"Error in get_poi_by_subcategories: {e}")
            return []

    @timing_decorator
    def get_poi_by_subcategories_with_user_distances(self, search_lat: float, search_lon: float,
                                                     user_lat: float, user_lon: float,
                                                     radius_m: int, search_subcategories: List[str]) -> List[POIData]:
        """
        Get POIs within a search area but calculate distances from user location.

        This method searches for POIs within radius_m of search_lat/search_lon (e.g., Beyoğlu center)
        but calculates all distances relative to user_lat/user_lon (user's actual location).

        Args:
            search_lat: Latitude of search area center (e.g., resolved location like Beyoğlu)
            search_lon: Longitude of search area center
            user_lat: User's actual latitude (for distance calculations)
            user_lon: User's actual longitude (for distance calculations)
            radius_m: Search radius in meters around search area
            search_subcategories: List of subcategories to filter by

        Returns:
            List of POIData objects with distances calculated from user location
        """
        try:
            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Create SQL query that searches around search coordinates but calculates distances from user coordinates
                query = """
                    SELECT
                        p.id,
                        p.name,
                        p.name_en,
                        p.name_tr,
                        p.category,
                        p.subcategory,
                        p.cuisine,
                        p.city,
                        p.district,
                        p.street,
                        p.latitude,
                        p.longitude,
                        p.phone_number,
                        p.opening_hours,
                        p.description,
                        ST_Distance(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                        ) AS distance_m
                    FROM
                        spatial_schema.pois p
                    WHERE
                        ST_DWithin(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography,
                            %s
                        )
                """

                # Parameters: user_lon, user_lat for distance calculation, search_lon, search_lat for area search, radius
                params = [user_lon, user_lat, search_lon, search_lat, radius_m]

                # Add subcategory filter if provided
                if search_subcategories:
                    subcategory_conditions = []
                    for subcategory in search_subcategories:
                        subcategory_conditions.append("p.subcategory ILIKE %s")
                        params.append(f'%{subcategory}%')

                    query += f" AND ({' OR '.join(subcategory_conditions)})"

                # Order by distance from user location
                query += " ORDER BY distance_m"

                cursor.execute(query, params)
                results = cursor.fetchall()

                # Convert results to POIData objects
                poi_data_list = []
                for row in results:
                    poi_dict = dict(row)
                    # Validate required fields
                    if poi_dict.get('latitude') and poi_dict.get('longitude') and poi_dict.get('subcategory'):
                        poi_data_list.append(POIData(poi_dict))

                info(
                    f"Found {len(poi_data_list)} POIs in search area with distances from user location")
                conn.close()
                return poi_data_list

        except Exception as e:
            error(
                f"Error in get_poi_by_subcategories_with_user_distances: {e}")
            return []

    @timing_decorator
    def get_available_categories(self, user_lat: float, user_lon: float, radius_m: int) -> str:
        """
        Get available categories using spatial database.

        Args:
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters

        Returns:
            String representation of available categories and subcategories
        """
        try:
            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Create SQL query to get categories and subcategories
                query = """
                    SELECT
                        p.category,
                        p.subcategory
                    FROM
                        spatial_schema.pois p
                    WHERE
                        ST_DWithin(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography,
                            %s
                        )
                    GROUP BY
                        p.category, p.subcategory
                    ORDER BY
                        p.category, p.subcategory
                """

                cursor.execute(query, (user_lon, user_lat, radius_m))
                results = cursor.fetchall()

                # Build a dictionary where each key is a category and the value is a set of subcategories
                category_to_subcategories = {}
                for row in results:
                    category = row['category'].strip(
                    ) if row['category'] else 'Unknown'
                    subcategory = row['subcategory'].strip(
                    ) if row['subcategory'] else 'Unknown'

                    if not category or not subcategory:
                        continue

                    if category not in category_to_subcategories:
                        category_to_subcategories[category] = set()

                    category_to_subcategories[category].add(subcategory)

                # Build the final multi-line string
                result_lines = []
                for category, subcategories in category_to_subcategories.items():
                    # Sort the subcategories alphabetically for consistency
                    subcategories_list = ", ".join(sorted(subcategories))
                    result_lines.append(f"{category}: {subcategories_list}")

                conn.close()
                return "\n".join(result_lines)

        except Exception as e:
            error(f"Error in get_available_categories: {e}")
            return ""

    @timing_decorator
    def get_poi_by_location_context(self, location_context: LocationContext,
                                    search_subcategories: List[str],
                                    radius_m: int) -> List[POIData]:
        """
        Enhanced POI search supporting location context from Phase 2.

        Args:
            location_context: LocationContext with resolved location information
            search_subcategories: List of subcategories to search for
            radius_m: Search radius in meters (used for point_radius searches)

        Returns:
            List of POIData objects matching the criteria
        """
        if location_context.search_method == "boundary":
            return self._get_poi_by_boundary(location_context, search_subcategories)
        elif location_context.search_method == "point_radius":
            if location_context.coordinates:
                lat, lon = location_context.coordinates
                return self.get_poi_by_subcategories(lat, lon, radius_m, search_subcategories)
            else:
                warning(
                    f"No coordinates available for point_radius search: {location_context.name}")
                return []
        else:
            # user_location - this should be handled by the calling code with user coordinates
            warning(
                f"Unexpected search method in get_poi_by_location_context: {location_context.search_method}")
            return []

    def _get_poi_by_boundary(self, location_context: LocationContext,
                             search_subcategories: List[str]) -> List[POIData]:
        """
        Get POIs within administrative boundary using PostGIS spatial queries.

        Args:
            location_context: LocationContext with boundary information
            search_subcategories: List of subcategories to search for

        Returns:
            List of POIData objects within the boundary
        """
        try:
            if not location_context.boundary_geom:
                warning(
                    f"No boundary geometry available for {location_context.name}")
                return []

            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Ensure boundary_geom is a string for ST_GeomFromGeoJSON
                if isinstance(location_context.boundary_geom, dict):
                    boundary_geom_str = json.dumps(
                        location_context.boundary_geom)
                else:
                    boundary_geom_str = location_context.boundary_geom

                # Create SQL query for boundary-based search
                query = """
                    SELECT
                        p.id,
                        p.name,
                        p.name_en,
                        p.name_tr,
                        p.category,
                        p.subcategory,
                        p.cuisine,
                        p.city,
                        p.district,
                        p.street,
                        p.latitude,
                        p.longitude,
                        p.phone_number,
                        p.opening_hours,
                        p.description
                    FROM
                        spatial_schema.pois p
                    WHERE
                        ST_Within(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326),
                            ST_SetSRID(ST_GeomFromGeoJSON(%s), 4326)
                        )
                """

                params = [boundary_geom_str]

                # Add subcategory filter if provided
                if search_subcategories:
                    subcategory_conditions = []
                    for subcategory in search_subcategories:
                        subcategory_conditions.append("p.subcategory ILIKE %s")
                        params.append(f'%{subcategory}%')

                    query += f" AND ({' OR '.join(subcategory_conditions)})"

                # Order by name for consistent results
                query += " ORDER BY p.name"

                cursor.execute(query, params)
                results = cursor.fetchall()

                # Convert results to POIData objects
                poi_data_list = []
                for row in results:
                    poi_dict = dict(row)
                    # Validate required fields
                    if poi_dict.get('latitude') and poi_dict.get('longitude') and poi_dict.get('subcategory'):
                        poi_data_list.append(POIData(poi_dict))

                info(
                    f"Found {len(poi_data_list)} POIs within boundary {location_context.name}")
                conn.close()
                return poi_data_list

        except Exception as e:
            error(
                f"Error in _get_poi_by_boundary for {location_context.name}: {e}")
            return []

    @timing_decorator
    def get_available_categories_by_location_context(self, location_context: LocationContext,
                                                     radius_m: int) -> str:
        """
        Get available categories for a location context.

        Args:
            location_context: LocationContext with resolved location information
            radius_m: Search radius in meters (used for point_radius searches)

        Returns:
            String representation of available categories and subcategories
        """
        if location_context.search_method == "boundary":
            return self._get_available_categories_by_boundary(location_context)
        elif location_context.search_method == "point_radius":
            if location_context.coordinates:
                lat, lon = location_context.coordinates
                return self.get_available_categories(lat, lon, radius_m)
            else:
                warning(
                    f"No coordinates available for point_radius search: {location_context.name}")
                return ""
        else:
            # user_location - this should be handled by the calling code with user coordinates
            warning(
                f"Unexpected search method in get_available_categories_by_location_context: {location_context.search_method}")
            return ""

    def _get_available_categories_by_boundary(self, location_context: LocationContext) -> str:
        """
        Get available categories within administrative boundary.

        Args:
            location_context: LocationContext with boundary information

        Returns:
            String representation of available categories and subcategories
        """
        try:
            if not location_context.boundary_geom:
                warning(
                    f"No boundary geometry available for {location_context.name}")
                return ""

            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Parse the boundary geometry
                boundary_geom = json.loads(location_context.boundary_geom)

                # Create SQL query to get categories and subcategories within boundary
                query = """
                    SELECT
                        p.category,
                        p.subcategory
                    FROM
                        spatial_schema.pois p
                    WHERE
                        ST_Within(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326),
                            ST_SetSRID(ST_GeomFromGeoJSON(%s), 4326)
                        )
                    GROUP BY
                        p.category, p.subcategory
                    ORDER BY
                        p.category, p.subcategory
                """

                cursor.execute(query, (json.dumps(boundary_geom),))
                results = cursor.fetchall()

                # Build a dictionary where each key is a category and the value is a set of subcategories
                category_to_subcategories = {}
                for row in results:
                    category = row['category'].strip(
                    ) if row['category'] else 'Unknown'
                    subcategory = row['subcategory'].strip(
                    ) if row['subcategory'] else 'Unknown'

                    if not category or not subcategory:
                        continue

                    if category not in category_to_subcategories:
                        category_to_subcategories[category] = set()

                    category_to_subcategories[category].add(subcategory)

                # Build the final multi-line string
                result_lines = []
                for category, subcategories in category_to_subcategories.items():
                    # Sort the subcategories alphabetically for consistency
                    subcategories_list = ", ".join(sorted(subcategories))
                    result_lines.append(f"{category}: {subcategories_list}")

                conn.close()
                return "\n".join(result_lines)

        except Exception as e:
            error(
                f"Error in _get_available_categories_by_boundary for {location_context.name}: {e}")
            return ""

    @timing_decorator
    def get_poi_by_subcategories_with_fallback(self, user_lat: float, user_lon: float,
                                               radius_m: int, search_subcategories: List[str],
                                               tags: List[str] = None) -> dict:
        """
        Get POIs with 4-stage fallback system. Returns empty if no relevant POIs found.

        Fallback stages:
        1. Exact match (subcategories + tags + radius)
        2. Expand radius by 2x (if no results)
        3. Remove tags constraint (keep subcategories + expanded radius)
        4. Remove subcategory constraint (all subcategories + expanded radius)
        5. Remove all constraints (all POIs in expanded radius)

        Args:
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters
            search_subcategories: List of subcategories to search for
            tags: List of tags to search for (optional)

        Returns:
            Dict containing:
            - 'pois': List of POIData objects
            - 'stage_used': Which fallback stage was used (1-4)
            - 'search_params': Final search parameters used
        """
        if tags is None:
            tags = []

        original_radius = radius_m
        current_radius = radius_m

        # Stage 1: Exact match
        info(
            f"Fallback Stage 1: Exact match - subcategories={list(search_subcategories)}, tags={list(tags)}, radius={current_radius}")
        pois = self._search_pois_with_tags(
            user_lat, user_lon, current_radius, search_subcategories, tags)
        if pois:
            return {
                'pois': pois,
                'stage_used': 1,
                'search_params': {
                    'subcategories': search_subcategories,
                    'tags': tags,
                    'radius': current_radius,
                    'description': 'Exact match'
                }
            }

        # Stage 2: Expand radius by 2x
        current_radius = original_radius * 2
        info(
            f"Fallback Stage 2: Expand radius - subcategories={list(search_subcategories)}, tags={list(tags)}, radius={current_radius}")
        pois = self._search_pois_with_tags(
            user_lat, user_lon, current_radius, search_subcategories, tags)
        if pois:
            return {
                'pois': pois,
                'stage_used': 2,
                'search_params': {
                    'subcategories': search_subcategories,
                    'tags': tags,
                    'radius': current_radius,
                    'description': 'Expanded radius'
                }
            }

        # Stage 3: Remove tags constraint
        info(
            f"Fallback Stage 3: Remove tags - subcategories={list(search_subcategories)}, radius={current_radius}")
        pois = self._search_pois_with_tags(
            user_lat, user_lon, current_radius, search_subcategories, [])
        if pois:
            return {
                'pois': pois,
                'stage_used': 3,
                'search_params': {
                    'subcategories': search_subcategories,
                    'tags': [],
                    'radius': current_radius,
                    'description': 'Removed tags constraint'
                }
            }

        # Stage 4: Remove subcategory constraint (all subcategories)
        info(f"Fallback Stage 4: All subcategories - radius={current_radius}")
        pois = self._search_pois_with_tags(
            user_lat, user_lon, current_radius, [], [])
        if pois:
            return {
                'pois': pois,
                'stage_used': 4,
                'search_params': {
                    'subcategories': [],
                    'tags': [],
                    'radius': current_radius,
                    'description': 'All subcategories'
                }
            }

        # No results found - return empty to trigger advice agent
        info("No POIs found in any fallback stage - returning empty results")
        return {
            'pois': [],
            'stage_used': 4,
            'search_params': {
                'subcategories': [],
                'tags': [],
                'radius': current_radius,
                'description': 'No results found'
            }
        }

    def _search_pois_with_tags(self, user_lat: float, user_lon: float, radius_m: int,
                               search_subcategories: List[str], tags: List[str]) -> List[POIData]:
        """
        Search POIs with subcategories and tags constraints.

        Args:
            user_lat: User's latitude
            user_lon: User's longitude
            radius_m: Search radius in meters
            search_subcategories: List of subcategories to filter by (empty = all)
            tags: List of tags to search for (empty = no tag filtering)

        Returns:
            List of POIData objects matching the criteria
        """
        try:
            conn = get_spatial_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Build dynamic query based on constraints
                base_query = """
                    SELECT
                        p.id, p.name, p.name_en, p.name_tr, p.category, p.subcategory,
                        p.cuisine, p.city, p.district, p.street, p.latitude, p.longitude,
                        p.phone_number, p.opening_hours, p.description,
                        ST_Distance(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                            ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                        ) as distance_m
                    FROM spatial_schema.pois p
                    WHERE ST_DWithin(
                        ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                        ST_SetSRID(ST_Point(%s, %s), 4326)::geography,
                        %s
                    )
                """

                params = [user_lon, user_lat, user_lon, user_lat, radius_m]

                # Add subcategory constraint if specified
                if search_subcategories:
                    placeholders = ', '.join(
                        ['%s'] * len(search_subcategories))
                    base_query += f" AND p.subcategory IN ({placeholders})"
                    params.extend(search_subcategories)

                # Add tag constraint if specified
                if tags:
                    # For now, we'll search in name, description, and category fields
                    # This is a simple implementation - you can enhance it later
                    tag_conditions = []
                    for tag in tags:
                        tag_conditions.append("""
                            (LOWER(p.name) LIKE LOWER(%s) OR
                             LOWER(p.description) LIKE LOWER(%s) OR
                             LOWER(p.category) LIKE LOWER(%s))
                        """)
                        tag_pattern = f"%{tag}%"
                        params.extend([tag_pattern, tag_pattern, tag_pattern])

                    if tag_conditions:
                        base_query += " AND (" + \
                            " OR ".join(tag_conditions) + ")"

                base_query += " ORDER BY distance_m LIMIT 50"

                cursor.execute(base_query, params)
                results = cursor.fetchall()

                pois = []
                for row in results:
                    # Create POIData with a dictionary as expected by the model
                    poi_data = {
                        'id': row['id'],
                        'name': row['name'],
                        'name_en': row['name_en'],
                        'name_tr': row['name_tr'],
                        'category': row['category'],
                        'subcategory': row['subcategory'],
                        'cuisine': row['cuisine'],
                        'city': row['city'],
                        'district': row['district'],
                        'street': row['street'],
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'phone_number': row['phone_number'],
                        'opening_hours': row['opening_hours'],
                        'description': row['description'],
                        'distance_m': float(row['distance_m'])
                    }
                    poi = POIData(poi_data)
                    pois.append(poi)

                conn.close()
                return pois

        except Exception as e:
            error(f"Error in _search_pois_with_tags: {e}")
            return []


# Replacement functions that maintain the same interface as filter.py
def get_available_categories(user_lat: float, user_lon: float, radius_m: int) -> str:
    """
    Get available categories - replacement for filter.py function.

    Args:
        user_lat: User's latitude
        user_lon: User's longitude
        radius_m: Search radius in meters

    Returns:
        String representation of available categories and subcategories
    """
    manager = SpatialPOIManager()
    return manager.get_available_categories(user_lat, user_lon, radius_m)


def get_poi_by_subcategories(user_lat: float, user_lon: float, radius_m: int,
                             search_subcategories: List[str]) -> List[POIData]:
    """
    Get POIs by subcategories with 4-stage fallback system.

    Args:
        user_lat: User's latitude
        user_lon: User's longitude
        radius_m: Search radius in meters
        search_subcategories: List of subcategories to search for

    Returns:
        List of POIData objects matching the criteria
    """
    manager = SpatialPOIManager()
    return manager.get_poi_by_subcategories_with_fallback(
        user_lat, user_lon, radius_m, search_subcategories, tags=[]
    )


def get_poi_by_subcategories_with_tags(user_lat: float, user_lon: float, radius_m: int,
                                       search_subcategories: List[str], tags: List[str]) -> dict:
    """
    Get POIs by subcategories and tags with 4-stage fallback system.

    Args:
        user_lat: User's latitude
        user_lon: User's longitude
        radius_m: Search radius in meters
        search_subcategories: List of subcategories to search for
        tags: List of tags to search for

    Returns:
        Dict containing POIs and metadata about which stage was used
    """
    manager = SpatialPOIManager()
    return manager.get_poi_by_subcategories_with_fallback(
        user_lat, user_lon, radius_m, search_subcategories, tags
    )


# Enhanced functions for Phase 2 location name search
def get_poi_by_location_context(location_context: LocationContext,
                                search_subcategories: List[str],
                                radius_m: int) -> List[POIData]:
    """
    Enhanced POI search supporting location context - Phase 2 functionality.

    Args:
        location_context: LocationContext with resolved location information
        search_subcategories: List of subcategories to search for
        radius_m: Search radius in meters (used for point_radius searches)

    Returns:
        List of POIData objects matching the criteria
    """
    manager = SpatialPOIManager()
    return manager.get_poi_by_location_context(location_context, search_subcategories, radius_m)


def get_poi_by_subcategories_with_user_distances(search_lat: float, search_lon: float,
                                                 user_lat: float, user_lon: float,
                                                 radius_m: int, search_subcategories: List[str]) -> List[POIData]:
    """
    Get POIs within a search area but calculate distances from user location - standalone function.

    This function searches for POIs within radius_m of search_lat/search_lon (e.g., Beyoğlu center)
    but calculates all distances relative to user_lat/user_lon (user's actual location).

    Args:
        search_lat: Latitude of search area center (e.g., resolved location like Beyoğlu)
        search_lon: Longitude of search area center
        user_lat: User's actual latitude (for distance calculations)
        user_lon: User's actual longitude (for distance calculations)
        radius_m: Search radius in meters around search area
        search_subcategories: List of subcategories to filter by

    Returns:
        List of POIData objects with distances calculated from user location
    """
    manager = SpatialPOIManager()
    return manager.get_poi_by_subcategories_with_user_distances(
        search_lat, search_lon, user_lat, user_lon, radius_m, search_subcategories
    )


def get_available_categories_by_location_context(location_context: LocationContext,
                                                 radius_m: int) -> str:
    """
    Get available categories for a location context - Phase 2 functionality.

    Args:
        location_context: LocationContext with resolved location information
        radius_m: Search radius in meters (used for point_radius searches)

    Returns:
        String representation of available categories and subcategories
    """
    manager = SpatialPOIManager()
    return manager.get_available_categories_by_location_context(location_context, radius_m)


def resolve_and_search_pois(location_name: Optional[str],
                            search_subcategories: List[str],
                            radius_m: int) -> List[POIData]:
    """
    Convenience function that combines location resolution and POI search.

    Args:
        location_name: Name of the location to resolve
        search_subcategories: List of subcategories to search for
        radius_m: Search radius in meters (used for point_radius searches)

    Returns:
        List of POIData objects matching the criteria
    """
    # Resolve the location
    location_context = resolve_location(location_name)

    # Search for POIs using the resolved location context
    return get_poi_by_location_context(location_context, search_subcategories, radius_m)


def get_poi_by_id(poi_id: str) -> Optional[Dict[str, Any]]:
    """
    Get detailed POI information by ID.

    Args:
        poi_id: POI ID to retrieve

    Returns:
        POI dictionary with full details, or None if not found
    """
    try:
        from src.infrastructure.database.spatial_db import get_spatial_db_connection
        from psycopg2.extras import RealDictCursor

        conn = get_spatial_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            query = """
                SELECT
                    id,
                    name,
                    name_en,
                    name_tr,
                    category,
                    subcategory,
                    cuisine,
                    city,
                    district,
                    street,
                    latitude,
                    longitude,
                    phone_number,
                    opening_hours,
                    description,
                    full_address,
                    neighborhood,
                    province
                FROM spatial_schema.pois
                WHERE id = %s
            """

            cursor.execute(query, [poi_id])
            result = cursor.fetchone()

            if result:
                poi_data = {
                    'id': result['id'],
                    'name': result['name'],
                    'name_en': result['name_en'],
                    'name_tr': result['name_tr'],
                    'category': result['category'],
                    'subcategory': result['subcategory'],
                    'cuisine': result['cuisine'],
                    'city': result['city'],
                    'district': result['district'],
                    'street': result['street'],
                    'latitude': float(result['latitude']) if result['latitude'] else 0.0,
                    'longitude': float(result['longitude']) if result['longitude'] else 0.0,
                    'phone_number': result['phone_number'],
                    'opening_hours': result['opening_hours'],
                    'description': result['description'],
                    'full_address': result['full_address'],
                    'neighborhood': result['neighborhood'],
                    'province': result['province']
                }
                return poi_data

        conn.close()
        return None

    except Exception as e:
        error(f"Error retrieving POI by ID {poi_id}: {e}")
        return None
