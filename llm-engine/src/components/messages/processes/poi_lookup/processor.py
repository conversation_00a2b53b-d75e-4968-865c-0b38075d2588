"""
POI Lookup Processor.

This processor handles POI coordinate lookup only.
It finds POI coordinates for use in subsequent relative searches.
"""

from typing import Dict, Any, Optional, List
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ..shared.base_processor import BaseProcessor


class PO<PERSON>ookupProcessor(BaseProcessor):
    """
    Processor for POI coordinate lookup.

    Handles:
    - Finding POI coordinates by name
    - Returning POI information for chaining with other searches
    - Does NOT search for nearby places
    """

    def __init__(self):
        """Initialize the POI lookup processor."""
        super().__init__("POILookup")

    def _do_initialize(self, **kwargs) -> None:
        """Perform POI lookup specific initialization."""
        # No specific initialization needed for POI lookup
        pass

    def get_process_info(self) -> Dict[str, Any]:
        """Get information about this process."""
        return {
            "name": "POILookup",
            "description": "Find POI coordinates by name for use in subsequent searches",
            "workflow_steps": [
                "poi_lookup",
                "coordinate_extraction",
                "result_formatting"
            ],
            "search_method": "poi_lookup",
            "version": "1.0.0"
        }

    def _execute_workflow(
        self,
        prompt: str,
        latitude: float,
        longitude: float,
        radius: int,
        num_candidates: int,
        poi_name: Optional[str] = None,
        boundary_name: Optional[str] = None,
        history: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the POI lookup workflow.

        Workflow Steps:
        1. POI Lookup: Search for POI by name
        2. Coordinate Extraction: Get POI coordinates
        3. Result Formatting: Format response with coordinates only
        """

        debug(f"Starting POI lookup for: {poi_name}")

        if not poi_name:
            return self._create_error_response(
                "POI name is required for POI lookup",
                session_title
            )

        # Step 1: POI Lookup - find POI coordinates only
        return self._lookup_poi_coordinates(poi_name, boundary_name, prompt, session_title, latitude, longitude, radius)

    def _lookup_poi_coordinates(self, poi_name: str, boundary_name: Optional[str],
                                prompt: str, session_title: Optional[str],
                                latitude: float, longitude: float, radius: int) -> Dict[str, Any]:
        """
        Lookup POI coordinates only (no nearby search).

        Args:
            poi_name: Name of POI to search for
            boundary_name: Optional boundary to search within
            prompt: Original user prompt
            session_title: Session title
            latitude: User's latitude
            longitude: User's longitude
            radius: Search radius

        Returns:
            Dict containing POI coordinates and basic info
        """
        try:
            from src.spatial_db.utils.db_connection import get_spatial_db_connection
            from psycopg2.extras import RealDictCursor

            conn = get_spatial_db_connection()

            # Search for POI by name using flexible matching strategies
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                if boundary_name:
                    # Search within specific boundary
                    debug(
                        f"Searching for POI '{poi_name}' within boundary '{boundary_name}'")
                    result = self._search_poi_with_flexible_matching(
                        cursor, poi_name, boundary_name, latitude, longitude)
                else:
                    # Search globally, prioritize by distance to user
                    debug(
                        f"Searching for POI '{poi_name}' globally, prioritizing by distance to user")
                    result = self._search_poi_with_flexible_matching(
                        cursor, poi_name, None, latitude, longitude)

            if result:
                poi_data = result[0]  # This is a RealDictRow (dictionary-like)

                poi_info = {
                    "id": poi_data['id'],
                    "name": poi_data['name'],
                    "latitude": float(poi_data['latitude']) if poi_data['latitude'] else 0.0,
                    "longitude": float(poi_data['longitude']) if poi_data['longitude'] else 0.0,
                    "district": poi_data['district'] or "",
                    "street": poi_data['street'] or "",
                    "is_poi_reference": True
                }

                info(
                    f"Found POI: {poi_data['name']} at ({poi_data['latitude']}, {poi_data['longitude']})")

                return {
                    "response": f"Found {poi_data['name']} at coordinates ({poi_data['latitude']}, {poi_data['longitude']})",
                    "top_candidates": [poi_info],
                    "session_title": session_title,
                    "search_method": "poi_lookup",
                    "poi_coordinates": {"latitude": float(poi_data['latitude']), "longitude": float(poi_data['longitude'])},
                    "poi_name": poi_data['name']
                }
            else:
                # POI not found
                warning(f"POI '{poi_name}' not found")
                return {
                    "response": f"Could not find POI '{poi_name}'. Please check the name and try again.",
                    "top_candidates": [],
                    "session_title": session_title,
                    "search_method": "poi_lookup",
                    "error": "poi_not_found"
                }

        except Exception as e:
            error(f"Error in POI lookup: {str(e)}")
            return self._create_error_response(
                f"Error looking up POI '{poi_name}': {str(e)}",
                session_title
            )
        finally:
            if 'conn' in locals():
                conn.close()

    def _search_poi_with_flexible_matching(self, cursor, poi_name: str, boundary_name: Optional[str],
                                           latitude: float, longitude: float) -> List[Dict[str, Any]]:
        """
        Search for POI using multiple flexible matching strategies.

        Strategies (in order of preference):
        1. Exact match (case-insensitive)
        2. Contains match (LIKE %term%)
        3. Word-based matching (all words present)
        4. Partial word matching (any word matches)

        Args:
            cursor: Database cursor
            poi_name: Name to search for
            boundary_name: Optional boundary constraint
            latitude: User latitude for distance ordering
            longitude: User longitude for distance ordering

        Returns:
            List of matching POI records, ordered by match quality and distance
        """
        # Clean and prepare search terms
        clean_name = poi_name.strip()
        words = [word.strip() for word in clean_name.split() if word.strip()]

        debug(f"Flexible POI search for '{poi_name}' with words: {words}")

        # Try multiple search strategies in order of preference
        strategies = [
            ("exact", clean_name),
            ("contains", f"%{clean_name}%"),
        ]

        # Add word-based strategies if we have multiple words
        if len(words) > 1:
            # All words present (any order)
            strategies.append(("all_words", words))
            # Any word matches
            strategies.append(("any_word", words))

        results = []

        for strategy_name, search_term in strategies:
            if results:  # If we found results with a higher priority strategy, stop
                break

            debug(f"Trying strategy '{strategy_name}' for '{poi_name}'")

            if boundary_name:
                # Search within boundary
                if strategy_name in ["exact", "contains"]:
                    query = """
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street
                        FROM spatial_schema.pois p
                        JOIN spatial_schema.administrative_boundaries ab ON ST_Within(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326),
                            ab.geom
                        )
                        WHERE LOWER(ab.name) LIKE LOWER(%s)
                        AND LOWER(p.name) LIKE LOWER(%s)
                        LIMIT 5
                    """
                    params = [
                        f"%{boundary_name}%", search_term if strategy_name == "exact" else search_term]

                elif strategy_name == "all_words":
                    # All words must be present
                    word_conditions = " AND ".join(
                        [f"LOWER(p.name) LIKE LOWER(%s)" for _ in words])
                    query = f"""
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street
                        FROM spatial_schema.pois p
                        JOIN spatial_schema.administrative_boundaries ab ON ST_Within(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326),
                            ab.geom
                        )
                        WHERE LOWER(ab.name) LIKE LOWER(%s)
                        AND ({word_conditions})
                        LIMIT 5
                    """
                    params = [f"%{boundary_name}%"] + \
                        [f"%{word}%" for word in words]

                elif strategy_name == "any_word":
                    # Any word matches
                    word_conditions = " OR ".join(
                        [f"LOWER(p.name) LIKE LOWER(%s)" for _ in words])
                    query = f"""
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street
                        FROM spatial_schema.pois p
                        JOIN spatial_schema.administrative_boundaries ab ON ST_Within(
                            ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326),
                            ab.geom
                        )
                        WHERE LOWER(ab.name) LIKE LOWER(%s)
                        AND ({word_conditions})
                        LIMIT 5
                    """
                    params = [f"%{boundary_name}%"] + \
                        [f"%{word}%" for word in words]

            else:
                # Global search with distance ordering
                if strategy_name in ["exact", "contains"]:
                    query = """
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street,
                               ST_Distance(
                                   ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                                   ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                               ) as distance
                        FROM spatial_schema.pois p
                        WHERE LOWER(p.name) LIKE LOWER(%s)
                        ORDER BY distance
                        LIMIT 5
                    """
                    params = [
                        longitude, latitude, search_term if strategy_name == "exact" else search_term]

                elif strategy_name == "all_words":
                    # All words must be present
                    word_conditions = " AND ".join(
                        [f"LOWER(p.name) LIKE LOWER(%s)" for _ in words])
                    query = f"""
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street,
                               ST_Distance(
                                   ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                                   ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                               ) as distance
                        FROM spatial_schema.pois p
                        WHERE ({word_conditions})
                        ORDER BY distance
                        LIMIT 5
                    """
                    params = [longitude, latitude] + \
                        [f"%{word}%" for word in words]

                elif strategy_name == "any_word":
                    # Any word matches
                    word_conditions = " OR ".join(
                        [f"LOWER(p.name) LIKE LOWER(%s)" for _ in words])
                    query = f"""
                        SELECT p.id, p.name, p.latitude, p.longitude, p.district, p.street,
                               ST_Distance(
                                   ST_SetSRID(ST_Point(p.longitude, p.latitude), 4326)::geography,
                                   ST_SetSRID(ST_Point(%s, %s), 4326)::geography
                               ) as distance
                        FROM spatial_schema.pois p
                        WHERE ({word_conditions})
                        ORDER BY distance
                        LIMIT 5
                    """
                    params = [longitude, latitude] + \
                        [f"%{word}%" for word in words]

            try:
                cursor.execute(query, params)
                strategy_results = cursor.fetchall()

                if strategy_results:
                    debug(
                        f"Strategy '{strategy_name}' found {len(strategy_results)} results")
                    for i, result in enumerate(strategy_results):
                        debug(f"  {i+1}. {result['name']}")
                    results = strategy_results
                    break
                else:
                    debug(f"Strategy '{strategy_name}' found no results")

            except Exception as e:
                error(f"Error in strategy '{strategy_name}': {str(e)}")
                continue

        debug(
            f"Final flexible search found {len(results)} results for '{poi_name}'")
        return results


def process_poi_lookup(
    prompt: str,
    latitude: float,
    longitude: float,
    radius: int,
    num_candidates: int,
    poi_name: Optional[str] = None,
    boundary_name: Optional[str] = None,
    history: Optional[str] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    session_title: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process POI lookup request.

    Args:
        prompt: User's search prompt
        latitude: User's latitude
        longitude: User's longitude
        radius: Search radius in meters
        num_candidates: Number of candidates to return
        poi_name: Name of POI to lookup
        boundary_name: Optional boundary name to search within
        history: Conversation history
        user_id: User ID
        session_id: Session ID
        session_title: Session title
        **kwargs: Additional parameters

    Returns:
        Dict containing POI coordinates and basic information
    """
    processor = POILookupProcessor()
    return processor.process_request(
        prompt=prompt,
        latitude=latitude,
        longitude=longitude,
        radius=radius,
        num_candidates=num_candidates,
        poi_name=poi_name,
        boundary_name=boundary_name,
        history=history,
        user_id=user_id,
        session_id=session_id,
        session_title=session_title,
        **kwargs
    )
