"""
POI Detail Retrieval Processor

Handles user requests for detailed information about specific POIs from the accumulated state.
This process searches through the user's session state to find POIs matching the user's description
and returns detailed information about them.
"""

from typing import Dict, Any, List, Optional
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ..shared import BaseProcessor
from ...orchestrator.state_management import StateManager
from ...shared.poi_manager import get_poi_by_id


class POIDetailRetrievalProcessor(BaseProcessor):
    """
    Processor for retrieving detailed information about specific POIs.

    This processor:
    1. Analyzes user request to identify which POI they want details about
    2. Searches through accumulated state to find matching POIs
    3. Retrieves full details from database if needed
    4. Returns detailed information about the requested POI
    """

    def get_process_info(self) -> Dict[str, Any]:
        """Get information about this process."""
        return {
            "name": "POIDetailRetrieval",
            "description": "Retrieve detailed information about specific POIs from accumulated state",
            "workflow_steps": [
                "request_analysis",
                "state_search",
                "detail_retrieval",
                "response_formatting"
            ],
            "search_method": "poi_detail_lookup",
            "version": "1.0.0"
        }

    def _execute_workflow(
        self,
        prompt: str,
        latitude: float,
        longitude: float,
        radius: int,
        num_candidates: int,
        user_id: str,
        session_id: str,
        history: Optional[str] = None,
        session_title: Optional[str] = None,
        state_manager: Optional[StateManager] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the POI detail retrieval workflow.

        Workflow Steps:
        1. Request Analysis: Parse user request to identify POI criteria
        2. State Search: Search accumulated state for matching POIs
        3. Detail Retrieval: Get full details from database if needed
        4. Response Formatting: Format detailed response
        """

        debug(f"Starting POI detail retrieval for: {prompt}")

        if not state_manager:
            return self._create_error_response(
                "State manager not available for POI detail retrieval",
                session_title
            )

        try:
            # Step 1: Request Analysis
            search_criteria = self._analyze_request(prompt)
            debug(f"Search criteria: {search_criteria}")

            # Step 2: State Search
            accumulated_candidates = state_manager.get_accumulated_candidates(
                user_id, session_id)
            matching_pois = self._search_accumulated_state(
                accumulated_candidates, search_criteria)

            if not matching_pois:
                return self._create_no_results_response(prompt, session_title)

            # Step 3: Detail Retrieval
            detailed_pois = self._retrieve_full_details(matching_pois)

            # Step 4: Response Formatting
            return self._format_detailed_response(detailed_pois, prompt, session_title)

        except Exception as e:
            error(f"Error in POI detail retrieval: {e}")
            return self._create_error_response(
                f"Error retrieving POI details: {str(e)}",
                session_title
            )

    def _analyze_request(self, prompt: str) -> Dict[str, Any]:
        """
        Analyze user request to extract POI search criteria.

        Args:
            prompt: User's request for POI details

        Returns:
            Dictionary with search criteria
        """
        prompt_lower = prompt.lower()
        criteria = {
            'keywords': [],
            'location_hints': [],
            'category_hints': [],
            'attribute_hints': []
        }

        # Extract keywords from prompt
        words = prompt_lower.split()
        criteria['keywords'] = [word for word in words if len(word) > 2]

        # Look for location hints
        location_keywords = ['in', 'at', 'near', 'around', 'from']
        for i, word in enumerate(words):
            if word in location_keywords and i + 1 < len(words):
                criteria['location_hints'].append(words[i + 1])

        # Look for category hints
        category_keywords = ['cafe', 'restaurant',
                             'bar', 'shop', 'store', 'hotel', 'market']
        for word in words:
            if word in category_keywords:
                criteria['category_hints'].append(word)

        # Look for attribute hints
        attribute_keywords = ['view', 'nice', 'good',
                              'best', 'popular', 'cheap', 'expensive']
        for word in words:
            if word in attribute_keywords:
                criteria['attribute_hints'].append(word)

        return criteria

    def _search_accumulated_state(self, accumulated_candidates: List[Dict[str, Any]],
                                  criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Search through accumulated state to find POIs matching criteria.

        Args:
            accumulated_candidates: List of accumulated POI candidates
            criteria: Search criteria from request analysis

        Returns:
            List of matching POI candidates
        """
        matching = []

        for candidate in accumulated_candidates:
            score = self._calculate_match_score(candidate, criteria)
            if score > 0:
                candidate_with_score = candidate.copy()
                candidate_with_score['match_score'] = score
                matching.append(candidate_with_score)

        # Sort by match score (highest first)
        matching.sort(key=lambda x: x.get('match_score', 0), reverse=True)

        # Return top 3 matches
        return matching[:3]

    def _calculate_match_score(self, candidate: Dict[str, Any], criteria: Dict[str, Any]) -> float:
        """
        Calculate how well a candidate matches the search criteria.

        Args:
            candidate: POI candidate dictionary
            criteria: Search criteria

        Returns:
            Match score (0.0 to 1.0)
        """
        score = 0.0
        max_score = 0.0

        # Check name match
        name = candidate.get('name', '').lower()
        for keyword in criteria['keywords']:
            max_score += 1.0
            if keyword in name:
                score += 1.0

        # Check location match
        location_fields = [
            candidate.get('city', '').lower(),
            candidate.get('district', '').lower(),
            candidate.get('street', '').lower()
        ]
        for hint in criteria['location_hints']:
            max_score += 0.8
            for location in location_fields:
                if hint in location:
                    score += 0.8
                    break

        # Check category match
        category_fields = [
            candidate.get('category', '').lower(),
            candidate.get('subcategory', '').lower(),
            candidate.get('cuisine', '').lower()
        ]
        for hint in criteria['category_hints']:
            max_score += 0.6
            for category in category_fields:
                if hint in category:
                    score += 0.6
                    break

        # Normalize score
        return score / max_score if max_score > 0 else 0.0

    def _retrieve_full_details(self, matching_pois: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Retrieve full details for matching POIs from database.

        Args:
            matching_pois: List of matching POI candidates

        Returns:
            List of POIs with full details
        """
        detailed_pois = []

        for poi in matching_pois:
            # If POI is already detailed (not summarized), use as is
            if not poi.get('is_summarized', False):
                detailed_pois.append(poi)
                continue

            # Otherwise, retrieve full details from database
            poi_id = poi.get('id')
            if poi_id:
                try:
                    full_details = get_poi_by_id(poi_id)
                    if full_details:
                        # Preserve match score
                        full_details['match_score'] = poi.get('match_score', 0)
                        detailed_pois.append(full_details)
                    else:
                        # Fallback to summarized version
                        detailed_pois.append(poi)
                except Exception as e:
                    warning(
                        f"Failed to retrieve full details for POI {poi_id}: {e}")
                    detailed_pois.append(poi)
            else:
                detailed_pois.append(poi)

        return detailed_pois

    def _format_detailed_response(self, detailed_pois: List[Dict[str, Any]],
                                  prompt: str, session_title: Optional[str]) -> Dict[str, Any]:
        """
        Format detailed response with POI information.

        Args:
            detailed_pois: List of POIs with full details
            prompt: Original user prompt
            session_title: Session title

        Returns:
            Formatted response dictionary
        """
        if not detailed_pois:
            return self._create_no_results_response(prompt, session_title)

        # Generate detailed response using advice agent
        from ...agents.advice import get_location_advice

        advice_result = get_location_advice(
            prompt=prompt,
            history="",  # No history needed for detail retrieval
            top_candidates=detailed_pois,
            latitude=0.0,  # Not needed for detail retrieval
            longitude=0.0,  # Not needed for detail retrieval
            search_radius=1000,  # Default value
            available_subcategories=[],
            requested_subcategories=[]
        )

        # Extract response text
        from ...agents.shared import extract_response_text
        response_text = extract_response_text(advice_result)

        return self._create_success_response(
            response_text=response_text,
            top_candidates=detailed_pois,
            session_title=session_title,
            search_method="poi_detail_retrieval",
            search_area="accumulated_state",
            poi_count=len(detailed_pois)
        )

    def _create_no_results_response(self, prompt: str, session_title: Optional[str]) -> Dict[str, Any]:
        """
        Create response when no matching POIs are found.

        Args:
            prompt: Original user prompt
            session_title: Session title

        Returns:
            No results response dictionary
        """
        response_text = (
            "I couldn't find any places in your previous searches that match your description. "
            "Could you be more specific about which place you're interested in, or try a new search?"
        )

        return self._create_success_response(
            response_text=response_text,
            top_candidates=[],
            session_title=session_title,
            search_method="poi_detail_retrieval",
            search_area="accumulated_state",
            poi_count=0
        )


# Convenience function for direct usage
def process_poi_detail_retrieval(
    prompt: str,
    latitude: float,
    longitude: float,
    radius: int,
    num_candidates: int,
    user_id: str,
    session_id: str,
    history: Optional[str] = None,
    session_title: Optional[str] = None,
    state_manager: Optional[StateManager] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process POI detail retrieval request.

    Args:
        prompt: User's request for POI details
        latitude: User's latitude (not used but required for interface)
        longitude: User's longitude (not used but required for interface)
        radius: Search radius (not used but required for interface)
        num_candidates: Number of candidates (not used but required for interface)
        user_id: User ID
        session_id: Session ID
        history: Conversation history
        session_title: Session title
        state_manager: State manager instance for accessing accumulated state
        **kwargs: Additional parameters

    Returns:
        Dict containing response and POI details
    """
    processor = POIDetailRetrievalProcessor()
    return processor.process_request(
        prompt=prompt,
        latitude=latitude,
        longitude=longitude,
        radius=radius,
        num_candidates=num_candidates,
        user_id=user_id,
        session_id=session_id,
        history=history,
        session_title=session_title,
        state_manager=state_manager,
        **kwargs
    )
