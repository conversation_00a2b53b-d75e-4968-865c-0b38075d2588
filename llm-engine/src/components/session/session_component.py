"""
Session Component - Simple standalone session creation
"""

import time
import uuid
from typing import Dict, Any
from src.infrastructure.state.interface import IStateManager


class SessionComponent:
    """Simple standalone session component."""

    def __init__(self, state_manager: IStateManager):
        self.state_manager = state_manager

    def create_session(self, user_id: str, session_id: str = None) -> str:
        """Create a new session."""
        if not session_id:
            session_id = f"{int(time.time())}_{uuid.uuid4().hex[:8]}"

        self.state_manager.create_session_with_id(user_id, session_id)
        return session_id

    def get_session_info(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """Get session information."""
        session_state = self.state_manager.get_session(user_id, session_id)
        if not session_state:
            return {}
        return {
            "session_id": session_id,
            "user_id": user_id,
            "status": "active"
        }
