"""
Standalone Components Package

This package contains standalone components for each API endpoint.
Each component is self-contained and operates independently while
sharing common infrastructure through the consolidated service.
"""

from .health import HealthComponent
from .session import SessionComponent
from .messages import MainLLM, get_main_llm
from .titles import TitlesComponent
from .delete import DeleteComponent


def find_location(*args, **kwargs):
    """Lazy import to avoid circular dependency"""
    from .messages import find_location as _find_location
    return _find_location(*args, **kwargs)


__all__ = [
    'HealthComponent',
    'SessionComponent',
    'MainLLM',
    'get_main_llm',
    'find_location',
    'TitlesComponent',
    'DeleteComponent'
]
