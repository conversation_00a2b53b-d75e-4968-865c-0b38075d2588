"""
Titles Component - Standalone component for session titles retrieval.

Simple, minimal implementation following the established pattern.
"""

from typing import List, Dict, Any
from src.infrastructure.state.interface import IStateManager
from src.infrastructure.history.interface import I<PERSON>istoryManager
from src.services.conversation.session_context import SessionContext


class TitlesComponent:
    """Standalone component for handling session titles retrieval."""

    def __init__(self, state_manager: IStateManager, history_manager: IHistoryManager):
        self.state_manager = state_manager
        self.history_manager = history_manager

    def get_session_titles(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all session titles for a user."""
        session_ids = self.state_manager.get_session_ids_for_user(user_id)
        titles = []
        for session_id in session_ids:
            session_context = SessionContext(user_id, session_id, self.state_manager, self.history_manager)
            title = session_context.get_session_title()
            if title:
                timestamp = self._extract_timestamp_from_session_id(session_id)
                titles.append({
                    "session_id": session_id,
                    "title": title,
                    "created_at": timestamp
                })
        titles.sort(key=lambda x: x.get("created_at", 0), reverse=True)
        return titles

    def _extract_timestamp_from_session_id(self, session_id: str) -> int:
        """Extract timestamp from session ID."""
        try:
            return int(session_id.split("_")[0])
        except (ValueError, IndexError):
            return 0
