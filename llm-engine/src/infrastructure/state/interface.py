# src/infrastructure/state/interface.py

"""
Interface for session state management.

This module defines the interface for session state management using the Protocol pattern.
"""

from typing import Protocol, Optional, Dict, Any, List
from src.models import TopCandidates
from src.models import SessionState, UserProfile


class IStateManager(Protocol):
    """
    Interface for session state management.
    Defines methods for getting, saving, and deleting user session states.
    """

    def get_session(self, user_id: str, session_id: str) -> Optional[SessionState]:
        """
        Retrieve a session state by user_id and session_id.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            SessionState object or None if not found
        """
        ...

    def save_session(self, user_id: str, session_id: str, state: SessionState) -> bool:
        """
        Save a session state for a user.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            state: The SessionState object to save

        Returns:
            True if successful, False otherwise
        """
        ...

    def delete_session(self, user_id: str, session_id: str) -> bool:
        """
        Delete a session state.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            True if successful, False otherwise
        """
        ...



    def create_session_with_id(self, user_id: str, session_id: str) -> bool:
        """
        Create a new session with a specific session ID.

        Args:
            user_id: The ID of the user
            session_id: The ID to use for the session

        Returns:
            True if successful, False otherwise
        """
        ...

    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Retrieve a user profile.

        Args:
            user_id: The ID of the user

        Returns:
            UserProfile object or None if not found
        """
        ...

    def save_user_profile(self, user_id: str, profile: UserProfile) -> bool:
        """
        Save a user profile.

        Args:
            user_id: The ID of the user
            profile: The UserProfile object to save

        Returns:
            True if successful, False otherwise
        """
        ...

    def get_top_candidates(self, user_id: str, session_id: str) -> Optional[TopCandidates]:
        """
        Retrieve top candidates for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            TopCandidates object or None if not found
        """
        ...

    def save_top_candidates(self, user_id: str, session_id: str, top_candidates: TopCandidates) -> bool:
        """
        Save top candidates for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            top_candidates: The TopCandidates object to save

        Returns:
            True if successful, False otherwise
        """
        ...

    def update_session_metadata(self, user_id: str, session_id: str, metadata: Dict[str, Any]) -> bool:
        """
        Update the metadata for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            metadata: The metadata to update

        Returns:
            True if successful, False otherwise
        """
        ...

    def get_session_ids_for_user(self, user_id: str) -> List[str]:
        """
        Get all session IDs for a user.

        Args:
            user_id: The ID of the user

        Returns:
            List of session IDs for the user
        """
        ...
