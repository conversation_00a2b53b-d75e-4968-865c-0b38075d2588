# src/infrastructure/state/json_manager.py

import json
import os
import time
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from .interface import IStateManager
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical

from src.utils.error_handling import safe_operation, StateError
from src.utils.file_operations import safe_read_json, safe_write_json, safe_rename_file
from src.models import SessionState, UserProfile
from src.models import TopCandidates
from src.infrastructure.cache.factory import CacheFactory


class JSONStateManager:
    """
    JSON file-based implementation of the StateManager interface.
    Stores session states in a JSON file.
    """

    def __init__(self, sessions_dir=None, cache_ttl_seconds: int = 300, cache_max_size: int = 1000, cache_enabled: bool = True):
        """
        Initialize JSONStateManager with the directory to store session files.

        Args:
            sessions_dir: Directory to store session files
            cache_ttl_seconds: Time-to-live in seconds for cached items
            cache_max_size: Maximum number of items to store in the cache
            cache_enabled: Whether the cache is enabled
        """
        if sessions_dir is None:
            # Use simple fallback to avoid circular imports
            sessions_dir = "app_data/sessions"

        self.sessions_dir = sessions_dir
        os.makedirs(self.sessions_dir, exist_ok=True)

        # Use specialized state cache
        self.state_cache = CacheFactory.create_state_cache()

        info(f"JSONStateManager initialized with cache enabled: {cache_enabled}, TTL: {cache_ttl_seconds}s")

    def _get_user_folder_path(self, user_id: str) -> str:
        """Get the folder path for a user and create if not exists."""
        user_folder = os.path.join(self.sessions_dir, user_id)
        os.makedirs(user_folder, exist_ok=True)
        return user_folder

    def _get_session_file_path(self, user_id: str, session_id: str) -> str:
        """Get the file path for a user's session."""
        user_folder = self._get_user_folder_path(user_id)
        return os.path.join(user_folder, f"{session_id}.json")

    def _get_profile_file_path(self, user_id: str) -> str:
        """Get the file path for a user's profile."""
        user_folder = self._get_user_folder_path(user_id)
        return os.path.join(user_folder, "profile.json")

    @safe_operation(default_value=None, error_message="Error retrieving session")
    def get_session(self, user_id: str, session_id: str) -> Optional[SessionState]:
        """
        Retrieve a session state from the cache or JSON file.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            SessionState object or None if not found

        Raises:
            StateError: If there is an error retrieving the session
        """
        # Create a cache key
        cache_key = f"{user_id}:{session_id}"

        # Try to get from cache first
        if self.state_cache:
            hit, cached_session = self.state_cache.get_session_state(session_id)
            if hit:
                pass  # debug(f"Session cache hit: {cache_key}")
                return cached_session

        # Not in cache, load from file
        pass  # debug(f"Session cache miss: {cache_key}")
        session_file = self._get_session_file_path(user_id, session_id)

        # Use safe_read_json to handle file operations with standardized error handling
        data = safe_read_json(session_file)
        if not data:
            # No data found
            return None

        # Convert to SessionState
        session_state: SessionState = {
            "user_id": data.get("user_id", user_id),
            "session_id": data.get("session_id", session_id),
            "current_state": data.get("current_state", "initial"),
            "last_updated": data.get("last_updated", datetime.now().isoformat())
        }

        # Add optional fields if they exist
        if "latitude" in data:
            session_state["latitude"] = data["latitude"]
        if "longitude" in data:
            session_state["longitude"] = data["longitude"]
        if "search_radius" in data:
            session_state["search_radius"] = data["search_radius"]
        if "last_query" in data:
            session_state["last_query"] = data["last_query"]
        if "last_response" in data:
            session_state["last_response"] = data["last_response"]
        if "metadata" in data:
            session_state["metadata"] = data["metadata"]
        elif "data" in data:  # For backward compatibility
            session_state["metadata"] = data["data"]

        # Cache the session state
        if self.state_cache:
            self.state_cache.cache_session_state(session_id, session_state)

        return session_state

    @safe_operation(default_value=False, error_message="Error saving session")
    def save_session(self, user_id: str, session_id: str, state: SessionState) -> bool:
        """
        Save a session state to the JSON file and update the cache.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            state: The SessionState object to save

        Returns:
            True if successful, False otherwise

        Raises:
            StateError: If there is an error saving the session
        """
        session_file = self._get_session_file_path(user_id, session_id)

        # Ensure the state has the latest timestamp
        if "last_updated" not in state:
            state["last_updated"] = datetime.now().isoformat()

        # State is already validated at the service boundary
        serialized_state = state

        # Use safe_write_json to handle file operations with standardized error handling
        success = safe_write_json(session_file, serialized_state, indent=2)

        if success:
            # Update the cache
            if self.state_cache:
                self.state_cache.cache_session_state(session_id, state)
            pass  # debug(f"Session saved: {session_id} for user {user_id}")
        else:
            pass  # error(f"Failed to save session {session_id} for user {user_id}")

        return success

    @safe_operation(default_value=False, error_message="Error deleting session")
    def delete_session(self, user_id: str, session_id: str) -> bool:
        """
        Rename the session file with REMOVED prefix and invalidate the cache.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            True if successful, False otherwise

        Raises:
            StateError: If there is an error deleting the session
        """
        session_file = self._get_session_file_path(user_id, session_id)

        if not os.path.exists(session_file):
            pass  # warning(f"Cannot delete non-existent session: {session_id}")
            return False

        # Invalidate the cache
        if self.state_cache:
            self.state_cache.invalidate_session(session_id)

        # Use safe_rename_file to handle file operations with standardized error handling
        file_name = os.path.basename(session_file)
        success = safe_rename_file(session_file, f"REMOVED_{file_name}")

        if success:
            pass  # info(f"Marked session as removed: {session_id}")
        else:
            pass  # error(f"Failed to mark session as removed: {session_id}")

        return success



    def create_session_with_id(self, user_id: str, session_id: str) -> bool:
        """Create a new session with a specific session ID."""
        initial_state: SessionState = {
            "session_id": session_id,
            "user_id": user_id,
            "current_state": "initial",
            "last_updated": datetime.now().isoformat(),
            "metadata": {}
        }

        return self.save_session(user_id, session_id, initial_state)

    @safe_operation(default_value=None, error_message="Error retrieving user profile")
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Retrieve a user profile from the cache or JSON file.

        Args:
            user_id: The ID of the user

        Returns:
            UserProfile object or None if not found

        Raises:
            StateError: If there is an error retrieving the profile
        """
        # Create a cache key
        cache_key = f"profile:{user_id}"

        # Try to get from cache first
        if self.state_cache:
            hit, cached_profile = self.state_cache.get_user_profile(user_id)
            if hit:
                pass  # debug(f"Profile cache hit: {cache_key}")
                return cached_profile

        # Not in cache, load from file
        pass  # debug(f"Profile cache miss: {cache_key}")
        profile_file = self._get_profile_file_path(user_id)

        # Use safe_read_json to handle file operations with standardized error handling
        data = safe_read_json(profile_file)
        if not data:
            # No data found
            return None

        # Convert to UserProfile
        user_profile: UserProfile = {
            "user_id": data.get("user_id", user_id),
            "preferences": data.get("preferences", {}),
            "favorite_locations": data.get("favorite_locations", []),
            "search_history": data.get("search_history", []),
            "created_at": data.get("created_at", datetime.now().isoformat()),
            "last_active": data.get("last_active", datetime.now().isoformat())
        }

        # Cache the profile
        if self.state_cache:
            self.state_cache.cache_user_profile(user_id, user_profile)

        return user_profile

    @safe_operation(default_value=False, error_message="Error saving user profile")
    def save_user_profile(self, user_id: str, profile: UserProfile) -> bool:
        """
        Save a user profile to the JSON file and update the cache.

        Args:
            user_id: The ID of the user
            profile: The UserProfile object to save

        Returns:
            True if successful, False otherwise

        Raises:
            StateError: If there is an error saving the profile
        """
        profile_file = self._get_profile_file_path(user_id)

        # Ensure the profile has the latest timestamp
        profile["last_active"] = datetime.now().isoformat()

        # Ensure the profile has a created_at timestamp if not already present
        if "created_at" not in profile:
            profile["created_at"] = datetime.now().isoformat()

        # Profile is already validated at the service boundary
        serialized_profile = profile

        # Use safe_write_json to handle file operations with standardized error handling
        success = safe_write_json(profile_file, serialized_profile, indent=2)

        if success:
            # Update the cache
            if self.state_cache:
                self.state_cache.cache_user_profile(user_id, profile)
            pass  # debug(f"Profile saved for user {user_id}")
        else:
            pass  # error(f"Failed to save profile for user {user_id}")

        return success

    def get_top_candidates(self, user_id: str, session_id: str) -> Optional[TopCandidates]:
        """
        Retrieve top candidates for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            TopCandidates object (consolidated list) or None if not found
        """
        session = self.get_session(user_id, session_id)
        if not session or "metadata" not in session or "top_candidates" not in session["metadata"]:
            return None

        # Get the stored top candidates
        stored_candidates = session["metadata"]["top_candidates"]

        # top_candidates is already validated at the service boundary

        return stored_candidates

    def save_top_candidates(self, user_id: str, session_id: str, top_candidates: TopCandidates) -> bool:
        """
        Save top candidates for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            top_candidates: The TopCandidates object to save

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(user_id, session_id)
        if not session:
            pass  # warning(f"Cannot save top candidates for non-existent session: {session_id}")
            return False

        # Ensure metadata exists
        if "metadata" not in session:
            session["metadata"] = {}

        # Save top candidates in metadata
        session["metadata"]["top_candidates"] = top_candidates

        # Update last_updated timestamp
        session["last_updated"] = datetime.now().isoformat()

        return self.save_session(user_id, session_id, session)

    def update_session_metadata(self, user_id: str, session_id: str, metadata: Dict[str, Any]) -> bool:
        """
        Update the metadata for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            metadata: The metadata to update

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(user_id, session_id)
        if not session:
            pass  # warning(f"Cannot update metadata for non-existent session: {session_id}")
            return False

        # Ensure metadata exists
        if "metadata" not in session:
            session["metadata"] = {}

        # Update metadata
        session["metadata"].update(metadata)

        # Update last_updated timestamp
        session["last_updated"] = datetime.now().isoformat()

        return self.save_session(user_id, session_id, session)

    def get_session_ids_for_user(self, user_id: str) -> List[str]:
        """Get all session IDs for a user."""
        user_folder = self._get_user_folder_path(user_id)
        session_ids = []

        if os.path.exists(user_folder):
            for filename in os.listdir(user_folder):
                if filename.endswith('.json') and filename != 'profile.json' and not filename.startswith('REMOVED_'):
                    session_id = filename[:-5]  # Remove .json extension
                    session_ids.append(session_id)

        return session_ids