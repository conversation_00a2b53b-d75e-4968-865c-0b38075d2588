"""
Base interface for LLM providers.

This module defines the interface that all LLM providers must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union

from src.models import LLMResponse


class LLMProviderInterface(ABC):
    """
    Abstract interface for LLM providers.

    This interface defines the contract that all LLM providers must implement.
    It provides methods for initialization, API calls, and response parsing.
    """

    @abstractmethod
    def initialize(self, api_key: Optional[str] = None, **kwargs) -> None:
        """
        Initialize the LLM provider with the given API key and additional parameters.

        Args:
            api_key: Optional API key for the provider. If not provided, will attempt
                    to use the API key from the configuration.
            **kwargs: Additional provider-specific parameters.
        """
        pass

    @abstractmethod
    def call_api(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Make a request to the LLM provider's API.

        Args:
            prompt: The input prompt to send to the LLM.
            model: Optional model name to use. If not provided, will use the default model.
            system_prompt: Optional system prompt to use.
            temperature: Optional temperature parameter for the model.
            max_tokens: Optional maximum number of tokens to generate.
            response_format: Optional response format specification.
            **kwargs: Additional provider-specific parameters.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        pass

    @abstractmethod
    def extract_content(self, response: Dict[str, Any]) -> LLMResponse:
        """
        Extract the relevant content from the API response.

        Args:
            response: The raw API response.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        pass

    @abstractmethod
    def get_available_models(self) -> List[str]:
        """
        Get a list of available models for this provider.

        Returns:
            List[str]: A list of model identifiers.
        """
        pass

    @abstractmethod
    def get_default_model(self) -> str:
        """
        Get the default model for this provider.

        Returns:
            str: The default model identifier.
        """
        pass

    @abstractmethod
    def validate_api_key(self, api_key: str) -> bool:
        """
        Validate the given API key.

        Args:
            api_key: The API key to validate.

        Returns:
            bool: True if the API key is valid, False otherwise.
        """
        pass
