"""
Llama API provider implementation.

This module implements the LLMProviderInterface for the Llama API.
"""

import json
from typing import Dict, Any, Optional, List, Union
import requests

from llamaapi import LlamaAPI

from src.models import LLMResponse
from src.infrastructure.llm_endpoints.interface import LL<PERSON>roviderInterface
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical


class LlamaProvider(LLMProviderInterface):
    """
    Implementation of LLMProviderInterface for the Llama API.
    """

    def __init__(self):
        """Initialize the LlamaProvider."""
        self.client = None
        self.api_key = None

    def initialize(self, api_key: Optional[str] = None, **kwargs) -> None:
        """
        Initialize the Llama API client with the given API key.

        Args:
            api_key: Optional API key for the Llama API. If not provided, will attempt
                    to use the API key from the configuration.
            **kwargs: Additional provider-specific parameters.
        """
        if api_key:
            self.api_key = api_key
            self.client = LlamaAPI(api_key)
        else:
            # Try to get the API key from the configuration
            from src.infrastructure.config.config import ConfigManager
            config = ConfigManager()
            self.api_key = config.get_api_key()
            if self.api_key:
                self.client = LlamaAPI(self.api_key)
            else:
                raise ValueError("No API key provided for Llama API")

    def call_api(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Make a request to the Llama API.

        Args:
            prompt: The input prompt to send to the LLM.
            model: Optional model name to use. If not provided, will use the default model.
            system_prompt: Optional system prompt to use.
            temperature: Optional temperature parameter for the model.
            max_tokens: Optional maximum number of tokens to generate.
            response_format: Optional response format specification.
            **kwargs: Additional provider-specific parameters.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        if not self.client:
            self.initialize()

        # Check if this is a request from the prompt builder
        if "user_prompt" in kwargs and "hyperparameters" in kwargs:
            # Extract parameters from the prompt builder format
            user_prompt = kwargs.get("user_prompt", prompt)
            hyperparameters = kwargs.get("hyperparameters", {})
            system_prompt = kwargs.get("system_prompt", system_prompt)

            # Extract hyperparameters
            temperature = hyperparameters.get("temperature", temperature)
            max_tokens = hyperparameters.get("max_tokens", max_tokens)
            response_format = hyperparameters.get(
                "response_format", response_format)

            # Use the user_prompt instead of the original prompt
            prompt = user_prompt

        # Model must be provided - no defaults
        if not model:
            raise ValueError("Model must be specified - no default model configured")
        model_id = model

        # Get default values from config if not provided
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()

        # Try to get provider configuration
        provider_config = config.config.get(
            "llm_providers", {}).get("llama", {})

        # Set default values if not provided
        if temperature is None:
            temperature = 0.7
        if max_tokens is None:
            max_tokens = 1000

        # Remove flow from kwargs if present
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'flow'}

        # Build the API request
        api_request = {
            "model": model_id,
            "messages": []
        }

        # Add system prompt if provided
        if system_prompt:
            api_request["messages"].append({
                "role": "system",
                "content": system_prompt
            })

        # Add user prompt
        api_request["messages"].append({
            "role": "user",
            "content": prompt
        })

        # Add additional parameters
        api_request["max_tokens"] = max_tokens
        api_request["temperature"] = temperature

        # Add response format if provided
        if response_format:
            api_request["response_format"] = response_format

        # Add any additional parameters
        for key, value in filtered_kwargs.items():
            if key not in api_request and key != "user_prompt" and key != "hyperparameters" and key != "system_prompt":
                api_request[key] = value

        # Log the full request for debugging
        try:
            request_json = json.dumps(api_request, default=str)
            debug(f"Llama API Request: {request_json}")
        except Exception as e:
            error(f"Failed to log Llama API request: {str(e)}")

        try:
            # Call the Llama API
            response = self.client.run(api_request)

            # Log the full response for debugging
            try:
                response_data = response.json()
                response_json = json.dumps(response_data, default=str)
                debug(f"Llama API Response: {response_json}")

                # Log token usage information if available
                if "usage" in response_data:
                    usage = response_data.get("usage", {})
                    info(f"Llama API Usage: model={model_id}, " +
                         f"prompt_tokens={usage.get('prompt_tokens', 'N/A')}, " +
                         f"completion_tokens={usage.get('completion_tokens', 'N/A')}, " +
                         f"total_tokens={usage.get('total_tokens', 'N/A')}")
            except Exception as e:
                error(f"Failed to log Llama API response: {str(e)}")

            # Extract the content from the response
            return self.extract_content(response.json())

        except Exception as e:
            error_msg = f"Failed to call Llama API: {str(e)}"
            error(error_msg)
            return LLMResponse({"error": error_msg})

    def extract_content(self, response: Dict[str, Any]) -> LLMResponse:
        """
        Extract the relevant content from the Llama API response.

        Args:
            response: The raw API response.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        try:
            # Extract the content from the response
            content = response.get("choices", [{}])[0].get(
                "message", {}).get("content", "")

            # Remove any markdown code block formatting
            if "```" in content:
                import re
                # Remove markdown code blocks but keep the content inside
                content = re.sub(r'```(?:json)?\s*(.*?)\s*```',
                                 r'\1', content, flags=re.DOTALL)
                content = content.strip()

            # Try to parse the content as JSON if it looks like JSON
            if content.strip().startswith("{") and content.strip().endswith("}"):
                try:
                    content_json = json.loads(content)
                    return LLMResponse(content_json)
                except json.JSONDecodeError:
                    # If it's not valid JSON, return the raw content
                    return LLMResponse({"response": content})
            else:
                # Return the raw content
                return LLMResponse({"response": content})

        except Exception as e:
            error_msg = f"Error extracting content from Llama API response: {str(e)}"
            error(error_msg)
            return LLMResponse({"error": error_msg})

    def get_available_models(self) -> List[str]:
        """
        Get a list of available models from your config.json tested_models.

        Returns:
            List[str]: A list of model identifiers.
        """
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()
        provider_config = config.config.get("llm_providers", {}).get("llama", {})
        return provider_config.get("tested_models", [])

    def get_default_model(self) -> str:
        """
        Get the first model from tested_models - agents should specify their model explicitly.

        Returns:
            str: The first tested model identifier.
        """
        tested_models = self.get_available_models()
        if not tested_models:
            raise ValueError("No tested models configured for Llama provider")
        return tested_models[0]

    def validate_api_key(self, api_key: str) -> bool:
        """
        Validate the given API key.

        Args:
            api_key: The API key to validate.

        Returns:
            bool: True if the API key is valid, False otherwise.
        """
        try:
            # Create a temporary client with the given API key
            temp_client = LlamaAPI(api_key)

            # Try to make a simple request to validate the API key
            # Use the first tested model for validation
            test_model = self.get_default_model()
            response = temp_client.run({
                "model": test_model,
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            })

            # If we get a successful response, the API key is valid
            return response.status_code == 200

        except Exception as e:
            return False
