"""
Adapter for the existing LLM interface.

This module provides an adapter that allows the existing LLM interface to use the new LLM endpoints system.
"""

from typing import Dict, Any, Optional, List, Union

from src.models import LLMResponse
from src.components.messages.orchestrator.interface import LLMInterface
from src.infrastructure.llm_endpoints import get_llm_provider


class LLMProviderAdapter(LLMInterface):
    """
    Adapter for the existing LLM interface.

    This class implements the existing LLMInterface using the new LLM endpoints system.
    It allows existing code to use the new system without changes.
    """

    def __init__(self, provider_id: Optional[str] = None, model_id: Optional[str] = None, hyperparameters: Optional[Dict[str, Any]] = None):
        """
        Initialize the adapter with the given provider, model, and hyperparameters.

        Args:
            provider_id: Optional provider ID. If None, uses the default provider.
            model_id: Optional model ID. If None, uses the default model for the provider.
            hyperparameters: Optional hyperparameters to use for API calls.
        """
        self.provider_id = provider_id
        self.model_id = model_id
        self.hyperparameters = hyperparameters
        self.provider = None

        # Initialize the provider
        self._initialize_provider()

    def _initialize_provider(self) -> None:
        """Initialize the LLM provider."""
        try:
            self.provider = get_llm_provider(self.provider_id)
            if not self.provider:
                raise ValueError(
                    f"Failed to initialize provider {self.provider_id}")
        except Exception as e:
            raise ValueError(f"Error initializing provider: {e}")

    def call_api(self, prompt: str, **kwargs) -> LLMResponse:
        """
        Make a request to the LLM provider's API.

        Args:
            prompt: The input prompt to send to the LLM.
            **kwargs: Additional parameters for the API request.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        if not self.provider:
            self._initialize_provider()

        # Remove named arguments from kwargs to avoid multiple values error
        named_args = [
            'model', 'system_prompt', 'temperature', 'max_tokens', 'response_format'
        ]
        filtered_kwargs = {k: v for k,
                           v in kwargs.items() if k not in named_args}

        response = self.provider.call_api(
            prompt,
            model=self.model_id,
            system_prompt=kwargs.get('system_prompt'),
            temperature=kwargs.get('temperature'),
            max_tokens=kwargs.get('max_tokens'),
            response_format=kwargs.get('response_format'),
            **filtered_kwargs
        )
        return response

    def extract_content(self, response: Dict[str, Any]) -> LLMResponse:
        """
        Extract the relevant content from the API response.

        Args:
            response: The raw API response.

        Returns:
            LLMResponse: Structured response from the LLM.
        """
        if not self.provider:
            self._initialize_provider()

        # Log the raw response for debugging
        import json
        try:
            # Try to convert the response to a JSON string for better logging
            response_str = json.dumps(response, default=str)
        except Exception as e:
            # If JSON conversion fails, log the response as a string
            pass

        # Let the provider extract the content first
        result = self.provider.extract_content(response)

        # Normalize the response format if needed
        try:
            # If the result is a string, try to parse it as JSON
            if isinstance(result, str):
                if result.strip().startswith('{') and result.strip().endswith('}'):
                    try:
                        parsed_json = json.loads(result)
                        result = LLMResponse(parsed_json)
                    except json.JSONDecodeError:
                        # If it's not valid JSON, wrap it in a response object
                        result = LLMResponse({"response": result})
                else:
                    # If it's not JSON, wrap it in a response object
                    result = LLMResponse({"response": result})

            # If the result is a dict but not a LLMResponse, convert it
            elif isinstance(result, dict) and not isinstance(result, LLMResponse):
                result = LLMResponse(result)

            # If the result is neither a string nor a dict, wrap it in a response object
            elif not isinstance(result, LLMResponse):
                result = LLMResponse({"response": str(result)})

            # Ensure top_candidates exists and is a list
            if "top_candidates" not in result or result["top_candidates"] is None:
                result["top_candidates"] = []
            elif not isinstance(result["top_candidates"], list):
                # If top_candidates is not a list, convert it to an empty list
                result["top_candidates"] = []

        except Exception as e:
            # Ensure we always return a LLMResponse
            if not isinstance(result, LLMResponse):
                result = LLMResponse(
                    {"error": f"Failed to normalize response: {str(e)}"})

            # Ensure top_candidates exists
            if "top_candidates" not in result:
                result["top_candidates"] = []

        # Log the processed response
        try:
            # Try to convert the result to a JSON string for better logging
            result_str = json.dumps(result, default=str)
        except Exception as e:
            # If JSON conversion fails, log the result as a string
            pass

        return result


def create_llm_adapter(provider_id: Optional[str] = None, model_id: Optional[str] = None) -> LLMInterface:
    """
    Create an adapter for the existing LLM interface.

    Args:
        provider_id: Optional provider ID. If None, uses the default provider.
        model_id: Optional model ID. If None, uses the default model for the provider.

    Returns:
        LLMInterface: An adapter that implements the existing LLM interface.
    """
    return LLMProviderAdapter(provider_id, model_id)


def create_agent_llm_adapter(agent_id: str) -> LLMInterface:
    """
    Create an adapter for a specific agent using its configuration from config.json.

    Args:
        agent_id: The agent ID (e.g., 'main', 'request', 'advice')

    Returns:
        LLMInterface: An adapter configured for the specific agent.

    Raises:
        ValueError: If the agent configuration is not found.
    """
    from src.infrastructure.config.config import ConfigManager

    # Get the agent configuration
    config = ConfigManager()
    agent_config = config.get_agent_llm_config(agent_id)

    # Extract provider, model, and hyperparameters from the agent configuration
    provider_id = agent_config.get("provider")
    model_id = agent_config.get("model")
    hyperparameters = agent_config.get("hyperparameters")

    # Create and return the adapter
    return LLMProviderAdapter(provider_id, model_id, hyperparameters)
