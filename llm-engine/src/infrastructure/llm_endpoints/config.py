"""
Configuration for LLM endpoints.

This module provides simple configuration utilities for LLM endpoints.
"""

from typing import Dict, Any, Optional

from src.infrastructure.config.config import ConfigManager


class LLMEndpointConfig:
    """
    Simple configuration for LLM endpoints - reads directly from config.json.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for LLMEndpointConfig."""
        if cls._instance is None:
            cls._instance = super(LLMEndpointConfig, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the LLMEndpointConfig."""
        if not getattr(self, "_initialized", False):
            self.config_manager = ConfigManager()
            self._initialized = True

    def get_default_provider(self) -> str:
        """
        Get the default LLM provider from the first agent configuration.

        Returns:
            str: The default provider ID.
        """
        agents_config = self.config_manager.get_config_value("agents", {})
        if agents_config:
            first_agent = next(iter(agents_config.values()))
            return first_agent.get("provider", "openrouter")
        return "openrouter"

    def get_provider_config(self, provider_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get the configuration for a specific provider from config.json.

        Args:
            provider_id: The provider ID. If None, returns the default provider configuration.

        Returns:
            Dict[str, Any]: The provider configuration.
        """
        if provider_id is None:
            provider_id = self.get_default_provider()

        # Get the provider configuration from your config.json
        providers_config = self.config_manager.get_config_value("llm_providers", {})
        return providers_config.get(provider_id, {})

