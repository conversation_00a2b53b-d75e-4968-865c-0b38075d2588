# src/infrastructure/data_access/__init__.py

"""
Data access infrastructure package.

This package provides a flexible infrastructure for accessing data from
different storage backends.
"""

from src.infrastructure.data_access.core.interfaces import (
    IDataProvider, IDataQuery, IDataResult,
    Query<PERSON>ilter, GeospatialFilter, QueryOptions
)
from src.infrastructure.data_access.core.query import DataQuery
from src.infrastructure.data_access.core.result import DataResult
from src.infrastructure.data_access.core.factory import (
    create_data_provider, switch_data_provider
)
from src.infrastructure.data_access.config import DataAccessConfig

# Microservice entry point
from .data_service import data_service

__all__ = [
    'IDataProvider', 'IDataQuery', 'IDataResult',
    'QueryFilter', 'GeospatialFilter', 'QueryOptions',
    'DataQuery', 'DataResult',
    'create_data_provider', 'switch_data_provider',
    'DataAccessConfig',
    # Microservice
    'data_service'
]
