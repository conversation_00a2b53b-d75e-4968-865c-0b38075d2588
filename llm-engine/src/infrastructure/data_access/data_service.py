# src/infrastructure/data_access/data_service.py

"""
Data Access Microservice Entry Point

Simple, clean microservice interface for data access operations.
Returns status codes directly without wrapper classes.
"""

from typing import Any, Tuple, Optional, Dict, List
from .core.factory import create_data_provider
from .core.query import DataQuery
from .core.interfaces import Query<PERSON><PERSON><PERSON>, GeospatialFilter, QueryOptions


# Data Access microservice status codes (5XXXX range)
DATA_SUCCESS = 50000
DATA_QUERY_SUCCESS = 50500
DATA_GET_SUCCESS = 50500
DATA_SCHEMA_SUCCESS = 50500
DATA_COUNT_SUCCESS = 50500
DATA_REFRESH_SUCCESS = 50400
DATA_MISSING_PARAMS = 50022
DATA_INVALID_TYPE = 50021
DATA_UNKNOWN_OPERATION = 50021
DATA_NOT_FOUND = 50520
DATA_QUERY_ERROR = 50082
DATA_SYSTEM_ERROR = 50084


def data_service(operation: str, **params) -> Tuple[int, Any]:
    """
    Single entry point for Data Access microservice.

    Args:
        operation: Operation to perform ('query', 'get_by_id', 'get_schema', 'get_unique_values', 'count', 'refresh')
        **params: Operation-specific parameters

    Returns:
        Tuple[int, Any]: (status_code, data)
        - status_code: 5XXXX format for data access microservice
        - data: Operation result or None for errors
    """
    try:
        # Get data provider
        provider = create_data_provider()
        
        if not provider:
            return DATA_SYSTEM_ERROR, None

        # Ensure provider is initialized
        if not provider.is_initialized():
            if not provider.initialize():
                return DATA_SYSTEM_ERROR, None

        if operation == "query":
            # Execute data query
            filters = params.get("filters", [])
            geo_filter = params.get("geo_filter")
            options = params.get("options", {})

            # Build query
            query = DataQuery()
            
            # Add filters
            if filters:
                for filter_data in filters:
                    if isinstance(filter_data, dict):
                        field = filter_data.get("field")
                        operator = filter_data.get("operator")
                        value = filter_data.get("value")
                        if field and operator and value is not None:
                            query.add_filter(field, operator, value)

            # Add geospatial filter
            if geo_filter and isinstance(geo_filter, dict):
                lat = geo_filter.get("center_lat")
                lon = geo_filter.get("center_lon")
                radius = geo_filter.get("radius_m")
                if lat is not None and lon is not None and radius is not None:
                    query.set_geospatial_filter(
                        lat, lon, radius,
                        geo_filter.get("lat_field", "latitude"),
                        geo_filter.get("lon_field", "longitude")
                    )

            # Add options
            if options:
                query.set_options(
                    limit=options.get("limit"),
                    offset=options.get("offset"),
                    sort_by=options.get("sort_by"),
                    sort_order=options.get("sort_order", "asc")
                )

            # Execute query
            result = provider.query(query)
            
            # Convert result to dict for JSON serialization
            return DATA_QUERY_SUCCESS, {
                "items": result.items,
                "total": result.total,
                "page": result.page,
                "page_size": result.page_size,
                "has_more": result.has_more
            }

        elif operation == "get_by_id":
            # Get single item by ID
            id_field = params.get("id_field")
            id_value = params.get("id_value")

            if not id_field or id_value is None:
                return DATA_MISSING_PARAMS, None

            item = provider.get_by_id(id_field, id_value)
            if item is None:
                return DATA_NOT_FOUND, None

            return DATA_GET_SUCCESS, {"item": item}

        elif operation == "get_schema":
            # Get dataset schema
            schema = provider.get_schema()
            return DATA_SCHEMA_SUCCESS, {"schema": schema}

        elif operation == "get_unique_values":
            # Get unique values for a field
            field = params.get("field")

            if not field:
                return DATA_MISSING_PARAMS, None

            unique_values = provider.get_unique_values(field)
            return DATA_GET_SUCCESS, {"field": field, "unique_values": unique_values}

        elif operation == "count":
            # Count items matching query
            filters = params.get("filters", [])
            geo_filter = params.get("geo_filter")

            # Build query if filters provided
            query = None
            if filters or geo_filter:
                query = DataQuery()
                
                # Add filters
                if filters:
                    for filter_data in filters:
                        if isinstance(filter_data, dict):
                            field = filter_data.get("field")
                            operator = filter_data.get("operator")
                            value = filter_data.get("value")
                            if field and operator and value is not None:
                                query.add_filter(field, operator, value)

                # Add geospatial filter
                if geo_filter and isinstance(geo_filter, dict):
                    lat = geo_filter.get("center_lat")
                    lon = geo_filter.get("center_lon")
                    radius = geo_filter.get("radius_m")
                    if lat is not None and lon is not None and radius is not None:
                        query.set_geospatial_filter(
                            lat, lon, radius,
                            geo_filter.get("lat_field", "latitude"),
                            geo_filter.get("lon_field", "longitude")
                        )

            count = provider.count(query)
            return DATA_COUNT_SUCCESS, {"count": count}

        elif operation == "refresh":
            # Refresh dataset from source
            success = provider.refresh()
            return DATA_REFRESH_SUCCESS, {"refreshed": success}

        else:
            return DATA_UNKNOWN_OPERATION, None

    except Exception:
        return DATA_SYSTEM_ERROR, None
