# src/infrastructure/data_access/config.py

"""
Configuration utilities for data access.

This module provides utilities for configuring and switching between
different data providers.
"""

from typing import Dict, Any, Optional
from src.infrastructure.config.config import ConfigManager
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical


class DataAccessConfig:
    """
    Configuration utilities for data access.
    """

    def __init__(self):
        """Initialize the data access configuration."""
        self.config = ConfigManager()

    def get_provider_type(self) -> str:
        """
        Get the current data provider type.

        Returns:
            Current provider type
        """
        return self.config.get_config_value("data_provider_type", "csv")

    def set_provider_type(self, provider_type: str) -> bool:
        """
        Set the data provider type.

        Args:
            provider_type: Type of provider to use

        Returns:
            True if successful, False otherwise
        """
        return self.config.update_config({"data_provider_type": provider_type})

    def get_provider_config(self, provider_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Get the configuration for a specific provider type.

        Args:
            provider_type: Type of provider (defaults to current provider type)

        Returns:
            Provider configuration
        """
        provider_type = provider_type or self.get_provider_type()

        if provider_type == "csv":
            return {
                "file_path": self.config.get_config_value("data_paths", {}).get("dataset", "data/dataset.csv"),
                "use_memory_map": self.config.get_config_value("csv_use_memory_map", True),
                "use_partitioning": self.config.get_config_value("csv_use_partitioning", True),
                "partition_dir": self.config.get_config_value("csv_partition_dir", None),
            }
        elif provider_type == "postgres":
            return {
                "connection_string": self.config.get_config_value("postgres_connection_string", ""),
                "table_name": self.config.get_config_value("postgres_table_name", "dataset"),
                "schema_name": self.config.get_config_value("postgres_schema_name", "public"),
            }
        else:
            return {}

    def set_provider_config(self, provider_type: str, config: Dict[str, Any]) -> bool:
        """
        Set the configuration for a specific provider type.

        Args:
            provider_type: Type of provider
            config: Provider configuration

        Returns:
            True if successful, False otherwise
        """
        updates = {}

        if provider_type == "csv":
            if "file_path" in config:
                updates["data_paths"] = {"dataset": config["file_path"]}
            if "use_memory_map" in config:
                updates["csv_use_memory_map"] = config["use_memory_map"]
            if "use_partitioning" in config:
                updates["csv_use_partitioning"] = config["use_partitioning"]
            if "partition_dir" in config:
                updates["csv_partition_dir"] = config["partition_dir"]
        elif provider_type == "postgres":
            if "connection_string" in config:
                updates["postgres_connection_string"] = config["connection_string"]
            if "table_name" in config:
                updates["postgres_table_name"] = config["table_name"]
            if "schema_name" in config:
                updates["postgres_schema_name"] = config["schema_name"]

        return self.config.update_config(updates)

    def switch_provider(self, provider_type: str, **provider_config) -> bool:
        """
        Switch to a different data provider type.

        Args:
            provider_type: Type of provider to switch to
            **provider_config: Provider-specific configuration

        Returns:
            True if successful, False otherwise
        """
        # Set provider type
        if not self.set_provider_type(provider_type):
            return False

        # Set provider configuration
        if provider_config and not self.set_provider_config(provider_type, provider_config):
            return False

        info(f"Switched data provider to: {provider_type}")
        return True
