# src/infrastructure/data_access/core/result.py

"""
Result handling abstractions.

This module provides implementations for handling query results
from data providers.
"""

from typing import List, TypeVar, Generic
from src.infrastructure.data_access.core.interfaces import IDataResult

# Type variable for generic result types
T = TypeVar('T')


class DataResult(Generic[T]):
    """Implementation of IDataResult."""

    def __init__(self, items: List[T], total: int, page: int = 1, page_size: int = 0):
        """
        Initialize a data result.
        
        Args:
            items: List of result items
            total: Total number of items matching the query (before pagination)
            page: Current page number (1-based)
            page_size: Number of items per page
        """
        self.items = items
        self.total = total
        self.page = page
        self.page_size = page_size or len(items)
        self.has_more = total > (page * page_size) if page_size > 0 else False
