# src/infrastructure/data_access/providers/csv/partitioning.py

"""
Partitioning utilities for CSV data provider.

This module provides utilities for partitioning CSV data for efficient
geospatial queries.
"""

import os
import json
import re
import math
import time
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical


class CSVPartitioner:
    """
    Utility for partitioning CSV data.

    This class provides methods for creating and managing partitions
    of CSV data for efficient querying.
    """

    def __init__(self,
                 file_path: str,
                 partition_dir: Optional[str] = None,
                 grid_size: int = 4,
                 min_partition_size: int = 100):
        """
        Initialize the CSV partitioner.

        Args:
            file_path: Path to the CSV file
            partition_dir: Directory for partitioned data (defaults to file_path + "_partitions")
            grid_size: Number of grid cells per dimension (smaller = fewer partitions)
            min_partition_size: Minimum number of records per partition

        """
        self.file_path = file_path
        self.partition_dir = partition_dir or f"{os.path.splitext(file_path)[0]}_partitions"
        self.grid_size = grid_size
        self.min_partition_size = min_partition_size
        # Using direct logging functions instead of logger instance

        # Partitioning information
        self._partitions: Dict[str, List[str]] = {}
        self._partition_metadata: Dict[str, Dict[str, Any]] = {}

    def load_partition_metadata(self) -> bool:
        """
        Load partition metadata from disk.

        Returns:
            True if metadata was loaded successfully, False otherwise
        """
        metadata_path = os.path.join(
            self.partition_dir, "partition_metadata.json")
        if not os.path.exists(metadata_path):
            return False

        try:
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
                self._partitions = metadata.get("partitions", {})
                self._partition_metadata = metadata.get("metadata", {})

            info(
                f"Loaded partition metadata with {sum(len(files) for files in self._partitions.values())} partitions")
            return True
        except Exception as e:
            error(f"Error loading partition metadata: {e}")
            return False

    def save_partition_metadata(self) -> bool:
        """
        Save partition metadata to disk.

        Returns:
            True if metadata was saved successfully, False otherwise
        """
        if not os.path.exists(self.partition_dir):
            os.makedirs(self.partition_dir, exist_ok=True)

        metadata_path = os.path.join(
            self.partition_dir, "partition_metadata.json")
        try:
            with open(metadata_path, 'w') as f:
                json.dump({
                    "partitions": self._partitions,
                    "metadata": self._partition_metadata,
                    "created_at": time.time()
                }, f, indent=2)

            info(f"Saved partition metadata to {metadata_path}")
            return True
        except Exception as e:
            error(f"Error saving partition metadata: {e}")
            return False

    def create_partitions(self, df: pd.DataFrame) -> bool:
        """
        Create partitions from a DataFrame.

        Args:
            df: DataFrame to partition

        Returns:
            True if partitions were created successfully, False otherwise
        """
        if df is None or len(df) == 0:
            warning(
                "Cannot create partitions: DataFrame is empty or None")
            return False

        info("Creating dataset partitions...")

        # Create partition directory if it doesn't exist
        if not os.path.exists(self.partition_dir):
            os.makedirs(self.partition_dir, exist_ok=True)

        # Create geographic partitions
        success = self._create_geographic_partitions(df)

        # Create category partitions if 'category' column exists
        if 'category' in df.columns:
            success = success and self._create_category_partitions(df)

        # Save partition metadata
        if success:
            success = success and self.save_partition_metadata()

        info(
            f"Created {sum(len(files) for files in self._partitions.values())} partitions")
        return success

    def _create_geographic_partitions(self, df: pd.DataFrame) -> bool:
        """
        Create geographic partitions.

        Args:
            df: DataFrame to partition

        Returns:
            True if partitions were created successfully, False otherwise
        """
        # Check if required columns exist
        if 'latitude' not in df.columns or 'longitude' not in df.columns:
            warning(
                "Cannot create geographic partitions: missing latitude/longitude columns")
            return False

        # Initialize geographic partitions list
        self._partitions['geo'] = []

        # Get min/max latitude and longitude
        min_lat = df['latitude'].min()
        max_lat = df['latitude'].max()
        min_lon = df['longitude'].min()
        max_lon = df['longitude'].max()

        # Use the configured grid size
        num_cells = self.grid_size

        # Create latitude and longitude ranges
        lat_step = (max_lat - min_lat) / num_cells
        lon_step = (max_lon - min_lon) / num_cells

        lat_ranges = [min_lat + i * lat_step for i in range(num_cells + 1)]
        lon_ranges = [min_lon + i * lon_step for i in range(num_cells + 1)]

        # Create a partition for each grid cell
        for i in range(len(lat_ranges) - 1):
            for j in range(len(lon_ranges) - 1):
                cell_min_lat = lat_ranges[i]
                cell_max_lat = lat_ranges[i + 1]
                cell_min_lon = lon_ranges[j]
                cell_max_lon = lon_ranges[j + 1]

                # Filter data for this cell
                cell_data = df[
                    (df['latitude'] >= cell_min_lat) &
                    (df['latitude'] < cell_max_lat) &
                    (df['longitude'] >= cell_min_lon) &
                    (df['longitude'] < cell_max_lon)
                ]

                # Skip cells with too few records
                if len(cell_data) < self.min_partition_size:
                    debug(
                        f"Skipping small partition with {len(cell_data)} records")
                    continue

                # Create partition file
                partition_name = f"geo_{cell_min_lat:.6f}_{cell_min_lon:.6f}_{cell_max_lat:.6f}_{cell_max_lon:.6f}.csv"
                partition_path = os.path.join(
                    self.partition_dir, partition_name)

                # Save partition
                cell_data.to_csv(partition_path, index=False)

                # Add to partitions list
                self._partitions['geo'].append(partition_name)

                # Add metadata
                self._partition_metadata[partition_name] = {
                    'type': 'geo',
                    'min_lat': cell_min_lat,
                    'max_lat': cell_max_lat,
                    'min_lon': cell_min_lon,
                    'max_lon': cell_max_lon,
                    'count': len(cell_data),
                    'created_at': time.time()
                }

        info(
            f"Created {len(self._partitions['geo'])} geographic partitions")
        return True

    def _create_category_partitions(self, df: pd.DataFrame) -> bool:
        """
        Create category partitions.

        Args:
            df: DataFrame to partition

        Returns:
            True if partitions were created successfully, False otherwise
        """
        # Initialize category partitions list
        self._partitions['category'] = []

        # Get unique categories
        categories = df['category'].dropna().unique()

        # Create a partition for each category
        for category in categories:
            # Skip empty categories
            if pd.isna(category) or category == '':
                continue

            # Filter data for this category
            category_data = df[df['category'] == category]

            # Skip partitions with too few records
            if len(category_data) < self.min_partition_size:
                debug(
                    f"Skipping small category partition with {len(category_data)} records")
                continue

            # Create partition file
            safe_category = re.sub(r'[^\w\-\.]', '_', str(category))
            partition_name = f"category_{safe_category}.csv"
            partition_path = os.path.join(self.partition_dir, partition_name)

            # Save partition
            category_data.to_csv(partition_path, index=False)

            # Add to partitions list
            self._partitions['category'].append(partition_name)

            # Add metadata
            self._partition_metadata[partition_name] = {
                'type': 'category',
                'category': category,
                'count': len(category_data),
                'created_at': time.time()
            }

        info(
            f"Created {len(self._partitions['category'])} category partitions")
        return True

    def find_matching_partitions(self,
                                 center_lat: Optional[float] = None,
                                 center_lon: Optional[float] = None,
                                 radius_m: Optional[int] = None,
                                 category: Optional[str] = None) -> List[str]:
        """
        Find partitions that match the given criteria.

        Args:
            center_lat: Center latitude for geospatial search
            center_lon: Center longitude for geospatial search
            radius_m: Radius in meters for geospatial search
            category: Category for category-based search

        Returns:
            List of matching partition names
        """
        matching_partitions = []

        # If no criteria provided, return all partitions
        if center_lat is None and center_lon is None and category is None:
            return [partition for partitions in self._partitions.values() for partition in partitions]

        # Find matching geographic partitions
        if center_lat is not None and center_lon is not None and radius_m is not None:
            # Convert radius to degrees (approximate)
            earth_radius_m = 6371000
            lat_radius_deg = (radius_m / earth_radius_m) * (180 / math.pi)
            lon_radius_deg = lat_radius_deg / \
                math.cos(math.radians(center_lat))

            # Calculate bounding box
            min_lat = center_lat - lat_radius_deg
            max_lat = center_lat + lat_radius_deg
            min_lon = center_lon - lon_radius_deg
            max_lon = center_lon + lon_radius_deg

            # Find matching partitions
            for partition_name in self._partitions.get('geo', []):
                metadata = self._partition_metadata.get(partition_name, {})

                # Check if partition overlaps with bounding box
                if (metadata.get('min_lat', 0) <= max_lat and
                    metadata.get('max_lat', 0) >= min_lat and
                    metadata.get('min_lon', 0) <= max_lon and
                        metadata.get('max_lon', 0) >= min_lon):
                    matching_partitions.append(partition_name)

        # Find matching category partitions
        if category is not None:
            for partition_name in self._partitions.get('category', []):
                metadata = self._partition_metadata.get(partition_name, {})

                # Check if partition matches category
                if metadata.get('category') == category:
                    matching_partitions.append(partition_name)

        return matching_partitions

    def get_partitions(self) -> Dict[str, List[str]]:
        """
        Get all partitions.

        Returns:
            Dictionary mapping partition types to lists of partition names
        """
        return self._partitions

    def get_partition_metadata(self) -> Dict[str, Dict[str, Any]]:
        """
        Get partition metadata.

        Returns:
            Dictionary mapping partition names to metadata
        """
        return self._partition_metadata
