# src/infrastructure/data_access/providers/postgres/geo_queries.py

"""
Geospatial query utilities for PostgreSQL provider.

This module provides utilities for building and executing geospatial
queries using PostGIS.
"""

from typing import Dict, List, Any, Optional, Tuple
from src.infrastructure.data_access.core.interfaces import GeospatialFilter


class PostGISQueryBuilder:
    """
    Utility for building PostGIS geospatial queries.
    
    This class provides methods for building SQL queries that use
    PostGIS functions for efficient geospatial operations.
    """
    
    @staticmethod
    def build_distance_query(geo_filter: GeospatialFilter) -> Tuple[str, List[Any]]:
        """
        Build a PostGIS query for finding points within a radius.
        
        Args:
            geo_filter: Geospatial filter parameters
            
        Returns:
            Tuple of (SQL fragment, parameters)
        """
        sql = f"ST_DWithin(ST_SetSRID(ST_MakePoint({geo_filter.lon_field}, {geo_filter.lat_field}), 4326)::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography, %s)"
        params = [geo_filter.center_lon, geo_filter.center_lat, geo_filter.radius_m]
        
        return sql, params
    
    @staticmethod
    def build_distance_calculation(geo_filter: GeospatialFilter, alias: str = "distance") -> Tuple[str, List[Any]]:
        """
        Build a PostGIS query for calculating distance between points.
        
        Args:
            geo_filter: Geospatial filter parameters
            alias: Alias for the distance column
            
        Returns:
            Tuple of (SQL fragment, parameters)
        """
        sql = f"ST_Distance(ST_SetSRID(ST_MakePoint({geo_filter.lon_field}, {geo_filter.lat_field}), 4326)::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography) AS {alias}"
        params = [geo_filter.center_lon, geo_filter.center_lat]
        
        return sql, params
    
    @staticmethod
    def build_bounding_box_query(geo_filter: GeospatialFilter) -> Tuple[str, List[Any]]:
        """
        Build a PostGIS query for finding points within a bounding box.
        
        This is often used as a pre-filter before the more expensive
        distance calculation.
        
        Args:
            geo_filter: Geospatial filter parameters
            
        Returns:
            Tuple of (SQL fragment, parameters)
        """
        # Calculate bounding box (approximate)
        earth_radius_m = 6371000
        lat_radius_deg = (geo_filter.radius_m / earth_radius_m) * (180 / 3.14159)
        lon_radius_deg = lat_radius_deg / max(0.1, abs(math.cos(math.radians(geo_filter.center_lat))))
        
        min_lat = geo_filter.center_lat - lat_radius_deg
        max_lat = geo_filter.center_lat + lat_radius_deg
        min_lon = geo_filter.center_lon - lon_radius_deg
        max_lon = geo_filter.center_lon + lon_radius_deg
        
        sql = f"ST_MakeEnvelope(%s, %s, %s, %s, 4326) && ST_SetSRID(ST_MakePoint({geo_filter.lon_field}, {geo_filter.lat_field}), 4326)"
        params = [min_lon, min_lat, max_lon, max_lat]
        
        return sql, params
