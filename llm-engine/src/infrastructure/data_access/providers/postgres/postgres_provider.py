# src/infrastructure/data_access/providers/postgres/postgres_provider.py

"""
PostgreSQL implementation of the data provider interface.

This module provides a PostgreSQL-based implementation of the data provider interface,
with support for geospatial queries using PostGIS.
"""

import os
import pandas as pd
import threading
import time
from typing import Any, Dict, List, Optional, Tuple, Union, cast
import re
import math

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical
from src.infrastructure.data_access.core.interfaces import (
    IDataProvider, IDataQuery, IDataResult,
    QueryFilter, GeospatialFilter
)
from src.infrastructure.data_access.core.query import DataQuery
from src.infrastructure.data_access.core.result import DataResult
from src.infrastructure.cache.factory import CacheFactory


class PostgresDataProvider(IDataProvider):
    """
    PostgreSQL implementation of the data provider interface.

    Features:
    - Native SQL query execution
    - Query result caching
    - Asynchronous background loading
    - PostGIS geospatial query support
    """

    def __init__(self,
                 connection_string: str,
                 table_name: str,
                 schema_name: str = "public",
                 cache_enabled: bool = True,
                 cache_ttl_seconds: int = 300,
                 cache_max_size: int = 1000,
                 auto_initialize: bool = True):
        """
        Initialize the PostgreSQL data provider.

        Args:
            connection_string: PostgreSQL connection string
            table_name: Name of the table
            schema_name: Name of the schema
            cache_enabled: Whether to enable query result caching
            cache_ttl_seconds: Time-to-live for cached results in seconds
            cache_max_size: Maximum number of cached results
            auto_initialize: Whether to initialize automatically
        """
        if not POSTGRES_AVAILABLE:
            raise ImportError("psycopg2 is required for PostgresDataProvider")

        self.connection_string = connection_string
        self.table_name = table_name
        self.schema_name = schema_name

        # Initialization state
        self._initialized = False
        self._initializing = False
        self._initialization_error = None
        self._initialization_lock = threading.RLock()
        self._loading_thread: Optional[threading.Thread] = None

        # Cache configuration
        self.cache_enabled = cache_enabled
        self.cache_ttl_seconds = cache_ttl_seconds
        self.cache_max_size = cache_max_size

        # Use specialized data cache
        self.data_cache = CacheFactory.create_data_cache()

        # Schema information
        self._schema: Dict[str, str] = {}

        # Start initialization if requested
        if auto_initialize:
            self._start_initialization()

    def _start_initialization(self) -> None:
        """Start asynchronous initialization in a background thread."""
        with self._initialization_lock:
            if not self._initialized and not self._initializing:
                self._initializing = True
                self._loading_thread = threading.Thread(
                    target=self._initialize_thread)
                self._loading_thread.daemon = True
                self._loading_thread.start()
                info(
                    f"Started background initialization of PostgreSQL dataset: {self.table_name}")

    def _initialize_thread(self) -> None:
        """Background thread function for initialization."""
        try:
            success = self._initialize_connection()
            with self._initialization_lock:
                self._initialized = success
                self._initializing = False
                if success:
                    info(
                        f"Successfully initialized PostgreSQL dataset: {self.table_name}")
                else:
                    error(
                        f"Failed to initialize PostgreSQL dataset: {self.table_name}")
        except Exception as e:
            with self._initialization_lock:
                self._initialization_error = str(e)
                self._initializing = False
                self._initialized = False
            error(
                f"Error initializing PostgreSQL dataset {self.table_name}: {e}")

    def _wait_for_initialization(self, timeout_seconds: float = 30.0) -> bool:
        """
        Wait for initialization to complete.

        Args:
            timeout_seconds: Maximum time to wait in seconds

        Returns:
            True if initialization completed successfully, False otherwise
        """
        start_time = time.time()
        while time.time() - start_time < timeout_seconds:
            with self._initialization_lock:
                if self._initialized:
                    return True
                if not self._initializing:
                    return False
            time.sleep(0.1)
        return False

    def _initialize_connection(self) -> bool:
        """
        Initialize the connection to the PostgreSQL database.

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Test the connection
            with psycopg2.connect(self.connection_string) as conn:
                with conn.cursor() as cursor:
                    # Check if the table exists
                    cursor.execute(
                        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = %s AND table_name = %s)",
                        (self.schema_name, self.table_name)
                    )
                    table_exists = cursor.fetchone()[0]

                    if not table_exists:
                        error(
                            f"Table {self.schema_name}.{self.table_name} does not exist")
                        return False

                    # Get table schema
                    cursor.execute(
                        """
                        SELECT column_name, data_type
                        FROM information_schema.columns
                        WHERE table_schema = %s AND table_name = %s
                        """,
                        (self.schema_name, self.table_name)
                    )

                    for column_name, data_type in cursor.fetchall():
                        self._schema[column_name] = data_type

                    # Check if PostGIS is available
                    cursor.execute("SELECT PostGIS_version()")
                    postgis_version = cursor.fetchone()[0]
                    info(f"PostGIS version: {postgis_version}")

                    # Get row count
                    cursor.execute(
                        f"SELECT COUNT(*) FROM {self.schema_name}.{self.table_name}")
                    row_count = cursor.fetchone()[0]

                    info(
                        f"Connected to PostgreSQL database, table {self.schema_name}.{self.table_name} has {row_count} rows")
                    return True
        except Exception as e:
            error(f"Error connecting to PostgreSQL database: {e}")
            return False

    def initialize(self) -> bool:
        """
        Initialize the data provider.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        with self._initialization_lock:
            if self._initialized:
                return True
            if self._initializing:
                return self._wait_for_initialization()

        return self._initialize_connection()

    def is_initialized(self) -> bool:
        """
        Check if the provider is initialized and ready to use.

        Returns:
            bool: True if the provider is initialized, False otherwise
        """
        with self._initialization_lock:
            return self._initialized

    def _generate_cache_key(self, query: IDataQuery) -> str:
        """
        Generate a cache key for a query.

        Args:
            query: Query to generate a key for

        Returns:
            Cache key string
        """
        # Convert filters to a string representation
        filter_strs = []
        for f in query.filters:
            filter_strs.append(f"{f.field}:{f.operator}:{str(f.value)}")

        # Convert geo filter to a string representation
        geo_str = ""
        if query.geo_filter:
            geo = query.geo_filter
            geo_str = f"geo:{geo.center_lat},{geo.center_lon},{geo.radius_m},{geo.lat_field},{geo.lon_field}"

        # Convert options to a string representation
        opt = query.options
        opt_str = f"opt:{opt.limit},{opt.offset},{opt.sort_by},{opt.sort_order}"

        # Combine all parts
        key_parts = [
            ",".join(filter_strs),
            geo_str,
            opt_str
        ]

        return "|".join(key_parts)

    def query(self, query: IDataQuery) -> IDataResult:
        """
        Query the dataset with the given parameters.

        Args:
            query: Query parameters

        Returns:
            Query results
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for query")
                return DataResult([], 0)

        # Check if the result is in cache
        cache_key = self._generate_cache_key(query)
        hit, cached_result = self.data_cache.get_query_result(cache_key)
        if hit:
            debug(f"Cache hit for query: {cache_key}")
            return cached_result

        # Cache miss, execute the query
        debug(f"Cache miss for query: {cache_key}")

        try:
            # Build SQL query
            sql = f"SELECT * FROM {self.schema_name}.{self.table_name} WHERE 1=1"
            params = []

            # Add filters
            for filter_obj in query.filters:
                if filter_obj.operator == 'eq':
                    sql += f" AND {filter_obj.field} = %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'neq':
                    sql += f" AND {filter_obj.field} != %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'gt':
                    sql += f" AND {filter_obj.field} > %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'gte':
                    sql += f" AND {filter_obj.field} >= %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'lt':
                    sql += f" AND {filter_obj.field} < %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'lte':
                    sql += f" AND {filter_obj.field} <= %s"
                    params.append(filter_obj.value)
                elif filter_obj.operator == 'in':
                    placeholders = ", ".join(["%s"] * len(filter_obj.value))
                    sql += f" AND {filter_obj.field} IN ({placeholders})"
                    params.extend(filter_obj.value)
                elif filter_obj.operator == 'contains':
                    sql += f" AND {filter_obj.field} ILIKE %s"
                    params.append(f"%{filter_obj.value}%")

            # Add geospatial filter using PostGIS
            if query.geo_filter:
                geo = query.geo_filter
                # Use ST_DWithin for efficient radius search
                sql += f" AND ST_DWithin(ST_SetSRID(ST_MakePoint({geo.lon_field}, {geo.lat_field}), 4326)::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography, %s)"
                params.extend([geo.center_lon, geo.center_lat, geo.radius_m])

            # Get total count for pagination
            count_sql = f"SELECT COUNT(*) FROM ({sql}) AS count_query"

            # Add sorting
            if query.options.sort_by:
                direction = "ASC" if query.options.sort_order == 'asc' else "DESC"
                sql += f" ORDER BY {query.options.sort_by} {direction}"

            # Add pagination
            if query.options.limit:
                sql += f" LIMIT %s"
                params.append(query.options.limit)

            if query.options.offset:
                sql += f" OFFSET %s"
                params.append(query.options.offset)

            # Execute query
            with psycopg2.connect(self.connection_string) as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    # Get total count
                    cursor.execute(count_sql, params[:-2] if query.options.limit and query.options.offset else params[:-1]
                                   if query.options.limit or query.options.offset else params)
                    total = cursor.fetchone()['count']

                    # Execute main query
                    cursor.execute(sql, params)
                    items = cursor.fetchall()

            # Calculate page number
            page = 1
            if query.options.offset and query.options.limit and query.options.limit > 0:
                page = (query.options.offset // query.options.limit) + 1

            # Create result
            result = DataResult(
                items=[dict(item) for item in items],
                total=total,
                page=page,
                page_size=query.options.limit or len(items)
            )

            # Cache the result
            self.data_cache.cache_query_result(cache_key, result)

            return result
        except Exception as e:
            error(f"Error executing PostgreSQL query: {e}")
            return DataResult([], 0)

    def get_by_id(self, id_field: str, id_value: Any) -> Optional[Dict[str, Any]]:
        """
        Get a single item by its ID.

        Args:
            id_field: Name of the ID field
            id_value: Value of the ID

        Returns:
            Item data or None if not found
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for get_by_id")
                return None

        try:
            # Build SQL query
            sql = f"SELECT * FROM {self.schema_name}.{self.table_name} WHERE {id_field} = %s"

            # Execute query
            with psycopg2.connect(self.connection_string) as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(sql, [id_value])
                    item = cursor.fetchone()

            if item:
                return dict(item)
            else:
                return None
        except Exception as e:
            error(f"Error executing PostgreSQL get_by_id: {e}")
            return None

    def get_dataframe(self) -> pd.DataFrame:
        """
        Get the entire dataset as a pandas DataFrame.

        Returns:
            DataFrame containing the dataset
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for get_dataframe")
                return pd.DataFrame()

        try:
            # Build SQL query
            sql = f"SELECT * FROM {self.schema_name}.{self.table_name}"

            # Execute query
            with psycopg2.connect(self.connection_string) as conn:
                return pd.read_sql(sql, conn)
        except Exception as e:
            error(f"Error executing PostgreSQL get_dataframe: {e}")
            return pd.DataFrame()

    def get_schema(self) -> Dict[str, str]:
        """
        Get the schema of the dataset.

        Returns:
            Dictionary mapping field names to their types
        """
        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for get_schema")
                return {}

        return self._schema.copy()

    def get_unique_values(self, field: str) -> List[Any]:
        """
        Get all unique values for a field.

        Args:
            field: Field name

        Returns:
            List of unique values
        """
        # Check if the result is in cache
        hit, cached_values = self.data_cache.get_unique_values(field)
        if hit:
            return cached_values

        # Ensure the dataset is initialized
        if not self.is_initialized():
            success = self.initialize()
            if not success:
                error("Failed to initialize dataset for get_unique_values")
                return []

        try:
            # Build SQL query
            sql = f"SELECT DISTINCT {field} FROM {self.schema_name}.{self.table_name} WHERE {field} IS NOT NULL ORDER BY {field}"

            # Execute query
            with psycopg2.connect(self.connection_string) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    unique_values = [row[0] for row in cursor.fetchall()]

            # Cache the result
            self.data_cache.cache_unique_values(field, unique_values)

            return unique_values
        except Exception as e:
            error(f"Error executing PostgreSQL get_unique_values: {e}")
            return []

    def count(self, query: Optional[IDataQuery] = None) -> int:
        """
        Count items matching the query.

        Args:
            query: Optional query parameters

        Returns:
            Count of matching items
        """
        # If no query, return total count
        if query is None:
            # Ensure the dataset is initialized
            if not self.is_initialized():
                success = self.initialize()
                if not success:
                    error("Failed to initialize dataset for count")
                    return 0

            try:
                # Build SQL query
                sql = f"SELECT COUNT(*) FROM {self.schema_name}.{self.table_name}"

                # Execute query
                with psycopg2.connect(self.connection_string) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(sql)
                        count = cursor.fetchone()[0]

                return count
            except Exception as e:
                error(f"Error executing PostgreSQL count: {e}")
                return 0

        # Check if the result is in cache
        cache_key = f"count:{self._generate_cache_key(query)}"
        hit, cached_count = self.data_cache.get_count(cache_key)
        if hit:
            return cached_count

        # Execute the query and get the total count
        result = self.query(query)
        count = result.total

        # Cache the result
        self.data_cache.cache_count(cache_key, count)

        return count

    def refresh(self) -> bool:
        """
        Refresh the dataset from its source.

        Returns:
            True if refresh was successful, False otherwise
        """
        with self._initialization_lock:
            self._initialized = False
            self._initializing = False
            self._initialization_error = None

        # Clear caches
        self.data_cache.clear()

        # Reload the data
        return self.initialize()
