# src/infrastructure/config/config.py

"""
Configuration Management

This module provides configuration utilities and manager factory functions.
"""

import os
import json
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class ConfigManager:
    """
    Configuration Manager with direct manager factory functions.

    Provides configuration loading and manager creation without microservice overhead.
    """

    _instance = None
    _is_initialized = False

    def __new__(cls, config_file: str = "config/config.json"):
        """Implement singleton pattern for ConfigManager."""
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_file: str = "config/config.json"):
        """Initialize the ConfigManager."""
        if not self._is_initialized:
            self.config_file = config_file
            self.config = self._load_config()
            load_dotenv()
            self._is_initialized = True

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        default_config = {
            "environment": "development",
            "storage_dir": "app_data",
            "sessions_dir": "app_data/sessions",
            "history_dir": "app_data/chat_history",
            "cache_dir": "app_data/cache",
            "logs_dir": "app_data/logs",
            "data_paths": {
                "dataset": "data/dataset.csv"
            },
            "cache_enabled": True,
            "llm_cache_enabled": True,
            "llm_providers": {
                "llama": {
                    "api_key_env_var": "apiKey",
                    "tested_models": ["llama3.1-70b"]
                },
                "openrouter": {
                    "api_key_env_var": "OPENROUTER_API_KEY",
                    "site_url": "https://wizlop.com",
                    "site_name": "Wizlop",
                    "tested_models": [
                        "qwen/qwen-2.5-7b-instruct:free",
                        "qwen/qwen2.5-vl-3b-instruct:free",
                        "deepseek/deepseek-r1-0528-qwen3-8b:free"
                    ]
                }
            },
            "agents": {
                "main": {
                    "provider": "openrouter",
                    "model": "qwen/qwen-2.5-7b-instruct:free",
                    "hyperparameters": {
                        "max_tokens": 5000,
                        "temperature": 0.2,
                        "response_format": {
                            "type": "json_object"
                        }
                    }
                },
                "request": {
                    "provider": "openrouter",
                    "model": "qwen/qwen-2.5-7b-instruct:free",
                    "hyperparameters": {
                        "max_tokens": 5000,
                        "temperature": 0.2,
                        "response_format": {
                            "type": "json_object"
                        }
                    }
                },
                "advice": {
                    "provider": "openrouter",
                    "model": "qwen/qwen-2.5-7b-instruct:free",
                    "hyperparameters": {
                        "max_tokens": 7000,
                        "temperature": 0.2,
                        "response_format": {
                            "type": "json_object"
                        }
                    }
                }
            }
        }

        if not os.path.exists(self.config_file):
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config

        try:
            with open(self.config_file, 'r') as f:
                loaded_config = json.load(f)
                return {**default_config, **loaded_config}
        except json.JSONDecodeError:
            return default_config

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a configuration value by key."""
        value = self.config.get(key, default)

        # Process path-related keys
        path_related_keys = ['sessions_dir',
                             'history_dir', 'cache_dir', 'logs_dir']
        if key in path_related_keys and value:
            if not os.path.isabs(value):
                storage_dir = self.config.get('storage_dir', 'app_data')
                if '/' in value:
                    value = value.split('/')[-1]
                value = os.path.join(storage_dir, value)
                os.makedirs(value, exist_ok=True)

        return value

    def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration values and save to file."""
        self.config.update(updates)
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except Exception:
            return False

    def get_state_manager(self):
        """Get state manager directly."""
        try:
            from src.infrastructure.state.json_manager import JSONStateManager

            sessions_dir = self.get_config_value(
                "sessions_dir", "app_data/sessions")
            cache_enabled = self.get_config_value("memory_cache_enabled", True)
            cache_ttl = self.get_config_value("memory_cache_ttl", 300)
            cache_max_size = self.get_config_value(
                "memory_cache_max_size", 1000)

            return JSONStateManager(
                sessions_dir=sessions_dir,
                cache_enabled=cache_enabled,
                cache_ttl_seconds=cache_ttl,
                cache_max_size=cache_max_size
            )
        except Exception:
            return None

    def get_history_manager(self):
        """Get history manager directly."""
        try:
            from src.infrastructure.history.json_manager import JSONHistoryManager

            history_dir = self.get_config_value(
                "history_dir", "app_data/chat_history")
            cache_enabled = self.get_config_value("memory_cache_enabled", True)
            cache_ttl = self.get_config_value("memory_cache_ttl", 300)
            cache_max_size = self.get_config_value(
                "memory_cache_max_size", 1000)

            return JSONHistoryManager(
                history_dir=history_dir,
                cache_enabled=cache_enabled,
                cache_ttl_seconds=cache_ttl,
                cache_max_size=cache_max_size
            )
        except Exception:
            return None

    def get_cache_manager(self):
        """Get cache manager directly."""
        try:
            from src.infrastructure.cache.manager import UnifiedCacheManager
            return UnifiedCacheManager()
        except Exception:
            return None

    def get_api_key(self, provider_id: Optional[str] = None) -> str:
        """Get API key from environment variables."""
        if provider_id is None:
            provider_id = self.config.get("llm_provider", "llama")

        provider_config = self.config.get(
            "llm_providers", {}).get(provider_id, {})
        env_var = provider_config.get("api_key_env_var")

        return os.getenv(env_var) if env_var else None

    def get_agent_llm_config(self, agent_id: str) -> Dict[str, Any]:
        """Get LLM configuration for a specific agent."""
        agents_config = self.config.get("agents", {})
        agent_config = agents_config.get(agent_id)

        if not agent_config:
            raise ValueError(
                f"Agent configuration not found for agent_id: {agent_id}")

        return agent_config

    def is_llm_caching_enabled(self) -> bool:
        """Check if LLM caching is enabled."""
        return self.config.get("llm_cache_enabled", True)

    def is_caching_enabled(self) -> bool:
        """Check if general caching is enabled."""
        return self.config.get("cache_enabled", True)

    def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration values and save to file."""
        try:
            # Update the in-memory config
            self.config.update(updates)

            # Save to file
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)

            return True
        except Exception:
            return False

    @property
    def dataset_path(self) -> str:
        """Get dataset path for backward compatibility."""
        return self.get_config_value("data_paths", {}).get("dataset", "data/dataset.csv")
