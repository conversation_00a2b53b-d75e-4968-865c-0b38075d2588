# src/infrastructure/config/factory.py

"""
Factory Functions

This module provides factory functions for creating services.
"""

from .config import Config<PERSON>ana<PERSON>


def create_conversation_service_from_config():
    """Create conversation service using ConfigManager."""
    try:
        from src.services.conversation.consolidated_service import ConsolidatedConversationService

        config = ConfigManager()
        state_manager = config.get_state_manager()
        history_manager = config.get_history_manager()

        if state_manager and history_manager:
            return ConsolidatedConversationService(
                state_manager=state_manager,
                history_manager=history_manager
            )
        return None
    except Exception as e:
        print(f"Factory error: {e}")
        import traceback
        traceback.print_exc()
        return None
