# src/infrastructure/cache/core/base_cache.py
from abc import ABC, abstractmethod
from typing import Any, Tu<PERSON>, Optional, Dict, List
from enum import Enum


class CacheType(Enum):
    """Cache type enumeration for specialized cache managers."""
    LLM_API = "llm_api"
    DATA_QUERY = "data_query"
    STATE_SESSION = "state_session"
    GRAPH_NETWORK = "graph_network"
    HISTORY_CONVERSATION = "history_conversation"


class BaseCacheInterface(ABC):
    """Base interface for all cache implementations."""

    @abstractmethod
    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve a value from the cache."""
        pass

    @abstractmethod
    def set(self, key: str, value: Any) -> bool:
        """Store a value in the cache."""
        pass

    @abstractmethod
    def invalidate(self, key: str) -> bool:
        """Remove a specific key from the cache."""
        pass

    @abstractmethod
    def clear(self) -> bool:
        """Clear all cache entries."""
        pass

    @abstractmethod
    def get_cache_type(self) -> CacheType:
        """Return the cache type for this implementation."""
        pass


class CacheConfig:
    """Configuration class for cache settings."""
    
    def __init__(self, 
                 enabled: bool = True,
                 ttl_seconds: int = 300,
                 max_size: int = 1000,
                 cache_dir: Optional[str] = None,
                 **kwargs):
        self.enabled = enabled
        self.ttl_seconds = ttl_seconds
        self.max_size = max_size
        self.cache_dir = cache_dir
        self.extra_config = kwargs

    @classmethod
    def for_llm_api(cls, **kwargs) -> 'CacheConfig':
        """Create configuration optimized for LLM API caching."""
        defaults = {
            'ttl_seconds': 3600,  # 1 hour for API responses
            'max_size': 500,      # Moderate size for API responses
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @classmethod
    def for_data_query(cls, **kwargs) -> 'CacheConfig':
        """Create configuration optimized for data query caching."""
        defaults = {
            'ttl_seconds': 600,   # 10 minutes for query results
            'max_size': 200,      # Smaller size for query results
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @classmethod
    def for_state_session(cls, **kwargs) -> 'CacheConfig':
        """Create configuration optimized for state/session caching."""
        defaults = {
            'ttl_seconds': 1800,  # 30 minutes for session data
            'max_size': 1000,     # Large size for session data
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @classmethod
    def for_graph_network(cls, **kwargs) -> 'CacheConfig':
        """Create configuration optimized for network graph caching."""
        defaults = {
            'ttl_seconds': 7200,  # 2 hours for network graphs
            'max_size': 50,       # Small size for large graph objects
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @classmethod
    def for_history_conversation(cls, **kwargs) -> 'CacheConfig':
        """Create configuration optimized for conversation history caching."""
        defaults = {
            'ttl_seconds': 900,   # 15 minutes for conversation history
            'max_size': 500,      # Moderate size for conversation data
        }
        defaults.update(kwargs)
        return cls(**defaults)
