# src/infrastructure/cache/memory_cache.py
import time
import threading
from typing import Dict, Any, Optional, TypeVar, Generic, Callable

T = TypeVar('T')


class MemoryCache(Generic[T]):
    """Simple in-memory cache with time-based expiration."""

    def __init__(self, ttl_seconds: int = 300, max_size: int = 1000, enabled: bool = True):
        """Initialize the memory cache."""
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.ttl_seconds = ttl_seconds
        self.max_size = max_size
        self.enabled = enabled
        self.lock = threading.RLock()

    def get(self, key: str) -> Optional[T]:
        """Get an item from the cache."""
        if not self.enabled:
            return None

        with self.lock:
            if key not in self.cache:
                return None

            entry = self.cache[key]
            current_time = time.time()

            # Check if expired
            if current_time > entry['expires']:
                del self.cache[key]
                return None

            # Update access time
            entry['last_accessed'] = current_time
            return entry['value']

    def set(self, key: str, value: T) -> None:
        """Set an item in the cache."""
        if not self.enabled:
            return

        with self.lock:
            # Evict if at capacity and this is a new key
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_oldest()

            current_time = time.time()
            self.cache[key] = {
                'value': value,
                'expires': current_time + self.ttl_seconds,
                'last_accessed': current_time
            }

    def delete(self, key: str) -> None:
        """Delete an item from the cache."""
        if not self.enabled:
            return

        with self.lock:
            if key in self.cache:
                del self.cache[key]

    def clear(self) -> None:
        """Clear all items from the cache."""
        if not self.enabled:
            return

        with self.lock:
            self.cache.clear()

    def get_or_set(self, key: str, factory: Callable[[], T]) -> T:
        """
        Get an item from the cache or set it using the factory function.

        Args:
            key: The cache key
            factory: Function to call to create the value if not in cache

        Returns:
            The cached or newly created value
        """
        if not self.enabled:
            return factory()

        value = self.get(key)
        if value is None:
            value = factory()
            self.set(key, value)
        return value

    def _evict_oldest(self) -> None:
        """Evict the least recently accessed item from the cache."""
        if not self.cache:
            return

        oldest_key = min(self.cache.keys(),
                        key=lambda k: self.cache[k]['last_accessed'])
        del self.cache[oldest_key]
