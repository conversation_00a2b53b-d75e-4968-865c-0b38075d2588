# src/infrastructure/cache/core/file_cache.py
import os
import joblib
import hashlib
import json
from typing import Any, Callable, Tu<PERSON>

from .base_cache import BaseCacheInterface, CacheType
from src.infrastructure.log.unified_logger import error


class FileCache(BaseCacheInterface):
    """Simple joblib-based file cache implementation."""

    def __init__(self, cache_dir: str = None, enabled: bool = True):
        """Initialize the cache manager."""
        if cache_dir is None:
            cache_dir = "app_data/cache"

        self.cache_dir = cache_dir
        self.enabled = enabled

        if self.enabled and not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

    def _get_cache_path(self, key: str) -> str:
        """
        Generate the file path for a cache key.

        Args:
            key: The cache key

        Returns:
            The file path for the cache entry
        """
        # Create a hash of the key to use as filename
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{key_hash}.joblib")

    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve a value from the cache."""
        if not self.enabled:
            return False, None

        cache_path = self._get_cache_path(key)
        if os.path.exists(cache_path):
            try:
                return True, joblib.load(cache_path)
            except Exception as e:
                error(f"Error loading cache for key {key}: {e}")
        return False, None

    def set(self, key: str, value: Any) -> bool:
        """Store a value in the cache."""
        if not self.enabled:
            return False

        cache_path = self._get_cache_path(key)
        try:
            joblib.dump(value, cache_path)
            return True
        except Exception as e:
            error(f"Error caching value for key {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """Remove a specific key from the cache."""
        cache_path = self._get_cache_path(key)
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                return True
            except Exception as e:
                error(f"Error invalidating cache for key {key}: {e}")
        return False

    def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.joblib'):
                    file_path = os.path.join(self.cache_dir, filename)
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        error(f"Error removing cache file {file_path}: {e}")
            return True
        except Exception as e:
            error(f"Error clearing cache: {e}")
            return False



    def cached_call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute a function with caching."""
        if not self.enabled:
            return func(*args, **kwargs)

        try:
            # Create simple cache key from function name and arguments
            func_name = func.__name__
            key_parts = [
                func_name,
                json.dumps(args, sort_keys=True, default=str),
                json.dumps(kwargs, sort_keys=True, default=str)
            ]
            cache_key = "_".join(key_parts)

            # Check cache
            hit, cached_result = self.get(cache_key)
            if hit:
                return cached_result

            # Execute function and cache result
            result = func(*args, **kwargs)
            self.set(cache_key, result)
            return result

        except Exception as e:
            error(f"Error in cached_call: {str(e)}")
            return func(*args, **kwargs)

    def get_cache_type(self) -> CacheType:
        """Return the cache type for this implementation."""
        return CacheType.LLM_API  # Default, can be overridden by specialized caches