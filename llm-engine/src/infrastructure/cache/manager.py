# src/infrastructure/cache/manager.py
from typing import Any, Dict, Optional

from .core.base_cache import CacheType, CacheConfig
from .factory import CacheFactory
from .specialized.llm_cache import LLMCache
from .specialized.data_cache import DataCache
from .specialized.state_cache import StateCache
from .specialized.graph_cache import GraphCache
from .specialized.history_cache import HistoryCache
from src.infrastructure.log.unified_logger import info, error


# Legacy CacheManager removed - use UnifiedCacheManager directly


class UnifiedCacheManager:
    """
    Unified cache manager that coordinates all specialized cache types.

    Provides a single interface to access all cache types while maintaining
    the specialized optimizations of each cache implementation.
    """

    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        """Initialize unified cache manager with configuration."""
        self.config = config_dict or {}
        self._caches: Dict[CacheType, Any] = {}

        # Initialize all cache types
        self._initialize_caches()

    def _initialize_caches(self):
        """Initialize all specialized cache instances."""
        try:
            # Create LLM cache
            llm_config = CacheConfig.for_llm_api(**self.config.get('llm', {}))
            self._caches[CacheType.LLM_API] = CacheFactory.create_llm_cache(llm_config)

            # Create data cache
            data_config = CacheConfig.for_data_query(**self.config.get('data', {}))
            self._caches[CacheType.DATA_QUERY] = CacheFactory.create_data_cache(data_config)

            # Create state cache
            state_config = CacheConfig.for_state_session(**self.config.get('state', {}))
            self._caches[CacheType.STATE_SESSION] = CacheFactory.create_state_cache(state_config)

            # Create graph cache
            graph_config = CacheConfig.for_graph_network(**self.config.get('graph', {}))
            self._caches[CacheType.GRAPH_NETWORK] = CacheFactory.create_graph_cache(graph_config)

            # Create history cache
            history_config = CacheConfig.for_history_conversation(**self.config.get('history', {}))
            self._caches[CacheType.HISTORY_CONVERSATION] = CacheFactory.create_history_cache(history_config)

            info("Initialized all specialized cache managers")

        except Exception as e:
            error(f"Error initializing cache managers: {e}")
            raise

    # Specialized cache accessors
    def get_llm_cache(self) -> LLMCache:
        """Get the LLM API cache instance."""
        return self._caches[CacheType.LLM_API]

    def get_data_cache(self) -> DataCache:
        """Get the data query cache instance."""
        return self._caches[CacheType.DATA_QUERY]

    def get_state_cache(self) -> StateCache:
        """Get the state/session cache instance."""
        return self._caches[CacheType.STATE_SESSION]

    def get_graph_cache(self) -> GraphCache:
        """Get the network graph cache instance."""
        return self._caches[CacheType.GRAPH_NETWORK]

    def get_history_cache(self) -> HistoryCache:
        """Get the conversation history cache instance."""
        return self._caches[CacheType.HISTORY_CONVERSATION]

    # Direct cache access methods - no legacy compatibility
    def invalidate_key_from_all_caches(self, key: str) -> bool:
        """Invalidate a key from all cache types."""
        success = False
        for cache in self._caches.values():
            if cache.invalidate(key):
                success = True
        return success

    def clear_all(self) -> Dict[str, bool]:
        """Clear all cache types."""
        results = {}
        for cache_type, cache in self._caches.items():
            try:
                results[cache_type.value] = cache.clear()
            except Exception as e:
                error(f"Error clearing {cache_type.value} cache: {e}")
                results[cache_type.value] = False
        return results

    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all cache types."""
        stats = {}
        for cache_type, cache in self._caches.items():
            try:
                if hasattr(cache, 'get_cache_stats'):
                    stats[cache_type.value] = cache.get_cache_stats()
            except Exception as e:
                error(f"Error getting stats for {cache_type.value}: {e}")
                stats[cache_type.value] = {"error": str(e)}
        return stats
