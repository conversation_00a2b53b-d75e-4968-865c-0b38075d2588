# src/infrastructure/cache/specialized/data_cache.py
from typing import Any, Tu<PERSON>, Dict, Optional, List

from ..core.base_cache import BaseCacheInterface, CacheType, CacheConfig
from ..core.memory_cache import MemoryCache
from src.infrastructure.log.unified_logger import error


class DataCache(BaseCacheInterface):
    """Specialized cache for data provider query results."""

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize data cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_data_query()
        
        self.config = config
        self.enabled = config.enabled
        
        # Use multiple memory caches for different data types
        self.query_cache = MemoryCache[Any](
            ttl_seconds=config.ttl_seconds,
            max_size=max(50, config.max_size // 4),
            enabled=config.enabled
        )
        
        self.partition_cache = MemoryCache[Any](
            ttl_seconds=config.ttl_seconds,
            max_size=20,
            enabled=config.enabled
        )
        
        self.unique_values_cache = MemoryCache[List[Any]](
            ttl_seconds=config.ttl_seconds * 2,  # Longer TTL for unique values
            max_size=30,
            enabled=config.enabled
        )
        
        self.count_cache = MemoryCache[int](
            ttl_seconds=config.ttl_seconds,
            max_size=30,
            enabled=config.enabled
        )

    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve data from appropriate cache based on key prefix."""
        if not self.enabled:
            return False, None
            
        # Route to appropriate cache based on key prefix
        if key.startswith("query_"):
            value = self.query_cache.get(key)
            return (value is not None, value)
        elif key.startswith("partition_"):
            value = self.partition_cache.get(key)
            return (value is not None, value)
        elif key.startswith("unique_"):
            value = self.unique_values_cache.get(key)
            return (value is not None, value)
        elif key.startswith("count_"):
            value = self.count_cache.get(key)
            return (value is not None, value)
        else:
            # Default to query cache
            value = self.query_cache.get(key)
            return (value is not None, value)

    def set(self, key: str, value: Any) -> bool:
        """Store data in appropriate cache based on key prefix."""
        if not self.enabled:
            return False
            
        try:
            # Route to appropriate cache based on key prefix
            if key.startswith("query_"):
                self.query_cache.set(key, value)
            elif key.startswith("partition_"):
                self.partition_cache.set(key, value)
            elif key.startswith("unique_"):
                self.unique_values_cache.set(key, value)
            elif key.startswith("count_"):
                self.count_cache.set(key, value)
            else:
                # Default to query cache
                self.query_cache.set(key, value)
            return True
        except Exception as e:
            error(f"Error setting data cache key {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """Remove specific data from cache."""
        if not self.enabled:
            return False
            
        try:
            # Try to remove from all caches
            if key.startswith("query_"):
                self.query_cache.delete(key)
            elif key.startswith("partition_"):
                self.partition_cache.delete(key)
            elif key.startswith("unique_"):
                self.unique_values_cache.delete(key)
            elif key.startswith("count_"):
                self.count_cache.delete(key)
            else:
                self.query_cache.delete(key)
            return True
        except Exception as e:
            error(f"Error invalidating data cache key {key}: {e}")
            return False

    def clear(self) -> bool:
        """Clear all data caches."""
        if not self.enabled:
            return False
            
        try:
            self.query_cache.clear()
            self.partition_cache.clear()
            self.unique_values_cache.clear()
            self.count_cache.clear()
            return True
        except Exception as e:
            error(f"Error clearing data caches: {e}")
            return False

    def get_cache_type(self) -> CacheType:
        """Return data query cache type."""
        return CacheType.DATA_QUERY

    # Specialized methods for data caching
    def cache_query_result(self, query_key: str, result: Any) -> bool:
        """Cache a query result with proper key prefix."""
        return self.set(f"query_{query_key}", result)

    def get_query_result(self, query_key: str) -> Tuple[bool, Any]:
        """Get a cached query result."""
        return self.get(f"query_{query_key}")

    def cache_partition(self, partition_key: str, partition_data: Any) -> bool:
        """Cache partition data."""
        return self.set(f"partition_{partition_key}", partition_data)

    def get_partition(self, partition_key: str) -> Tuple[bool, Any]:
        """Get cached partition data."""
        return self.get(f"partition_{partition_key}")

    def cache_unique_values(self, column: str, values: List[Any]) -> bool:
        """Cache unique values for a column."""
        return self.set(f"unique_{column}", values)

    def get_unique_values(self, column: str) -> Tuple[bool, List[Any]]:
        """Get cached unique values for a column."""
        return self.get(f"unique_{column}")

    def cache_count(self, count_key: str, count: int) -> bool:
        """Cache a count result."""
        return self.set(f"count_{count_key}", count)

    def get_count(self, count_key: str) -> Tuple[bool, int]:
        """Get a cached count result."""
        return self.get(f"count_{count_key}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get data cache statistics."""
        return {
            "cache_type": self.get_cache_type().value,
            "enabled": self.enabled,
            "query_cache_size": len(self.query_cache.cache) if hasattr(self.query_cache, 'cache') else 0,
            "partition_cache_size": len(self.partition_cache.cache) if hasattr(self.partition_cache, 'cache') else 0,
            "unique_cache_size": len(self.unique_values_cache.cache) if hasattr(self.unique_values_cache, 'cache') else 0,
            "count_cache_size": len(self.count_cache.cache) if hasattr(self.count_cache, 'cache') else 0,
        }
