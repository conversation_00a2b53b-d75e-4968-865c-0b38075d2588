# src/infrastructure/cache/specialized/history_cache.py
from typing import Any, <PERSON><PERSON>, Dict, Optional, List

from ..core.base_cache import Base<PERSON>acheInterface, CacheType, CacheConfig
from ..core.memory_cache import MemoryCache
from src.infrastructure.log.unified_logger import error


class HistoryCache(BaseCacheInterface):
    """Specialized cache for conversation history data."""

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize history cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_history_conversation()
        
        self.config = config
        self.enabled = config.enabled
        
        # Memory cache for conversation history
        self.conversation_cache = MemoryCache[Any](
            ttl_seconds=config.ttl_seconds,
            max_size=config.max_size,
            enabled=config.enabled
        )

    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve conversation history from cache."""
        if not self.enabled:
            return False, None
            
        value = self.conversation_cache.get(key)
        return (value is not None, value)

    def set(self, key: str, value: Any) -> bool:
        """Store conversation history in cache."""
        if not self.enabled:
            return False
            
        try:
            self.conversation_cache.set(key, value)
            return True
        except Exception as e:
            error(f"Error setting history cache key {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """Remove specific conversation history from cache."""
        if not self.enabled:
            return False
            
        try:
            self.conversation_cache.delete(key)
            return True
        except Exception as e:
            error(f"Error invalidating history cache key {key}: {e}")
            return False

    def clear(self) -> bool:
        """Clear all conversation history from cache."""
        if not self.enabled:
            return False
            
        try:
            self.conversation_cache.clear()
            return True
        except Exception as e:
            error(f"Error clearing history cache: {e}")
            return False

    def get_cache_type(self) -> CacheType:
        """Return history conversation cache type."""
        return CacheType.HISTORY_CONVERSATION

    # Specialized methods for history caching
    def cache_conversation_history(self, session_id: str, history_data: Any) -> bool:
        """Cache conversation history for a session."""
        key = f"conversation_{session_id}"
        return self.set(key, history_data)

    def get_conversation_history(self, session_id: str) -> Tuple[bool, Any]:
        """Get cached conversation history for a session."""
        key = f"conversation_{session_id}"
        return self.get(key)

    def cache_formatted_history(self, session_id: str, formatted_history: str) -> bool:
        """Cache formatted conversation history string."""
        key = f"formatted_{session_id}"
        return self.set(key, formatted_history)

    def get_formatted_history(self, session_id: str) -> Tuple[bool, str]:
        """Get cached formatted conversation history."""
        key = f"formatted_{session_id}"
        hit, value = self.get(key)
        return hit, value if hit else ""

    def cache_message_list(self, session_id: str, messages: List[Dict[str, Any]]) -> bool:
        """Cache list of messages for a session."""
        key = f"messages_{session_id}"
        return self.set(key, messages)

    def get_message_list(self, session_id: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """Get cached message list for a session."""
        key = f"messages_{session_id}"
        hit, value = self.get(key)
        return hit, value if hit else []

    def invalidate_session_history(self, session_id: str) -> bool:
        """Invalidate all cached history for a session."""
        success = True
        
        # Invalidate all history-related keys for this session
        keys_to_invalidate = [
            f"conversation_{session_id}",
            f"formatted_{session_id}",
            f"messages_{session_id}"
        ]
        
        for key in keys_to_invalidate:
            if not self.invalidate(key):
                success = False
                
        return success

    def invalidate_user_history(self, user_id: str) -> int:
        """
        Invalidate all cached history for a user.
        
        Returns:
            Number of sessions invalidated
        """
        # This would require implementing a key listing mechanism
        # For now, return 0 as a placeholder
        return 0

    def append_to_cached_history(self, session_id: str, new_message: Dict[str, Any]) -> bool:
        """
        Append a new message to cached conversation history.
        
        This is an optimization to avoid reloading entire history from disk.
        """
        try:
            # Get current cached messages
            hit, messages = self.get_message_list(session_id)
            
            if hit and isinstance(messages, list):
                # Append new message
                messages.append(new_message)
                
                # Update cache
                return self.cache_message_list(session_id, messages)
            
            return False
            
        except Exception as e:
            error(f"Error appending to cached history for session {session_id}: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get history cache statistics."""
        return {
            "cache_type": self.get_cache_type().value,
            "enabled": self.enabled,
            "conversation_cache_size": len(self.conversation_cache.cache) if hasattr(self.conversation_cache, 'cache') else 0,
            "config": {
                "ttl_seconds": self.config.ttl_seconds,
                "max_size": self.config.max_size
            }
        }
