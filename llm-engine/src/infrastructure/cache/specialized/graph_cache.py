# src/infrastructure/cache/specialized/graph_cache.py
from collections import OrderedDict
from typing import Any, <PERSON><PERSON>, Dict, Optional
from functools import lru_cache

from ..core.base_cache import BaseCacheInterface, CacheType, CacheConfig
from src.infrastructure.log.unified_logger import error, info


class GraphCache(BaseCacheInterface):
    """Specialized cache for network graphs and spatial data."""

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize graph cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_graph_network()
        
        self.config = config
        self.enabled = config.enabled
        self.max_graph_size = config.max_size
        
        # Use OrderedDict for LRU eviction of large graph objects
        self._graph_cache: OrderedDict = OrderedDict()

    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve graph from cache."""
        if not self.enabled:
            return False, None
            
        if key in self._graph_cache:
            # Move to end (most recently used)
            graph = self._graph_cache.pop(key)
            self._graph_cache[key] = graph
            return True, graph
        
        return False, None

    def set(self, key: str, value: Any) -> bool:
        """Store graph in cache with LRU eviction."""
        if not self.enabled:
            return False
            
        try:
            # Remove if already exists (to update position)
            if key in self._graph_cache:
                del self._graph_cache[key]
            
            # Evict oldest if at capacity
            while len(self._graph_cache) >= self.max_graph_size:
                oldest_key = next(iter(self._graph_cache))
                del self._graph_cache[oldest_key]
            
            # Add new graph
            self._graph_cache[key] = value
            return True
            
        except Exception as e:
            error(f"Error caching graph {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """Remove specific graph from cache."""
        if not self.enabled:
            return False
            
        try:
            if key in self._graph_cache:
                del self._graph_cache[key]
                return True
            return False
        except Exception as e:
            error(f"Error invalidating graph cache key {key}: {e}")
            return False

    def clear(self) -> bool:
        """Clear all graphs from cache."""
        if not self.enabled:
            return False
            
        try:
            self._graph_cache.clear()
            return True
        except Exception as e:
            error(f"Error clearing graph cache: {e}")
            return False

    def get_cache_type(self) -> CacheType:
        """Return graph network cache type."""
        return CacheType.GRAPH_NETWORK

    # Specialized methods for graph caching
    def cache_network_graph(self, lat: float, lon: float, radius_m: int, 
                           travel_mode: str, graph: Any) -> bool:
        """Cache a network graph with spatial key."""
        key = self._create_graph_key(lat, lon, radius_m, travel_mode)
        success = self.set(key, graph)
        if success:
            info(f"Cached network graph for {travel_mode} mode at ({lat}, {lon})")
        return success

    def get_network_graph(self, lat: float, lon: float, radius_m: int, 
                         travel_mode: str) -> Tuple[bool, Any]:
        """Get a cached network graph."""
        key = self._create_graph_key(lat, lon, radius_m, travel_mode)
        hit, graph = self.get(key)
        if hit:
            info(f"Using cached network graph for {travel_mode} mode")
        return hit, graph

    def _create_graph_key(self, lat: float, lon: float, radius_m: int, 
                         travel_mode: str) -> str:
        """Create a standardized key for graph caching."""
        return f"graph_{lat:.6f}_{lon:.6f}_{radius_m}_{travel_mode}"

    @staticmethod
    @lru_cache(maxsize=256)
    def get_nearest_node_cached(graph_id: int, lat: float, lon: float) -> int:
        """
        Cache nearest node lookups using LRU cache.
        
        Note: This is a static method that uses functools.lru_cache
        for fast in-memory caching of node lookups.
        """
        # This method signature is for caching - actual implementation
        # would need the graph object, but we use graph_id as a proxy
        # The actual implementation should be in the component that uses this
        pass

    def invalidate_by_location(self, lat: float, lon: float, radius_km: float = 1.0) -> int:
        """
        Invalidate graphs within a certain radius of a location.
        
        Args:
            lat: Latitude of center point
            lon: Longitude of center point  
            radius_km: Radius in kilometers to invalidate
            
        Returns:
            Number of graphs invalidated
        """
        invalidated = 0
        keys_to_remove = []
        
        for key in self._graph_cache.keys():
            if key.startswith("graph_"):
                try:
                    # Parse coordinates from key
                    parts = key.split("_")
                    if len(parts) >= 4:
                        graph_lat = float(parts[1])
                        graph_lon = float(parts[2])
                        
                        # Simple distance check (approximate)
                        lat_diff = abs(lat - graph_lat)
                        lon_diff = abs(lon - graph_lon)
                        
                        # Rough distance in km (not precise but good enough for cache invalidation)
                        distance_km = ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 111
                        
                        if distance_km <= radius_km:
                            keys_to_remove.append(key)
                            
                except (ValueError, IndexError):
                    continue
        
        # Remove identified keys
        for key in keys_to_remove:
            if self.invalidate(key):
                invalidated += 1
                
        return invalidated

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get graph cache statistics."""
        return {
            "cache_type": self.get_cache_type().value,
            "enabled": self.enabled,
            "graph_count": len(self._graph_cache),
            "max_size": self.max_graph_size,
            "utilization": len(self._graph_cache) / self.max_graph_size * 100 if self.max_graph_size > 0 else 0,
            "config": {
                "ttl_seconds": self.config.ttl_seconds,
                "max_size": self.config.max_size
            }
        }
