# src/infrastructure/cache/specialized/state_cache.py
from typing import Any, <PERSON><PERSON>, Dict, Optional

from ..core.base_cache import BaseCacheInterface, CacheType, CacheConfig
from ..core.memory_cache import MemoryCache
from src.infrastructure.log.unified_logger import error


class StateCache(BaseCacheInterface):
    """Specialized cache for session state and user profile data."""

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize state cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_state_session()
        
        self.config = config
        self.enabled = config.enabled
        
        # Separate caches for different state types
        self.session_cache = MemoryCache[Any](
            ttl_seconds=config.ttl_seconds,
            max_size=config.max_size // 2,
            enabled=config.enabled
        )
        
        self.profile_cache = MemoryCache[Any](
            ttl_seconds=config.ttl_seconds * 2,  # Longer TTL for profiles
            max_size=config.max_size // 2,
            enabled=config.enabled
        )

    def get(self, key: str) -> <PERSON>ple[bool, Any]:
        """Retrieve state data from appropriate cache."""
        if not self.enabled:
            return False, None
            
        # Route to appropriate cache based on key prefix
        if key.startswith("session_"):
            value = self.session_cache.get(key)
            return (value is not None, value)
        elif key.startswith("profile_"):
            value = self.profile_cache.get(key)
            return (value is not None, value)
        else:
            # Default to session cache
            value = self.session_cache.get(key)
            return (value is not None, value)

    def set(self, key: str, value: Any) -> bool:
        """Store state data in appropriate cache."""
        if not self.enabled:
            return False
            
        try:
            # Route to appropriate cache based on key prefix
            if key.startswith("session_"):
                self.session_cache.set(key, value)
            elif key.startswith("profile_"):
                self.profile_cache.set(key, value)
            else:
                # Default to session cache
                self.session_cache.set(key, value)
            return True
        except Exception as e:
            error(f"Error setting state cache key {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """Remove specific state data from cache."""
        if not self.enabled:
            return False
            
        try:
            if key.startswith("session_"):
                self.session_cache.delete(key)
            elif key.startswith("profile_"):
                self.profile_cache.delete(key)
            else:
                self.session_cache.delete(key)
            return True
        except Exception as e:
            error(f"Error invalidating state cache key {key}: {e}")
            return False

    def clear(self) -> bool:
        """Clear all state caches."""
        if not self.enabled:
            return False
            
        try:
            self.session_cache.clear()
            self.profile_cache.clear()
            return True
        except Exception as e:
            error(f"Error clearing state caches: {e}")
            return False

    def get_cache_type(self) -> CacheType:
        """Return state session cache type."""
        return CacheType.STATE_SESSION

    # Specialized methods for state caching
    def cache_session_state(self, session_id: str, state_data: Any) -> bool:
        """Cache session state data."""
        return self.set(f"session_{session_id}", state_data)

    def get_session_state(self, session_id: str) -> Tuple[bool, Any]:
        """Get cached session state data."""
        return self.get(f"session_{session_id}")

    def cache_user_profile(self, user_id: str, profile_data: Any) -> bool:
        """Cache user profile data."""
        return self.set(f"profile_{user_id}", profile_data)

    def get_user_profile(self, user_id: str) -> Tuple[bool, Any]:
        """Get cached user profile data."""
        return self.get(f"profile_{user_id}")

    def invalidate_user_sessions(self, user_id: str) -> int:
        """
        Invalidate all sessions for a specific user.
        
        Returns:
            Number of sessions invalidated
        """
        # This would require implementing a key listing mechanism
        # For now, return 0 as a placeholder
        return 0

    def invalidate_session(self, session_id: str) -> bool:
        """Invalidate a specific session."""
        return self.invalidate(f"session_{session_id}")

    def invalidate_profile(self, user_id: str) -> bool:
        """Invalidate a specific user profile."""
        return self.invalidate(f"profile_{user_id}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get state cache statistics."""
        return {
            "cache_type": self.get_cache_type().value,
            "enabled": self.enabled,
            "session_cache_size": len(self.session_cache.cache) if hasattr(self.session_cache, 'cache') else 0,
            "profile_cache_size": len(self.profile_cache.cache) if hasattr(self.profile_cache, 'cache') else 0,
            "config": {
                "ttl_seconds": self.config.ttl_seconds,
                "max_size": self.config.max_size
            }
        }
