# src/infrastructure/cache/specialized/llm_cache.py
import json
from typing import Any, Callable, Tu<PERSON>, Dict, Optional

from ..core.base_cache import BaseCacheInterface, CacheType, CacheConfig
from ..core.file_cache import FileCache
from src.infrastructure.log.unified_logger import error


class LLMCache(BaseCacheInterface):
    """Specialized cache for LLM API responses with intelligent key generation."""

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize LLM cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_llm_api()
        
        self.config = config
        self.enabled = config.enabled
        
        # Use file cache for persistent LLM response storage
        self._file_cache = FileCache(
            cache_dir=config.cache_dir or "app_data/cache/llm",
            enabled=config.enabled
        )

    def get(self, key: str) -> Tuple[bool, Any]:
        """Retrieve LLM response from cache."""
        return self._file_cache.get(key)

    def set(self, key: str, value: Any) -> bool:
        """Store LLM response in cache."""
        return self._file_cache.set(key, value)

    def invalidate(self, key: str) -> bool:
        """Remove specific LLM response from cache."""
        return self._file_cache.invalidate(key)

    def clear(self) -> bool:
        """Clear all LLM responses from cache."""
        return self._file_cache.clear()

    def get_cache_type(self) -> CacheType:
        """Return LLM API cache type."""
        return CacheType.LLM_API

    def cached_llm_call(self, func: Callable, prompt: str, **kwargs) -> Any:
        """
        Execute LLM function with intelligent caching.
        
        Creates cache keys based on:
        - Function name (agent type)
        - Prompt content
        - Relevant parameters (excluding history for better hit rates)
        """
        if not self.enabled:
            return func(prompt, **kwargs)

        try:
            # Create intelligent cache key for LLM calls
            cache_key = self._create_llm_cache_key(func.__name__, prompt, **kwargs)
            
            # Check cache first
            hit, cached_result = self.get(cache_key)
            if hit:
                return cached_result

            # Execute LLM function and cache result
            result = func(prompt, **kwargs)
            self.set(cache_key, result)
            return result

        except Exception as e:
            error(f"Error in LLM cached_call: {str(e)}")
            return func(prompt, **kwargs)

    def _create_llm_cache_key(self, func_name: str, prompt: str, **kwargs) -> str:
        """
        Create intelligent cache key for LLM calls.
        
        Excludes volatile parameters like history to improve cache hit rates.
        """
        # Extract stable parameters for caching
        stable_params = {}
        
        # Include important parameters but exclude volatile ones
        exclude_params = {'history', 'session_id', 'user_id', 'timestamp', 'flow'}
        
        for key, value in kwargs.items():
            if key not in exclude_params:
                stable_params[key] = value

        # Create cache key components
        key_parts = [
            f"llm_{func_name}",
            self._hash_prompt(prompt),
            json.dumps(stable_params, sort_keys=True, default=str)
        ]
        
        return "_".join(key_parts)

    def _hash_prompt(self, prompt: str) -> str:
        """Create a hash of the prompt for cache key generation."""
        import hashlib
        return hashlib.md5(prompt.encode()).hexdigest()[:16]

    def invalidate_by_agent(self, agent_name: str) -> int:
        """
        Invalidate all cache entries for a specific agent.
        
        Returns:
            Number of entries invalidated
        """
        # This would require implementing a key listing mechanism
        # For now, return 0 as a placeholder
        return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get LLM cache statistics."""
        return {
            "cache_type": self.get_cache_type().value,
            "enabled": self.enabled,
            "config": {
                "ttl_seconds": self.config.ttl_seconds,
                "max_size": self.config.max_size,
                "cache_dir": self.config.cache_dir
            }
        }
