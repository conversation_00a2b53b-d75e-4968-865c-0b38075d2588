# src/infrastructure/cache/factory.py
from typing import Dict, Any, Optional, Type

from .core.base_cache import BaseCacheInterface, CacheType, CacheConfig
from .specialized.llm_cache import LLMCache
from .specialized.data_cache import DataCache
from .specialized.state_cache import StateCache
from .specialized.graph_cache import GraphCache
from .specialized.history_cache import HistoryCache
from src.infrastructure.log.unified_logger import info, error


class CacheFactory:
    """Factory for creating specialized cache instances."""
    
    # Registry of cache types to their implementations
    _cache_registry: Dict[CacheType, Type[BaseCacheInterface]] = {
        CacheType.LLM_API: LLMCache,
        CacheType.DATA_QUERY: DataCache,
        CacheType.STATE_SESSION: StateCache,
        CacheType.GRAPH_NETWORK: GraphCache,
        CacheType.HISTORY_CONVERSATION: HistoryCache,
    }
    
    # Singleton instances for each cache type
    _cache_instances: Dict[CacheType, BaseCacheInterface] = {}

    @classmethod
    def create_cache(cls, cache_type: CacheType, 
                    config: Optional[CacheConfig] = None,
                    force_new: bool = False) -> BaseCacheInterface:
        """
        Create or get a cache instance of the specified type.
        
        Args:
            cache_type: Type of cache to create
            config: Optional configuration for the cache
            force_new: If True, create a new instance instead of reusing singleton
            
        Returns:
            Cache instance of the specified type
        """
        # Return existing singleton unless force_new is True
        if not force_new and cache_type in cls._cache_instances:
            return cls._cache_instances[cache_type]
        
        # Get the cache class for this type
        if cache_type not in cls._cache_registry:
            raise ValueError(f"Unknown cache type: {cache_type}")
        
        cache_class = cls._cache_registry[cache_type]
        
        try:
            # Create cache instance
            cache_instance = cache_class(config)
            
            # Store as singleton if not forcing new
            if not force_new:
                cls._cache_instances[cache_type] = cache_instance
            
            info(f"Created {cache_type.value} cache instance")
            return cache_instance
            
        except Exception as e:
            error(f"Error creating {cache_type.value} cache: {e}")
            raise

    @classmethod
    def create_llm_cache(cls, config: Optional[CacheConfig] = None) -> LLMCache:
        """Create LLM API cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_llm_api()
        return cls.create_cache(CacheType.LLM_API, config)

    @classmethod
    def create_data_cache(cls, config: Optional[CacheConfig] = None) -> DataCache:
        """Create data query cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_data_query()
        return cls.create_cache(CacheType.DATA_QUERY, config)

    @classmethod
    def create_state_cache(cls, config: Optional[CacheConfig] = None) -> StateCache:
        """Create state/session cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_state_session()
        return cls.create_cache(CacheType.STATE_SESSION, config)

    @classmethod
    def create_graph_cache(cls, config: Optional[CacheConfig] = None) -> GraphCache:
        """Create network graph cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_graph_network()
        return cls.create_cache(CacheType.GRAPH_NETWORK, config)

    @classmethod
    def create_history_cache(cls, config: Optional[CacheConfig] = None) -> HistoryCache:
        """Create conversation history cache with optimized settings."""
        if config is None:
            config = CacheConfig.for_history_conversation()
        return cls.create_cache(CacheType.HISTORY_CONVERSATION, config)

    @classmethod
    def create_from_config(cls, cache_type: CacheType, 
                          config_dict: Dict[str, Any]) -> BaseCacheInterface:
        """
        Create cache from configuration dictionary.
        
        Args:
            cache_type: Type of cache to create
            config_dict: Configuration dictionary
            
        Returns:
            Cache instance configured from dictionary
        """
        config = CacheConfig(**config_dict)
        return cls.create_cache(cache_type, config)

    @classmethod
    def get_all_cache_types(cls) -> list[CacheType]:
        """Get list of all available cache types."""
        return list(cls._cache_registry.keys())

    @classmethod
    def get_cache_instance(cls, cache_type: CacheType) -> Optional[BaseCacheInterface]:
        """Get existing cache instance if it exists."""
        return cls._cache_instances.get(cache_type)

    @classmethod
    def clear_all_caches(cls) -> Dict[CacheType, bool]:
        """
        Clear all cache instances.
        
        Returns:
            Dictionary mapping cache types to success status
        """
        results = {}
        
        for cache_type, cache_instance in cls._cache_instances.items():
            try:
                success = cache_instance.clear()
                results[cache_type] = success
                if success:
                    info(f"Cleared {cache_type.value} cache")
                else:
                    error(f"Failed to clear {cache_type.value} cache")
            except Exception as e:
                error(f"Error clearing {cache_type.value} cache: {e}")
                results[cache_type] = False
        
        return results

    @classmethod
    def get_all_cache_stats(cls) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all cache instances."""
        stats = {}
        
        for cache_type, cache_instance in cls._cache_instances.items():
            try:
                if hasattr(cache_instance, 'get_cache_stats'):
                    stats[cache_type.value] = cache_instance.get_cache_stats()
                else:
                    stats[cache_type.value] = {
                        "cache_type": cache_type.value,
                        "enabled": getattr(cache_instance, 'enabled', True)
                    }
            except Exception as e:
                error(f"Error getting stats for {cache_type.value} cache: {e}")
                stats[cache_type.value] = {"error": str(e)}
        
        return stats
