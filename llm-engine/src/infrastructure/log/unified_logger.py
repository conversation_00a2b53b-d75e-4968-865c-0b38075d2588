# Unified logger implementation (self-contained, FlowHub-free)

"""
Unified logger that combines traditional logging with flow tracking.

This module provides a unified logging system that combines traditional logging
with flow tracking, making it easier to correlate log messages with specific flows.
"""

from typing import Tuple, Any
import logging
import threading
import os
import json
from pathlib import Path
from typing import Optional, Dict, Any


class UnifiedLogger:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._setup()
        return cls._instance

    def _setup(self):
        self._local = threading.local()

        # Find the project root directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(
            os.path.join(current_dir, '..', '..', '..'))

        # Load config directly from config/config.json
        config_path = os.path.join(project_root, 'config', 'config.json')
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                logs_dir = config.get('logs_dir', 'app_data/logs')
            else:
                logs_dir = 'app_data/logs'
        except Exception:
            # Fallback to default if config can't be read
            logs_dir = 'app_data/logs'

        # If the path is not absolute, make it relative to the project root
        if not os.path.isabs(logs_dir):
            logs_dir = os.path.join(project_root, logs_dir)

        self._log_dir = Path(logs_dir)
        self._log_dir.mkdir(exist_ok=True, parents=True)

    def start_session(self, user_id: str, session_id: str):
        user_dir = self._log_dir / user_id
        user_dir.mkdir(exist_ok=True, parents=True)
        log_file = user_dir / f"{session_id}.log"
        logger = logging.getLogger(f"user.{user_id}.session.{session_id}")
        logger.setLevel(logging.DEBUG)
        if not logger.handlers:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(message)s'
            ))
            logger.addHandler(file_handler)
        self._local.logger = logger
        self._local.user_id = user_id
        self._local.session_id = session_id

    def _get_logger(self) -> logging.Logger:
        if not hasattr(self._local, 'logger'):
            # Create a system logger that saves to file
            system_logger = logging.getLogger("system")
            if not system_logger.handlers:
                # Create system log file
                system_log_file = self._log_dir / "system.log"
                file_handler = logging.FileHandler(system_log_file)
                file_handler.setFormatter(logging.Formatter(
                    '%(asctime)s | %(levelname)-8s | %(message)s'
                ))
                system_logger.addHandler(file_handler)
                system_logger.setLevel(logging.DEBUG)  # Log all levels to file
            return system_logger
        return self._local.logger

    def debug(self, msg: str, *args, **kwargs):
        self._get_logger().debug(msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs):
        self._get_logger().info(msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs):
        self._get_logger().warning(msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs):
        self._get_logger().error(msg, *args, **kwargs)

    def critical(self, msg: str, *args, **kwargs):
        self._get_logger().critical(msg, *args, **kwargs)


unified_logger = UnifiedLogger()


def start_session(user_id: str, session_id: str) -> None:
    unified_logger.start_session(user_id, session_id)


def debug(msg: str, *args, **kwargs):
    unified_logger.debug(msg, *args, **kwargs)


def info(msg: str, *args, **kwargs):
    unified_logger.info(msg, *args, **kwargs)


def warning(msg: str, *args, **kwargs):
    unified_logger.warning(msg, *args, **kwargs)


def error(msg: str, *args, **kwargs):
    unified_logger.error(msg, *args, **kwargs)


def critical(msg: str, *args, **kwargs):
    unified_logger.critical(msg, *args, **kwargs)


# Log Microservice Entry Point


def logging_service(operation: str, **params) -> Tuple[int, Any]:
    """
    Single entry point for Log microservice.

    Args:
        operation: Operation to perform ('log_message', 'start_session', 'get_logs', 'clear_logs')
        **params: Operation-specific parameters

    Returns:
        Tuple[int, Any]: (status_code, data)
        - status_code: 2XXXX format for log microservice
        - data: Operation result or None for errors
    """
    # Log microservice status codes (2XXXX range) - HARDCODED as per rules
    LOG_SUCCESS = 20000
    LOG_MESSAGE_SUCCESS = 20400
    LOG_SESSION_SUCCESS = 20400
    LOG_GET_SUCCESS = 20500
    LOG_CLEAR_SUCCESS = 20400
    LOG_MISSING_PARAMS = 20022
    LOG_INVALID_LEVEL = 20021
    LOG_UNKNOWN_OPERATION = 20021
    LOG_SYSTEM_ERROR = 20084

    try:
        if operation == "log_message":
            # Log a message with specified level
            message = params.get("message")
            level = params.get("level", "info")

            if not message:
                return LOG_MISSING_PARAMS, None

            # Validate log level
            valid_levels = ["debug", "info", "warning", "error", "critical"]
            if level not in valid_levels:
                return LOG_INVALID_LEVEL, None

            # Log the message using the appropriate level
            if level == "debug":
                debug(message)
            elif level == "info":
                info(message)
            elif level == "warning":
                warning(message)
            elif level == "error":
                error(message)
            elif level == "critical":
                critical(message)

            return LOG_MESSAGE_SUCCESS, {"logged": True, "level": level, "message": message}

        elif operation == "start_session":
            # Start a new logging session
            user_id = params.get("user_id")
            session_id = params.get("session_id")

            if not user_id or not session_id:
                return LOG_MISSING_PARAMS, None

            start_session(user_id, session_id)
            return LOG_SESSION_SUCCESS, {"session_started": True, "user_id": user_id, "session_id": session_id}

        elif operation == "get_logs":
            # Get log entries (basic implementation)
            user_id = params.get("user_id")
            session_id = params.get("session_id")

            if not user_id or not session_id:
                return LOG_MISSING_PARAMS, None

            # Basic log file reading
            try:
                log_file = unified_logger._log_dir / \
                    user_id / f"{session_id}.log"
                if log_file.exists():
                    with open(log_file, 'r') as f:
                        logs = f.readlines()
                    return LOG_GET_SUCCESS, {"logs": logs, "count": len(logs)}
                else:
                    return LOG_GET_SUCCESS, {"logs": [], "count": 0}
            except Exception:
                return LOG_SYSTEM_ERROR, None

        elif operation == "clear_logs":
            # Clear log files
            user_id = params.get("user_id")
            session_id = params.get("session_id")

            if not user_id or not session_id:
                return LOG_MISSING_PARAMS, None

            try:
                log_file = unified_logger._log_dir / \
                    user_id / f"{session_id}.log"
                if log_file.exists():
                    log_file.unlink()
                return LOG_CLEAR_SUCCESS, {"cleared": True, "user_id": user_id, "session_id": session_id}
            except Exception:
                return LOG_SYSTEM_ERROR, None

        else:
            return LOG_UNKNOWN_OPERATION, None

    except Exception:
        return LOG_SYSTEM_ERROR, None
