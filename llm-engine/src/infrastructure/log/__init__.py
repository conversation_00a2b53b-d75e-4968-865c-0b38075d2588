"""
Log Infrastructure Package

Provides unified logging with microservice entry point.
"""

# Traditional logging functions
from .unified_logger import debug, info, warning, error, critical, start_session

# Microservice entry point
from .unified_logger import logging_service

__all__ = [
    # Traditional logging
    'debug', 'info', 'warning', 'error', 'critical', 'start_session',
    # Microservice
    'logging_service'
]
