# src/infrastructure/history/json_manager.py

import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

from .interface import IHistoryManager
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical

from src.utils.error_handling import safe_operation, HistoryError
from src.utils.file_operations import safe_read_json, safe_write_json, safe_rename_file
from src.models import ConversationMessage, ConversationHistory
from src.infrastructure.cache.factory import CacheFactory


class JSONHistoryManager:
    """
    JSON file-based implementation of the HistoryManager interface.
    Stores conversation history in JSON files.
    """

    def __init__(self, history_dir=None, cache_ttl_seconds: int = 300, cache_max_size: int = 1000, cache_enabled: bool = True):
        """
        Initialize JSONHistoryManager with the directory to store history files.

        Args:
            history_dir: Directory to store history files
            cache_ttl_seconds: Time-to-live in seconds for cached items
            cache_max_size: Maximum number of items to store in the cache
            cache_enabled: Whether the cache is enabled
        """
        if history_dir is None:
            # Use ConfigManager to get history directory
            try:
                from src.infrastructure.config.config import ConfigManager
                config = ConfigManager()
                history_dir = config.get_config_value("history_dir")
                # Get cache configuration from config if not explicitly provided
                if cache_enabled:
                    cache_enabled = config.get_config_value(
                        "cache_enabled", True)
            except Exception:
                # Fallback to default if ConfigManager can't be loaded
                try:
                    import json
                    with open("config/config.json", "r") as f:
                        config = json.load(f)
                        storage_dir = config.get("storage_dir", "app_data")
                        history_dir = config.get(
                            "history_dir", f"{storage_dir}/chat_history")
                        # Get cache configuration from config if not explicitly provided
                        if cache_enabled:
                            cache_enabled = config.get("cache_enabled", True)
                except Exception:
                    # Last resort fallback
                    history_dir = "app_data/chat_history"

        self.history_dir = history_dir
        os.makedirs(self.history_dir, exist_ok=True)

        # Use specialized history cache
        self.history_cache = CacheFactory.create_history_cache()

        info(
            f"JSONHistoryManager initialized with cache enabled: {cache_enabled}, TTL: {cache_ttl_seconds}s")

    def _get_user_folder_path(self, user_id: str) -> str:
        """Get the folder path for a user and create if not exists."""
        user_folder = os.path.join(self.history_dir, user_id)
        os.makedirs(user_folder, exist_ok=True)
        return user_folder

    def _get_history_file_path(self, user_id: str, session_id: str) -> str:
        """Get the file path for a user's conversation history."""
        user_folder = self._get_user_folder_path(user_id)
        return os.path.join(user_folder, f"{session_id}.json")

    @safe_operation(error_message="Error retrieving conversation")
    def _get_conversation(self, user_id: str, session_id: str) -> ConversationHistory:
        """
        Get the conversation data from cache or file, or create a new one if it doesn't exist.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Returns:
            ConversationHistory object

        Raises:
            HistoryError: If there is an error retrieving the conversation
        """
        # Create a cache key
        cache_key = f"{user_id}:{session_id}"

        # Try to get from cache first
        hit, cached_conversation = self.history_cache.get_conversation_history(
            session_id)
        if hit:
            debug(f"Conversation cache hit: {cache_key}")
            return cached_conversation

        # Not in cache, load from file
        debug(f"Conversation cache miss: {cache_key}")
        history_file = self._get_history_file_path(user_id, session_id)

        # Create a default conversation
        default_conversation = {
            "user_id": user_id,
            "session_id": session_id,
            "messages": [],
            "metadata": {
                "created_at": datetime.now().isoformat()
            }
        }

        # Use safe_read_json to handle file operations with standardized error handling
        data = safe_read_json(history_file, default_value=None)
        if not data:
            # No data found, use default conversation
            self.history_cache.cache_conversation_history(
                session_id, default_conversation)
            return default_conversation

        # Convert legacy format to ConversationHistory
        if "messages" not in data or not isinstance(data["messages"], list):
            # Initialize with empty messages if not present or invalid
            data["messages"] = []

        # Convert legacy messages to ConversationMessage format
        messages: List[ConversationMessage] = []
        for msg in data.get("messages", []):
            if "prompt" in msg and "response" in msg:
                # Legacy format with prompt and response
                prompt = msg["prompt"]
                response = msg["response"]

                # Add user message
                if prompt.get("type") == "user_message":
                    user_msg: ConversationMessage = {
                        "role": "user",
                        "content": prompt.get("content", ""),
                        "timestamp": datetime.fromtimestamp(prompt.get("timestamp", time.time())).isoformat()
                    }
                    if "metadata" in prompt:
                        user_msg["metadata"] = prompt["metadata"]
                    messages.append(user_msg)

                # Add assistant response if it exists
                if response.get("response"):
                    assistant_msg: ConversationMessage = {
                        "role": "assistant",
                        "content": response.get("response", ""),
                        "timestamp": datetime.fromtimestamp(prompt.get("timestamp", time.time()) + 1).isoformat()
                    }

                    # Add metadata if available
                    metadata = {}
                    if "status" in response:
                        metadata["status"] = response["status"]
                    if "continuation" in response:
                        metadata["continuation"] = response["continuation"]
                    if "top_candidate_result" in response:
                        metadata["top_candidate_result"] = response["top_candidate_result"]

                    if metadata:
                        assistant_msg["metadata"] = metadata

                    messages.append(assistant_msg)
            elif "role" in msg and "content" in msg:
                # Already in ConversationMessage format
                messages.append(msg)

        # Create ConversationHistory
        conversation: ConversationHistory = {
            "user_id": data.get("user_id", user_id),
            "session_id": data.get("session_id", session_id),
            "messages": messages,
            "metadata": data.get("metadata", {
                "created_at": datetime.fromtimestamp(data.get("created_at", time.time())).isoformat()
            })
        }

        # Cache the conversation
        self.history_cache.cache_conversation_history(session_id, conversation)

        return conversation

    @safe_operation(default_value=False, error_message="Error saving conversation")
    def _save_conversation(self, user_id: str, session_id: str, conversation: ConversationHistory) -> bool:
        """
        Save the conversation data to the file and update the cache.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session
            conversation: The conversation data to save

        Returns:
            True if successful, False otherwise

        Raises:
            HistoryError: If there is an error saving the conversation
        """
        history_file = self._get_history_file_path(user_id, session_id)

        # Conversation is already validated at the service boundary
        serialized_conversation = conversation

        # Use safe_write_json to handle file operations with standardized error handling
        success = safe_write_json(
            history_file, serialized_conversation, indent=2)

        if success:
            # Update the cache
            self.history_cache.cache_conversation_history(
                session_id, conversation)
            return True
        else:
            error(
                f"Failed to save conversation {session_id} for user {user_id}")
            return False

    def log_event(self, user_id: str, session_id: str, event_type: str,
                  content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log an event to the conversation history."""
        # Ensure content is a string
        if not isinstance(content, str):
            content = str(content)

        conversation = self._get_conversation(user_id, session_id)

        # Create a new message
        message: ConversationMessage = {
            "role": "system" if event_type not in ["user_message", "assistant_message"] else
            "user" if event_type == "user_message" else "assistant",
            "content": content,
            "timestamp": datetime.now().isoformat()
        }

        if metadata:
            message["metadata"] = metadata

        conversation["messages"].append(message)
        return self._save_conversation(user_id, session_id, conversation)

    def get_history(self, user_id: str, session_id: str, limit: Optional[int] = None) -> List[ConversationMessage]:
        """Retrieve the conversation history for a session."""
        conversation = self._get_conversation(user_id, session_id)
        messages = conversation.get("messages", [])

        if limit is not None and limit > 0:
            messages = messages[-limit:]

        return messages

    def get_formatted_history(self, user_id: str, session_id: str, limit: Optional[int] = None) -> str:
        """Get the conversation history formatted as a string."""
        messages = self.get_history(user_id, session_id, limit)
        formatted_history = []

        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")

            if role == "user":
                formatted_history.append(f"User: {content}")
            elif role == "assistant":
                formatted_history.append(f"Assistant: {content}")
            elif role == "system":
                formatted_history.append(f"System: {content}")

        return "\n".join(formatted_history)

    def log_user_message(self, user_id: str, session_id: str, content: str,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log a user message to history."""
        # Only keep essential metadata
        essential_metadata = {}
        if metadata:
            # Only include location data if it exists
            if all(key in metadata for key in ["latitude", "longitude", "search_radius"]):
                essential_metadata = {
                    "latitude": metadata.get("latitude"),
                    "longitude": metadata.get("longitude"),
                    "search_radius": metadata.get("search_radius")
                }

        conversation = self._get_conversation(user_id, session_id)

        # Create a new user message
        message: ConversationMessage = {
            "role": "user",
            "content": content,
            "timestamp": datetime.now().isoformat()
        }

        if essential_metadata:
            message["metadata"] = essential_metadata

        conversation["messages"].append(message)
        return self._save_conversation(user_id, session_id, conversation)

    def log_assistant_message(self, user_id: str, session_id: str, content: str,
                              metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log an assistant message to history."""
        conversation = self._get_conversation(user_id, session_id)

        # Create a new assistant message
        message: ConversationMessage = {
            "role": "assistant",
            "content": content,
            "timestamp": datetime.now().isoformat()
        }

        # Add metadata if available
        if metadata:
            processed_metadata = {}
            if "status" in metadata:
                processed_metadata["status"] = metadata["status"]
            if "continuation" in metadata:
                processed_metadata["continuation"] = metadata["continuation"]
            if "top_candidate_result" in metadata:
                processed_metadata["top_candidate_result"] = metadata["top_candidate_result"]
            if "top_candidates" in metadata:
                processed_metadata["top_candidates"] = metadata["top_candidates"]
            if "session_title" in metadata:
                processed_metadata["session_title"] = metadata["session_title"]

            if processed_metadata:
                message["metadata"] = processed_metadata

        conversation["messages"].append(message)
        return self._save_conversation(user_id, session_id, conversation)

    def clear_history(self, user_id: str, session_id: str) -> bool:
        """Clear the history for a session."""
        conversation = self._get_conversation(user_id, session_id)
        conversation["messages"] = []
        return self._save_conversation(user_id, session_id, conversation)

    @safe_operation(error_message="Error deleting history")
    def delete_history(self, user_id: str, session_id: str) -> None:
        """
        Rename the history file with REMOVED prefix and invalidate the cache.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session

        Raises:
            HistoryError: If there is an error deleting the history
        """
        file_path = self._get_history_file_path(user_id, session_id)
        cache_key = f"{user_id}:{session_id}"

        # Invalidate the cache - use correct cache reference and method
        self.history_cache.invalidate_session_history(session_id)

        if os.path.exists(file_path):
            # Use safe_rename_file to handle file operations with standardized error handling
            file_name = os.path.basename(file_path)
            success = safe_rename_file(file_path, f"REMOVED_{file_name}")

            if success:
                info(f"Marked history as removed: {session_id}")
            else:
                error(f"Failed to mark history as removed: {session_id}")

    def save_conversation(self, user_id: str, session_id: str, conversation: ConversationHistory) -> bool:
        """
        Save a conversation to history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            conversation: The ConversationHistory object to save

        Returns:
            True if successful, False otherwise
        """
        # Use the conversation as is - NaN values are already handled at the service boundary
        processed_conversation = conversation
        return self._save_conversation(user_id, session_id, processed_conversation)

    def get_conversation(self, user_id: str, session_id: str) -> Optional[ConversationHistory]:
        """
        Get a conversation from history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation

        Returns:
            ConversationHistory object or None if not found
        """
        try:
            return self._get_conversation(user_id, session_id)
        except Exception as e:
            error(f"Error getting conversation: {str(e)}")
            return None
