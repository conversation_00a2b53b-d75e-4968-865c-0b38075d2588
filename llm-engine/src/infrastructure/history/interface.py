# src/infrastructure/history/interface.py

"""
Interface for conversation history management.

This module defines the interface for conversation history management using the Protocol pattern.
"""

from typing import Protocol, Dict, List, Any, Optional
from src.models import ConversationMessage, ConversationHistory


class IHistoryManager(Protocol):
    """
    Interface for conversation history management.
    Defines methods for logging events and retrieving conversation history.
    """

    def log_event(self, user_id: str, session_id: str, event_type: str,
                  content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Log an event to the conversation history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            event_type: Type of event (e.g., 'user_message', 'assistant_message')
            content: The content of the event
            metadata: Optional additional data about the event

        Returns:
            True if successful, False otherwise
        """
        ...

    def get_history(self, user_id: str, session_id: str, limit: Optional[int] = None) -> List[ConversationMessage]:
        """
        Retrieve the conversation history for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            limit: Optional maximum number of events to return

        Returns:
            List of ConversationMessage objects
        """
        ...

    def get_formatted_history(self, user_id: str, session_id: str, limit: Optional[int] = None) -> str:
        """
        Get the conversation history formatted as a string.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            limit: Optional maximum number of events to return

        Returns:
            Formatted history string
        """
        ...

    def log_user_message(self, user_id: str, session_id: str, content: str,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Log a user message to history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            content: The message content
            metadata: Optional additional data

        Returns:
            True if successful, False otherwise
        """
        ...

    def log_assistant_message(self, user_id: str, session_id: str, content: str,
                              metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Log an assistant message to history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            content: The message content
            metadata: Optional additional data

        Returns:
            True if successful, False otherwise
        """
        ...

    def clear_history(self, user_id: str, session_id: str) -> bool:
        """
        Clear the history for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation

        Returns:
            True if successful, False otherwise
        """
        ...

    def delete_history(self, user_id: str, session_id: str) -> None:
        """
        Delete or mark as deleted the history for a session.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
        """
        ...

    def save_conversation(self, user_id: str, session_id: str, conversation: ConversationHistory) -> bool:
        """
        Save a conversation to history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation
            conversation: The ConversationHistory object to save

        Returns:
            True if successful, False otherwise
        """
        ...

    def get_conversation(self, user_id: str, session_id: str) -> Optional[ConversationHistory]:
        """
        Get a conversation from history.

        Args:
            user_id: The ID of the user
            session_id: The ID of the session/conversation

        Returns:
            ConversationHistory object or None if not found
        """
        ...
