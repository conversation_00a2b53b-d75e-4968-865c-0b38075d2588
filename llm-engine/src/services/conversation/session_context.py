# src/services/conversation/session_context.py

"""
Session Context for unified access to state and history.

This module provides a SessionContext class that encapsulates access to both
state and history for a specific user/session, providing a unified interface
and reducing code duplication.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from src.infrastructure.state.interface import IStateManager
from src.infrastructure.history.interface import IHistoryManager
from src.models import SessionState, ConversationHistory, ConversationMessage


class SessionContext:
    """
    Provides unified access to state and history for a specific user/session.

    This class encapsulates the operations related to both state and history,
    providing a single interface and reducing code duplication.
    """

    def __init__(self, user_id: str, session_id: str,
                 state_manager: IStateManager,
                 history_manager: IHistoryManager):
        """Initialize the session context."""
        self.user_id = user_id
        self.session_id = session_id
        self.state_manager = state_manager
        self.history_manager = history_manager

    def get_state(self) -> Optional[SessionState]:
        """Get the current session state."""
        return self.state_manager.get_session(self.user_id, self.session_id)

    def save_state(self, state: SessionState) -> bool:
        """Save the session state."""
        return self.state_manager.save_session(self.user_id, self.session_id, state)

    def get_or_create_state(self) -> SessionState:
        """Get the current session state or create a new one if it doesn't exist."""
        state = self.get_state()
        if not state:
            state = {
                "user_id": self.user_id,
                "session_id": self.session_id,
                "current_state": "initial",
                "last_updated": datetime.now().isoformat(),
                "metadata": {}
            }
            self.save_state(state)
        return state

    def update_state_metadata(self, metadata: Dict[str, Any]) -> bool:
        """Update the metadata in the session state."""
        state = self.get_or_create_state()
        if "metadata" not in state:
            state["metadata"] = {}
        state["metadata"].update(metadata)
        state["last_updated"] = datetime.now().isoformat()
        return self.save_state(state)

    def get_top_candidates(self) -> List[Dict[str, Any]]:
        """Get the top candidates from the session state."""
        state = self.get_state()
        if not state or "metadata" not in state:
            return []
        return state["metadata"].get("top_candidates", [])

    def get_session_title(self) -> str:
        """Get the session title from the session state."""
        state = self.get_state()
        if not state or "metadata" not in state:
            return ""
        return state["metadata"].get("session_title", "")

    def save_top_candidates(self, top_candidates: Any) -> bool:
        """Save the top candidates to the session state, accumulating unique candidates unless the new list is empty."""
        if not top_candidates:
            # If empty, clear the state
            return self.update_state_metadata({"top_candidates": []})
        existing = self.get_top_candidates() or []
        # Use POI id for uniqueness
        seen_ids = {c.get('id') for c in existing if c.get('id') is not None}
        new_unique = [c for c in top_candidates if c.get('id') not in seen_ids]
        combined = existing + new_unique
        return self.update_state_metadata({"top_candidates": combined})

    def get_history(self, limit: Optional[int] = None) -> List[ConversationMessage]:
        """Get the conversation history."""
        return self.history_manager.get_history(self.user_id, self.session_id, limit)

    def get_formatted_history(self, limit: Optional[int] = None) -> str:
        """Get the formatted conversation history."""
        return self.history_manager.get_formatted_history(self.user_id, self.session_id, limit)

    def log_user_message(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log a user message to the conversation history."""
        return self.history_manager.log_user_message(self.user_id, self.session_id, message, metadata)

    def log_assistant_message(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log an assistant message to the conversation history."""
        return self.history_manager.log_assistant_message(self.user_id, self.session_id, message, metadata)

    def delete(self) -> bool:
        """Delete both the session state and conversation history."""
        state_deleted = self.state_manager.delete_session(
            self.user_id, self.session_id)
        self.history_manager.delete_history(self.user_id, self.session_id)
        return state_deleted

    def is_first_message(self) -> bool:
        """
        Check if this is the first message in the session.

        Returns:
            True if this is the first message (no history exists), False otherwise
        """
        try:
            history = self.get_history()
            # Check if there are no messages or only system messages
            user_or_assistant_messages = [
                msg for msg in history
                if msg.get('role') in ['user', 'assistant']
            ]
            return len(user_or_assistant_messages) == 0
        except Exception:
            # If there's an error getting history, assume it's the first message
            return True
