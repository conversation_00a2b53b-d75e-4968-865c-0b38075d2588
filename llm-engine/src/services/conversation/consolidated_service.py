# src/services/conversation/consolidated_service.py

from typing import Dict, Any, List, Optional
from src.infrastructure.state.interface import IState<PERSON>anager
from src.infrastructure.history.interface import IHistoryManager
from src.components.health import HealthComponent
from src.components.session import SessionComponent
from src.components.messages.orchestrator import get_main_llm
from src.components.titles import TitlesComponent
from src.components.delete import DeleteComponent


class ConsolidatedConversationService:
    """
    Service for managing conversations between users and the system.
    This service acts as the core of the application, handling all conversation-related functionality.

    This class consolidates the functionality of the original ConversationService and FlowManager.
    """

    def __init__(self, state_manager: IStateManager, history_manager: IHistoryManager):
        """
        Initialize the conversation service with required dependencies.

        Args:
            state_manager: Manager for storing and retrieving session state
            history_manager: Manager for logging conversation history
        """
        self.state_manager = state_manager
        self.history_manager = history_manager

        # Initialize standalone components
        self.health_component = HealthComponent()
        self.session_component = SessionComponent(state_manager)
        self.main_llm = get_main_llm()
        self.titles_component = TitlesComponent(state_manager, history_manager)
        self.delete_component = DeleteComponent(state_manager, history_manager)

    def process_message(self, user_id: str, session_id: str, message: str,
                        latitude: float, longitude: float, search_radius: int,
                        num_candidates: int, request_flow=None, streaming: bool = False) -> Dict[str, Any]:
        """Process a user message using main LLM orchestrator."""
        # Create session context for unified state and history access
        from src.services.conversation.session_context import SessionContext
        session = SessionContext(
            user_id, session_id, self.state_manager, self.history_manager)

        # Call main LLM with session context (following component pattern)
        result = self.main_llm.process_message(
            session, message, latitude, longitude, search_radius, num_candidates, streaming
        )

        return result

    def create_session(self, user_id: str, session_id: Optional[str] = None) -> str:
        """
        Create a new session using the standalone session component.

        Args:
            user_id: Unique identifier for the user
            session_id: Optional session ID to use

        Returns:
            New session ID
        """
        return self.session_component.create_session(user_id, session_id)

    def get_session_info(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get session information using the standalone session component.

        Args:
            user_id: Unique identifier for the user
            session_id: Session identifier

        Returns:
            Session information
        """
        return self.session_component.get_session_info(user_id, session_id)

    def get_message_history(self, user_id: str, session_id: str) -> list:
        """Get raw message history using history manager directly."""
        messages = self.history_manager.get_history(user_id, session_id)
        # Convert TypedDict objects to regular dicts for Pydantic compatibility
        return [dict(message) for message in messages]

    def delete_session(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """Delete a session using standalone component."""
        return self.delete_component.delete_session(user_id, session_id)

    def get_session_titles(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all session titles using standalone component."""
        return self.titles_component.get_session_titles(user_id)

    def check_health(self) -> Dict[str, Any]:
        """Health check using standalone component."""
        return self.health_component.check_health()
