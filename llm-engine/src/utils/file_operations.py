# src/utils/file_operations.py

"""
Standardized file operation utilities.

This module provides standardized file operation utilities for consistent
file handling across the application.
"""

import os
import json
import shutil
import time
from typing import Any, Dict, Optional, List, Callable, TypeVar, cast, BinaryIO, TextIO
from contextlib import contextmanager

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical
from src.utils.error_handling import FileOperationError, safe_operation, handle_file_operation

# Type variable for generic function return type
T = TypeVar('T')


@contextmanager
def file_lock(path: str, timeout: float = 10.0, check_interval: float = 0.1):
    """
    Context manager for file locking to prevent concurrent access.

    Args:
        path: Path of the file to lock
        timeout: Maximum time to wait for the lock in seconds
        check_interval: Interval between lock acquisition attempts in seconds

    Yields:
        None

    Raises:
        FileOperationError: If the lock cannot be acquired within the timeout
    """
    lock_path = f"{path}.lock"
    start_time = time.time()

    # Try to acquire the lock
    while True:
        try:
            # Try to create the lock file
            with open(lock_path, 'x') as f:
                f.write(str(os.getpid()))
            break
        except FileExistsError:
            # Lock file exists, check if it's stale
            try:
                lock_age = time.time() - os.path.getmtime(lock_path)
                if lock_age > 60:  # Lock is older than 60 seconds, consider it stale
                    warning(f"Removing stale lock file: {lock_path}")
                    os.remove(lock_path)
                    continue
            except Exception:
                # Ignore errors when checking lock age
                pass

            # Check if we've exceeded the timeout
            if time.time() - start_time > timeout:
                raise FileOperationError(
                    f"Could not acquire lock for {path} within {timeout} seconds",
                    "LOCK_TIMEOUT",
                    {"path": path, "lock_path": lock_path}
                )

            # Wait and try again
            time.sleep(check_interval)

    try:
        # Lock acquired, yield control
        yield
    finally:
        # Release the lock
        try:
            os.remove(lock_path)
        except Exception as e:
            warning(f"Error removing lock file {lock_path}: {str(e)}")


def ensure_directory(path: str) -> bool:
    """
    Ensure that a directory exists, creating it if necessary.

    Args:
        path: Path of the directory to ensure

    Returns:
        True if the directory exists or was created, False otherwise
    """
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        error(f"Error ensuring directory {path}: {str(e)}")
        return False


def safe_read_json(path: str, default_value: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Safely read JSON from a file with standardized error handling.

    Args:
        path: Path of the file to read
        default_value: Value to return if the operation fails

    Returns:
        Loaded JSON data or default_value if it fails
    """
    if not os.path.exists(path):
        # Use info level instead of warning for first-time file creation
        info(f"File not found (will be created): {path}")
        return default_value or {}

    def operation() -> Dict[str, Any]:
        with file_lock(path):
            with open(path, 'r') as f:
                return json.load(f)

    return handle_file_operation(
        operation,
        path,
        f"Error reading JSON from {path}",
        default_value or {}
    )


def safe_write_json(path: str, data: Dict[str, Any], indent: int = 2) -> bool:
    """
    Safely write JSON to a file with standardized error handling.

    Args:
        path: Path of the file to write
        data: Data to write
        indent: Indentation level for the JSON file

    Returns:
        True if successful, False otherwise
    """
    # Ensure the directory exists
    directory = os.path.dirname(path)
    if directory and not ensure_directory(directory):
        error(f"Could not create directory: {directory}")
        return False

    def operation() -> bool:
        # Write to a temporary file first
        temp_path = f"{path}.tmp"
        with open(temp_path, 'w') as f:
            json.dump(data, f, indent=indent)

        # Use file locking for the actual file
        with file_lock(path):
            # Rename the temporary file to the actual file
            os.replace(temp_path, path)

        return True

    return handle_file_operation(
        operation,
        path,
        f"Error writing JSON to {path}",
        False
    )


def safe_rename_file(path: str, new_name: str) -> bool:
    """
    Safely rename a file with standardized error handling.

    Args:
        path: Path of the file to rename
        new_name: New name for the file

    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(path):
        info(f"File not found: {path}")
        return False

    dir_path = os.path.dirname(path)
    new_path = os.path.join(dir_path, new_name)

    def operation() -> bool:
        with file_lock(path):
            os.rename(path, new_path)
        return True

    return handle_file_operation(
        operation,
        path,
        f"Error renaming file {path} to {new_path}",
        False
    )


def safe_delete_file(path: str) -> bool:
    """
    Safely delete a file with standardized error handling.

    Args:
        path: Path of the file to delete

    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(path):
        info(f"File not found: {path}")
        return True  # Consider it a success if the file doesn't exist

    def operation() -> bool:
        with file_lock(path):
            os.remove(path)
        return True

    return handle_file_operation(
        operation,
        path,
        f"Error deleting file {path}",
        False
    )


def safe_list_files(directory: str, pattern: Optional[str] = None) -> List[str]:
    """
    Safely list files in a directory with standardized error handling.

    Args:
        directory: Path of the directory to list
        pattern: Optional pattern to filter files

    Returns:
        List of file paths
    """
    if not os.path.exists(directory):
        info(f"Directory not found: {directory}")
        return []

    if not os.path.isdir(directory):
        error(f"Not a directory: {directory}")
        return []

    try:
        files = os.listdir(directory)

        # Filter by pattern if provided
        if pattern:
            import fnmatch
            files = [f for f in files if fnmatch.fnmatch(f, pattern)]

        # Return full paths
        return [os.path.join(directory, f) for f in files]
    except Exception as e:
        error(f"Error listing files in {directory}: {str(e)}")
        return []
