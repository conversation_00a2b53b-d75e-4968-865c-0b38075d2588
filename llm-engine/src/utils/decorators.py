# src/utils/decorators.py

"""
Utility decorators for common functionality.

This module provides utility decorators for common functionality like timing,
logging, and error handling.
"""

import functools
import time
import logging
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical


def timing_decorator(func):
    """
    Decorator to measure and log the execution time of a function.

    Args:
        func: The function to decorate

    Returns:
        The decorated function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        info(f"{func.__name__} execution time: {end_time - start_time:.4f} seconds")
        return result
    return wrapper


def log_io(func):
    """
    Decorator to log the input and output of a function.

    Args:
        func: The function to decorate

    Returns:
        The decorated function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        info(f"INPUT to {func.__name__}: args={args}, kwargs={kwargs}")
        result = func(*args, **kwargs)
        info(f"OUTPUT from {func.__name__}: {result}")
        return result
    return wrapper
