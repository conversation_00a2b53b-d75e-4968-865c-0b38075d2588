# src/utils/error_handling.py

"""
Standardized error handling utilities.

This module provides standardized error handling utilities for consistent
error handling across the application.
"""

import traceback
import json
import os
from typing import Any, Dict, Optional, Tuple, Callable, TypeVar, cast
from functools import wraps

from src.infrastructure.log.unified_logger import debug, info, warning, error, critical

# Type variable for generic function return type
T = TypeVar('T')


class AppError(Exception):
    """Base exception class for application-specific errors."""

    def __init__(self, message: str, error_code: str = "UNKNOWN_ERROR", details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
        """
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)


class StateError(AppError):
    """Exception raised for errors related to state management."""

    def __init__(self, message: str, error_code: str = "STATE_ERROR", details: Optional[Dict[str, Any]] = None):
        """Initialize the exception."""
        super().__init__(message, error_code, details)


class HistoryError(AppError):
    """Exception raised for errors related to history management."""

    def __init__(self, message: str, error_code: str = "HISTORY_ERROR", details: Optional[Dict[str, Any]] = None):
        """Initialize the exception."""
        super().__init__(message, error_code, details)


class ConfigError(AppError):
    """Exception raised for errors related to configuration."""

    def __init__(self, message: str, error_code: str = "CONFIG_ERROR", details: Optional[Dict[str, Any]] = None):
        """Initialize the exception."""
        super().__init__(message, error_code, details)


class FileOperationError(AppError):
    """Exception raised for errors related to file operations."""

    def __init__(self, message: str, error_code: str = "FILE_OPERATION_ERROR",
                 details: Optional[Dict[str, Any]] = None, path: Optional[str] = None):
        """
        Initialize the exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
            path: Path of the file that caused the error
        """
        if path and "path" not in (details or {}):
            details = details or {}
            details["path"] = path
        super().__init__(message, error_code, details)


class SerializationError(AppError):
    """Exception raised for errors related to serialization/deserialization."""

    def __init__(self, message: str, error_code: str = "SERIALIZATION_ERROR", details: Optional[Dict[str, Any]] = None):
        """Initialize the exception."""
        super().__init__(message, error_code, details)


def safe_operation(default_value: Any = None,
                   error_message: str = "Operation failed",
                   log_error: bool = True,
                   reraise: bool = False) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator for safely executing operations with standardized error handling.

    Args:
        default_value: Value to return if the operation fails
        error_message: Message to log if the operation fails
        log_error: Whether to log the error
        reraise: Whether to reraise the exception after logging

    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_error:
                    error(f"{error_message}: {str(e)}")
                    debug(f"Exception details: {traceback.format_exc()}")

                if reraise:
                    raise

                return cast(T, default_value)
        return wrapper
    return decorator


def handle_file_operation(operation: Callable[..., T],
                          path: str,
                          error_message: str,
                          default_value: Any = None) -> T:
    """
    Safely handle a file operation with standardized error handling.

    Args:
        operation: Function that performs the file operation
        path: Path of the file being operated on
        error_message: Message to log if the operation fails
        default_value: Value to return if the operation fails

    Returns:
        Result of the operation or default_value if it fails

    Raises:
        FileOperationError: If the operation fails and default_value is None
    """
    try:
        return operation()
    except FileNotFoundError:
        # Use info level for file not found since it's expected for first-time creation
        info(f"File not found: {path}")
        if default_value is not None:
            return cast(T, default_value)
        raise FileOperationError(
            f"File not found: {path}", "FILE_NOT_FOUND", {"path": path})
    except PermissionError:
        error(f"Permission denied: {path}")
        if default_value is not None:
            return cast(T, default_value)
        raise FileOperationError(
            f"Permission denied: {path}", "PERMISSION_DENIED", {"path": path})
    except IsADirectoryError:
        error(f"Expected file, got directory: {path}")
        if default_value is not None:
            return cast(T, default_value)
        raise FileOperationError(
            f"Expected file, got directory: {path}", "IS_A_DIRECTORY", {"path": path})
    except json.JSONDecodeError as e:
        error(f"JSON decode error in file {path}: {str(e)}")
        if default_value is not None:
            return cast(T, default_value)
        raise SerializationError(f"JSON decode error in file {path}", "JSON_DECODE_ERROR", {
                                 "path": path, "position": e.pos})
    except Exception as e:
        error(f"{error_message}: {str(e)}")
        debug(f"Exception details: {traceback.format_exc()}")
        if default_value is not None:
            return cast(T, default_value)
        raise FileOperationError(
            f"{error_message}: {str(e)}", "FILE_OPERATION_ERROR", {"path": path})


def safe_json_load(path: str, default_value: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Safely load JSON from a file with standardized error handling.

    Args:
        path: Path of the file to load
        default_value: Value to return if the operation fails

    Returns:
        Loaded JSON data or default_value if it fails
    """
    def operation() -> Dict[str, Any]:
        with open(path, 'r') as f:
            return json.load(f)

    return handle_file_operation(
        operation,
        path,
        f"Error loading JSON from {path}",
        default_value or {}
    )


def safe_json_save(path: str, data: Dict[str, Any], indent: int = 2) -> bool:
    """
    Safely save JSON to a file with standardized error handling.

    Args:
        path: Path of the file to save
        data: Data to save
        indent: Indentation level for the JSON file

    Returns:
        True if successful, False otherwise
    """
    def operation() -> bool:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(path), exist_ok=True)

        with open(path, 'w') as f:
            json.dump(data, f, indent=indent)
        return True

    return handle_file_operation(
        operation,
        path,
        f"Error saving JSON to {path}",
        False
    )


def format_error_for_response(error: Exception) -> Dict[str, Any]:
    """
    Format an exception for inclusion in an API response.

    Args:
        error: The exception to format

    Returns:
        Formatted error dictionary
    """
    if isinstance(error, AppError):
        result = {
            "error": error.error_code,
            "message": error.message
        }
        if error.details:
            result["details"] = error.details
        return result
    else:
        return {
            "error": "UNKNOWN_ERROR",
            "message": str(error)
        }


def log_and_format_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Log an exception and format it for inclusion in an API response.

    Args:
        error: The exception to log and format
        context: Additional context information for logging

    Returns:
        Formatted error dictionary
    """
    # Log the error with context
    context_str = f" Context: {context}" if context else ""
    error(f"Error: {str(error)}.{context_str}")
    debug(f"Exception details: {traceback.format_exc()}")

    # Format the error for response
    return format_error_for_response(error)
