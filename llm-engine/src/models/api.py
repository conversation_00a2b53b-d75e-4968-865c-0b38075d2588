# src/managers/data_models/api.py

"""
API-related data models.

This module contains standardized data structures for API requests and responses.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel


class MessageRequest(BaseModel):
    """
    Type definition for a message request.

    This represents the request body for the /message endpoint.
    """
    user_id: str
    session_id: str
    message: str
    latitude: float
    longitude: float
    search_radius: int
    num_candidates: int


class MessageResponse(BaseModel):
    """
    Type definition for a message response.

    This represents the response body for the /message endpoint.
    """
    response: str
    # Using List[Dict] instead of TopCandidates for Pydantic compatibility
    top_candidates: List[Dict[str, Any]] = []
    # Session title for the conversation - only included when present
    session_title: Optional[str] = None
    # Location data
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    search_radius: Optional[int] = None


class SessionRequest(BaseModel):
    """
    Type definition for a session request.

    This represents the request body for the /session endpoint.
    """
    user_id: str


class SessionResponse(BaseModel):
    """
    Type definition for a session response.

    This represents the response body for the /session endpoint.
    """
    session_id: str


class MessagesRequest(BaseModel):
    """
    Type definition for a messages request.

    This represents the request body for the /messages endpoint.
    """
    user_id: str
    session_id: str


class MessagesResponse(BaseModel):
    """
    Type definition for a messages response.

    This represents the response body for the /messages endpoint.
    """
    messages: List[Dict[str, Any]]


class DeleteRequest(BaseModel):
    """
    Type definition for a delete request.

    This represents the request body for the /delete endpoint.
    """
    user_id: str
    session_id: str


class DeleteResponse(BaseModel):
    """
    Type definition for a delete response.

    This represents the response body for the /delete endpoint.
    """
    message: str


class HealthResponse(BaseModel):
    """
    Type definition for a health response.

    This represents the response body for the /health endpoint.
    """
    status: str


class TitlesRequest(BaseModel):
    """
    Type definition for a titles request.

    This represents the request body for the /titles endpoint.
    """
    user_id: str


class SessionTitle(BaseModel):
    """
    Type definition for a session title.

    This represents a single session title in the response.
    """
    session_id: str
    title: str
    created_at: int


class TitlesResponse(BaseModel):
    """
    Type definition for a titles response.

    This represents the response body for the /titles endpoint.
    """
    titles: List[SessionTitle] = []
