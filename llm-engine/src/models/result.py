# src/managers/data_models/result.py

"""
Result-related data models.

This module contains standardized data structures for representing results
of operations, including success/failure status and associated data.
"""

from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Dict, Any, Optional
from datetime import datetime
import uuid


class ResultType(Enum):
    """Type of a result in a flow."""
    SUCCESS = auto()
    FAILURE = auto()
    PARTIAL = auto()
    CANCELLED = auto()
    TIMEOUT = auto()
    CUSTOM = auto()


@dataclass
class Result:
    """
    Represents a result of an operation.

    A result captures the outcome of an operation, including success/failure status,
    data returned, and any error information.
    """
    # Core identifiers
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    flow_id: str = ""

    # Result information
    result_type: ResultType = ResultType.SUCCESS
    data: Dict[str, Any] = field(default_factory=dict)

    # Timing information
    timestamp: datetime = field(default_factory=datetime.now)

    # Error information
    error: Optional[Dict[str, Any]] = None

    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Performance metrics
    duration_ms: Optional[float] = None
    metrics: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate the result after initialization."""
        # Ensure error is a dictionary if provided
        if self.error is not None and not isinstance(self.error, dict):
            self.error = self._convert_error_to_dict(self.error)

    def _convert_error_to_dict(self, error: Any) -> Dict[str, Any]:
        """Convert an error object to a dictionary."""
        try:
            return {"message": str(error), "type": type(error).__name__}
        except Exception:
            return {"message": "Error message extraction failed", "type": "Unknown"}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the result to a dictionary."""
        return {
            "id": self.id,
            "flow_id": self.flow_id,
            "result_type": self.result_type.name,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "error": self.error,
            "metadata": self.metadata,
            "duration_ms": self.duration_ms,
            "metrics": self.metrics
        }
