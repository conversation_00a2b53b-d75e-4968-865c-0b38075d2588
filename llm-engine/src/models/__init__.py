# src/models/__init__.py

"""
Centralized data models for the application.

This package contains standardized data structures used throughout the application,
providing a single source of truth for data models.
"""

from .api import (
    MessageRequest, MessageResponse,
    SessionRequest, SessionResponse,
    MessagesRequest, MessagesResponse,
    DeleteRequest, DeleteResponse,
    HealthResponse, TitlesRequest, TitlesResponse,
    SessionTitle
)
from .session import SessionState, ConversationMessage, ConversationHistory, UserProfile
from .result import Result, ResultType
from .llm import LLMResponse, ClassificationResult, FunctionCallRequest, FunctionCallResponse
from .location import POIData, LocationCandidate, TopCandidates, LocationAdviceResponse


__all__ = [
    # Location models
    'POIData',
    'LocationCandidate',
    'TopCandidates',
    'LocationAdviceResponse',

    # Result models
    'Result',
    'ResultType',

    # LLM models
    'LLMResponse',
    'ClassificationResult',
    'FunctionCallRequest',
    'FunctionCallResponse',

    # Session models
    'SessionState',
    'ConversationMessage',
    'ConversationHistory',
    'UserProfile',

    # API models
    'MessageRequest',
    'MessageResponse',
    'SessionRequest',
    'SessionResponse',
    'MessagesRequest',
    'MessagesResponse',
    'DeleteRequest',
    'DeleteResponse',
    'HealthResponse',
    'TitlesRequest',
    'TitlesResponse',
    'SessionTitle'
]
