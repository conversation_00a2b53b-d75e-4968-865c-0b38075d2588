# src/managers/data_models/session.py

"""
Session-related data models.

This module contains standardized data structures for session-related data,
including session state and conversation history.
"""

from typing import Dict, Any, Optional, List, TypedDict, Union
from datetime import datetime


class SessionState(TypedDict, total=False):
    """
    Type definition for session state.
    
    This represents the current state of a user session.
    """
    user_id: str
    session_id: str
    current_state: str
    latitude: Optional[float]
    longitude: Optional[float]
    search_radius: Optional[int]
    last_query: Optional[str]
    last_response: Optional[str]
    last_updated: Optional[str]  # ISO format datetime
    metadata: Optional[Dict[str, Any]]


class ConversationMessage(TypedDict, total=False):
    """
    Type definition for a conversation message.
    
    This represents a single message in a conversation history.
    """
    role: str  # "user" or "assistant"
    content: str
    timestamp: str  # ISO format datetime
    metadata: Optional[Dict[str, Any]]


class ConversationHistory(TypedDict):
    """
    Type definition for conversation history.
    
    This represents the full history of a conversation.
    """
    user_id: str
    session_id: str
    messages: List[ConversationMessage]
    metadata: Optional[Dict[str, Any]]


class UserProfile(TypedDict, total=False):
    """
    Type definition for user profile.
    
    This represents user-specific information and preferences.
    """
    user_id: str
    preferences: Dict[str, Any]
    favorite_locations: List[Dict[str, Any]]
    search_history: List[Dict[str, Any]]
    created_at: str  # ISO format datetime
    last_active: str  # ISO format datetime
