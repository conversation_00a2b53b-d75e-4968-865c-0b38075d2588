# src/models/location.py

"""
Location-related data models.

This module contains standardized data structures for location-related data,
including Points of Interest (POIs) and location candidates.
"""

from typing import Dict, List, Any, TypedDict, Optional, Union


class POIData(Dict[str, Any]):
    """
    Type definition for POI (Point of Interest) data.

    This represents a point of interest with various attributes.
    """

    def __init__(self, data: Dict[str, Any]):
        """
        Initialize a POIData instance.

        Args:
            data: Dictionary containing POI data
        """
        super().__init__(data)

        # Ensure required fields are present
        required_fields = ['latitude', 'longitude', 'subcategory']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")

        # Set attributes for easier access
        self.id = data.get('id', None)  # Add id attribute
        self.latitude = data['latitude']
        self.longitude = data['longitude']
        self.subcategory = data['subcategory']
        self.name = data.get('name', 'Unknown')
        self.category = data.get('category', 'Unknown')
        self.address = data.get('address', 'Unknown')
        self.rating = data.get('rating', None)

        # Store the original data for reference
        self.raw = data


class LocationCandidate(TypedDict):
    """
    Type definition for a location candidate.

    This represents a point of interest with various attributes.
    Required fields are latitude, longitude, and subcategory.
    """
    # Required fields
    latitude: float
    longitude: float
    subcategory: str

    # Optional fields (defined in a separate class to maintain TypedDict functionality)


class LocationCandidateOptional(LocationCandidate, total=False):
    """
    Extended type definition for a location candidate with optional fields.
    """
    id: int
    name: str
    category: str
    city: str
    district: str
    street: str
    phone_number: str
    opening_hours: str
    cuisine: str
    rating: float
    drive_route_distance_m: float
    walk_route_distance_m: float
    modes: set


# Type alias for top candidates
TopCandidates = List[Union[LocationCandidate,
                           LocationCandidateOptional, POIData]]


class LocationAdviceResponseRequired(TypedDict):
    """
    Type definition for required fields in a location advice response.
    """
    response: str


class LocationAdviceResponse(LocationAdviceResponseRequired, total=False):
    """
    Type definition for a location advice response.

    This represents the response from the location advice API.
    The 'response' field is required, all others are optional.
    """
    error: str
    top_candidates: TopCandidates
    action: str
    prompt: str
    latitude: float
    longitude: float
    radius: int
    status_code: int
    execution_time_ms: float
