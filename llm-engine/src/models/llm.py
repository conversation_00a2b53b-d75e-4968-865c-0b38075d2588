# src/models/llm.py

"""
LLM-related data models.

This module contains standardized data structures for LLM-related data,
including request and response types.
"""

from typing import Dict, Any, Optional, List, TypedDict, Union


class LLMResponse(TypedDict, total=False):
    """
    Generic response type for LLM API calls.

    This represents the standardized response format from any LLM provider.
    """
    response: str
    error: Optional[str]
    status: Optional[str]
    top_candidates: Optional[Dict[str, List[Dict[str, Any]]]]
    action: Optional[str]
    prompt: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    radius: Optional[int]


class ClassificationResult(TypedDict, total=False):
    """
    Result of a classification operation.

    This represents the result of classifying a user query.
    """
    subcategories: List[str]
    confidence: Optional[float]
    error: Optional[str]


class FunctionCallRequest(TypedDict, total=False):
    """
    Request for a function call.

    This represents a request to call a function in the LLM.
    """
    name: str
    arguments: Dict[str, Any]
    description: Optional[str]


class FunctionCallResponse(TypedDict, total=False):
    """
    Response from a function call.

    This represents the response from a function call in the LLM.
    """
    name: str
    result: Any
    error: Optional[str]
