#!/usr/bin/env python3
"""
Simple Database Connection Utility for Spatial Database.

This module provides a simple database connection for the spatial database.
"""

import getpass
import psycopg2


def get_spatial_db_connection():
    """
    Get a simple database connection to the wizlop_db database.

    Returns:
        psycopg2 connection object
    """
    db_config = {
        'dbname': 'wizlop_db',
        'user': 'wizlop_user',
        'password': 'wizlop_pass',
        'host': 'localhost',
        'port': '5432'
    }

    return psycopg2.connect(**db_config)
