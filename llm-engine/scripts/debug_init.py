#!/usr/bin/env python3

"""
Debug script to test service initialization
"""

import sys
import traceback

def test_config_manager():
    """Test ConfigManager initialization"""
    print("🧪 Testing ConfigManager...")
    try:
        from src.infrastructure.config.config import ConfigManager
        config = ConfigManager()
        print("✅ ConfigManager created successfully")
        return config
    except Exception as e:
        print(f"❌ ConfigManager failed: {e}")
        traceback.print_exc()
        return None

def test_state_manager(config):
    """Test state manager creation"""
    print("\n🧪 Testing State Manager...")
    try:
        state_manager = config.get_state_manager()
        if state_manager:
            print("✅ State Manager created successfully")
            return state_manager
        else:
            print("❌ State Manager returned None")
            return None
    except Exception as e:
        print(f"❌ State Manager failed: {e}")
        traceback.print_exc()
        return None

def test_history_manager(config):
    """Test history manager creation"""
    print("\n🧪 Testing History Manager...")
    try:
        history_manager = config.get_history_manager()
        if history_manager:
            print("✅ History Manager created successfully")
            return history_manager
        else:
            print("❌ History Manager returned None")
            return None
    except Exception as e:
        print(f"❌ History Manager failed: {e}")
        traceback.print_exc()
        return None

def test_conversation_service():
    """Test conversation service creation"""
    print("\n🧪 Testing Conversation Service...")
    try:
        from src.infrastructure.config.factory import create_conversation_service_from_config
        service = create_conversation_service_from_config()
        if service:
            print("✅ Conversation Service created successfully")
            return service
        else:
            print("❌ Conversation Service returned None")
            return None
    except Exception as e:
        print(f"❌ Conversation Service failed: {e}")
        traceback.print_exc()
        return None

def test_imports():
    """Test critical imports"""
    print("\n🧪 Testing Critical Imports...")
    
    imports_to_test = [
        "src.infrastructure.state.json_manager.JSONStateManager",
        "src.infrastructure.history.json_manager.JSONHistoryManager", 
        "src.services.conversation.consolidated_service.ConsolidatedConversationService",
        "src.components.health.HealthComponent",
        "src.components.session.SessionComponent"
    ]
    
    for import_path in imports_to_test:
        try:
            module_path, class_name = import_path.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {import_path}")
        except Exception as e:
            print(f"❌ {import_path}: {e}")

if __name__ == "__main__":
    print("🔍 Debugging Service Initialization\n")
    
    # Test imports first
    test_imports()
    
    # Test config manager
    config = test_config_manager()
    if not config:
        print("\n❌ Cannot proceed without ConfigManager")
        sys.exit(1)
    
    # Test managers
    state_manager = test_state_manager(config)
    history_manager = test_history_manager(config)
    
    # Test conversation service
    service = test_conversation_service()
    
    if service:
        print("\n🎉 All services initialized successfully!")
        
        # Test health check
        try:
            health = service.check_health()
            print(f"✅ Health check: {health}")
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            traceback.print_exc()
    else:
        print("\n❌ Service initialization failed")
        sys.exit(1)
