aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
appnope==0.1.4
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.1.0
blinker==1.9.0
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
comm==0.2.2
debugpy==1.8.12
decorator==5.1.1
distro==1.9.0
exceptiongroup==1.2.2
executing==2.2.0
fastapi==0.115.11
Flask==3.1.0
frozenlist==1.5.0
geopandas==1.0.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
ipykernel==6.29.5
ipython==8.32.0
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
jupyter_client==8.6.3
jupyter_core==5.7.2
llamaapi==0.1.36
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
multidict==6.1.0
nest-asyncio==1.6.0
networkx==3.4.2
numpy==2.2.3
openai==1.65.1
osmnx==2.0.1
packaging==24.2
pandas==2.2.3
parso==0.8.4
pexpect==4.9.0
platformdirs==4.3.6
prompt_toolkit==3.0.50
propcache==0.3.0
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
pyogrio==0.10.0
pyproj==3.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.1
pyzmq==26.2.1
requests==2.32.3
scikit-learn==1.6.1
scipy==1.15.2
shapely==2.0.7
six==1.17.0
sniffio==1.3.1
stack-data==0.6.3
starlette==0.46.0
threadpoolctl==3.5.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uvicorn==0.34.0
wcwidth==0.2.13
Werkzeug==3.1.3
yarl==1.18.3
