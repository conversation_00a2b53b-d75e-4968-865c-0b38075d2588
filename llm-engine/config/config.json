{"environment": "development", "state_backend": "json", "history_backend": "json", "cache_backend": "joblib", "cache_enabled": true, "llm_cache_enabled": false, "memory_cache_enabled": true, "memory_cache_ttl": 300, "memory_cache_max_size": 1000, "data_provider_type": "csv", "data_cache_enabled": true, "data_cache_ttl": 300, "data_cache_max_size": 200, "csv_use_memory_map": true, "csv_use_partitioning": true, "csv_partition_dir": null, "csv_grid_size": 4, "csv_min_partition_size": 500, "use_dataset_infrastructure": true, "storage_dir": "app_data", "sessions_dir": "app_data/sessions", "history_dir": "app_data/chat_history", "cache_dir": "app_data/cache", "logs_dir": "app_data/logs", "project_root_dir": "/Users/<USER>/Documents/Projects/wizlop/llm-engine", "data_paths": {"dataset": "data/dataset.csv"}, "llm_providers": {"llama": {"api_key_env_var": "<PERSON><PERSON><PERSON><PERSON>", "tested_models": ["llama3.1-70b"]}, "openrouter": {"api_key_env_var": "OPENROUTER_API_KEY", "site_url": "https://wizlop.com", "site_name": "<PERSON><PERSON><PERSON>", "tested_models": ["qwen/qwen2.5-vl-32b-instruct:free", "deepseek/deepseek-r1-0528-qwen3-8b:free"]}}, "agents": {"main": {"provider": "openrouter", "model": "qwen/qwen2.5-vl-32b-instruct:free", "hyperparameters": {"max_tokens": 5000, "temperature": 0.2, "response_format": {"type": "json_object"}}}, "request": {"provider": "openrouter", "model": "qwen/qwen2.5-vl-32b-instruct:free", "hyperparameters": {"max_tokens": 5000, "temperature": 0.2, "response_format": {"type": "json_object"}}}, "advice": {"provider": "openrouter", "model": "qwen/qwen2.5-vl-32b-instruct:free", "hyperparameters": {"max_tokens": 7000, "temperature": 0.2, "response_format": {"type": "json_object"}}}, "error_handler": {"provider": "openrouter", "model": "qwen/qwen2.5-vl-32b-instruct:free", "hyperparameters": {"max_tokens": 3000, "temperature": 0.3, "response_format": {"type": "json_object"}}}}}