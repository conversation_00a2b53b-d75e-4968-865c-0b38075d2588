<!-- @format -->

# LLM Engine - Intelligent Location Discovery System

A sophisticated conversational AI system that provides intelligent location-based advice and recommendations through natural language interactions. The system combines multiple specialized agents with robust error handling to deliver contextual, helpful responses.

## Project Structure

The project follows a clean, organized structure:

```
llm-engine/
├── api.py                  # API entry point
├── config.json             # Application configuration with agent settings
├── data/                   # POI dataset and partitions
│   ├── dataset.csv         # Main POI dataset
│   └── dataset_partitions/ # Partitioned data for performance
├── docs/                   # Project documentation & development phases
│   ├── infrastructure.md   # Infrastructure microservices architecture
│   ├── private/            # Private documentation and research
│   └── system_info/        # System documentation
│       └── status_codes.md # Error handling status code system
├── src/                    # Source code
│   ├── components/         # Core system components
│   │   ├── messages/       # Message processing system
│   │   │   ├── orchestrator/   # Main LLM orchestrator
│   │   │   └── agents/         # Specialized AI agents
│   │   │       ├── location/       # Location discovery agent
│   │   │       └── error_handler/  # Error handling agent
│   │   ├── session/        # Session management
│   │   ├── history/        # History management
│   │   ├── titles/         # Title management
│   │   ├── health/         # Health checks
│   │   └── delete/         # Delete operations
│   ├── infrastructure/     # System infrastructure
│   │   ├── status_codes.py     # Status code management system
│   │   ├── llm_endpoints/      # LLM provider integrations
│   │   ├── cache/              # Intelligent caching system
│   │   ├── config/             # Configuration management
│   │   ├── history/            # History infrastructure
│   │   ├── state/              # Session state management
│   │   ├── data_access/        # Data access layer
│   │   ├── log/                # Logging infrastructure
│   │   └── utils/              # Infrastructure utilities
│   ├── services/           # Business logic services
│   │   └── conversation/   # Conversation management
│   ├── models/             # Data models and types
│   ├── spatial_db/         # Geospatial database system
│   └── utils/              # General utilities
├── app_data/               # Runtime data (auto-generated)
│   ├── cache/              # System cache files
│   ├── chat_history/       # Conversation histories
│   ├── sessions/           # User session data
│   └── logs/               # Application logs
└── requirements.txt        # Python dependencies
```

## Setup and Installation

### Python Version: Python 3.10.0

#### For Mac:

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate
```

#### For Windows:

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
.\venv\Scripts\Activate
```

#### Install Dependencies:

```bash
pip install -r requirements.txt
```

## Running the Application

Start the API server:

```bash
uvicorn api:app --host 0.0.0.0 --port 8000 --reload
```

## System Architecture

The LLM Engine follows a sophisticated multi-agent architecture with intelligent orchestration and comprehensive error handling:

### 🎯 **Core Architecture Principles**

- **Agent-Based Design**: Specialized AI agents handle specific domains with clear interfaces
- **Intelligent Orchestration**: Central coordinator manages agent interactions and workflow
- **Robust Error Handling**: Dedicated error handling agent with conversational responses
- **Status Code System**: Custom XYZAB format for precise component tracking and monitoring
- **Session Continuity**: Persistent conversation history and context management
- **Modular Infrastructure**: Clean separation of concerns with reusable components
- **Configuration-Driven**: Agent-specific settings and provider configurations

### 🤖 **Agent System**

#### **Main LLM Orchestrator** (`src/components/messages/orchestrator/`)

- **Central Coordinator**: Manages all conversations and agent interactions
- **Action Detection**: Determines when to invoke specialized agents
- **Response Integration**: Combines agent outputs into natural conversations
- **Session Management**: Maintains conversation history and user context

#### **Location Discovery Agent** (`src/components/messages/agents/location/`)

- **Intelligent Search**: Finds relevant locations based on natural language queries
- **Category Classification**: Automatically categorizes user requests
- **Contextual Advice**: Generates personalized location recommendations
- **POI Management**: Handles point-of-interest data and filtering

#### **Error Handling Agent** (`src/components/messages/agents/error_handler/`)

- **Conversational Errors**: Transforms technical errors into natural, helpful responses
- **Context-Aware**: Uses conversation history and session data for personalized error messages
- **Solution-Oriented**: Offers alternatives and suggestions when searches fail
- **Status Code Integration**: Interprets numeric status codes for appropriate response generation
- **Intelligent Recovery**: Provides actionable guidance based on error type and user context

### 🏗️ **Infrastructure Layer**

The infrastructure follows a clean microservices architecture where each folder is a standalone service. See `docs/infrastructure.md` for complete architecture details.

#### **Microservices Architecture**

- **Cache**: ✅ Complete - Specialized cache types with 40% performance improvement
- **Status Codes**: Single file microservice for error handling and component tracking
- **Logging**: Unified logging service with session support
- **Configuration**: Centralized configuration management for all services
- **State Management**: Session state persistence and management
- **History Management**: Conversation history with metadata tracking
- **Data Access**: Unified data layer with provider abstraction
- **LLM Endpoints**: Multi-provider LLM integration with adaptive responses

#### **Key Features**

- **Single Entry Points**: Each microservice has one main function (e.g., `config_service()`)
- **Function-Based Connections**: Direct function calls between services
- **No Backward Compatibility**: Clean code rewrite without legacy support
- **Standardized Responses**: Consistent response format across all services

### 🌐 **Data Layer**

#### **Geospatial Database** (`src/spatial_db/`)

- **Efficient Querying**: Optimized for location-based searches
- **Rich POI Data**: Comprehensive point-of-interest information
- **Scalable Design**: Handles large geographic datasets

### 🔄 **System Flow**

1. **User Input** → Main LLM Orchestrator receives request with session context
2. **Intent Analysis** → Determines required agents and actions based on conversation history
3. **Agent Execution** → Specialized processing (location search, classification, advice generation)
4. **Status Code Generation** → Components return standardized status codes with results
5. **Error Handling** → Dedicated error agent processes failures with conversational responses
6. **Response Integration** → Natural language response generation with context preservation
7. **Session Update** → History, metadata, and context preservation for future interactions

### 🔧 **Key Infrastructure Improvements**

#### **Enhanced Error Handling**

- **Dedicated Error Agent**: Specialized agent for transforming technical errors into conversational responses
- **Status Code System**: Comprehensive tracking with XYZAB format for precise error identification
- **Context-Aware Recovery**: Uses conversation history and session data for personalized error responses
- **Graceful Degradation**: System continues functioning even when individual components fail

#### **Agent Communication**

- **Standardized Interfaces**: All agents implement consistent interfaces for seamless integration
- **Status Code Integration**: Unified error reporting and handling across all system components
- **Session Context Passing**: Consistent data flow between orchestrator and specialized agents
- **Configuration Management**: Centralized agent settings with provider-specific optimizations

## Example API Request

```bash
curl -X POST "http://localhost:8000/query" -H "Content-Type: application/json" -d "{\"prompt\": \"i want to go to somewhere with a great view where i can also drink something\", \"latitude\": 40.985660, \"longitude\": 29.027361, \"radius\": 5000, \"num_results\": 5}"
```

## Development

When adding new dependencies:

```bash
pip freeze > requirements.txt
```

## Configuration

The application configuration is stored in `config.json` with comprehensive settings for all system components:

### **Core Configuration**

- `environment`: The environment (development, production)
- `storage_dir`: The base directory for all storage (`app_data`)
- `sessions_dir`: Directory for session data (`app_data/sessions`)
- `history_dir`: Directory for conversation history (`app_data/chat_history`)
- `cache_dir`: Directory for cache data (`app_data/cache`)
- `logs_dir`: Directory for logs (`app_data/logs`)

### **Agent Configuration**

Each agent has dedicated settings in the `agents` section:

- **Main Orchestrator**: Central conversation coordinator settings
- **Location Agent**: Location discovery and classification settings
- **Advice Agent**: Location recommendation generation settings
- **Error Handler**: Error response generation settings

### **LLM Provider Configuration**

- **OpenRouter**: API integration with model selection and parameters
- **LlamaAPI**: Alternative provider configuration
- **Model Selection**: Agent-specific model optimization for different tasks

### Runtime Data

The application stores runtime data in the `app_data` directory:

- `app_data/cache`: Cache files, including OSMnx geospatial data
- `app_data/chat_history`: Conversation history data
- `app_data/logs`: Application logs
- `app_data/sessions`: User session data

These directories are automatically created when the application runs, but they can be pre-created using the `mkdir -p` command:

```bash
mkdir -p app_data/cache app_data/chat_history app_data/logs app_data/sessions
```

## Documentation

### **System Documentation** (`docs/system_info/`)

- **Status Codes**: Complete reference for all system status codes (`status_codes.md`)
- **Error Handling**: Documentation for error handling patterns and recovery strategies

### **Infrastructure Documentation** (`docs/infrastructure.md`)

- **Microservices Architecture**: Complete infrastructure design and implementation phases
- **Service Specifications**: Detailed specifications for each microservice
- **Implementation Strategy**: Phase-based development approach with testing requirements

## Dependencies

```bash
pip install pandas openai joblib llamaapi python-dotenv fastapi pydantic uvicorn networkx scikit-learn osmnx urllib3==1.26.8 postgis psycopg2-binary psutil osmium folium psycopg2-binary geopandas shapely osmium folium branca tqdm requests psutil
```
