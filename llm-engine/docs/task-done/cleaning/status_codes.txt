# LLM Engine Status Code System

## Overview
Custom numeric status code system for microservice-based architecture. Each microservice returns status codes directly without wrapper classes for clean, efficient communication.

## Code Structure: `XYZAB`

- **X**: Microservice ID (1-9)
- **Y**: Sub-component (0-9)
- **Z**: Operation Type (0-9)
- **AB**: Specific Error/Status (00-99)

## Microservice Architecture Pattern

Each microservice follows this pattern:
```python
def service_name(operation: str, **params) -> Tuple[int, Any]:
    """
    Returns: (status_code, data)
    - status_code: XYZAB format where X identifies the microservice
    - data: Operation result or None for errors
    """
```

## Microservice IDs (X)

| Code | Microservice | Description |
|------|--------------|-------------|
| 1 | Reserved | Reserved for future use (cache uses direct patterns) |
| 2 | Location/Data | Location finding and data access |
| 3 | Classification | Classification agent |
| 4 | Advice | Advice agent |
| 5 | Data Access | Data layer operations |
| 6 | Session/State | Session and state management |
| 7 | History | History management |
| 8 | Error Handler | Error handling agent |
| 9 | LLM Endpoints | LLM provider management |

## Sub-components (Y)

### Cache Infrastructure (Direct Usage - No Status Codes)
Cache uses direct patterns for optimal performance:
- `CacheFactory.create_llm_cache()` - Direct factory usage
- `UnifiedCacheManager().get_llm_cache()` - Unified management
- `config.get_cache_manager().get_llm_cache()` - Through config

### Location/Data System (2XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 20 | Main | Main location agent |
| 21 | Filter | POI filtering |
| 22 | Candidates | Top candidates selection |
| 23 | Geocoding | Geocoding operations |

### Classification System (3XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 30 | Main | Main classification agent |
| 31 | Validation | Input validation |
| 32 | Processing | Data processing |

### Advice System (4XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 40 | Main | Main advice agent |
| 41 | Formatting | Response formatting |
| 42 | Context | Context processing |

### Data Access Microservice (5XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 50 | Main | Main data operations |
| 51 | Query | Query processing |
| 52 | Processing | Data processing |
| 53 | Communication | API communication |

### Session/State Microservice (6XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 60 | Main | Main session operations |
| 61 | Validation | Session validation |
| 62 | Storage | Session storage |

### History Microservice (7XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 70 | Main | Main history operations |
| 71 | Validation | History validation |
| 72 | Storage | History storage |

### Error Handler System (8XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 80 | Main | Main error handler |
| 81 | Analysis | Error analysis |
| 82 | Response | Response generation |

### LLM Endpoints Microservice (9XXXX)
| Code | Sub-component | Description |
|------|---------------|-------------|
| 90 | Main | Main LLM operations |
| 91 | Monitoring | LLM monitoring |
| 92 | Recovery | Error recovery |

## Operation Types (Z)

| Code | Operation | Description |
|------|-----------|-------------|
| 0 | Search | Search operations |
| 1 | Validation | Validation operations |
| 2 | Processing | Data processing |
| 3 | Communication | Inter-component communication |
| 4 | Storage | Data storage operations |
| 5 | Retrieval | Data retrieval operations |

## Specific Status Codes (AB)

### Success Codes (00-19)
| Code | Status | Description |
|------|--------|-------------|
| 00 | Success | Operation completed successfully |
| 01 | Partial Success | Operation completed with limited results |

### Client/Request Errors (20-79)
| Code | Status | Description |
|------|--------|-------------|
| 20 | No Results | No results found |
| 21 | Invalid Input | Invalid input provided |
| 22 | Missing Data | Required data missing |
| 23 | Format Error | Data format error |
| 24 | Parse Error | Parsing failed |

### System Errors (80-99)
| Code | Status | Description |
|------|--------|-------------|
| 80 | Timeout | Operation timeout |
| 81 | Connection Failed | Connection failed |
| 82 | Processing Failed | Processing failed |
| 83 | Memory Error | Memory allocation error |
| 84 | Unknown Error | Unknown error occurred |

## Current Implementation Status Codes

### Cache Infrastructure ✅ DIRECT USAGE
Cache uses direct patterns without status codes for optimal performance:
- Direct factory methods return cache instances
- Unified manager provides coordinated access
- No status code overhead for basic cache operations

### Log Microservice (2XXXX) ✅ IMPLEMENTED
| Code | Operation | Description |
|------|-----------|-------------|
| 20000 | General | Log operation success |
| 20400 | Storage | Log/Session/Clear operations success |
| 20500 | Retrieval | Get logs operations success |
| 20021 | Error | Invalid log level or unknown operation |
| 20022 | Error | Missing required parameters |
| 20084 | Error | System error |

### History Infrastructure ✅ DIRECT USAGE
History uses direct patterns without status codes for optimal performance:
- `config.get_history_manager()` - Through config dependency injection
- `JSONHistoryManager()` - Direct manager usage
- No status code overhead for basic history operations

### Data Access Microservice (5XXXX) ✅ IMPLEMENTED
| Code | Operation | Description |
|------|-----------|-------------|
| 50000 | General | Data operation success |
| 50400 | Storage | Refresh operations success |
| 50500 | Retrieval | Query/Get/Schema/Count operations success |
| 50021 | Error | Invalid type or unknown operation |
| 50022 | Error | Missing required parameters |
| 50520 | Error | Data not found |
| 50082 | Error | Query execution error |
| 50084 | Error | System error |

### Classification Agent (3XXXX)
| Code | Description |
|------|-------------|
| 30020 | Classification search found no subcategories |
| 30021 | Classification received invalid input format |
| 30024 | Classification parsing failed |

### Location Agent (2XXXX)
| Code | Description |
|------|-------------|
| 20020 | Location search found no POIs |
| 22020 | Top candidates selection found no candidates |
| 20021 | Location search received invalid coordinates |

### Data Layer (5XXXX)
| Code | Description |
|------|-------------|
| 51020 | Database query returned no results |
| 52080 | Data processing timeout |
| 53081 | API communication failed |

## Usage Examples

```python
# Cache infrastructure - direct usage (no status codes)
from src.infrastructure.cache import CacheFactory, UnifiedCacheManager

# Direct factory usage
llm_cache = CacheFactory.create_llm_cache()
hit, result = llm_cache.get("test_key")
if hit:
    print(f"Cache hit: {result}")
else:
    print("Cache miss")

# Unified manager usage
cache_manager = UnifiedCacheManager()
llm_cache = cache_manager.get_llm_cache()
success = llm_cache.set("test_key", "test_value")

# Direct usage infrastructure (no status codes)
from src.infrastructure.history import JSONHistoryManager
from src.infrastructure.llm_endpoints import create_agent_llm_adapter

# History infrastructure - direct usage
config = ConfigManager()
history_manager = config.get_history_manager()
success = history_manager.log_user_message("user123", "session456", "Hello!")

# LLM endpoints infrastructure - direct usage
llm_adapter = create_agent_llm_adapter("request")
response = llm_adapter.call_api("Hello, how are you?", system_prompt="You are helpful")

# Microservice calls with status code returns
from src.infrastructure.log import logging_service
from src.infrastructure.data_access import data_service

# Log microservice example
status_code, data = logging_service("log_message", level="info", message="test")
if status_code == 20400:  # Log storage success
    print("Message logged successfully")

# Data access microservice example
geo_filter = {
    "center_lat": 37.7749,
    "center_lon": -122.4194,
    "radius_m": 1000
}
status_code, data = data_service("query",
                                geo_filter=geo_filter,
                                options={"limit": 5})
if status_code == 50500:  # Data query success
    print(f"Found {len(data['items'])} items")

# Status code parsing
code = 20400
microservice = code // 10000        # 2 = Log
subcomponent = (code // 1000) % 10  # 0 = Main
operation = (code // 100) % 10      # 4 = Storage
status = code % 100                 # 00 = Success

# Microservice identification
microservice_map = {
    1: "Reserved", 2: "Location/Data", 3: "Classification",
    4: "Advice", 5: "Data Access", 6: "Session/State",
    7: "History", 8: "Error Handler", 9: "LLM Endpoints"
}
print(f"Microservice: {microservice_map[microservice]}")
```

## Extension Guidelines

1. **New Microservices**: Use next available X digit (1-9)
2. **New Sub-components**: Use next available Y digit (0-9) within microservice
3. **New Operations**: Use next available Z digit (0-9)
4. **New Status Codes**: Use next available AB digits (00-99) within category

## Microservice Implementation Rules

1. **Direct Returns**: Each microservice returns `Tuple[int, Any]` directly
2. **No Wrapper Classes**: Avoid ServiceResponse or similar wrapper classes
3. **Status Code First**: Always return status code as first element
4. **Data or None**: Return operation data on success, None on error
5. **Consistent Patterns**: Follow the same operation naming across microservices

## Monitoring and Debugging

- **Microservice Errors**: Filter by first digit (e.g., all 1XXXX for Cache issues)
- **Operation Errors**: Filter by third digit (e.g., all XX5XX for Retrieval issues)
- **Specific Errors**: Filter by last two digits (e.g., all XXX20 for No Results)
- **Error Patterns**: Track frequency by microservice/operation combinations
- **Performance Tracking**: Monitor response times by status code patterns

## Implementation Status

- ✅ **Phase 1 Complete**: Cache Infrastructure with direct usage patterns (no status codes)
- ✅ **Phase 2A Complete**: Log Microservice (2XXXX) with hardcoded status codes
- ✅ **Phase 2B Complete**: Config Infrastructure with direct usage patterns (no microservice)
- ✅ **Phase 2C Complete**: State Infrastructure with direct usage patterns (no microservice)
- ✅ **Phase 2D Complete**: History Infrastructure with direct usage patterns (no microservice)
- ✅ **Phase 2E Complete**: Data Access Microservice (5XXXX) with hardcoded status codes
- ✅ **Phase 2F Complete**: LLM Endpoints Infrastructure with direct usage patterns (no microservice)
- ✅ **Decentralized Status Codes**: Each component manages its own status codes (NO centralized file)
- ✅ **Hardcoded Status Codes**: Each microservice hardcodes its own status codes
- ✅ **Microservice Entry Points**: `logging_service()` and `data_service()` functions
- ✅ **Direct Usage Patterns**: Cache, Config, State, History, LLM Endpoints use optimal direct patterns
- 🎉 **ALL PHASES COMPLETE**: Optimal microservice architecture fully implemented

## Current Implementation

### Microservices (2 services):
- **Log Microservice**: Hardcoded status codes in logging_service function (2XXXX range)
- **Data Access Microservice**: Hardcoded status codes in data_service function (5XXXX range)

### Direct Usage Infrastructure (5 components):
- **Cache Infrastructure**: Direct usage patterns with CacheFactory and UnifiedCacheManager
- **Config Infrastructure**: Direct usage patterns with ConfigManager
- **State Infrastructure**: Direct usage patterns with JSONStateManager
- **History Infrastructure**: Direct usage patterns with JSONHistoryManager
- **LLM Endpoints Infrastructure**: Direct usage patterns with create_agent_llm_adapter

### Agent Status Codes (hardcoded in components):
- **Location Agent**: Hardcoded status codes in location_agent.py (2XXXX range)
- **Error Handler**: Hardcoded status codes in error_handler.py (8XXXX range)
- **Classification**: Hardcoded status codes in system_prompt.py (3XXXX range)
