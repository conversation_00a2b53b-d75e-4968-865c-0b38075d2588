# LLM Engine Development Plan: Phase-Based Implementation

## 🎯 OVERVIEW
Restructure LLM engine architecture and add enhanced location finding capabilities.

---

## ✅ PHASE 1: CORE ARCHITECTURE RESTRUCTURING - COMPLETED

### What Was Done:
1. **Moved agents** from `src/agents/` to `src/components/messages/agents/`
2. **Organized prompts** into component-specific folders
3. **Renamed files** for clarity (e.g., `agent.py` → `location_agent.py`)
4. **Consolidated logic** in MainLLM as central orchestrator
5. **Fixed all imports** and eliminated circular dependencies

### Result:
```
src/components/messages/
├── orchestrator/main_llm.py + prompts/
└── agents/location/ (classification/, advice/, data/)
```

### Status: ✅ **COMPLETE & WORKING**
- Location search works perfectly
- Finds cafes, restaurants, etc.
- Proper session management
- Clean architecture
- **UPDATED**: History timing fixed for proper title generation
- **UPDATED**: Session title fallback generation removed

### Recent Improvements (Latest Updates):

#### 🔧 History Timing Fix
**Problem**: User messages were logged to history BEFORE the API call, causing first requests to include current message in history, preventing proper title generation.

**Solution**:
- User messages now logged AFTER successful API call
- History retrieved BEFORE API call (excludes current message)
- First request has empty history → perfect for title generation
- Subsequent requests include proper conversation context

**Files Changed**: `src/components/messages/orchestrator/main_llm.py` (lines 79-99)

#### 🔧 Session Title Fallback Removal
**Problem**: Automatic session title generation created unwanted titles like "Looking for cafes" when LLM didn't provide one.

**Solution**:
- Removed fallback session title generation logic
- Now respects LLM response exactly - only uses titles LLM explicitly provides
- Empty session_title when not provided by LLM

**Files Changed**: `src/components/messages/orchestrator/main_llm.py` (lines 169, 256)

## ✅ PHASE 2: CLASSIFICATION CLEANUP - COMPLETED

### What Was Done:
1. **Removed clarification logic** from classification system prompt
2. **Simplified classification** to only return subcategories and tags (NER only)
3. **Updated output format** to single JSON response format
4. **Removed redundant test files** with outdated clarification logic
5. **Fixed error handling** - failed searches now pass to advice system instead of returning errors
6. **Verified changes** work correctly

### Why:
- Main LLM can handle clarification with normal prompts
- Keep classification focused on NER (Named Entity Recognition) only
- Clarification is not currently handled by location agent anyway

### Changes Made:
- **File**: `src/components/messages/agents/location/classification/prompts/system_prompt.py`
- **Lines removed**: 36-51 (clarification logic and dual output formats)
- **New logic**: "Return best match even if uncertain"
- **New format**: Single JSON output format only
- **Deleted**: `service_test/` directory (contained outdated clarification test code)
- **File**: `src/components/messages/agents/location/location_agent.py`
- **Error handling**: Replaced error returns with advice system calls for better UX

#### 🔧 Error Handling Improvement
**Problem**: When classification returned empty subcategories or no POIs were found, the system returned error codes instead of helpful responses.

**Solution**:
- **No subcategories found**: Return helpful message with available categories to main LLM
- **No POIs found**: Return helpful context about the failed search to main LLM
- **No candidates found**: Return context about found POIs but no top candidates to main LLM
- **Session management**: Main LLM properly logs responses and maintains session state
- **Result**: Users get natural, helpful responses with proper chat history

**Example**: "Find bar near BELTUR mini" → Classification returns `[]` → Location agent returns: "Could not find a matching category for your request. Available categories in radius of 1000m: cafe, restaurant, museum..." → Main LLM logs to history and maintains session state

### Status: ✅ **COMPLETED**

---

## 🔄 PHASE 3: RELATIVE LOCATION FUNCTIONALITY - PENDING

### What To Do:
Add ability to search near previously found locations instead of user's current location.

### Example Use Case:
1. User: "Find a cafe" → System finds "Mizu Cafe" at (40.966, 28.796)
2. User: "Find a restaurant next to Mizu Cafe"
3. System should search for restaurants near Mizu Cafe's coordinates, not user's location

### Implementation Steps:
1. **Update main_llm prompt** to recognize relative location requests
2. **Add "find_relative_location" action** to main_llm responses
3. **Add response handling** in main_llm to resolve reference location coordinates
4. **Call existing location_agent** with reference location's lat/lng instead of user's
5. **Test end-to-end** functionality

### Key Technical Changes:
- **Main LLM prompt**: Recognize relative requests and return "find_relative_location" action
- **Main LLM response handling**: Resolve location name → get coordinates → call location_agent
- **Location Agent**: No changes needed - same logic, different origin coordinates
- **Same flow**: Exact same as find_location, just different lat/lng origin

### Status: 🔄 **PENDING**

## 📋 NEXT STEPS (IN ORDER)

### ✅ Phase 2: Classification Cleanup - COMPLETED
1. ✅ Edited `src/components/messages/agents/location/classification/prompts/system_prompt.py`
2. ✅ Removed clarification logic from prompt (lines 36-51)
3. ✅ Kept only NER functionality (subcategories and tags extraction)
4. ✅ Deleted `service_test/` directory with outdated clarification code
5. ✅ Fixed error handling in `location_agent.py` - now returns helpful messages with proper session management
6. ✅ Verified all clarification logic completely removed from codebase

### Phase 3: Relative Location Functionality
1. Edit `src/components/messages/orchestrator/prompts/system_prompt.py`
2. Add recognition for relative location requests in main_llm prompt
3. Add `"find_relative_location"` action to main_llm responses
4. Edit `src/components/messages/orchestrator/main_llm.py`
5. Add response handling for "find_relative_location" action
6. Add location name resolution from previous top_candidates
7. Call existing location_agent with reference location coordinates
8. Test with example: "Find restaurant next to Mizu Cafe"

---

## 🎯 CURRENT STATUS

**✅ PHASE 1 COMPLETE**: Core architecture restructuring done and working
**✅ RECENT IMPROVEMENTS**: History timing and session title fixes completed
**✅ PHASE 2 COMPLETE**: Classification cleanup + Error handling agent implementation
**� NEXT PHASES**: See `docs/6_do.txt` for Phase 3+ (Relative location, Advanced error handling, Performance optimization)

### Latest Completed Work:
- ✅ Fixed history timing for proper title generation
- ✅ Removed unwanted session title fallback generation
- ✅ User prompts now added AFTER API calls, not before
- ✅ LLM responses respected exactly for session titles
- ✅ **PHASE 2**: Removed clarification logic from classification system
- ✅ **PHASE 2**: Simplified classification to NER-only functionality
- ✅ **PHASE 2**: Deleted outdated test files with clarification code
- ✅ **PHASE 2**: Fixed error handling - now returns helpful messages with proper session management

**Ready for next phase implementation!**



