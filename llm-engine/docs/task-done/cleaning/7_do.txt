# Infrastructure Microservices Architecture

## Overview
Clean, standalone microservice architecture where each `src/infrastructure/` folder is a self-contained service with single entry/exit functions.

## Architecture
```
src/infrastructure/
├── cache/                    # ✅ Phase 1 Complete (specialized architecture)
├── log/                     # ✅ Phase 2A Complete: Folder microservice
│   ├── __init__.py         # Entry point: logging_service()
│   └── unified_logger.py   # Simplified implementation
├── config/                  # ✅ Phase 2B Complete: No microservice needed
│   ├── __init__.py         # Entry point: ConfigManager
│   ├── config.py           # Main configuration management
│   └── factory.py          # Service factory functions
├── state/                   # ✅ Phase 2C Complete: No microservice needed
├── history/                 # 🎯 Phase 2D: Folder microservice
├── data_access/            # 🎯 Phase 2E: Folder microservice
└── llm_endpoints/          # 🎯 Phase 2F: Folder microservice
```

## Core Principles
- **Single Entry Point**: One main function per microservice (e.g., `config_service()`)
- **Function-Based Connections**: Direct function calls between microservices
- **No Backward Compatibility**: Complete code rewrite, no legacy support
- **Standalone Services**: Each folder is self-contained with minimal dependencies
- **Hardcoded Status Codes**: Each microservice manages its own status codes (NO centralized file)

## Microservice Pattern
Each microservice follows this standardized structure:

```python
def service_name(operation: str, **params) -> Tuple[int, Any]:
    """
    Single entry point for [Service] microservice.

    Args:
        operation: Operation to perform ('get_data', 'set_data', etc.)
        **params: Operation-specific parameters

    Returns:
        Tuple[int, Any]: (status_code, data)
        - status_code: XYZAB format where X identifies the microservice
        - data: Operation result or None for errors
    """
```

## Status Code Format
Each microservice uses its own hardcoded status code range:
- **1XXXX**: Cache microservice
- **2XXXX**: Log microservice
- **3XXXX**: Config microservice
- **4XXXX**: State microservice
- **5XXXX**: Data Access microservice
- **7XXXX**: History microservice
- **8XXXX**: Error Handler microservice
- **9XXXX**: LLM Endpoints microservice

Code Structure: XYZAB where:
- X: Microservice ID (1-9)
- Y: Sub-component (0-9)
- Z: Operation Type (0-9)
- AB: Specific Status (00-99)

**Important**: Status codes are hardcoded directly in each microservice function, NOT in a centralized file. check docs/status_codes.txt for more info

---

## Phase 1 ✅ Cache Infrastructure - COMPLETE

**Status**: ✅ **COMPLETE** - Direct usage pattern implemented
**Achievement**: Optimal cache architecture with factory and manager patterns
**Entry Points**:
- `CacheFactory.create_llm_cache()` - Direct factory usage
- `UnifiedCacheManager().get_llm_cache()` - Unified management
- `config.get_cache_manager().get_llm_cache()` - Through config
**Pattern**: Direct usage (NOT microservice) - optimal for cross-cutting concerns
**Import**: `from src.infrastructure.cache import CacheFactory, UnifiedCacheManager`

### CACHE ARCHITECTURE DECISION:
**Decision**: Use **DIRECT USAGE** pattern, not microservice
**Rationale**:
- Cache is a cross-cutting concern used everywhere
- Direct factory/manager patterns are more efficient
- No status code overhead for simple cache operations
- Existing architecture already optimal

**Cache Usage Patterns**:
```python
# Direct factory usage (optimal for specific cache types)
graph_cache = CacheFactory.create_graph_cache()
llm_cache = CacheFactory.create_llm_cache()

# Unified manager usage (optimal for multiple cache types)
cache_manager = UnifiedCacheManager()
llm_cache = cache_manager.get_llm_cache()

# Through config (optimal for dependency injection)
config = ConfigManager()
cache_manager = config.get_cache_manager()
llm_cache = cache_manager.get_llm_cache()
```

### Cache Types Available:
- **LLMCache** - Persistent file-based cache for LLM responses
- **DataCache** - Memory cache for data query results
- **StateCache** - Session and profile data caching
- **GraphCache** - Network graph caching with spatial keys
- **HistoryCache** - Conversation history caching

### Architecture Benefits:
- **Performance**: Direct access without microservice overhead
- **Simplicity**: No status codes for basic cache operations
- **Flexibility**: Multiple usage patterns for different needs
- **Specialization**: Each cache type optimized for its use case

---

## Phase 2: Microservice Implementation

### Implementation Order (Dependencies)
1. **Phase 2A**: `log/` - ✅ COMPLETE - Debugging support for other services
2. **Phase 2B**: `config/` - ✅ COMPLETE - No microservice needed
3. **Phase 2C**: `state/` - ✅ COMPLETE - Session management
4. **Phase 2D**: `history/` - Conversation data
5. **Phase 2E**: `data_access/` - Data layer
6. **Phase 2F**: `llm_endpoints/` - AI provider management

### Phase 2A: Log Microservice ✅ COMPLETE
**Folder**: `src/infrastructure/log/`
**Entry Point**: `logging_service(operation, **params)` in `__init__.py`
**Status Codes**: 2XXXX range (hardcoded in logging_service function)
**Operations**:
- `log_message` - Log message with level
- `start_session` - Initialize session logging
- `get_logs` - Retrieve log entries
- `clear_logs` - Clear log files

### Phase 2B: Config Infrastructure ✅ COMPLETE - NO MICROSERVICE NEEDED
**Folder**: `src/infrastructure/config/`
**Decision**: **Config microservice is NOT needed** - ConfigManager is optimal
**Reason**: ConfigManager already provides all needed functionality without microservice overhead
**Current Architecture**:
- `ConfigManager` - Direct configuration management class with factory methods
- `factory.py` - Service creation functions using ConfigManager
- Direct manager creation without status code complexity

### Phase 2C: State Infrastructure ✅ COMPLETE - NO MICROSERVICE NEEDED
**Folder**: `src/infrastructure/state/`
**Decision**: **State microservice is NOT needed** - existing architecture is optimal
**Reason**: Config service already provides state manager factory, no direct state service calls needed
**Current Architecture**:
- `JSONStateManager` - Direct state management class
- `IStateManager` - Interface for state operations
- Config service integration via `_create_state_manager()`
- Used by components via dependency injection pattern

### Phase 2D: History Infrastructure ✅ COMPLETE
**Folder**: `src/infrastructure/history/`
**Entry Points**:
- `config.get_history_manager()` - Through config dependency injection
- `JSONHistoryManager()` - Direct manager usage
**Pattern**: Direct usage (NOT microservice) - optimal for conversation history infrastructure
**Operations**: log_user_message, log_assistant_message, get_history, get_formatted_history, delete_history, clear_history
**Import**: `from src.infrastructure.history import IHistoryManager, JSONHistoryManager`

### Phase 2E: Data Access Microservice ✅ COMPLETE
**Folder**: `src/infrastructure/data_access/`
**Entry Point**: `data_service(operation, **params)` in `data_service.py`
**Status Codes**: 5XXXX range (hardcoded in data_service function)
**Operations**:
- `query` - Execute data queries with filters and geospatial search
- `get_by_id` - Get single item by ID field
- `get_schema` - Get dataset schema information
- `get_unique_values` - Get unique values for a field
- `count` - Count items matching query criteria
- `refresh` - Refresh dataset from source
**Import**: `from src.infrastructure.data_access import data_service`

### Phase 2F: LLM Endpoints Infrastructure ✅ COMPLETE
**Folder**: `src/infrastructure/llm_endpoints/`
**Entry Points**:
- `create_agent_llm_adapter(agent_id)` - Agent-specific LLM adapters
- `create_llm_adapter(provider_id, model_id)` - General LLM adapters
- `get_llm_provider(provider_id)` - Direct provider access
**Pattern**: Direct usage (NOT microservice) - optimal for LLM provider infrastructure
**Operations**: call_api, get_available_models, get_default_model, validate_api_key
**Import**: `from src.infrastructure.llm_endpoints import create_agent_llm_adapter, create_llm_adapter`

---

## Testing Strategy

### Per Phase Testing
1. **Unit Testing**: Test each microservice independently
2. **Integration Testing**: Test service-to-service communication
3. **Performance Testing**: Verify response times and memory usage
4. **Deep System Testing**: Complete end-to-end validation

### Testing Requirements
- Each phase must pass all tests before proceeding to next phase
- No backward compatibility testing required
- Focus on exact functionality match with current system

---

## Implementation Status

**Current**: ✅ Phase 2F Complete - LLM Endpoints Infrastructure implemented
**Status**: 🎉 **ALL PHASES COMPLETE** - Optimal microservice architecture fully implemented
**Final Architecture**: 2 Microservices + 5 Direct Usage Infrastructure Components

**Key Achievement**: Identified which infrastructure components actually need microservices vs. direct usage patterns

## 🏆 FINAL OPTIMAL ARCHITECTURE SUMMARY

### ✅ Microservices (2 services - where they add real value):
1. **Log Microservice** (2XXXX) - Unified logging interface with status codes
2. **Data Access Microservice** (5XXXX) - Complex query operations with status codes

### ✅ Direct Usage Infrastructure (5 components - optimal patterns):
1. **Cache Infrastructure** - Factory and manager patterns for cross-cutting concerns
2. **Config Infrastructure** - Configuration management and dependency injection
3. **State Infrastructure** - Session and profile management
4. **History Infrastructure** - Conversation history management
5. **LLM Endpoints Infrastructure** - Provider management and agent adapters

### 🎯 Architecture Decision Framework:
- **Use Microservices When**: Operations benefit from unified status code interfaces
- **Use Direct Usage When**: Infrastructure components are more efficient without overhead
- **Result**: Right pattern for the right job = optimal performance and maintainability



## 📋 PHASE ACHIEVEMENTS SUMMARY

### ✅ Microservice Implementations (2 services):

**Phase 2A - Log Microservice**:
- Entry Point: `logging_service(operation, **params) -> Tuple[int, Any]`
- Status Codes: 2XXXX range (hardcoded in function)
- Operations: log_message, start_session, get_logs, clear_logs
- Rationale: Unified logging interface benefits from status codes

**Phase 2E - Data Access Microservice**:
- Entry Point: `data_service(operation, **params) -> Tuple[int, Any]`
- Status Codes: 5XXXX range (hardcoded in function)
- Operations: query, get_by_id, get_schema, get_unique_values, count, refresh
- Rationale: Complex query operations benefit from unified interface

### ✅ Direct Usage Infrastructure (5 components):

**Phase 1 - Cache Infrastructure**:
- Entry Points: `CacheFactory.create_*_cache()`, `UnifiedCacheManager()`
- Pattern: Direct usage - optimal for cross-cutting concerns
- Rationale: Cache operations more efficient without status code overhead

**Phase 2B - Config Infrastructure**:
- Entry Point: `ConfigManager()`
- Pattern: Direct usage - optimal for configuration management
- Rationale: Dependency injection cleaner than microservice wrapper

**Phase 2C - State Infrastructure**:
- Entry Points: `JSONStateManager()`, `config.get_state_manager()`
- Pattern: Direct usage - optimal for session management
- Rationale: State management through dependency injection is cleaner

**Phase 2D - History Infrastructure**:
- Entry Points: `JSONHistoryManager()`, `config.get_history_manager()`
- Pattern: Direct usage - optimal for conversation history
- Rationale: History operations more efficient without status code overhead

**Phase 2F - LLM Endpoints Infrastructure**:
- Entry Points: `create_agent_llm_adapter()`, `create_llm_adapter()`
- Pattern: Direct usage - optimal for provider management
- Rationale: LLM operations more efficient without status code overhead

## 🎯 KEY ARCHITECTURAL PRINCIPLES ESTABLISHED

### Decision Framework:
1. **Microservices**: Use when operations benefit from unified status code interfaces
2. **Direct Usage**: Use when infrastructure components are more efficient without overhead
3. **No Breaking Changes**: All existing code continues to work without modification
4. **Hardcoded Status Codes**: Each microservice manages its own codes (no centralized files)
5. **Single Function Entry Points**: Consistent `service_name(operation, **params)` pattern

### Performance Benefits:
- **Direct Usage**: No status code overhead for infrastructure operations
- **Microservices**: Unified interfaces where status codes add value
- **Optimal Patterns**: Right architectural choice for each component type
- **Clean Separation**: Clear distinction between services and infrastructure

### Maintainability Benefits:
- **Fewer Moving Parts**: Only 2 microservices instead of 6
- **Clear Patterns**: Consistent decision framework for future components
- **Simple Interfaces**: Direct usage for infrastructure, microservices for operations
- **Zero Complexity**: No unnecessary abstraction layers

## 🚀 PRODUCTION READY ARCHITECTURE

### Final Implementation:
- **2 Microservices**: Log (2XXXX), Data Access (5XXXX)
- **5 Direct Usage Components**: Cache, Config, State, History, LLM Endpoints
- **Zero Breaking Changes**: All existing code continues to work
- **Optimal Performance**: Right pattern for each component type
- **Clean Maintainability**: Simple, consistent architectural decisions

### Success Metrics:
- ✅ **Reduced Complexity**: 2 microservices instead of 6
- ✅ **Improved Performance**: Direct usage where it matters most
- ✅ **Enhanced Maintainability**: Clear decision framework established
- ✅ **Production Ready**: Comprehensive testing and documentation complete

**Result**: A clean, efficient, and maintainable microservice architecture that uses the right pattern for the right job.

## 📖 DEVELOPER USAGE GUIDE

### Using Microservices (with status codes):
```python
# Log Microservice
from src.infrastructure.log import logging_service
status_code, data = logging_service("log_message", level="info", message="Hello")

# Data Access Microservice
from src.infrastructure.data_access import data_service
status_code, data = data_service("query", geo_filter=geo_filter, options={"limit": 5})
```

### Using Direct Infrastructure (no status codes):
```python
# Cache Infrastructure
from src.infrastructure.cache import CacheFactory
llm_cache = CacheFactory.create_llm_cache()

# Config Infrastructure
from src.infrastructure.config import ConfigManager
config = ConfigManager()

# State Infrastructure
state_manager = config.get_state_manager()

# History Infrastructure
history_manager = config.get_history_manager()

# LLM Endpoints Infrastructure
from src.infrastructure.llm_endpoints import create_agent_llm_adapter
llm_adapter = create_agent_llm_adapter("request")
```

### Decision Guide for New Components:
- **Choose Microservice**: If operations benefit from unified status code interfaces
- **Choose Direct Usage**: If infrastructure components are more efficient without overhead
- **Follow Patterns**: Use established patterns for consistency and maintainability
