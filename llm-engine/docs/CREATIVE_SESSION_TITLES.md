# Creative Session Title Generation System 🎨

## Overview

The session title generation system has been completely redesigned to create **engaging, creative, and visually appealing titles** that make each search session feel like an adventure!

## 🌟 Creative Features

### **1. Emoji-Enhanced Titles**
- ☕ Coffee-related searches get coffee emojis
- 🍽️ Restaurant searches get dining emojis  
- 💊 Pharmacy searches get medicine emojis
- 🛍️ Shopping searches get shopping emojis
- 🗺️ General searches get exploration emojis

### **2. Thematic Naming Conventions**
- **Quest**: "Coffee Quest", "Pharmacy Quest"
- **Adventure**: "Foodie Adventure", "Shopping Adventure"
- **Safari**: "Food Safari", "Shopping Safari"
- **Hunt**: "Coffee Hunt", "Place Hunter"
- **Mission**: "Caffeine Mission", "Medicine Mission"
- **Discovery**: "Local Discovery", "Retail Discovery"
- **Expedition**: "Espresso Expedition", "Taste Expedition"

### **3. Multi-Category Combinations**
Creative titles for searches involving multiple categories:

#### **Coffee & Pharmacy:**
- ☕💊 Coffee & Care
- Brew & Health Hub
- Caffeine & Wellness
- Coffee & Medicine Run
- Latte & Life Essentials

#### **Restaurant & Shopping:**
- 🍽️🛍️ Dine & Shop
- Food & Fashion Hunt
- Eat & Buy Adventure
- Culinary Shopping Tour
- Feast & Fashion Quest

#### **Cafe & Restaurant:**
- ☕🍽️ Food & Coffee Tour
- Dining & Coffee Quest
- Eat & Brew Adventure
- Foodie's Coffee Trail
- Taste & Sip Journey

### **4. POI-Based Adventures**
Location-specific creative titles:

#### **Templates:**
- 🎯 {Category} Hunt near {POI}
- 📍 {POI} Area {Category} Quest
- 🗺️ Exploring {Category}s around {POI}
- 🔍 {POI} Neighborhood {Category} Safari
- 📌 {Category} Discovery: {POI} Edition

#### **Examples:**
- 🎯 Cafe Hunt near Saltbae
- 📍 Galata Tower Area Restaurant Quest
- 🗺️ Exploring Cafes around Taksim Square
- 📌 Shopping Discovery: Salt Bae Edition

### **5. Single Category Creative Titles**

#### **Cafe/Coffee:**
- ☕ Coffee Quest
- Café Adventures
- Brew Discovery
- Caffeine Mission
- Espresso Expedition
- Bean There, Done That
- Latte Locations
- Coffee Culture Tour

#### **Restaurant/Food:**
- 🍽️ Foodie Adventure
- Culinary Quest
- Taste Expedition
- Food Safari
- Flavor Hunt
- Gourmet Journey
- Bite-Size Adventures
- Foodie's Paradise

#### **Pharmacy/Health:**
- 💊 Health Hub Hunt
- Wellness Quest
- Medicine Mission
- Health Stop Search
- Wellness Journey
- Care Corner Quest
- Pharmacy Patrol
- Health & Wellness Tour

#### **Shopping:**
- 🛍️ Shopping Spree
- Retail Therapy
- Shopping Safari
- Mall Crawl
- Boutique Hunt
- Shopping Adventure
- Retail Quest
- Store Safari

### **6. General Exploration Titles**
For unspecified or general searches:
- 🗺️ Urban Explorer
- 🔍 City Safari
- 📍 Local Discovery
- 🎯 Place Hunter
- 🌟 Hidden Gems Quest
- 🚀 Adventure Awaits
- 🗺️ Neighborhood Navigator
- 📍 Local Legends Hunt
- 🎪 City Circus Tour
- 🎭 Urban Adventure Show

## 🎯 Example Results

### **Your Request:**
```
"search for a cafe close to saltbae and also find pharmacies in the same area"
```

### **Possible Creative Titles:**
- ☕💊 Coffee & Care
- Brew & Health Hub
- Caffeine & Wellness
- Coffee & Medicine Run
- Latte & Life Essentials

### **Other Examples:**

| Request | Creative Title |
|---------|---------------|
| "restaurants near galata tower" | 📍 Galata Tower Area Restaurant Quest |
| "shopping and coffee" | ☕🛍️ Shop & Sip |
| "find pharmacies" | 💊 Health Hub Hunt |
| "places to visit" | 🌟 Hidden Gems Quest |
| "hotels and restaurants" | 🏨🍽️ Stay & Dine |

## 🔧 Technical Implementation

### **Random Selection**
- Each category has multiple creative options
- Random selection ensures variety across sessions
- No two sessions will have identical titles (unless by chance)

### **Smart Category Detection**
- Detects multiple categories in complex requests
- Prioritizes multi-category combinations
- Falls back to single category if needed

### **POI Name Extraction**
- Intelligently extracts POI names from requests
- Filters out common words ("and", "also", "find", etc.)
- Limits to 2 meaningful words for clean titles

### **Length Optimization**
- Maximum 35 characters for optimal display
- Truncates with "..." if needed
- Balances creativity with readability

## 🎨 Benefits

✅ **Engaging User Experience**: Makes each search feel like an adventure  
✅ **Visual Appeal**: Emoji integration for modern UI aesthetics  
✅ **Memorable Sessions**: Creative names help users remember their searches  
✅ **Variety**: Random selection prevents repetitive titles  
✅ **Context-Aware**: Titles reflect the actual search content  
✅ **Scalable**: Easy to add new categories and creative templates  
✅ **Fallback Handling**: Always generates something creative, even for edge cases  

## 🚀 Future Enhancements

- **Seasonal Themes**: Holiday-specific titles
- **Time-Based Variations**: Morning coffee vs evening dining
- **User Preferences**: Customizable creativity levels
- **Location-Specific**: City-specific creative elements
- **Achievement System**: Special titles for frequent users

The creative session title system transforms mundane location searches into exciting adventures! 🎉
