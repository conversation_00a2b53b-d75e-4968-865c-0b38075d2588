# 🌍 Wizlop API Reference

> **Location-based AI Assistant API** - Find places, get recommendations, and manage conversations with spatial intelligence.

---

## 📋 **Quick Reference**

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/message` | POST | 🤖 Main chat endpoint with location search |
| `/message/stream` | POST | ⚡ Streaming chat endpoint for faster responses |
| `/session` | POST | 🆕 Create new conversation session |
| `/messages` | POST | 💬 Get conversation history |
| `/titles` | POST | 📝 Get session titles |
| `/delete` | POST | 🗑️ Delete session |
| `/health` | GET | ❤️ Health check |

**Base URL:** `http://127.0.0.1:8000`

---

## 🤖 **Main Chat Endpoint**

### **POST /message**
*Process user messages with location-based AI assistance*

**With Location (for location-based queries):**
```bash
curl -X POST "http://127.0.0.1:8000/message" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "session_456",
    "message": "Find cafes near me",
    "latitude": 40.971255,
    "longitude": 28.793878,
    "search_radius": 1000,
    "num_candidates": 3
  }'
```

**Without Location (for general conversation):**
```bash
curl -X POST "http://127.0.0.1:8000/message" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "session_456",
    "message": "Hello, how are you?",
    "latitude": null,
    "longitude": null,
    "search_radius": 1000,
    "num_candidates": 3
  }'
```

**Response:**
```json
{
  "response": "I found some great cafes nearby! Here are your options...",
  "top_candidates": [
    {
      "name": "BELTUR mini",
      "latitude": 40.9684729,
      "longitude": 28.7936791,
      "walk_route_distance_m": 672.13,
      "address": "Florya, Bakırköy, İstanbul"
    }
  ],
  "session_title": "Looking for cafes",
  "latitude": 40.971255,
  "longitude": 28.793878,
  "search_radius": 1000
}
```

---

## ⚡ **Streaming Chat Endpoint**

### **POST /message/stream**
*Process messages with streaming response for faster user experience*

**Same request format as `/message` but returns Server-Sent Events (SSE) stream**

```bash
curl -X POST "http://127.0.0.1:8000/message/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "1748425902_0f45170f",
    "message": "find me a good cafe nearby",
    "latitude": 40.971255,
    "longitude": 28.793878,
    "search_radius": 1000,
    "num_candidates": 5
  }'
```

**Streaming Response Format:**
The endpoint returns Server-Sent Events (SSE) with different chunk types:

```
data: {"type": "metadata", "latitude": 40.971255, "longitude": 28.793878, "search_radius": 1000, "status": "processing"}

data: {"type": "text_chunk", "content": "I", "chunk_index": 0, "is_final_text_chunk": false}

data: {"type": "text_chunk", "content": "found ", "chunk_index": 1, "is_final_text_chunk": false}

data: {"type": "text_chunk", "content": "some ", "chunk_index": 2, "is_final_text_chunk": false}

data: {"type": "text_chunk", "content": "great ", "chunk_index": 3, "is_final_text_chunk": false}

data: {"type": "text_chunk", "content": "cafes", "chunk_index": 4, "is_final_text_chunk": true}

data: {"type": "candidates", "top_candidates": [...], "total_candidates": 3}

data: {"type": "session_title", "session_title": "Looking for cafes"}

data: {"type": "complete", "status": "success", "total_chunks": 5}
```

**Stream Chunk Types:**
- `metadata`: Initial processing information
- `text_chunk`: Word-by-word response text streaming
- `candidates`: POI candidates data
- `session_title`: Session title (if available)
- `complete`: Final completion signal
- `error`: Error information (if any)

**Benefits:**
- ⚡ **Faster perceived response time** - Users see text immediately
- 🔄 **Progressive loading** - Content appears as it's processed
- 📱 **Better UX** - Ideal for real-time chat interfaces
- 🚀 **Reduced latency** - No waiting for complete response

---

## 🆕 **Session Management**

### **POST /session**
*Create a new conversation session*

```bash
curl -X POST "http://127.0.0.1:8000/session" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123"
  }'
```

**Response:**
```json
{
  "session_id": "1748425902_0f45170f"
}
```

---

## 💬 **Conversation History**

### **POST /messages**
*Get conversation history for a session*

```bash
curl -X POST "http://127.0.0.1:8000/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "1748425902_0f45170f"
  }'
```

**Response:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Find cafes near me",
      "timestamp": "2024-01-15T10:30:00Z"
    },
    {
      "role": "assistant",
      "content": "I found some great cafes nearby!",
      "timestamp": "2024-01-15T10:30:05Z"
    }
  ]
}
```

---

## 📝 **Session Titles**

### **POST /titles**
*Get all session titles for a user*

```bash
curl -X POST "http://127.0.0.1:8000/titles" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123"
  }'
```

**Response:**
```json
{
  "titles": [
    {
      "session_id": "1748425902_0f45170f",
      "title": "Looking for cafes",
      "created_at": 1748425902
    },
    {
      "session_id": "**********_ab1234cd",
      "title": "Restaurant recommendations",
      "created_at": **********
    }
  ]
}
```

---

## 🗑️ **Session Deletion**

### **POST /delete**
*Delete a conversation session*

```bash
curl -X POST "http://127.0.0.1:8000/delete" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "1748425902_0f45170f"
  }'
```

**Response:**
```json
{
  "message": "Session 1748425902_0f45170f has been marked as removed"
}
```

---

## ❤️ **Health Check**

### **GET /health**
*Check API health status*

```bash
curl -X GET "http://127.0.0.1:8000/health"
```

**Response:**
```json
{
  "status": "healthy"
}
```

---

## 📚 **Usage Examples**

### **Complete Workflow Example**

```bash
# 1. Create a session
SESSION_RESPONSE=$(curl -s -X POST "http://127.0.0.1:8000/session" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "demo_user"}')

SESSION_ID=$(echo $SESSION_RESPONSE | jq -r '.session_id')

# 2. Send a message
curl -X POST "http://127.0.0.1:8000/message" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"demo_user\",
    \"session_id\": \"$SESSION_ID\",
    \"message\": \"Find restaurants near Taksim Square\",
    \"latitude\": 41.0369,
    \"longitude\": 28.9850,
    \"search_radius\": 1500,
    \"num_candidates\": 5
  }"

# 3. Get conversation history
curl -X POST "http://127.0.0.1:8000/messages" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"demo_user\",
    \"session_id\": \"$SESSION_ID\"
  }"
```

---

## 🔧 **Request Parameters**

### **Message Endpoint Parameters**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | string | ✅ | Unique user identifier |
| `session_id` | string | ✅ | Session identifier |
| `message` | string | ✅ | User's message/query |
| `latitude` | number/null | ⚠️ | User's latitude coordinate (null if location unavailable) |
| `longitude` | number/null | ⚠️ | User's longitude coordinate (null if location unavailable) |
| `search_radius` | integer | ✅ | Search radius in meters |
| `num_candidates` | integer | ✅ | Max number of location results |

**Note:** When `latitude` and `longitude` are `null`, the assistant will respond with general conversation but cannot perform location-based searches.

### **Common Parameters**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | string | ✅ | Unique user identifier |
| `session_id` | string | ✅ | Session identifier |

---

*🚀 **Ready to explore? Start with creating a session and sending your first location query!***
