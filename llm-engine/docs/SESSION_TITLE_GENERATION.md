# Session Title Generation Implementation

## Problem Solved

**Session titles were not being generated** in the orchestrator, leaving sessions without meaningful titles for the frontend to display.

## Solution Implemented

### **Centralized Title Generation in Orchestrator**

The session title generation is now handled by the **Multi-task Orchestrator** with the following strategy:

#### **1. Generation Strategy**
- ✅ **Generate titles ONLY for first message** in a session
- ✅ **Analyze user request** to determine primary intent and categories
- ✅ **Detect multi-category searches** (e.g., "cafes & pharmacies")
- ✅ **Extract POI context** for location-based searches
- ✅ **Smart categorization** based on request content

#### **2. Title Patterns**

**Single Category Searches:**
- `"find coffee shops"` → `"Cafe Search"`
- `"restaurants nearby"` → `"Restaurant Search"`
- `"pharmacy locations"` → `"Pharmacy Search"`

**Multi-Category Searches:**
- `"cafes and pharmacies"` → `"Cafes & Pharmacies"`
- `"hotels and restaurants"` → `"Hotels & Restaurants"`
- `"shops and stores"` → `"Shops & More"`

**POI-Based Searches:**
- `"cafe near saltbae"` → `"Cafe near Saltbae"`
- `"pharmacy close to starbucks"` → `"Pharmacy near Starbucks"`

**Fallback:**
- Any unrecognized pattern → `"Location Search"`

#### **3. Hidden HTML Embedding**

Session titles are embedded in the response as **hidden HTML tags** that:
- ✅ **Don't affect user experience** (invisible to users)
- ✅ **Can be extracted by frontend** for UI display
- ✅ **Don't interfere with streaming** responses

**Format:**
```html
<span style="display:none;" data-session-title="Cafes & Pharmacies"></span>
```

## Implementation Details

### **Files Modified:**

#### **1. Multi-task Orchestrator** (`multi_task_orchestrator.py`)
- Added `_generate_session_title()` method
- Added `_embed_session_title_in_response()` method
- Modified `_aggregate_results()` to generate and embed titles
- Added session title to response data

#### **2. Session Context** (`session_context.py`)
- Added `is_first_message()` method to detect first message in session

### **Key Methods:**

#### **Title Generation Logic:**
```python
def _generate_session_title(self, user_request: str, task_results: List, top_candidates: List) -> str:
    # Analyze request for primary intent (cafe, restaurant, pharmacy, etc.)
    # Detect multi-category searches
    # Extract POI context for location-based searches
    # Generate appropriate title pattern
```

#### **Title Embedding:**
```python
def _embed_session_title_in_response(self, response_text: str, session_title: str) -> str:
    # Create hidden HTML tag with session title
    # Prepend to response text
    # Return embedded response
```

#### **First Message Detection:**
```python
def is_first_message(self) -> bool:
    # Check conversation history
    # Return True if no user/assistant messages exist
```

### **Integration Flow:**

1. **User sends first message** → Main LLM
2. **Main LLM** calls Multi-task Orchestrator with `session_context`
3. **Orchestrator** processes request and generates results
4. **Orchestrator** detects it's first message via `session_context.is_first_message()`
5. **Orchestrator** generates session title based on request analysis
6. **Orchestrator** embeds title as hidden HTML tag in response
7. **Orchestrator** includes title in response data for centralized session management
8. **Frontend** can extract title from hidden tag for UI display

## Benefits

✅ **Automatic title generation** for all first messages  
✅ **Smart categorization** based on request content  
✅ **Hidden embedding** doesn't affect user experience  
✅ **Frontend integration** ready via data attributes  
✅ **Centralized management** in orchestrator  
✅ **No duplicate generation** (only first message)  
✅ **Fallback handling** for unrecognized patterns  

## Example Usage

### **Request:**
```
"search for a cafe close to saltbae and also find pharmacies in the same area"
```

### **Generated Title:**
```
"Cafes & Pharmacies"
```

### **Embedded Response:**
```html
<span style="display:none;" data-session-title="Cafes & Pharmacies"></span>
<p>I found some great cafes around here! <strong data-location-id="9690">Kırmızı Kedi Café</strong>...</p>
```

### **Response Data:**
```json
{
  "response": "<span style=\"display:none;\" data-session-title=\"Cafes & Pharmacies\"></span><p>I found...",
  "top_candidates": [...],
  "session_title": "Cafes & Pharmacies"
}
```

## Frontend Integration

The frontend can extract the session title using:

```javascript
// Extract from hidden HTML tag
const hiddenSpan = response.querySelector('[data-session-title]');
const sessionTitle = hiddenSpan?.getAttribute('data-session-title');

// Or use from response data
const sessionTitle = responseData.session_title;
```

## Testing

Created `test_session_title_generation.py` to verify:
- Title generation logic for various request patterns
- Hidden HTML embedding functionality
- First message detection accuracy

The session title generation is now fully implemented and ready for production use! 🎉
