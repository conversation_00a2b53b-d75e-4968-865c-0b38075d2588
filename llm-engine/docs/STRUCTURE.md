<!-- @format -->

# LLM Engine - Organized Structure

## 📁 Directory Structure

```
llm-engine/
├── api.py                    # Main FastAPI application entry point
├── config/                   # Configuration files
│   ├── config.json          # Application configuration
│   └── requirements.txt     # Python dependencies
├── data/                     # Data files and datasets
│   ├── dataset.csv          # Main dataset
│   └── dataset_partitions/  # Partitioned data
├── docs/                     # Documentation and notes
│   ├── README.md            # Main documentation
│   ├── *.md files           # Various documentation files
│   ├── *.txt files          # Notes and task files
│   └── private/             # Private documentation
├── scripts/                  # Utility scripts
│   └── debug_init.py        # Debug initialization script
├── src/                      # Source code (main application)
│   ├── components/          # Application components
│   ├── infrastructure/      # Infrastructure layer
│   ├── models/              # Data models
│   ├── services/            # Business logic services
│   ├── spatial_db/          # Spatial database utilities
│   └── utils/               # Utility functions
├── tests/                    # Test files
│   └── system/              # System integration tests
│       └── full_test.py     # Complete system test
├── tools/                    # Development tools
│   ├── experimental/        # Experimental features
│   └── out_terminal.json    # Terminal output logs
└── venv/                     # Python virtual environment
```

## 🧹 Cleanup Summary

### ✅ Completed Actions:

- **Organized configuration**: Moved `config.json` and `requirements.txt` to `config/` directory
- **Consolidated documentation**: All `.md` and `.txt` files moved to `docs/` directory
- **Cleaned up tests**: Removed all test files except `full_test.py`, moved to `tests/system/`
- **Organized utilities**: Moved scripts to `scripts/` and experimental code to `tools/experimental/`
- **Fixed nested structure**: Removed redundant `llm-engine/llm-engine/` directory
- **Updated import paths**: Fixed configuration file references in:
  - `src/infrastructure/config/config.py`
  - `src/infrastructure/log/unified_logger.py`
  - `src/infrastructure/history/json_manager.py`
- **Cleaned cache files**: Removed all `__pycache__` directories

### 🗑️ Removed Files:

- `dull_test.py`
- `simple_poi_test.py`
- `test_creative_titles.py`
- `test_improved_title_generation.py`
- `test_phase4.py`
- `test_phase5_integration.py`
- `test_poi_import_fix.py`
- `test_poi_improvements.py`
- `test_poi_lookup.py`
- `test_session_management.py`
- `test_session_title_generation.py`
- `test_syntax_only.py`
- `new-stage/test copy.py`

## 🚀 Running the Application

### Start the API server:

```bash
cd llm-engine
source venv/bin/activate
uvicorn api:app --host 0.0.0.0 --port 8000 --reload
```

### Run system tests:

```bash
cd llm-engine
source venv/bin/activate
python tests/system/full_test.py
```

## 📝 Notes

- All imports have been updated to work with the new structure
- Configuration files are now centralized in the `config/` directory
- The main application entry point (`api.py`) remains in the root for easy access
- Virtual environment and data directories are preserved as-is
- The `src/` directory structure remains unchanged to maintain existing import paths
