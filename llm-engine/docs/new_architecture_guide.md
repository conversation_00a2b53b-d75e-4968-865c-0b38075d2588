# Wizlop LLM-Engine New Modular Architecture Guide

## Overview

The Wizlop LLM-Engine has been completely restructured from a monolithic location agent system into a modular, scalable architecture with dynamic function discovery. This guide explains the new system architecture, components, and how to work with them.

## Architecture Principles

### 1. **Separation of Concerns**
- **Agents** = Reusable tools (classification, location resolution, advice)
- **Processes** = Business logic workflows (search types)
- **Orchestrator** = Dynamic routing and coordination
- **Shared** = Common utilities and infrastructure

### 2. **Dynamic Function Discovery**
- Processes are automatically discovered and registered
- No manual orchestrator updates needed for new processes
- Metadata-driven routing and selection

### 3. **Zero Code Duplication**
- Shared utilities used across all processes
- Agents are reusable across multiple processes
- Common workflow patterns standardized

## Directory Structure

```
llm-engine/src/components/messages/
├── shared/                    # Shared infrastructure
│   ├── llm_endpoints/         # LLM provider management
│   ├── data_access/           # Data access layer
│   └── utils/                 # Common utilities
├── processes/                 # Business logic processes
│   ├── function_registry.py   # Dynamic discovery system
│   ├── shared/                # Shared process utilities
│   ├── user_location_search/  # Process 1: Search around user
│   ├── point_radius_search/   # Process 2: Search around location
│   └── boundary_search/       # Process 3: Search within boundaries
├── agents/                    # Reusable agent tools
│   ├── shared/                # Base agent utilities
│   ├── classification/        # LLM classification
│   ├── location_resolver/     # Location resolution
│   ├── advice/                # Response generation
│   └── error_handler/         # Error handling
└── orchestrator/              # Request orchestration
    ├── main_llm.py            # Main orchestrator
    └── process_router.py      # Process routing logic
```

## Core Components

### Function Registry (`processes/function_registry.py`)

The heart of the new architecture. Automatically discovers and registers all processes.

**Key Features:**
- Auto-discovery of processes in `processes/` directory
- Metadata extraction from `PROCESS_METADATA` constants
- Dynamic loading and caching of process functions
- Priority-based process selection

**Usage:**
```python
from components.messages.processes import get_function_registry, load_function

# Get registry
registry = get_function_registry()

# Load a specific process
process_func = load_function("user_location_search")
```

### Process Router (`orchestrator/process_router.py`)

Intelligent routing system that selects the best process for each request.

**Selection Logic:**
1. Find processes matching the requested action
2. Analyze prompt for location-specific keywords
3. Select based on priority and context
4. Execute the chosen process

### Base Processor (`processes/shared/base_processor.py`)

Standardized base class for all processes with common workflow patterns.

**Standard Workflow:**
1. Session normalization and logging setup
2. Coordinate validation
3. Process-specific workflow execution
4. Error handling and response formatting

## Process Types

### 1. User Location Search (`user_location_search/`)
- **Purpose**: Search around user's current coordinates
- **When to use**: User asks for places without specifying location
- **Priority**: 1 (highest)

### 2. Point Radius Search (`point_radius_search/`)
- **Purpose**: Search around a specific location's coordinates
- **When to use**: User specifies a location name or landmark
- **Priority**: 2

### 3. Boundary Search (`boundary_search/`)
- **Purpose**: Search within administrative boundaries
- **When to use**: Searching within districts or neighborhoods
- **Priority**: 3

## Standard Process Workflow

All processes follow the same 7-step workflow:

1. **Pre-Classifier**: Get available categories (process-specific)
2. **Classification Agent**: LLM classification (shared)
3. **Post-Classifier**: Parse response, handle errors (shared)
4. **Location Resolution**: Resolve coordinates/boundaries (shared)
5. **POI Search**: Search using appropriate method (shared)
6. **Top Candidates**: Rank by distance (shared)
7. **Advice Generation**: Generate final response (shared)

## Adding New Processes

### Step 1: Create Process Directory
```bash
mkdir llm-engine/src/components/messages/processes/my_new_process
```

### Step 2: Create Processor
```python
# my_new_process/processor.py
from ..shared.base_processor import BaseProcessor

# Process metadata for function registry
PROCESS_METADATA = {
    "action": "find_location",
    "description": "My new search process",
    "when_to_use": "When user needs special search",
    "parameters": ["prompt", "latitude", "longitude", "radius", "num_candidates"],
    "processor_function": "process_my_new_search",
    "priority": 4,
    "enabled": True
}

class MyNewProcessor(BaseProcessor):
    def __init__(self):
        super().__init__("MyNewProcess")
    
    def _do_initialize(self, **kwargs):
        pass
    
    def _execute_workflow(self, **kwargs):
        # Implement your 7-step workflow
        pass
    
    def get_process_info(self):
        return {"name": "MyNewProcess", "version": "1.0.0"}

def process_my_new_search(**kwargs):
    processor = MyNewProcessor()
    return processor.process_request(**kwargs)
```

### Step 3: Create Pre-Classifier (Optional)
```python
# my_new_process/pre_classifier.py
def get_available_categories_for_my_process(latitude, longitude, radius):
    # Process-specific category retrieval logic
    pass
```

### Step 4: Create __init__.py
```python
# my_new_process/__init__.py
"""My new search process."""
__all__ = []
```

The function registry will automatically discover and register your new process!

## Agent Usage

### Classification Agent
```python
from components.messages.agents.classification import llm_api

response = llm_api(prompt, available_subcategories, history=history)
```

### Location Resolver Agent
```python
from components.messages.agents.location_resolver import resolve_location

location_context = resolve_location("downtown")
```

### Advice Agent
```python
from components.messages.agents.advice import get_location_advice

advice = get_location_advice(prompt, history, top_candidates, lat, lon, radius)
```

## Shared Utilities

### Error Codes (`shared/utils/error_codes.py`)
Centralized status codes and error handling utilities.

### Response Parser (`shared/utils/response_parser.py`)
Common response parsing and JSON extraction utilities.

### Session Helpers (`shared/utils/session_helpers.py`)
Session management and coordinate validation utilities.

## Migration Benefits

1. **Dynamic Function Loading**: No orchestrator changes for new processes
2. **Zero Code Duplication**: Shared utilities eliminate redundancy
3. **Easy Extensibility**: Add processes without touching existing code
4. **Clear Separation**: Each component has single responsibility
5. **Easier Debugging**: Issues isolated to specific components
6. **Better Testing**: Each component can be tested independently
7. **Scalable Architecture**: Supports unlimited process types

## Performance Optimizations

1. **Function Caching**: Loaded processes are cached for reuse
2. **Lazy Loading**: Processes loaded only when needed
3. **Shared Utilities**: Common operations optimized once
4. **Efficient Routing**: Smart process selection reduces overhead

## Testing

Each component can be tested independently:

```python
# Test function registry
from components.messages.processes import get_function_registry
registry = get_function_registry()
assert len(registry.get_enabled_functions()) >= 3

# Test process execution
from components.messages.processes import load_function
process_func = load_function("user_location_search")
result = process_func(prompt="test", latitude=40.7, longitude=-74.0, radius=1000, num_candidates=5)
```

## Troubleshooting

### Process Not Found
- Check `PROCESS_METADATA` is defined in processor.py
- Verify processor.py is in correct directory structure
- Check function registry logs for discovery errors

### Import Errors
- Ensure all imports use relative paths within messages component
- Check shared utilities are properly exported in __init__.py files

### Performance Issues
- Check function registry cache is working
- Verify processes aren't being reloaded unnecessarily
- Monitor process selection logic efficiency

This new architecture provides a solid foundation for scalable, maintainable location search functionality while preserving all existing features.
