#!/bin/bash
# Development Build Script

set -e

echo "🚀 Starting Development Build..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
npm run clean

# Type checking
echo "🔍 Running TypeScript checks..."
npm run check-types:dev

# ESLint checking
echo "🔧 Running ESLint..."
npm run lint

# Build with webpack (for debugging)
echo "📦 Building with Webpack (development)..."
npm run build:webpack

# Build with Next.js
echo "⚡ Building with Next.js..."
npm run build:next

echo "✅ Development build completed successfully!"
echo "📁 Output: .next/ and dist/"
