#!/bin/bash
# Production Build Script

set -e

echo "🚀 Starting Production Build..."

# Set production environment
export NODE_ENV=production

# Clean previous builds
echo "🧹 Cleaning previous builds..."
npm run clean

# Install production dependencies
echo "📦 Installing dependencies..."
npm ci --only=production

# Type checking with strict rules
echo "🔍 Running TypeScript checks (strict)..."
npm run check-types:prod

# ESLint checking
echo "🔧 Running ESLint..."
npm run lint

# Build with webpack (production)
echo "📦 Building with Webpack (production)..."
npm run build:webpack-prod

# Build with Next.js
echo "⚡ Building with Next.js (production)..."
npm run build:next

# Bundle analysis (optional)
if [ "$ANALYZE_BUNDLE" = "true" ]; then
    echo "📊 Analyzing bundle..."
    npm run analyze
fi

echo "✅ Production build completed successfully!"
echo "📁 Output: .next/ and dist/"
echo "🚀 Ready for deployment!"
