/** @format */

import { config } from './config';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
	timestamp: string;
	level: LogLevel;
	message: string;
	meta?: Record<string, unknown>;
	userId?: string;
	sessionId?: string;
	requestId?: string;
}

class Logger {
	private logLevel: LogLevel;

	constructor() {
		this.logLevel = config.logging.level as LogLevel;
	}

	private shouldLog(level: LogLevel): boolean {
		const levels: Record<LogLevel, number> = {
			debug: 0,
			info: 1,
			warn: 2,
			error: 3,
		};
		return levels[level] >= levels[this.logLevel];
	}

	private formatMessage(entry: LogEntry): string {
		const { timestamp, level, message, meta, userId, sessionId, requestId } =
			entry;

		let formatted = `[${timestamp}] ${level.toUpperCase()}: ${message}`;

		if (userId) formatted += ` | User: ${userId}`;
		if (sessionId) formatted += ` | Session: ${sessionId}`;
		if (requestId) formatted += ` | Request: ${requestId}`;
		if (meta && Object.keys(meta).length > 0) {
			formatted += ` | Meta: ${JSON.stringify(meta)}`;
		}

		return formatted;
	}

	private log(
		level: LogLevel,
		message: string,
		meta?: Record<string, unknown>,
		context?: {
			userId?: string;
			sessionId?: string;
			requestId?: string;
		}
	) {
		if (!this.shouldLog(level)) return;

		const entry: LogEntry = {
			timestamp: new Date().toISOString(),
			level,
			message,
			meta,
			...context,
		};

		const formatted = this.formatMessage(entry);

		// Console logging
		if (config.logging.enableConsole) {
			switch (level) {
				case 'debug':
					console.debug(formatted);
					break;
				case 'info':
					console.info(formatted);
					break;
				case 'warn':
					console.warn(formatted);
					break;
				case 'error':
					console.error(formatted);
					break;
			}
		}

		// File logging (in production, you might want to use a proper logging library)
		if (config.logging.enableFile && typeof window === 'undefined') {
			// Server-side only
			this.writeToFile(formatted);
		}
	}

	private async writeToFile(message: string) {
		// In a real production app, use a proper logging library like Winston
		// This is a simple implementation for demonstration
		try {
			const fs = await import('fs');
			const path = await import('path');
			const logDir = path.dirname(config.logging.filePath);
			if (!fs.existsSync(logDir)) {
				fs.mkdirSync(logDir, { recursive: true });
			}
			fs.appendFileSync(config.logging.filePath, message + '\n');
		} catch (error) {
			console.error('Failed to write to log file:', error);
		}
	}

	debug(
		message: string,
		meta?: Record<string, unknown>,
		context?: { userId?: string; sessionId?: string; requestId?: string }
	) {
		this.log('debug', message, meta, context);
	}

	info(
		message: string,
		meta?: Record<string, unknown>,
		context?: { userId?: string; sessionId?: string; requestId?: string }
	) {
		this.log('info', message, meta, context);
	}

	warn(
		message: string,
		meta?: Record<string, unknown>,
		context?: { userId?: string; sessionId?: string; requestId?: string }
	) {
		this.log('warn', message, meta, context);
	}

	error(
		message: string,
		meta?: Record<string, unknown>,
		context?: { userId?: string; sessionId?: string; requestId?: string }
	) {
		this.log('error', message, meta, context);
	}

	// Specialized logging methods
	authEvent(
		event: string,
		userId: string,
		success: boolean,
		meta?: Record<string, unknown>
	) {
		this.info(`Auth ${event}: ${success ? 'SUCCESS' : 'FAILED'}`, meta, {
			userId,
		});
	}

	chatEvent(
		event: string,
		userId: string,
		sessionId: string,
		meta?: Record<string, unknown>
	) {
		this.info(`Chat ${event}`, meta, { userId, sessionId });
	}

	apiRequest(
		method: string,
		path: string,
		userId?: string,
		duration?: number,
		status?: number
	) {
		this.info(
			`API ${method} ${path}`,
			{
				duration: duration ? `${duration}ms` : undefined,
				status,
			},
			{ userId }
		);
	}

	securityEvent(
		event: string,
		details: Record<string, unknown>,
		userId?: string
	) {
		this.warn(`Security Event: ${event}`, details, { userId });
	}
}

// Export singleton instance
export const logger = new Logger();

// Helper function for request logging middleware
export function createRequestLogger(requestId: string) {
	return {
		debug: (message: string, meta?: Record<string, unknown>) =>
			logger.debug(message, meta, { requestId }),
		info: (message: string, meta?: Record<string, unknown>) =>
			logger.info(message, meta, { requestId }),
		warn: (message: string, meta?: Record<string, unknown>) =>
			logger.warn(message, meta, { requestId }),
		error: (message: string, meta?: Record<string, unknown>) =>
			logger.error(message, meta, { requestId }),
	};
}
