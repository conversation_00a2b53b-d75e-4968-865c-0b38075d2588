/**
 * POI Search and Validation Service
 * Handles POI searching, validation, and confirmation for media uploads
 *
 * @format
 */

/**
 * POI search result interface
 */
export interface POISearchResult {
	poi_type: 'official' | 'user_temp' | 'user_approved';
	poi_id: string;
	temp_id?: string;
	approved_id?: string;
	name: string;
	name_en?: string;
	name_tr?: string;
	name_uk?: string;
	name_de?: string;
	name_ru?: string;
	name_ar?: string;
	category?: string;
	subcategory?: string;
	cuisine?: string;
	city?: string;
	district?: string;
	neighborhood?: string;
	street?: string;
	full_address?: string;
	province?: string;
	country?: string;
	latitude: number;
	longitude: number;
	phone_number?: string;
	opening_hours?: string;
	description?: string;
	profile_picture_url?: string;
	is_favorite?: boolean;
}

/**
 * POI search filters interface
 */
export interface POISearchFilters {
	name?: string;
	city?: string;
	district?: string;
	neighborhood?: string;
	category?: string;
	subcategory?: string;
	cuisine?: string;
	latitude?: number;
	longitude?: number;
	radius?: number; // in meters
	limit?: number;
	page?: number;
}

/**
 * POI validation result interface
 */
export interface POIValidationResult {
	isValid: boolean;
	poi?: POISearchResult;
	error?: string;
	suggestions?: POISearchResult[];
}

/**
 * POI search and validation service
 */
export class POISearchService {
	private static readonly BASE_URL = '/api/pois';

	/**
	 * Search POIs by various criteria
	 */
	static async searchPOIs(filters: POISearchFilters): Promise<{
		success: boolean;
		pois: POISearchResult[];
		totalCount: number;
		totalPages: number;
		error?: string;
	}> {
		try {
			const searchParams = new URLSearchParams();

			// Add filters to search params
			if (filters.name) searchParams.set('name', filters.name);
			if (filters.city) searchParams.set('city', filters.city);
			if (filters.district) searchParams.set('district', filters.district);
			if (filters.category) searchParams.set('category', filters.category);
			if (filters.subcategory)
				searchParams.set('subcategory', filters.subcategory);
			if (filters.cuisine) searchParams.set('cuisine', filters.cuisine);
			if (filters.limit) searchParams.set('limit', filters.limit.toString());
			if (filters.page) searchParams.set('page', filters.page.toString());

			const response = await fetch(
				`${this.BASE_URL}/filter?${searchParams.toString()}`
			);
			const data = await response.json();

			if (data.success) {
				return {
					success: true,
					pois: data.pois || [],
					totalCount: data.totalCount || 0,
					totalPages: data.totalPages || 0,
				};
			} else {
				return {
					success: false,
					pois: [],
					totalCount: 0,
					totalPages: 0,
					error: data.error || 'Search failed',
				};
			}
		} catch (error) {
			return {
				success: false,
				pois: [],
				totalCount: 0,
				totalPages: 0,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Search POIs by name with fuzzy matching
	 */
	static async searchByName(
		name: string,
		limit: number = 10
	): Promise<{
		success: boolean;
		pois: POISearchResult[];
		error?: string;
	}> {
		return this.searchPOIs({
			name,
			limit,
		});
	}

	/**
	 * Search POIs by location (city, district, neighborhood)
	 */
	static async searchByLocation(
		city?: string,
		district?: string,
		neighborhood?: string,
		limit: number = 20
	): Promise<{
		success: boolean;
		pois: POISearchResult[];
		error?: string;
	}> {
		return this.searchPOIs({
			city,
			district,
			neighborhood,
			limit,
		});
	}

	/**
	 * Search nearby POIs by coordinates
	 */
	static async searchNearby(
		latitude: number,
		longitude: number,
		radius: number = 1000,
		limit: number = 20
	): Promise<{
		success: boolean;
		pois: POISearchResult[];
		error?: string;
	}> {
		try {
			const searchParams = new URLSearchParams({
				lat: latitude.toString(),
				lng: longitude.toString(),
				radius: radius.toString(),
				limit: limit.toString(),
			});

			const response = await fetch(
				`${this.BASE_URL}/nearby?${searchParams.toString()}`
			);
			const data = await response.json();

			if (data.success) {
				return {
					success: true,
					pois: data.pois || [],
				};
			} else {
				return {
					success: false,
					pois: [],
					error: data.error || 'Nearby search failed',
				};
			}
		} catch (error) {
			return {
				success: false,
				pois: [],
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Get POI by ID and type
	 */
	static async getPOIById(
		poiId: string,
		poiType: 'official' | 'user_temp' | 'user_approved' = 'official'
	): Promise<{
		success: boolean;
		poi?: POISearchResult;
		error?: string;
	}> {
		try {
			const response = await fetch(`${this.BASE_URL}/${poiType}/${poiId}`);
			const data = await response.json();

			if (data.success) {
				return {
					success: true,
					poi: data.poi,
				};
			} else {
				return {
					success: false,
					error: data.error || 'POI not found',
				};
			}
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Validate POI for media upload
	 */
	static async validatePOIForUpload(
		poiId: string,
		poiType?: 'official' | 'user_temp' | 'user_approved'
	): Promise<POIValidationResult> {
		try {
			// If no type specified, try to find the POI in all tables
			if (!poiType) {
				const types: ('official' | 'user_temp' | 'user_approved')[] = [
					'official',
					'user_approved',
					'user_temp',
				];

				for (const type of types) {
					const result = await this.getPOIById(poiId, type);
					if (result.success && result.poi) {
						return {
							isValid: true,
							poi: result.poi,
						};
					}
				}

				// If not found by ID, try searching by name
				const searchResult = await this.searchByName(poiId, 5);
				if (searchResult.success && searchResult.pois.length > 0) {
					return {
						isValid: false,
						error: 'POI ID not found. Did you mean one of these?',
						suggestions: searchResult.pois,
					};
				}

				return {
					isValid: false,
					error: 'POI not found in database',
				};
			}

			// Validate specific POI type and ID
			const result = await this.getPOIById(poiId, poiType);
			if (result.success && result.poi) {
				return {
					isValid: true,
					poi: result.poi,
				};
			} else {
				return {
					isValid: false,
					error: result.error || 'POI validation failed',
				};
			}
		} catch (error) {
			return {
				isValid: false,
				error: error instanceof Error ? error.message : 'Validation error',
			};
		}
	}

	/**
	 * Get filter options for POI search
	 */
	static async getFilterOptions(): Promise<{
		success: boolean;
		cities: string[];
		districts: string[];
		categories: string[];
		subcategories: string[];
		cuisines: string[];
		error?: string;
	}> {
		try {
			const response = await fetch(`${this.BASE_URL}/filter-options`);
			const data = await response.json();

			if (data.success) {
				return {
					success: true,
					cities: data.cities || [],
					districts: data.districts || [],
					categories: data.categories || [],
					subcategories: data.subcategories || [],
					cuisines: data.cuisines || [],
				};
			} else {
				return {
					success: false,
					cities: [],
					districts: [],
					categories: [],
					subcategories: [],
					cuisines: [],
					error: data.error || 'Failed to get filter options',
				};
			}
		} catch (error) {
			return {
				success: false,
				cities: [],
				districts: [],
				categories: [],
				subcategories: [],
				cuisines: [],
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Get city suggestions
	 */
	static async getCitySuggestions(query: string = ''): Promise<{
		success: boolean;
		cities: string[];
		error?: string;
	}> {
		try {
			const searchParams = new URLSearchParams({ field: 'city' });
			if (query) searchParams.set('query', query);

			const response = await fetch(
				`${this.BASE_URL}/suggestions?${searchParams.toString()}`
			);
			const data = await response.json();

			return {
				success: data.success,
				cities: data.cities || [],
				error: data.error,
			};
		} catch (error) {
			return {
				success: false,
				cities: [],
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Get district suggestions for a city
	 */
	static async getDistrictSuggestions(
		city: string,
		query: string = ''
	): Promise<{
		success: boolean;
		districts: string[];
		error?: string;
	}> {
		try {
			const searchParams = new URLSearchParams({ field: 'district', city });
			if (query) searchParams.set('query', query);

			const response = await fetch(
				`${this.BASE_URL}/suggestions?${searchParams.toString()}`
			);
			const data = await response.json();

			return {
				success: data.success,
				districts: data.districts || [],
				error: data.error,
			};
		} catch (error) {
			return {
				success: false,
				districts: [],
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Get neighborhood suggestions for a city and district
	 */
	static async getNeighborhoodSuggestions(
		city: string,
		district: string,
		query: string = ''
	): Promise<{
		success: boolean;
		neighborhoods: string[];
		error?: string;
	}> {
		try {
			const searchParams = new URLSearchParams({
				field: 'neighborhood',
				city,
				district,
			});
			if (query) searchParams.set('query', query);

			const response = await fetch(
				`${this.BASE_URL}/suggestions?${searchParams.toString()}`
			);
			const data = await response.json();

			return {
				success: data.success,
				neighborhoods: data.neighborhoods || [],
				error: data.error,
			};
		} catch (error) {
			return {
				success: false,
				neighborhoods: [],
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}
}
