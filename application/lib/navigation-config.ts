/** @format */

export interface PageConfig {
	showNavigation: boolean;
	layoutType: 'standard' | 'fullscreen' | 'custom';
	requireAuth: boolean;
	allowedRoles?: string[];
	navigationVariant?: 'default' | 'transparent' | 'minimal';
	contentPadding?: boolean;
}

export interface NavigationConfig {
	[path: string]: PageConfig;
}

export const NAVIGATION_CONFIG: NavigationConfig = {
	// Landing page - public, standard layout
	'/': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: false,
		navigationVariant: 'default',
		contentPadding: false, // Landing page handles its own spacing
	},

	// Authentication pages - no navigation
	'/auth': {
		showNavigation: false,
		layoutType: 'custom',
		requireAuth: false,
	},

	// Chat page - custom layout with own navigation
	'/chat': {
		showNavigation: false,
		layoutType: 'fullscreen',
		requireAuth: true,
		contentPadding: false,
	},

	// Globe page - fullscreen, no navigation (has its own controls)
	'/globe': {
		showNavigation: false,
		layoutType: 'fullscreen',
		requireAuth: false,
		contentPadding: false,
	},

	// POIs page - standard layout
	'/pois': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: false,
		contentPadding: true,
	},

	// POI dynamic routes
	'/pois/[poiType]': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: false,
		contentPadding: true,
	},

	'/pois/submit': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		contentPadding: true,
	},

	// Profile page - requires auth
	'/profile': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		contentPadding: true,
	},

	// Settings page - requires auth
	'/settings': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		contentPadding: true,
	},

	// Credits page - requires auth
	'/credits': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		contentPadding: true,
	},

	// Admin dashboard - requires superuser
	'/admin/dashboard': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		allowedRoles: ['superuser'],
		contentPadding: true,
	},

	// Agent dashboard - requires agent or superuser
	'/agent/dashboard': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		allowedRoles: ['agent', 'superuser'],
		contentPadding: true,
	},

	// Welcome page - requires auth
	'/welcome': {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: true,
		contentPadding: true,
	},
};

export function getPageConfig(pathname: string): PageConfig {
	// Try exact match first
	if (NAVIGATION_CONFIG[pathname]) {
		return NAVIGATION_CONFIG[pathname];
	}

	// Try pattern matching for dynamic routes
	for (const [pattern, config] of Object.entries(NAVIGATION_CONFIG)) {
		if (pattern.includes('[') || pattern.includes('*')) {
			// Handle dynamic routes like /pois/[poiType]
			const regex = new RegExp(
				'^' + pattern.replace(/\[.*?\]/g, '[^/]+').replace(/\*/g, '.*') + '$'
			);
			if (regex.test(pathname)) {
				return config;
			}
		}
	}

	// Default configuration for unknown routes
	return {
		showNavigation: true,
		layoutType: 'standard',
		requireAuth: false,
		contentPadding: true,
	};
}

export const Z_INDEX_LAYERS = {
	// Base layers
	background: 'z-0',
	content: 'z-10',

	// UI layers
	navigation: 'z-40',
	sidebar: 'z-50',
	modal: 'z-60',
	dropdown: 'z-70',
	tooltip: 'z-80',

	// System layers
	notification: 'z-90',
	loading: 'z-[100]',
};
