/** @format */

import { config } from './config';
import { logger } from './logger';

// Performance timer utility
export class PerformanceTimer {
	private startTime: number;
	private name: string;
	private metadata: Record<string, unknown>;

	constructor(name: string, metadata: Record<string, unknown> = {}) {
		this.name = name;
		this.metadata = metadata;
		this.startTime = performance.now();
	}

	end(additionalMetadata: Record<string, unknown> = {}): number {
		const duration = performance.now() - this.startTime;

		// Only log if performance logging is enabled and duration exceeds threshold
		if (
			config.logging.enablePerformanceLogs &&
			duration >= config.logging.performanceLogThreshold
		) {
			logger.info(`Performance: ${this.name}`, {
				duration: `${duration.toFixed(2)}ms`,
				...this.metadata,
				...additionalMetadata,
			});
		}

		return duration;
	}
}

// Database query performance monitoring
export function monitorDatabaseQuery<T>(
	queryName: string,
	query: () => Promise<T>,
	metadata: Record<string, unknown> = {}
): Promise<T> {
	const timer = new PerformanceTimer(`DB Query: ${queryName}`, metadata);

	return query()
		.then((result) => {
			timer.end({ success: true });
			return result;
		})
		.catch((error) => {
			timer.end({ success: false, error: error.message });
			throw error;
		});
}

// API request performance monitoring
export function monitorApiRequest<T>(
	endpoint: string,
	request: () => Promise<T>,
	metadata: Record<string, unknown> = {}
): Promise<T> {
	const timer = new PerformanceTimer(`API Request: ${endpoint}`, metadata);

	return request()
		.then((result) => {
			timer.end({ success: true });
			return result;
		})
		.catch((error) => {
			timer.end({ success: false, error: error.message });
			throw error;
		});
}

// LLM Engine request monitoring
export function monitorLLMRequest<T>(
	operation: string,
	request: () => Promise<T>,
	metadata: Record<string, unknown> = {}
): Promise<T> {
	const timer = new PerformanceTimer(`LLM Engine: ${operation}`, metadata);

	return request()
		.then((result) => {
			timer.end({ success: true });
			return result;
		})
		.catch((error) => {
			timer.end({ success: false, error: error.message });
			throw error;
		});
}

// Memory usage monitoring
export function logMemoryUsage(context: string) {
	if (typeof window === 'undefined' && process.memoryUsage) {
		const usage = process.memoryUsage();

		logger.info(`Memory Usage: ${context}`, {
			rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
			heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
			heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
			external: `${Math.round(usage.external / 1024 / 1024)}MB`,
		});
	}
}

// Performance metrics collector
class PerformanceMetrics {
	private metrics: Map<string, number[]> = new Map();

	record(name: string, value: number) {
		if (!this.metrics.has(name)) {
			this.metrics.set(name, []);
		}

		const values = this.metrics.get(name)!;
		values.push(value);

		// Keep only last 100 measurements
		if (values.length > 100) {
			values.shift();
		}
	}

	getStats(name: string) {
		const values = this.metrics.get(name);
		if (!values || values.length === 0) {
			return null;
		}

		const sorted = [...values].sort((a, b) => a - b);
		const count = values.length;
		const sum = values.reduce((a, b) => a + b, 0);

		return {
			count,
			min: sorted[0],
			max: sorted[count - 1],
			avg: sum / count,
			p50: sorted[Math.floor(count * 0.5)],
			p95: sorted[Math.floor(count * 0.95)],
			p99: sorted[Math.floor(count * 0.99)],
		};
	}

	getAllStats() {
		const stats: Record<string, unknown> = {};

		for (const [name] of this.metrics) {
			stats[name] = this.getStats(name);
		}

		return stats;
	}

	logStats() {
		const stats = this.getAllStats();
		logger.info('Performance Statistics', stats);
	}
}

// Global metrics instance
export const performanceMetrics = new PerformanceMetrics();

// Periodic stats logging (disabled to reduce log noise)
// if (typeof window === 'undefined') {
// 	setInterval(() => {
// 		performanceMetrics.logStats();
// 		logMemoryUsage('Periodic Check');
// 	}, 5 * 60 * 1000);
// }

// React component performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
	if (typeof window !== 'undefined') {
		const startTime = performance.now();

		return {
			endRender: () => {
				const duration = performance.now() - startTime;
				performanceMetrics.record(`React Render: ${componentName}`, duration);

				if (duration > 100) {
					logger.warn(`Slow React Render: ${componentName}`, {
						duration: `${duration.toFixed(2)}ms`,
					});
				}
			},
		};
	}

	return { endRender: () => {} };
}

// Middleware for API route performance monitoring
export function withPerformanceMonitoring<T extends unknown[], R>(
	handler: (...args: T) => Promise<R>,
	name: string
) {
	return async (...args: T): Promise<R> => {
		const timer = new PerformanceTimer(`API Handler: ${name}`);

		try {
			const result = await handler(...args);
			const duration = timer.end({ success: true });
			performanceMetrics.record(`API: ${name}`, duration);
			return result;
		} catch (error) {
			const duration = timer.end({ success: false });
			performanceMetrics.record(`API: ${name}`, duration);
			throw error;
		}
	};
}
