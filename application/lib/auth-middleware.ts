/** @format */

// Simple authorization middleware for API endpoints
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export interface AuthContext {
	userId: string;
	isAuthenticated: boolean;
}

// Type for request with user info
export type AuthenticatedRequest = NextRequest & {
	user: Record<string, unknown>;
};

// Simple middleware to extract and validate user ID from request
export function withAuth(
	handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
	return async (request: NextRequest) => {
		try {
			const session = await getServerSession(authOptions);

			if (!session?.user?.id) {
				return NextResponse.json(
					{ error: 'Authentication required' },
					{ status: 401 }
				);
			}

			// Add user info to request for the handler to use
			const requestWithUser = Object.assign(request, {
				user: session.user,
			}) as AuthenticatedRequest;

			return handler(requestWithUser);
		} catch (error) {
			console.error('Auth middleware error:', error);
			return NextResponse.json(
				{ error: 'Authentication failed' },
				{ status: 401 }
			);
		}
	};
}

// Middleware to validate user ownership of resources
export function withUserOwnership(
	resourceType: string,
	getResourceUserId: (request: NextRequest) => Promise<string | null>
) {
	return (
		handler: (request: AuthenticatedRequest) => Promise<NextResponse>
	) => {
		return withAuth(async (request: AuthenticatedRequest) => {
			try {
				const resourceUserId = await getResourceUserId(request);

				if (!resourceUserId) {
					return NextResponse.json(
						{ error: `${resourceType} not found` },
						{ status: 404 }
					);
				}

				if (resourceUserId !== request.user.id) {
					return NextResponse.json({ error: 'Access denied' }, { status: 403 });
				}

				return await handler(request);
			} catch (error) {
				console.error('User ownership validation error:', error);
				return NextResponse.json(
					{ error: 'Authorization failed' },
					{ status: 403 }
				);
			}
		});
	};
}
