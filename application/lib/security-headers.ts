/** @format */

// Enhanced security headers and input validation middleware
import { NextRequest, NextResponse } from 'next/server';

export function withSecurityHeaders(
	handler: (request: NextRequest) => Promise<NextResponse>
) {
	return async (request: NextRequest): Promise<NextResponse> => {
		const response = await handler(request);

		// Add security headers
		response.headers.set('X-Content-Type-Options', 'nosniff');
		response.headers.set('X-Frame-Options', 'DENY');
		response.headers.set('X-XSS-Protection', '1; mode=block');
		response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

		// Add Content Security Policy (enhanced)
		response.headers.set(
			'Content-Security-Policy',
			"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; object-src 'none'; base-uri 'self';"
		);

		// Add CORS headers for API routes
		const origin = request.headers.get('origin');
		if (origin) {
			response.headers.set('Access-Control-Allow-Origin', origin);
			response.headers.set(
				'Access-Control-Allow-Methods',
				'GET, POST, PUT, DELETE, OPTIONS'
			);
			response.headers.set(
				'Access-Control-Allow-Headers',
				'Content-Type, Authorization'
			);
			response.headers.set('Access-Control-Allow-Credentials', 'true');
		}

		return response;
	};
}

// Enhanced input sanitization function
export function sanitizeInput(input: string): string {
	if (typeof input !== 'string') {
		return '';
	}

	// Remove potentially dangerous characters and patterns
	return input
		.replace(/[<>]/g, '') // Remove < and > to prevent basic XSS
		.replace(/javascript:/gi, '') // Remove javascript: protocol
		.replace(/on\w+=/gi, '') // Remove event handlers
		.replace(/data:text\/html/gi, '') // Remove data URLs
		.replace(/vbscript:/gi, '') // Remove VBScript
		.replace(/expression\(/gi, '') // Remove CSS expressions
		.replace(/url\(/gi, '') // Remove CSS url() functions
		.replace(/import\(/gi, '') // Remove CSS import() functions
		.trim();
}

// Enhanced validation for common input types
export function validateEmail(email: string): boolean {
	if (!email || typeof email !== 'string') return false;

	const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
	return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
}

export function validateUsername(username: string): boolean {
	if (!username || typeof username !== 'string') return false;

	// Username should be 3-20 characters, alphanumeric and underscores only
	const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
	return usernameRegex.test(username);
}

export function validatePassword(password: string): {
	isValid: boolean;
	error?: string;
} {
	if (!password || typeof password !== 'string') {
		return { isValid: false, error: 'Password is required' };
	}

	if (password.length < 8) {
		return {
			isValid: false,
			error: 'Password must be at least 8 characters long',
		};
	}

	if (password.length > 128) {
		return {
			isValid: false,
			error: 'Password must be less than 128 characters',
		};
	}

	// Check for at least one uppercase letter, one lowercase letter, and one number
	const hasUpperCase = /[A-Z]/.test(password);
	const hasLowerCase = /[a-z]/.test(password);
	const hasNumbers = /\d/.test(password);

	if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
		return {
			isValid: false,
			error:
				'Password must contain at least one uppercase letter, one lowercase letter, and one number',
		};
	}

	return { isValid: true };
}

export function validateName(name: string): boolean {
	if (!name || typeof name !== 'string') return false;

	// Name should be 2-50 characters, letters, spaces, hyphens, and apostrophes only
	const nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
	return nameRegex.test(name.trim());
}

export function validateAge(age: number): boolean {
	return typeof age === 'number' && age >= 13 && age <= 120;
}

export function validateMessage(message: string): {
	isValid: boolean;
	error?: string;
} {
	if (!message || typeof message !== 'string') {
		return { isValid: false, error: 'Message is required' };
	}

	if (message.length > 2000) {
		return {
			isValid: false,
			error: 'Message must be less than 2000 characters',
		};
	}

	// Check for potentially dangerous content
	const dangerousPatterns = [
		/<script/i,
		/javascript:/i,
		/on\w+=/i,
		/data:text\/html/i,
		/vbscript:/i,
	];

	for (const pattern of dangerousPatterns) {
		if (pattern.test(message)) {
			return {
				isValid: false,
				error: 'Message contains potentially dangerous content',
			};
		}
	}

	return { isValid: true };
}

// File upload validation (basic)
export function validateFileUpload(file: File): {
	isValid: boolean;
	error?: string;
} {
	if (!file) {
		return { isValid: false, error: 'File is required' };
	}

	// Check file size (max 5MB)
	const maxSize = 5 * 1024 * 1024; // 5MB
	if (file.size > maxSize) {
		return { isValid: false, error: 'File size must be less than 5MB' };
	}

	// Check file type (basic)
	const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
	if (!allowedTypes.includes(file.type)) {
		return {
			isValid: false,
			error: 'Only JPEG, PNG, GIF, and WebP images are allowed',
		};
	}

	return { isValid: true };
}

// Comprehensive input sanitization for different data types
export function sanitizeObject<T = unknown>(obj: T): T {
	if (typeof obj !== 'object' || obj === null) {
		return obj;
	}

	if (Array.isArray(obj)) {
		return obj.map((item) => sanitizeObject(item)) as unknown as T;
	}

	const sanitized: Record<string, unknown> = {};
	for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
		if (typeof value === 'string') {
			sanitized[key] = sanitizeInput(value);
		} else if (typeof value === 'object') {
			sanitized[key] = sanitizeObject(value);
		} else {
			sanitized[key] = value;
		}
	}

	return sanitized as T;
}
