/** @format */

import { db, table } from '@/lib/database';
import { logger } from '@/lib/logger';

export interface CreditTransaction {
	id: string;
	user_id: string;
	transaction_type: 'earn' | 'purchase' | 'use' | 'refund';
	amount: number;
	reason: string;
	description?: string;
	metadata?: Record<string, unknown>;
	related_entity_type?: string;
	related_entity_id?: string;
	created_at: string;
}

export interface UserCredits {
	id: string;
	user_id: string;
	credits_earned: number;
	credits_purchased: number;
	credits_used: number;
	subscription_type: 'none' | 'basic' | 'premium';
	subscription_expires_at?: string;
	created_at: string;
	updated_at: string;
}

// NOTE: DailyLimits interface removed - no daily limits needed

/**
 * Check if user has sufficient credits for an action
 */
export async function hasCredits(
	userId: string,
	requiredCredits: number = 1
): Promise<boolean> {
	try {
		// Convert required credits to integer for comparison
		const requiredIntegerCredits = creditsToInteger(requiredCredits);

		// Check if user is agent or superuser (they bypass credit requirements)
		const user = await db.getOne(
			`SELECT * FROM ${table('nextauth_users')} WHERE id = $1`,
			[userId]
		);
		if (user && ['agent', 'superuser'].includes(user.role)) {
			return true;
		}

		// Get user credits
		const credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		if (!credits) {
			return false;
		}

		const availableIntegerCredits =
			credits.credits_earned + credits.credits_purchased - credits.credits_used;
		return availableIntegerCredits >= requiredIntegerCredits;
	} catch (error) {
		logger.error('Error checking user credits', { error, userId });
		return false;
	}
}

/**
 * Deduct credits from user account
 */
export async function deductCredits(
	userId: string,
	amount: number,
	reason: string,
	description?: string,
	relatedEntityType?: string,
	relatedEntityId?: string
): Promise<boolean> {
	try {
		// Convert decimal amount to integer for database storage
		const integerAmount = creditsToInteger(amount);

		// Check if user is agent or superuser (they bypass credit deduction)
		const user = await db.getOne(
			`SELECT * FROM ${table('nextauth_users')} WHERE id = $1`,
			[userId]
		);
		if (user && ['agent', 'superuser'].includes(user.role)) {
			return true;
		}

		// Check if user has enough credits
		if (!(await hasCredits(userId, amount))) {
			return false;
		}

		// Start transaction
		await db.query('BEGIN');

		try {
			// Update credits_used
			const credits = await db.getOne(
				`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
				[userId]
			);

			if (!credits) {
				await db.query('ROLLBACK');
				return false;
			}

			await db.update(table('user_credits'), credits.id, {
				credits_used: credits.credits_used + integerAmount,
			});

			// Record transaction
			await db.insert(table('credit_transactions'), {
				user_id: userId,
				transaction_type: 'use',
				amount: integerAmount,
				reason,
				description,
				related_entity_type: relatedEntityType,
				related_entity_id: relatedEntityId,
			});

			await db.query('COMMIT');
			return true;
		} catch (error) {
			await db.query('ROLLBACK');
			throw error;
		}
	} catch (error) {
		logger.error('Error deducting credits', { error, userId, amount, reason });
		return false;
	}
}

/**
 * Award credits to user account (with transaction management)
 */
export async function awardCredits(
	userId: string,
	amount: number,
	reason: string,
	description?: string,
	relatedEntityType?: string,
	relatedEntityId?: string
): Promise<boolean> {
	try {
		logger.info('Attempting to award credits', {
			userId,
			amount,
			reason,
			description,
			relatedEntityType,
			relatedEntityId,
		});

		// Start transaction
		await db.query('BEGIN');

		try {
			const result = await awardCreditsWithoutTransaction(
				userId,
				amount,
				reason,
				description,
				relatedEntityType,
				relatedEntityId
			);

			await db.query('COMMIT');

			logger.info('Credits awarded successfully', {
				userId,
				amount,
				reason,
			});

			return result;
		} catch (error) {
			await db.query('ROLLBACK');
			logger.error('Database error in credit transaction', {
				error: error instanceof Error ? error.message : 'Unknown error',
				stack: error instanceof Error ? error.stack : undefined,
				userId,
				amount,
				reason,
			});
			throw error;
		}
	} catch (error) {
		logger.error('Error awarding credits', {
			error: error instanceof Error ? error.message : 'Unknown error',
			stack: error instanceof Error ? error.stack : undefined,
			userId,
			amount,
			reason,
		});
		return false;
	}
}

/**
 * Check if credits have already been awarded for a specific entity
 */
export async function hasCreditsBeenAwarded(
	userId: string,
	relatedEntityType: string,
	relatedEntityId: string,
	reason: string
): Promise<boolean> {
	try {
		const existingTransaction = await db.getOne(
			`SELECT id FROM ${table('credit_transactions')}
			 WHERE user_id = $1
			 AND related_entity_type = $2
			 AND related_entity_id = $3
			 AND reason = $4
			 AND transaction_type = 'earn'`,
			[userId, relatedEntityType, relatedEntityId, reason]
		);

		return !!existingTransaction;
	} catch (error) {
		logger.error('Error checking if credits have been awarded', {
			error,
			userId,
			relatedEntityType,
			relatedEntityId,
			reason,
		});
		return false;
	}
}

/**
 * Convert decimal credits to integer (database stores credits as integers, e.g., 5 = 0.5 credits)
 */
export function creditsToInteger(amount: number): number {
	return Math.round(amount * 10);
}

/**
 * Convert integer credits to decimal for display
 */
export function creditsToDecimal(amount: number): number {
	return amount / 10;
}

/**
 * Award credits to user account (without transaction management - for use within existing transactions)
 */
export async function awardCreditsWithoutTransaction(
	userId: string,
	amount: number,
	reason: string,
	description?: string,
	relatedEntityType?: string,
	relatedEntityId?: string,
	skipDuplicateCheck: boolean = false
): Promise<boolean> {
	try {
		// Convert decimal amount to integer for database storage
		const integerAmount = creditsToInteger(amount);

		logger.info('Awarding credits within existing transaction', {
			userId,
			amount,
			integerAmount,
			reason,
			description,
			relatedEntityType,
			relatedEntityId,
			skipDuplicateCheck,
		});

		// Check for duplicate credits unless explicitly skipped
		if (!skipDuplicateCheck && relatedEntityType && relatedEntityId) {
			const alreadyAwarded = await hasCreditsBeenAwarded(
				userId,
				relatedEntityType,
				relatedEntityId,
				reason
			);

			if (alreadyAwarded) {
				logger.info('Credits already awarded for this entity, skipping', {
					userId,
					relatedEntityType,
					relatedEntityId,
					reason,
				});
				return true; // Return true since credits were already awarded
			}
		}

		// Get or create user credits record
		let credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		if (!credits) {
			logger.info('Creating new credits record for user', { userId });
			credits = await db.insert(table('user_credits'), {
				user_id: userId,
				credits_earned: integerAmount,
				credits_purchased: 0,
				credits_used: 0,
				subscription_type: 'none',
			});
			logger.info('Created new credits record', {
				userId,
				creditsId: credits.id,
				amount,
				integerAmount,
			});
		} else {
			logger.info('Updating existing credits record', {
				userId,
				currentEarned: credits.credits_earned,
				addingAmount: integerAmount,
				newTotal: credits.credits_earned + integerAmount,
			});
			await db.update(table('user_credits'), credits.id, {
				credits_earned: credits.credits_earned + integerAmount,
			});
		}

		// Record transaction
		const transaction = await db.insert(table('credit_transactions'), {
			user_id: userId,
			transaction_type: 'earn',
			amount: integerAmount,
			reason,
			description,
			related_entity_type: relatedEntityType,
			related_entity_id: relatedEntityId,
		});

		logger.info('Credit transaction recorded within existing transaction', {
			userId,
			transactionId: transaction.id,
			amount,
			integerAmount,
			reason,
		});

		return true;
	} catch (error) {
		logger.error('Error awarding credits within transaction', {
			error: error instanceof Error ? error.message : 'Unknown error',
			stack: error instanceof Error ? error.stack : undefined,
			userId,
			amount,
			reason,
		});
		throw error; // Let the outer transaction handle rollback
	}
}

/**
 * Award credits for historical POI approvals that haven't been credited yet
 */
export async function awardCreditsForHistoricalApprovals(): Promise<{
	success: boolean;
	processed: number;
	credited: number;
	errors: number;
	details: Array<{
		userId: string;
		tempPoiId: string;
		status: string;
		error?: string;
	}>;
}> {
	const result = {
		success: true,
		processed: 0,
		credited: 0,
		errors: 0,
		details: [] as Array<{
			userId: string;
			tempPoiId: string;
			status: string;
			error?: string;
		}>,
	};

	try {
		logger.info('Starting historical POI approval credit awarding process');

		// Get all approved POIs that haven't been credited yet
		const uncreditedApprovals = await db.getMany(
			`
			SELECT DISTINCT
				t.id as temp_poi_id,
				t.submitted_by_user_id,
				t.name,
				t.created_at,
				t.reviewed_at
			FROM spatial_schema.user_pois_temp t
			WHERE t.admin_review_status = 'approved'
			AND t.submitted_by_user_id IS NOT NULL
			AND NOT EXISTS (
				SELECT 1 FROM ${table('credit_transactions')} ct
				WHERE ct.user_id = t.submitted_by_user_id
				AND ct.related_entity_type = 'poi_submission'
				AND ct.related_entity_id = t.id::text
				AND ct.reason = 'poi_submission_approved'
				AND ct.transaction_type = 'earn'
			)
			ORDER BY t.reviewed_at ASC
			`
		);

		logger.info(`Found ${uncreditedApprovals.length} uncredited POI approvals`);

		for (const approval of uncreditedApprovals) {
			result.processed++;

			try {
				await db.query('BEGIN');

				// Import credit configuration for historical awarding
				const { creditConfig, creditReasons } = await import(
					'@/lib/credits/creditConfig'
				);

				const creditAwarded = await awardCreditsWithoutTransaction(
					approval.submitted_by_user_id,
					creditConfig.poi.add,
					creditReasons.poi.add,
					`Historical POI approval credit: ${approval.name || 'Unnamed POI'}`,
					'poi_submission',
					approval.temp_poi_id.toString(),
					false // Don't skip duplicate check
				);

				if (creditAwarded) {
					await db.query('COMMIT');
					result.credited++;
					result.details.push({
						userId: approval.submitted_by_user_id,
						tempPoiId: approval.temp_poi_id.toString(),
						status: 'credited',
					});

					logger.info('Historical credit awarded', {
						userId: approval.submitted_by_user_id,
						tempPoiId: approval.temp_poi_id,
						poiName: approval.name,
					});
				} else {
					await db.query('ROLLBACK');
					result.errors++;
					result.details.push({
						userId: approval.submitted_by_user_id,
						tempPoiId: approval.temp_poi_id.toString(),
						status: 'failed',
						error: 'Credit awarding returned false',
					});
				}
			} catch (error) {
				await db.query('ROLLBACK');
				result.errors++;
				result.details.push({
					userId: approval.submitted_by_user_id,
					tempPoiId: approval.temp_poi_id.toString(),
					status: 'error',
					error: error instanceof Error ? error.message : 'Unknown error',
				});

				logger.error('Error awarding historical credit', {
					error,
					userId: approval.submitted_by_user_id,
					tempPoiId: approval.temp_poi_id,
				});
			}
		}

		logger.info('Historical POI approval credit awarding completed', {
			processed: result.processed,
			credited: result.credited,
			errors: result.errors,
		});

		return result;
	} catch (error) {
		logger.error('Error in historical credit awarding process', { error });
		result.success = false;
		return result;
	}
}

/**
 * Get statistics about POI approvals and credits
 */
export async function getPOIApprovalCreditStats(): Promise<{
	totalApprovedPOIs: number;
	totalCreditedPOIs: number;
	uncreditedPOIs: number;
	totalCreditsAwarded: number;
	userStats: Array<{
		userId: string;
		username?: string;
		approvedPOIs: number;
		creditedPOIs: number;
		totalCreditsEarned: number;
	}>;
}> {
	try {
		// Get total approved POIs
		const totalApprovedResult = await db.getOne(
			`SELECT COUNT(*) as count FROM spatial_schema.user_pois_temp
			 WHERE admin_review_status = 'approved' AND submitted_by_user_id IS NOT NULL`
		);

		// Get total credited POIs
		const totalCreditedResult = await db.getOne(
			`SELECT COUNT(DISTINCT related_entity_id) as count
			 FROM ${table('credit_transactions')}
			 WHERE related_entity_type = 'poi_submission'
			 AND reason = 'poi_submission_approved'
			 AND transaction_type = 'earn'`
		);

		// Get total credits awarded for POI approvals
		const totalCreditsResult = await db.getOne(
			`SELECT COALESCE(SUM(amount), 0) as total
			 FROM ${table('credit_transactions')}
			 WHERE related_entity_type = 'poi_submission'
			 AND reason = 'poi_submission_approved'
			 AND transaction_type = 'earn'`
		);

		// Get user statistics
		const userStats = await db.getMany(
			`
			SELECT
				u.id as user_id,
				u.username,
				COUNT(DISTINCT t.id) as approved_pois,
				COUNT(DISTINCT ct.related_entity_id) as credited_pois,
				COALESCE(SUM(ct.amount), 0) as total_credits_earned
			FROM ${table('nextauth_users')} u
			LEFT JOIN spatial_schema.user_pois_temp t ON u.id = t.submitted_by_user_id
				AND t.admin_review_status = 'approved'
			LEFT JOIN ${table('credit_transactions')} ct ON u.id = ct.user_id
				AND ct.related_entity_type = 'poi_submission'
				AND ct.reason = 'poi_submission_approved'
				AND ct.transaction_type = 'earn'
			WHERE u.role = 'user'
			GROUP BY u.id, u.username
			HAVING COUNT(DISTINCT t.id) > 0 OR COUNT(DISTINCT ct.related_entity_id) > 0
			ORDER BY approved_pois DESC, total_credits_earned DESC
			`
		);

		const totalApproved = totalApprovedResult?.count || 0;
		const totalCredited = totalCreditedResult?.count || 0;

		return {
			totalApprovedPOIs: totalApproved,
			totalCreditedPOIs: totalCredited,
			uncreditedPOIs: totalApproved - totalCredited,
			totalCreditsAwarded: creditsToDecimal(totalCreditsResult?.total || 0),
			userStats: userStats.map((stat) => ({
				userId: stat.user_id,
				username: stat.username,
				approvedPOIs: parseInt(stat.approved_pois) || 0,
				creditedPOIs: parseInt(stat.credited_pois) || 0,
				totalCreditsEarned: creditsToDecimal(
					parseInt(stat.total_credits_earned) || 0
				),
			})),
		};
	} catch (error) {
		logger.error('Error getting POI approval credit stats', { error });
		return {
			totalApprovedPOIs: 0,
			totalCreditedPOIs: 0,
			uncreditedPOIs: 0,
			totalCreditsAwarded: 0,
			userStats: [],
		};
	}
}

// NOTE: Daily limit functions removed - no limits for any actions

// NOTE: incrementDailyLimit function removed - no limits for any actions

/**
 * Get user's available credits (returns decimal value for display)
 */
export async function getAvailableCredits(userId: string): Promise<number> {
	try {
		const credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		if (!credits) {
			return 0;
		}

		const integerCredits =
			credits.credits_earned + credits.credits_purchased - credits.credits_used;

		return creditsToDecimal(integerCredits);
	} catch (error) {
		logger.error('Error getting available credits', { error, userId });
		return 0;
	}
}

// NOTE: getDailyLimitStatus function removed - no limits for any actions
