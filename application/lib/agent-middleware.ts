/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export interface AgentUser {
	id: string;
	email: string;
	name: string;
	role: 'user' | 'agent' | 'superuser';
	permissions: string[];
}

export interface AuthenticatedAgentRequest extends NextRequest {
	agent: AgentUser;
}

// Role-aware permission check: only 'full_access' and 'user_management' are valid
export async function hasPermission(
	userId: string,
	permission: 'full_access' | 'user_management'
): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.user_has_permission($1, $2) as has_permission',
			[userId, permission]
		);
		return result.rows[0]?.has_permission || false;
	} catch (error) {
		logger.error('Error checking user permission', {
			error,
			userId,
			permission,
		});
		return false;
	}
}

// Get user with agent information
export async function getAgentUser(userId: string): Promise<AgentUser | null> {
	try {
		const result = await db.query(
			`
      SELECT
        id,
        email,
        name,
        role,
        permissions
      FROM backend_schema.nextauth_users
      WHERE id = $1
    `,
			[userId]
		);

		if (result.rows.length === 0) {
			return null;
		}

		const user = result.rows[0];
		return {
			...user,
			permissions: user.permissions || [],
		};
	} catch (error) {
		logger.error('Error fetching agent user', { error, userId });
		return null;
	}
}

// Log agent activity
export async function logAgentActivity(
	agentId: string,
	action: string,
	targetType?: string,
	targetId?: string,
	details?: Record<string, unknown>,
	request?: NextRequest
) {
	try {
		const ipAddress =
			request?.headers.get('x-forwarded-for') ||
			request?.headers.get('x-real-ip') ||
			null;
		const userAgent = request?.headers.get('user-agent') || null;

		await db.query(
			'SELECT backend_schema.log_agent_activity($1, $2, $3, $4, $5, $6, $7)',
			[
				agentId,
				action,
				targetType || null,
				targetId || null,
				JSON.stringify(details || {}),
				ipAddress,
				userAgent,
			]
		);
	} catch (error) {
		logger.error('Error logging agent activity', { error, agentId, action });
	}
}

// Middleware to require agent or superuser authentication with role-aware permission check
export function withAgentAuth(
	requiredPermissions: ('full_access' | 'user_management')[] = []
) {
	return (
		handler: (request: AuthenticatedAgentRequest) => Promise<NextResponse>
	) => {
		return async (request: NextRequest) => {
			try {
				// Get user session
				const session = await getServerSession(authOptions);
				if (!session?.user?.id) {
					return NextResponse.json(
						{ success: false, error: 'Authentication required' },
						{ status: 401 }
					);
				}

				// Get agent user information
				const agentUser = await getAgentUser(session.user.id);
				if (!agentUser) {
					return NextResponse.json(
						{ success: false, error: 'User not found' },
						{ status: 404 }
					);
				}

				// Check if user is agent or superuser
				if (agentUser.role !== 'agent' && agentUser.role !== 'superuser') {
					await logAgentActivity(
						session.user.id,
						'unauthorized_access_attempt',
						'agent_endpoint',
						request.url,
						{ requiredPermissions, userRole: agentUser.role },
						request
					);

					return NextResponse.json(
						{ success: false, error: 'Agent access required' },
						{ status: 403 }
					);
				}

				// Role-aware permission check
				for (const permission of requiredPermissions) {
					const hasPerm = await hasPermission(agentUser.id, permission);
					if (!hasPerm) {
						await logAgentActivity(
							session.user.id,
							'insufficient_permissions',
							'agent_endpoint',
							request.url,
							{
								requiredPermissions,
								missingPermission: permission,
								userPermissions: agentUser.permissions,
							},
							request
						);
						return NextResponse.json(
							{ success: false, error: `Missing permission: ${permission}` },
							{ status: 403 }
						);
					}
				}

				// Add agent user to request
				const authenticatedRequest = request as AuthenticatedAgentRequest;
				authenticatedRequest.agent = agentUser;

				return handler(authenticatedRequest);
			} catch (error) {
				logger.error('Agent auth middleware error', { error });
				return NextResponse.json(
					{ success: false, error: 'Internal server error' },
					{ status: 500 }
				);
			}
		};
	};
}

// Middleware to require a specific permission (role-aware)
export function withPermission(permission: 'full_access' | 'user_management') {
	return withAgentAuth([permission]);
}

// Middleware to require superuser authentication
export function withSuperuserAuth() {
	return (
		handler: (request: AuthenticatedAgentRequest) => Promise<NextResponse>
	) => {
		return async (request: NextRequest) => {
			try {
				const session = await getServerSession(authOptions);
				if (!session?.user?.id) {
					return NextResponse.json(
						{ success: false, error: 'Authentication required' },
						{ status: 401 }
					);
				}
				const agentUser = await getAgentUser(session.user.id);
				if (!agentUser || agentUser.role !== 'superuser') {
					await logAgentActivity(
						session.user.id,
						'unauthorized_access_attempt',
						'superuser_endpoint',
						request.url,
						{ userRole: agentUser?.role },
						request
					);
					return NextResponse.json(
						{ success: false, error: 'Superuser access required' },
						{ status: 403 }
					);
				}
				const authenticatedRequest = request as AuthenticatedAgentRequest;
				authenticatedRequest.agent = agentUser;
				return handler(authenticatedRequest);
			} catch (error) {
				logger.error('Superuser auth middleware error', { error });
				return NextResponse.json(
					{ success: false, error: 'Internal server error' },
					{ status: 500 }
				);
			}
		};
	};
}

// Promote user to agent (only 'full_access' allowed)
export async function promoteUserToAgent(
	userId: string,
	promotedBy: string
): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.promote_user_to_agent($1, $2, $3)',
			[userId, promotedBy, JSON.stringify(['full_access'])]
		);
		return result.rows[0]?.promote_user_to_agent || false;
	} catch (error) {
		logger.error('Error promoting user to agent', {
			error,
			userId,
			promotedBy,
		});
		return false;
	}
}

// Promote user to superuser (only 'full_access' or 'user_management' allowed)
export async function promoteUserToSuperuser(
	userId: string,
	promotedBy: string,
	permissions: ('full_access' | 'user_management')[] = ['full_access']
): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.promote_user_to_superuser($1, $2, $3)',
			[userId, promotedBy, JSON.stringify(permissions)]
		);
		return result.rows[0]?.promote_user_to_superuser || false;
	} catch (error) {
		logger.error('Error promoting user to superuser', {
			error,
			userId,
			promotedBy,
			permissions,
		});
		return false;
	}
}

// Check if user is superuser
export async function isSuperuser(userId: string): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.is_superuser($1) as is_superuser',
			[userId]
		);
		return result.rows[0]?.is_superuser || false;
	} catch (error) {
		logger.error('Error checking superuser status', { error, userId });
		return false;
	}
}

// Check if user can access admin panel
export async function canAccessAdminPanel(userId: string): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.can_access_admin_panel($1) as can_access',
			[userId]
		);
		return result.rows[0]?.can_access || false;
	} catch (error) {
		logger.error('Error checking admin panel access', { error, userId });
		return false;
	}
}

// Check if user can access agent dashboard
export async function canAccessAgentDashboard(
	userId: string
): Promise<boolean> {
	try {
		const result = await db.query(
			'SELECT backend_schema.can_access_agent_dashboard($1) as can_access',
			[userId]
		);
		return result.rows[0]?.can_access || false;
	} catch (error) {
		logger.error('Error checking agent dashboard access', { error, userId });
		return false;
	}
}

// Middleware to require admin authentication (alias for superuser auth)
export function withAdminAuth() {
	return withSuperuserAuth();
}

// Validate API security for a given role and permissions (role-aware)
export async function validateApiSecurity(
	request: NextRequest,
	requiredRole: 'user' | 'agent' | 'superuser',
	requiredPermissions: ('full_access' | 'user_management')[] = []
): Promise<{ isValid: boolean; user?: AgentUser; error?: string }> {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return { isValid: false, error: 'Authentication required' };
		}
		const agentUser = await getAgentUser(session.user.id);
		if (!agentUser) {
			return { isValid: false, error: 'User not found' };
		}
		if (requiredRole === 'superuser' && agentUser.role !== 'superuser') {
			return { isValid: false, error: 'Superuser access required' };
		}
		if (
			requiredRole === 'agent' &&
			agentUser.role !== 'agent' &&
			agentUser.role !== 'superuser'
		) {
			return { isValid: false, error: 'Agent access required' };
		}
		for (const permission of requiredPermissions) {
			const hasPerm = await hasPermission(agentUser.id, permission);
			if (!hasPerm) {
				return { isValid: false, error: `Missing permission: ${permission}` };
			}
		}
		return { isValid: true, user: agentUser };
	} catch (error) {
		logger.error('Error validating API security', { error });
		return { isValid: false, error: 'Internal server error' };
	}
}
