/** @format */

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { LayoutWrapper } from './shared/system';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
	title: 'Wizlop - Global Exploration Through AI',
	description:
		'AI-powered chat application for exploring the world with real-time geolocation',
	icons: {
		icon: [
			{ url: '/logo/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
			{ url: '/logo/32x32.png', sizes: '32x32', type: 'image/png' },
		],
		apple: [{ url: '/logo/192x192.png', sizes: '192x192', type: 'image/png' }],
		other: [
			{ url: '/logo/192x192.png', sizes: '192x192', type: 'image/png' },
			{ url: '/logo/512x512.png', sizes: '512x512', type: 'image/png' },
		],
	},
	manifest: '/manifest.json',
};

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html
			lang='en'
			className='h-full'
			suppressHydrationWarning>
			<body className={`${inter.className} h-full`}>
				<Providers>
					<LayoutWrapper>{children}</LayoutWrapper>
				</Providers>
			</body>
		</html>
	);
}
