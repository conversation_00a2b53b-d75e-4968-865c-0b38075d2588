'use client'

import { useAuthGuard, AUTH_CONFIGS, LoadingSpinner } from '@/app/shared/system'
import ChatPageContent from './ChatPage'

export default function ChatPageModule() {
  const { isAuthenticated, isLoading } = useAuthGuard(AUTH_CONFIGS.CHAT_PAGE)

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <LoadingSpinner text="Loading chat..." />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Auth guard will handle redirect
  }

  return <ChatPageContent />
}
