/** @format */

import React from 'react';

interface DynamicLeafletMapProps {
	mapCenter: [number, number];
	zoom: number;
	userLocation?: {
		latitude: number;
		longitude: number;
		accuracy?: number;
	} | null;
	extractedLocations: Array<{
		name: string;
		latitude: number;
		longitude: number;
		walk_route_distance_m?: number;
		address?: string;
	}>;
	selectedLocationIndex?: number | null;
}

const DynamicLeafletMap: React.FC<DynamicLeafletMapProps> = ({
	mapCenter,
	zoom,
	userLocation,
	extractedLocations,
	selectedLocationIndex = null,
}) => {
	// Debug: Log when rendering locations on map
	if (extractedLocations?.length > 0) {
		console.log(
			'🗺️ Map rendering',
			extractedLocations.length,
			'location markers'
		);
	}

	// Import unified components using dynamic imports to avoid require()
	const UnifiedMapContainer = React.useMemo(() => {
		// eslint-disable-next-line @typescript-eslint/no-require-imports
		return require('@/app/shared/maps/components/UnifiedMapContainer').default;
	}, []);

	const {
		UnifiedUserLocationMarker,
		UnifiedExtractedLocationMarker,
		UnifiedMapResizeHandler,
		UnifiedFitBounds,
	} = React.useMemo(() => {
		// eslint-disable-next-line @typescript-eslint/no-require-imports
		const markers = require('@/app/shared/maps/components/UnifiedMarkers');
		return {
			UnifiedUserLocationMarker: markers.UnifiedUserLocationMarker,
			UnifiedExtractedLocationMarker: markers.UnifiedExtractedLocationMarker,
			UnifiedMapResizeHandler: markers.UnifiedMapResizeHandler,
			UnifiedFitBounds: markers.UnifiedFitBounds,
		};
	}, []);

	// Prepare markers for bounds fitting
	const allMarkers: Array<[number, number]> = [];

	// Add user location if available
	if (
		userLocation &&
		typeof userLocation.latitude === 'number' &&
		typeof userLocation.longitude === 'number' &&
		!isNaN(userLocation.latitude) &&
		!isNaN(userLocation.longitude)
	) {
		allMarkers.push([userLocation.latitude, userLocation.longitude]);
	}

	// Add extracted locations
	extractedLocations.forEach((location) => {
		const lat = location.latitude;
		const lng = location.longitude;
		if (
			typeof lat === 'number' &&
			typeof lng === 'number' &&
			!isNaN(lat) &&
			!isNaN(lng)
		) {
			allMarkers.push([lat, lng]);
		}
	});

	return (
		<UnifiedMapContainer
			mapId={`chat-map-${mapCenter[0]}-${mapCenter[1]}`}
			center={mapCenter}
			zoom={zoom}
			style={{ height: '100%', width: '100%' }}>
			<UnifiedMapResizeHandler />

			{/* Fit bounds to all markers */}
			{allMarkers.length > 0 && (
				<UnifiedFitBounds
					markers={allMarkers}
					padding={0.1}
					maxZoom={16}
				/>
			)}

			{/* User location marker */}
			{userLocation &&
				typeof userLocation.latitude === 'number' &&
				typeof userLocation.longitude === 'number' &&
				!isNaN(userLocation.latitude) &&
				!isNaN(userLocation.longitude) && (
					<UnifiedUserLocationMarker
						latitude={userLocation.latitude}
						longitude={userLocation.longitude}
						accuracy={userLocation.accuracy}
						showPopup={true}
					/>
				)}

			{/* Extracted location markers */}
			{extractedLocations.map((location, index) => (
				<UnifiedExtractedLocationMarker
					key={`extracted-${index}`}
					location={location}
					isSelected={selectedLocationIndex === index}
					showPopup={true}
				/>
			))}
		</UnifiedMapContainer>
	);
};

export default DynamicLeafletMap;
