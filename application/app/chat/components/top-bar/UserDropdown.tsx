/** @format */

import { useSessionManager } from '@/app/shared/system';
import { dropdownStyles, zIndexLayers } from 'app/chat/styles';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import {
	FaCog,
	FaMapMarkerAlt,
	FaSignOutAlt,
	FaSync,
	FaUser,
} from 'react-icons/fa';
import { FiZap } from 'react-icons/fi';

interface UserDropdownProps {
	isOpen: boolean;
	onClose: () => void;
	buttonRef: React.RefObject<HTMLDivElement>;
	userLocation: { lat: number; lng: number } | null;
	locationError: string | null;
	locationLoading: boolean;
	requestAutoLocation: () => Promise<void>;
}

const UserDropdown: React.FC<UserDropdownProps> = ({
	isOpen,
	onClose,
	buttonRef,
	userLocation,
	locationError,
	locationLoading,
	requestAutoLocation,
}) => {
	const router = useRouter();
	const { clearSession } = useSessionManager();
	const dropdownRef = useRef<HTMLDivElement>(null);
	const [position, setPosition] = useState({ top: 0, right: 0 });

	// Calculate position based on button position
	useEffect(() => {
		if (isOpen && buttonRef.current) {
			const buttonRect = buttonRef.current.getBoundingClientRect();
			setPosition({
				top: buttonRect.bottom + 8,
				right: window.innerWidth - buttonRect.right,
			});
		}
	}, [isOpen, buttonRef]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node) &&
				buttonRef.current &&
				!buttonRef.current.contains(event.target as Node)
			) {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [isOpen, onClose, buttonRef]);

	const handleLogout = async () => {
		try {
			await clearSession();
		} catch (error) {
			console.error('Logout failed:', error);
			// Force refresh as fallback
			if (typeof window !== 'undefined') {
				window.location.href = '/';
			}
		}
	};

	const formatLocation = () => {
		if (!userLocation) return 'Location unavailable';

		const latDir = userLocation.lat >= 0 ? 'N' : 'S';
		const lngDir = userLocation.lng >= 0 ? 'E' : 'W';

		// Simple format since we only have lat/lng coordinates
		return `${Math.abs(userLocation.lat).toFixed(4)}°${latDir}, ${Math.abs(
			userLocation.lng
		).toFixed(4)}°${lngDir}`;
	};

	if (!isOpen) return null;

	const dropdownContent = (
		<div
			ref={dropdownRef}
			className={`w-80 bg-white border border-wizlop-200 rounded-xl shadow-xl ${zIndexLayers.userDropdown} py-2 text-sm overflow-hidden`}
			style={{
				...dropdownStyles.userDropdown,
				top: position.top,
				right: position.right,
			}}>
			{/* Location Display */}
			<div className='px-4 py-3 bg-wizlop-25 border-b border-wizlop-100'>
				<div className='flex items-start gap-3'>
					<FaMapMarkerAlt className='text-wizlop-600 mt-0.5 flex-shrink-0' />
					<div className='min-w-0 flex-1'>
						<div className='flex items-center justify-between mb-1'>
							<div className='flex items-center gap-2'>
								<span className='font-medium text-wizlop-800'>
									Your Location
								</span>
								{userLocation && (
									<div
										className='w-2 h-2 bg-green-500 rounded-full animate-pulse'
										title='Location active'></div>
								)}
							</div>
							<button
								onClick={requestAutoLocation}
								disabled={locationLoading}
								className='text-wizlop-600 hover:text-wizlop-800 disabled:opacity-50 transition-colors'
								title='Refresh location'>
								<FaSync
									className={`w-3 h-3 ${locationLoading ? 'animate-spin' : ''}`}
								/>
							</button>
						</div>
						<div className='text-xs text-wizlop-600 break-all'>
							{locationLoading ? (
								<span className='text-wizlop-500'>📍 Getting location...</span>
							) : locationError ? (
								<span className='text-red-600'>⚠️ {locationError}</span>
							) : (
								formatLocation()
							)}
						</div>
					</div>
				</div>
			</div>

			<button
				onClick={() => {
					onClose();
					router.push('/profile');
				}}
				className='w-full flex items-center gap-3 px-4 py-3 hover:bg-wizlop-50 transition-colors text-wizlop-700 hover:text-wizlop-900'>
				<FaUser className='text-base' /> Profile
			</button>
			<button
				onClick={() => {
					onClose();
					router.push('/credits');
				}}
				className='w-full flex items-center gap-3 px-4 py-3 hover:bg-wizlop-50 transition-colors text-wizlop-700 hover:text-wizlop-900'>
				<FiZap className='text-base' /> Credits
			</button>
			<button
				onClick={() => {
					onClose();
					router.push('/settings');
				}}
				className='w-full flex items-center gap-3 px-4 py-3 hover:bg-wizlop-50 transition-colors text-wizlop-700 hover:text-wizlop-900'>
				<FaCog className='text-base' /> Settings
			</button>
			<div className='border-t border-wizlop-200 my-1' />
			<div className='border-t border-wizlop-200 my-1' />
			<button
				onClick={() => {
					onClose();
					handleLogout();
				}}
				className='w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-red-600 hover:text-red-700'>
				<FaSignOutAlt className='text-base' /> Logout
			</button>
		</div>
	);

	// Render dropdown as portal to document body to avoid z-index issues
	return typeof window !== 'undefined'
		? createPortal(dropdownContent, document.body)
		: null;
};

export default UserDropdown;
