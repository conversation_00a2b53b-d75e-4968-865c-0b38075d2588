import React from 'react';
import { zIndexLayers } from 'app/chat/styles';

interface ModalOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  className?: string;
  overlayClassName?: string;
}

const ModalOverlay: React.FC<ModalOverlayProps> = ({
  isOpen,
  onClose,
  children,
  className = '',
  overlayClassName = '',
}) => {
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/50 ${zIndexLayers.modal} sm:hidden ${overlayClassName}`}
      onClick={onClose}
    >
      <div className={className} onClick={(e) => e.stopPropagation()}>
        {children}
      </div>
    </div>
  );
};

export default ModalOverlay;
