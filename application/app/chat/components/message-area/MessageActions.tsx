import React from 'react';
import { FaCopy, FaMap } from 'react-icons/fa';
import { IconButton } from 'app/chat/components/ui';

interface MessageActionsProps {
  messageText: string;
  onShowMap: () => void;
}

const MessageActions: React.FC<MessageActionsProps> = ({
  messageText,
  onShowMap,
}) => {
  const handleCopyMessage = () => {
    navigator.clipboard.writeText(messageText);
  };

  return (
    <div className="flex items-center justify-end mt-2 space-x-2">
      <IconButton
        onClick={handleCopyMessage}
        icon={<FaCopy size={12} />}
        title="Copy message"
        variant="ghost"
        size="sm"
      />
      <IconButton
        onClick={onShowMap}
        icon={<FaMap size={12} />}
        title="Show on map"
        variant="ghost"
        size="sm"
      />
    </div>
  );
};

export default MessageActions;
