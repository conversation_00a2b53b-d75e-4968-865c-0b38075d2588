import React from 'react';
import { colors } from 'app/colors';

interface ScrollToBottomButtonProps {
  isVisible: boolean;
  onClick: () => void;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  isVisible,
  onClick,
}) => {
  if (!isVisible) return null;

  return (
    <button
      onClick={onClick}
      className="fixed bottom-24 right-8 text-white p-3 rounded-full shadow-lg transition-colors z-50"
      style={{
        backgroundColor: colors.brand.blue,
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = colors.supporting.lightBlue;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = colors.brand.blue;
      }}
    >
      ↓
    </button>
  );
};

export default ScrollToBottomButton;
