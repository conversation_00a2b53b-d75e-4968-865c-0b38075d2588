/** @format */

import { colors } from '@/app/colors';
import { useCreditsData } from '@/app/shared/credits';
import { useSession } from 'next-auth/react';
import React from 'react';
import { FiCreditCard, FiInfo } from 'react-icons/fi';

interface CreditStatusProps {
	className?: string;
	compact?: boolean;
}

const CreditStatus: React.FC<CreditStatusProps> = ({
	className = '',
	compact = false,
}) => {
	const { data: session } = useSession();
	const { credits, availableCredits, loading, error } = useCreditsData();

	// Don't show for agents/superusers
	if (
		session?.user?.role &&
		['agent', 'superuser'].includes(session.user.role)
	) {
		return null;
	}

	if (loading) {
		return (
			<div className={`flex items-center gap-2 ${className}`}>
				<div className='w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500'></div>
				<span className='text-xs text-gray-500'>Loading credits...</span>
			</div>
		);
	}

	if (error || !credits) {
		return (
			<div className={`flex items-center gap-2 ${className}`}>
				<FiInfo className='w-4 h-4 text-orange-500' />
				<span className='text-xs text-orange-600'>Credits unavailable</span>
			</div>
		);
	}

	const isLowCredits = availableCredits < 5;
	const isOutOfCredits = availableCredits === 0;

	const getStatusColor = () => {
		if (isOutOfCredits) return colors.utility.error;
		if (isLowCredits) return colors.utility.warning;
		return colors.brand.green;
	};

	if (compact) {
		return (
			<div className={`flex items-center gap-1 ${className}`}>
				<FiCreditCard
					className='w-3 h-3'
					style={{ color: getStatusColor() }}
				/>
				<span
					className='text-xs font-medium'
					style={{ color: getStatusColor() }}>
					{availableCredits}
				</span>
			</div>
		);
	}

	return (
		<div className={`flex items-center gap-2 ${className}`}>
			<FiCreditCard
				className='w-4 h-4'
				style={{ color: getStatusColor() }}
			/>
			<div className='flex flex-col'>
				<span
					className='text-xs font-medium'
					style={{ color: getStatusColor() }}>
					{availableCredits} credits
				</span>
				{isOutOfCredits && (
					<span className='text-xs text-gray-500'>
						Visit Credits page to get more
					</span>
				)}
			</div>
		</div>
	);
};

export default CreditStatus;
