/** @format */

import { zIndexLayers } from '@/app/chat/styles';
import { BackgroundGlobeProps } from '@/app/chat/types';
import dynamic from 'next/dynamic';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// Dynamically import Globe component to avoid SSR issues
const Globe = dynamic(() => import('react-globe.gl'), {
	ssr: false,
	loading: () => null,
});

const BackgroundGlobe: React.FC<BackgroundGlobeProps> = ({
	isVisible,
	isTransitioning,
	onTransitionComplete,
}) => {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const backgroundGlobeRef = useRef<any>(null);
	const [isClient, setIsClient] = useState(false);
	const [globeColor, setGlobeColor] = useState('#1e40af'); // Default blue
	const [atmosphereColor, setAtmosphereColor] = useState(
		'rgba(30, 64, 175, 0.3)'
	);

	// Color transition effect
	const transitionGlobeColor = useCallback(
		(newColor: string, newAtmosphere: string) => {
			setGlobeColor(newColor);
			setAtmosphereColor(newAtmosphere);
		},
		[]
	);

	useEffect(() => {
		setIsClient(true);
	}, []);

	// Color transition effect based on time of day or interactions
	useEffect(() => {
		if (!isClient) return;

		const colorTransitions = [
			{ color: '#1e40af', atmosphere: 'rgba(30, 64, 175, 0.3)' }, // Blue
			{ color: '#059669', atmosphere: 'rgba(5, 150, 105, 0.3)' }, // Green
			{ color: '#7c3aed', atmosphere: 'rgba(124, 58, 237, 0.3)' }, // Purple
			{ color: '#dc2626', atmosphere: 'rgba(220, 38, 38, 0.3)' }, // Red
			{ color: '#ea580c', atmosphere: 'rgba(234, 88, 12, 0.3)' }, // Orange
		];

		let currentIndex = 0;
		const interval = setInterval(() => {
			currentIndex = (currentIndex + 1) % colorTransitions.length;
			const transition = colorTransitions[currentIndex];
			transitionGlobeColor(transition.color, transition.atmosphere);
		}, 8000); // Change color every 8 seconds

		return () => clearInterval(interval);
	}, [isClient, transitionGlobeColor]);

	// Configure background globe
	useEffect(() => {
		const globe = backgroundGlobeRef.current;
		if (!globe || !isClient) return;

		// Configure background globe (non-interactive)
		globe.controls().autoRotate = true;
		globe.controls().autoRotateSpeed = 0.3;
		globe.controls().enableZoom = false;
		globe.controls().enableRotate = false;
		globe.controls().enablePan = false;

		// Set initial size
		const updateGlobeSize = () => {
			if (globe && backgroundGlobeRef.current?.parentElement) {
				const container = backgroundGlobeRef.current.parentElement;
				const width = container.clientWidth;
				const height = container.clientHeight;
				globe.width(width);
				globe.height(height);
				globe.renderer().setSize(width, height);
			}
		};

		updateGlobeSize();

		// Handle resize
		const handleResize = () => {
			updateGlobeSize();
		};

		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, [isClient]);

	// Handle transition completion
	useEffect(() => {
		if (isTransitioning && onTransitionComplete) {
			const timer = setTimeout(() => {
				onTransitionComplete();
			}, 1000);
			return () => clearTimeout(timer);
		}
	}, [isTransitioning, onTransitionComplete]);

	if (!isClient || !isVisible) return null;

	return (
		<div
			className={`absolute inset-0 pointer-events-none w-full h-full overflow-hidden transition-all duration-700 ease-in-out ${
				isTransitioning
					? `opacity-100 ${zIndexLayers.globeTransition} scale-110`
					: `opacity-20 ${zIndexLayers.globeBackground} scale-100`
			}`}
			style={{ isolation: 'isolate' }}>
			<div className='w-full h-full globe-container globe-color-transition'>
				<style jsx>{`
					.globe-color-transition {
						transition: all 2s ease-in-out;
					}
				`}</style>
				<Globe
					ref={backgroundGlobeRef}
					globeImageUrl={null}
					backgroundColor={
						isTransitioning ? 'rgba(15, 23, 42, 1)' : 'rgba(0,0,0,0)'
					}
					showGlobe={true}
					showAtmosphere={true}
					atmosphereColor={atmosphereColor}
					atmosphereAltitude={0.15}
					globeMaterial={{
						transparent: true,
						opacity: 0.8,
						color: globeColor,
					}}
					animateIn={true}
					width={undefined}
					height={undefined}
					rendererConfig={{
						antialias: true,
						alpha: true,
						shadowMap: {
							enabled: false,
						},
					}}
				/>
			</div>
		</div>
	);
};

export default BackgroundGlobe;
