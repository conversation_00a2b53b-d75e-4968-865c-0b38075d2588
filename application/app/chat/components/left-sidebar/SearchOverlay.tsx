import React, { useState } from 'react';
import { Session } from 'app/chat/types';
import { colors } from 'app/colors';

interface SearchOverlayProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  sessionList: Session[];
  loadOldMessages: (id: string) => void;
  currentSessionId: string;
}

const SearchOverlay: React.FC<SearchOverlayProps> = ({
  isOpen,
  setIsOpen,
  sessionList,
  loadOldMessages,
  currentSessionId,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  if (!isOpen) return null;

  const filteredSessions = sessionList.filter(session =>
    session.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <div
        className="w-full max-w-md mx-4 rounded-lg shadow-lg"
        style={{
          backgroundColor: colors.neutral.cloudWhite,
          border: `1px solid ${colors.ui.gray200}`,
        }}
      >
        <div className="p-4">
          <input
            type="text"
            placeholder="Search chats..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-3 rounded-lg border focus:outline-none focus:ring-2"
            style={{
              borderColor: colors.ui.blue200,
              backgroundColor: colors.neutral.cloudWhite,
            }}
            autoFocus
          />
        </div>

        <div className="max-h-60 overflow-y-auto">
          {filteredSessions.map((session) => (
            <button
              key={session.id}
              onClick={() => {
                loadOldMessages(session.id);
                setIsOpen(false);
              }}
              className="w-full p-3 text-left hover:bg-gray-100 transition-colors"
              style={{
                backgroundColor: session.id === currentSessionId ? colors.ui.blue100 : 'transparent',
              }}
            >
              <div className="text-sm font-medium truncate">{session.title}</div>
            </button>
          ))}
        </div>

        <div className="p-4 border-t" style={{ borderColor: colors.ui.gray200 }}>
          <button
            onClick={() => setIsOpen(false)}
            className="w-full p-2 rounded-lg transition-colors"
            style={{
              backgroundColor: colors.neutral.slateGray,
              color: 'white',
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SearchOverlay;
