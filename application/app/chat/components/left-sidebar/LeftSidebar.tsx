import React, { useState, useRef, useCallback, useEffect } from 'react';
import { LeftSidebarProps } from '@/app/chat/types';
import { sidebarStyles, zIndexLayers } from '@/app/chat/styles';
import { ModalOverlay } from '@/app/chat/components/ui';
import SidebarHeader from './SidebarHeader';
import ChatHistorySection from './ChatHistorySection';

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  isOpen,
  onClose,
  sessionList,
  activeSessionId,
  dropdownIndex,
  onStartNewChat,
  onLoadOldMessages,
  onToggleDropdown,
  onDeleteSession,
}) => {
  console.log('LeftSidebar loaded');
  const [leftSidebarWidth, setLeftSidebarWidth] = useState(280); // Default width
  const [isResizing, setIsResizing] = useState(false);
  const leftSidebarRef = useRef<HTMLDivElement>(null);

  const MIN_LEFT_WIDTH = 280;
  const MAX_LEFT_WIDTH = 420;

  const handleLeftResize = useCallback((e: MouseEvent) => {
    if (!isResizing || !leftSidebarRef.current) return;

    e.preventDefault();
    e.stopPropagation();

    const rect = leftSidebarRef.current.getBoundingClientRect();
    const newWidth = e.clientX - rect.left;
    const clampedWidth = Math.max(MIN_LEFT_WIDTH, Math.min(MAX_LEFT_WIDTH, newWidth));
    setLeftSidebarWidth(clampedWidth);
  }, [isResizing]);

  const handleLeftResizeEnd = useCallback(() => {
    setIsResizing(false);
    // Re-enable interactions
    document.body.classList.remove('resizing');
  }, []);

  const handleLeftResizeStart = useCallback(() => {
    setIsResizing(true);
    // Disable interactions during resize
    document.body.classList.add('resizing');
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleLeftResize, { passive: false });
      document.addEventListener('mouseup', handleLeftResizeEnd);
      document.addEventListener('selectstart', (e) => e.preventDefault());
      return () => {
        document.removeEventListener('mousemove', handleLeftResize);
        document.removeEventListener('mouseup', handleLeftResizeEnd);
        document.removeEventListener('selectstart', (e) => e.preventDefault());
      };
    }
  }, [isResizing, handleLeftResize, handleLeftResizeEnd]);

  return (
    <>
      <ModalOverlay
        isOpen={isOpen}
        onClose={onClose}
      />

      <div
        ref={leftSidebarRef}
        className={`transition-all duration-300 overflow-hidden whitespace-nowrap flex flex-col items-start ${zIndexLayers.sidebar} border-r relative
          ${
            isOpen
              ? 'fixed sm:relative inset-0 sm:inset-auto w-full h-full sm:h-auto'
              : 'w-0 sm:w-0 p-0'
          }
        `}
        style={{
          ...sidebarStyles.left,
          width: isOpen ? `${leftSidebarWidth}px` : '0px',
          cursor: isResizing ? 'col-resize' : 'default',
        }}
      >
        {isOpen && (
          <>
            <SidebarHeader onClose={onClose} />
            <ChatHistorySection
              sessionList={sessionList}
              activeSessionId={activeSessionId}
              dropdownIndex={dropdownIndex}
              onStartNewChat={onStartNewChat}
              onLoadOldMessages={onLoadOldMessages}
              onToggleDropdown={onToggleDropdown}
              onDeleteSession={onDeleteSession}
            />

            {/* Resize Handle */}
            <div
              className="resize-handle absolute top-0 right-0 w-2 h-full cursor-col-resize hover:bg-blue-400 transition-colors duration-200 group"
              onMouseDown={handleLeftResizeStart}
              title="Resize sidebar"
            >
              <div className="w-full h-full bg-transparent group-hover:bg-blue-400 group-hover:opacity-50" />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default LeftSidebar;
