import React, { useState } from 'react';
import { FaPlus, FaSearch } from 'react-icons/fa';
import { Session } from '@/app/chat/types';
import { colors } from '@/app/colors';
import { IconButton } from '@/app/chat/components/ui';
import ChatSessionItem from './ChatSessionItem';
import SearchOverlay from './SearchOverlay';

interface ChatHistorySectionProps {
  sessionList: Session[];
  activeSessionId: string | null;
  dropdownIndex: number | null;
  onStartNewChat: () => void;
  onLoadOldMessages: (id: string) => void;
  onToggleDropdown: (index: number) => void;
  onDeleteSession: (id: string) => void;
}

const ChatHistorySection: React.FC<ChatHistorySectionProps> = ({
  sessionList,
  activeSessionId,
  dropdownIndex,
  onStartNewChat,
  onLoadOldMessages,
  onToggleDropdown,
  onDeleteSession,
}) => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const handleCloseDropdown = () => {
    onToggleDropdown(-1);
  };

  return (
    <>
      <div className="w-full flex-1 p-4 flex flex-col">
        <div className="flex-1 flex flex-col">
          <div className="flex items-center justify-between mb-4">
            <h3
              className="text-sm font-semibold uppercase tracking-wide"
              style={{ color: colors.brand.blue }}
            >
              Recent Chats
            </h3>
            <div className="flex items-center space-x-2">
              <IconButton
                onClick={onStartNewChat}
                icon={<FaPlus className="w-4 h-4" />}
                title="New chat"
                variant="default"
              />
              <IconButton
                onClick={() => setIsSearchOpen(true)}
                icon={<FaSearch className="w-4 h-4" />}
                title="Search chats"
                variant="default"
              />
            </div>
          </div>

          {/* Chat List */}
          <div className="flex-1 overflow-y-auto">
            {sessionList.length === 0 ? (
              <div
                className="text-center py-8 text-sm"
                style={{ color: colors.neutral.slateGray }}
              >
                No chat history yet.
                <br />
                Start a new conversation!
              </div>
            ) : (
              <div className="space-y-2">
                {sessionList
                  .filter((session) => session.id && session.title)
                  .map((session, index) => (
                    <ChatSessionItem
                      key={session.id}
                      session={session}
                      isActive={session.id === activeSessionId}
                      isDropdownOpen={dropdownIndex === index}
                      onSessionClick={() => {
                        if (session.id !== activeSessionId) {
                          onLoadOldMessages(session.id);
                        }
                      }}
                      onToggleDropdown={() => onToggleDropdown(index)}
                      onDeleteSession={() => onDeleteSession(session.id)}
                      onCloseDropdown={handleCloseDropdown}
                    />
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <SearchOverlay
        isOpen={isSearchOpen}
        setIsOpen={setIsSearchOpen}
        sessionList={sessionList}
        loadOldMessages={onLoadOldMessages}
        currentSessionId={activeSessionId || ''}
      />
    </>
  );
};

export default ChatHistorySection;
