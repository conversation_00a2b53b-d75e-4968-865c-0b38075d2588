import React, { useEffect, useRef } from 'react';
import { FaEllipsisV, FaTrash } from 'react-icons/fa';
import { Session } from 'app/chat/types';
import { colors } from 'app/colors';
import { getSessionItemStyles, zIndexLayers } from 'app/chat/styles';

interface ChatSessionItemProps {
  session: Session;
  isActive: boolean;
  isDropdownOpen: boolean;
  onSessionClick: () => void;
  onToggleDropdown: () => void;
  onDeleteSession: () => void;
  onCloseDropdown: () => void;
}

const ChatSessionItem: React.FC<ChatSessionItemProps> = ({
  session,
  isActive,
  isDropdownOpen,
  onSessionClick,
  onToggleDropdown,
  onDeleteSession,
  onCloseDropdown,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onCloseDropdown();
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen, onCloseDropdown]);

  return (
    <div className="relative">
      <div
        className="flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors group"
        style={getSessionItemStyles(isActive)}
        onClick={onSessionClick}
      >
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">
            {session.title}
          </div>
        </div>

        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleDropdown();
          }}
          className="p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
          style={{ color: isActive ? 'white' : colors.neutral.slateGray }}
        >
          <FaEllipsisV size={12} />
        </button>
      </div>

      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className={`absolute right-0 top-full mt-1 w-32 rounded-lg shadow-lg border ${zIndexLayers.sidebarDropdown}`}
          style={{
            backgroundColor: colors.neutral.cloudWhite,
            borderColor: colors.ui.gray200,
          }}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDeleteSession();
              onCloseDropdown();
            }}
            className="w-full px-3 py-2 text-left text-sm transition-colors flex items-center space-x-2"
            style={{ color: colors.utility.error }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.ui.gray100;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <FaTrash size={12} />
            <span>Delete</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatSessionItem;
