import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/nextauth-options'
import { db } from '@/lib/database'
import { logger } from '@/lib/logger'

/**
 * GET /api/profile/picture-history
 * Get user's profile picture history
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    // Verify user can access this data (own profile or admin)
    if (userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // For now, we'll create a simple history based on the current profile picture
    // In a real implementation, you'd have a profile_picture_history table
    const userResult = await db.query(
      'SELECT profile_picture_url, updated_at FROM backend_schema.nextauth_users WHERE id = $1',
      [userId]
    )

    const user = userResult.rows[0]
    const history = []

    if (user?.profile_picture_url) {
      history.push({
        id: '1',
        url: user.profile_picture_url,
        uploadedAt: user.updated_at || new Date().toISOString(),
        isActive: true
      })
    }

    logger.info('Profile picture history retrieved', {
      userId,
      historyCount: history.length
    })

    return NextResponse.json({
      success: true,
      history
    })

  } catch (error) {
    logger.error('Error retrieving profile picture history', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })

    return NextResponse.json(
      { error: 'Failed to retrieve profile picture history' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/profile/picture-history
 * Restore a previous profile picture
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { historyId, pictureUrl } = await request.json()

    if (!historyId || !pictureUrl) {
      return NextResponse.json(
        { error: 'History ID and picture URL are required' },
        { status: 400 }
      )
    }

    // Update user's profile picture
    await db.query(
      'UPDATE backend_schema.nextauth_users SET profile_picture_url = $1, updated_at = NOW() WHERE id = $2',
      [pictureUrl, session.user.id]
    )

    logger.info('Profile picture restored from history', {
      userId: session.user.id,
      historyId,
      pictureUrl
    })

    return NextResponse.json({
      success: true,
      message: 'Profile picture restored successfully'
    })

  } catch (error) {
    logger.error('Error restoring profile picture from history', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })

    return NextResponse.json(
      { error: 'Failed to restore profile picture' },
      { status: 500 }
    )
  }
}
