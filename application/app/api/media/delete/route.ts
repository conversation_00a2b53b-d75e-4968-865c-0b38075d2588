/**
 * Media Delete API
 * Handles deleting POI media and cleaning up files
 *
 * @format
 */

import { MediaDatabaseService, MediaManager } from '@/app/shared/media/utils';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

/**
 * DELETE /api/media/delete
 * Delete specific media by ID
 */
export async function DELETE(request: NextRequest) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;
		const { searchParams } = new URL(request.url);
		const mediaId = searchParams.get('mediaId');

		if (!mediaId) {
			return NextResponse.json(
				{ success: false, error: 'Media ID is required' },
				{ status: 400 }
			);
		}

		logger.info('Starting media deletion', { userId, mediaId });

		// Get media record to verify ownership and get file paths
		const media = await MediaDatabaseService.getMediaById(mediaId, userId);

		if (!media) {
			return NextResponse.json(
				{ success: false, error: 'Media not found or access denied' },
				{ status: 404 }
			);
		}

		// Extract POI ID from media record
		const poiId = media.poi_id?.toString();

		if (!poiId) {
			return NextResponse.json(
				{ success: false, error: 'Invalid media record: missing POI ID' },
				{ status: 400 }
			);
		}

		// Delete media files from filesystem
		const deleteResult = await MediaManager.deletePOIMedia(
			userId,
			poiId,
			media.media_url,
			media.thumbnail_url
		);

		if (!deleteResult.success) {
			logger.error('Failed to delete media files', {
				userId,
				mediaId,
				error: deleteResult.error,
			});
			// Continue with database deletion even if file deletion fails
		}

		// Delete database record
		const deleted = await MediaDatabaseService.deleteMedia(mediaId, userId);

		if (!deleted) {
			return NextResponse.json(
				{ success: false, error: 'Failed to delete media record' },
				{ status: 500 }
			);
		}

		logger.info('Media deleted successfully', {
			userId,
			mediaId,
			poiId,
			deletedFiles: deleteResult.deletedFiles?.length || 0,
			cleanedUpDirectories: deleteResult.cleanedUpDirectories?.length || 0,
		});

		return NextResponse.json({
			success: true,
			message: 'Media deleted successfully',
			deletedFiles: deleteResult.deletedFiles?.length || 0,
			cleanedUpDirectories: deleteResult.cleanedUpDirectories?.length || 0,
			mediaId,
		});
	} catch (error) {
		logger.error('Error deleting media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete media' },
			{ status: 500 }
		);
	}
}

/**
 * POST /api/media/delete
 * Alternative endpoint for frameworks that don't support DELETE method well
 */
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { mediaId } = body;

		if (!mediaId) {
			return NextResponse.json(
				{ success: false, error: 'Media ID is required' },
				{ status: 400 }
			);
		}

		// Create a new URL with the mediaId as a query parameter
		const url = new URL(request.url);
		url.searchParams.set('mediaId', mediaId);

		// Create a new request with the modified URL
		const newRequest = new NextRequest(url.toString(), {
			method: 'DELETE',
			headers: request.headers,
		});

		return DELETE(newRequest);
	} catch (error) {
		logger.error('Error in POST media delete', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete media' },
			{ status: 500 }
		);
	}
}
