/** @format */

import {
	AuthenticatedAgentRequest,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { awardCreditsWithoutTransaction } from '@/lib/credits';
import {
	creditConfig,
	creditDescriptions,
	creditReasons,
} from '@/lib/credits/creditConfig';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// POST /api/agent/submissions/approve - Approve POI and move to main table
export const POST = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { temp_poi_id, notes } = body;

			if (!temp_poi_id) {
				return NextResponse.json(
					{ success: false, error: 'Temp POI ID is required' },
					{ status: 400 }
				);
			}

			logger.info('Agent approving POI', {
				agentId: request.agent.id,
				tempPoiId: temp_poi_id,
				notes,
			});

			await db.query('BEGIN');

			try {
				// Get the temp POI data
				const tempPoiResult = await db.query(
					`
        SELECT * FROM spatial_schema.user_pois_temp
        WHERE id = $1 AND admin_review_status = 'pending'
      `,
					[temp_poi_id]
				);

				if (tempPoiResult.rows.length === 0) {
					await db.query('ROLLBACK');
					return NextResponse.json(
						{ success: false, error: 'POI not found or already processed' },
						{ status: 404 }
					);
				}

				const tempPoi = tempPoiResult.rows[0];

				// Insert into main POI table
				const insertPoiResult = await db.query(
					`
        INSERT INTO spatial_schema.pois (
          name, name_en, name_tr, name_uk, name_de, name_ru, name_ar,
          category, subcategory, cuisine, city, district, neighborhood,
          street, full_address, province, phone_number, opening_hours,
          description, latitude, longitude, geom
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
          $14, $15, $16, $17, $18, $19, $20, $21, $22
        ) RETURNING id
      `,
					[
						tempPoi.name,
						tempPoi.name_en,
						tempPoi.name_tr,
						tempPoi.name_uk,
						tempPoi.name_de,
						tempPoi.name_ru,
						tempPoi.name_ar,
						tempPoi.category,
						tempPoi.subcategory,
						tempPoi.cuisine,
						tempPoi.city,
						tempPoi.district,
						tempPoi.neighborhood,
						tempPoi.street,
						tempPoi.full_address,
						tempPoi.province,
						tempPoi.phone_number,
						tempPoi.opening_hours,
						tempPoi.description,
						tempPoi.latitude,
						tempPoi.longitude,
						tempPoi.geom,
					]
				);

				const newPoiId = insertPoiResult.rows[0].id;

				// Update temp POI status
				await db.query(
					`
        UPDATE spatial_schema.user_pois_temp
        SET admin_review_status = 'approved',
            admin_review_notes = $1,
            reviewed_by = $2,
            reviewed_at = NOW(),
            updated_at = NOW()
        WHERE id = $3
      `,
					[notes, request.agent.id, temp_poi_id]
				);

				// Update user submission tracking
				await db.query(
					`
        UPDATE backend_schema.user_poi_submissions
        SET status = 'approved',
            reviewed_at = NOW(),
            reviewed_by = $1,
            review_notes = $2
        WHERE temp_poi_id = $3
      `,
					[request.agent.id, notes, temp_poi_id]
				);

				// Create approved POI record for tracking
				await db.query(
					`
        INSERT INTO spatial_schema.user_pois_approved (
          original_temp_id, submitted_by_user_id, approved_by_admin_id,
          name, category, subcategory, latitude, longitude, city, district,
          phone_number, opening_hours, description, main_poi_id
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      `,
					[
						temp_poi_id,
						tempPoi.submitted_by_user_id,
						request.agent.id,
						tempPoi.name,
						tempPoi.category,
						tempPoi.subcategory,
						tempPoi.latitude,
						tempPoi.longitude,
						tempPoi.city,
						tempPoi.district,
						tempPoi.phone_number,
						tempPoi.opening_hours,
						tempPoi.description,
						newPoiId,
					]
				);

				// Award credits for approved POI submission - BEFORE commit
				if (tempPoi.submitted_by_user_id) {
					logger.info('Awarding credits for approved POI submission', {
						userId: tempPoi.submitted_by_user_id,
						tempPoiId: temp_poi_id,
						poiName: tempPoi.name,
						creditAmount: creditConfig.poi.add,
					});

					const creditAwarded = await awardCreditsWithoutTransaction(
						tempPoi.submitted_by_user_id,
						creditConfig.poi.add,
						creditReasons.poi.add,
						creditDescriptions.poi.add(tempPoi.name || 'Unnamed POI'),
						'poi_submission',
						temp_poi_id.toString(),
						false // Don't skip duplicate check
					);

					if (!creditAwarded) {
						logger.warn('Failed to award credits for POI approval', {
							userId: tempPoi.submitted_by_user_id,
							tempPoiId: temp_poi_id,
						});
						// Don't fail the approval if credit awarding fails
					} else {
						logger.info('Credits awarded successfully for POI approval', {
							userId: tempPoi.submitted_by_user_id,
							tempPoiId: temp_poi_id,
							amount: creditConfig.poi.add,
						});
					}
				}

				await db.query('COMMIT');

				logger.info('POI approved successfully', {
					tempPoiId: temp_poi_id,
					newPoiId,
					agentId: request.agent.id,
				});

				return NextResponse.json({
					success: true,
					message: 'POI approved and added to main database',
					poi_id: newPoiId,
					temp_poi_id,
				});
			} catch (dbError) {
				await db.query('ROLLBACK');
				throw dbError;
			}
		} catch (error) {
			logger.error('Error approving POI', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to approve POI' },
				{ status: 500 }
			);
		}
	}
);
