/** @format */

import { POI_EDITABLE_FIELDS } from '@/app/shared/poi';
import {
	AuthenticatedAgentRequest,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/agent/submissions - Get user POI submissions for agent review
export const GET = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const { searchParams } = new URL(request.url);
			const status = searchParams.get('status') || 'all';
			const submissionReason = searchParams.get('submission_reason') || 'all';
			const sortBy = searchParams.get('sortBy') || 'created_at';
			const sortOrder = searchParams.get('sortOrder') || 'desc';
			const page = parseInt(searchParams.get('page') || '1');
			const limit = parseInt(searchParams.get('limit') || '20');
			const offset = (page - 1) * limit;

			logger.info('Agent fetching POI submissions', {
				agentId: request.agent.id,
				agentRole: request.agent.role,
				status,
				submissionReason,
				sortBy,
				sortOrder,
				page,
				limit,
			});

			// Build query with filters
			let whereClause = '';
			const queryParams: (string | number)[] = [];
			let paramIndex = 1;
			const conditions: string[] = [];

			if (status !== 'all') {
				conditions.push(`temp.admin_review_status = $${paramIndex}`);
				queryParams.push(status);
				paramIndex++;
			}

			if (submissionReason !== 'all') {
				conditions.push(`temp.submission_reason = $${paramIndex}`);
				queryParams.push(submissionReason);
				paramIndex++;
			}

			if (conditions.length > 0) {
				whereClause = `WHERE ${conditions.join(' AND ')}`;
			}

			// Validate sort fields
			const allowedSortFields = [
				'created_at',
				'updated_at',
				'name',
				'admin_review_status',
			];
			const sortField = allowedSortFields.includes(sortBy)
				? sortBy
				: 'created_at';
			const sortDirection = sortOrder === 'asc' ? 'ASC' : 'DESC';

			const editableFieldsSql = POI_EDITABLE_FIELDS.map(
				(f) => `temp.${f}`
			).join(',\n        ');
			const query = `
      SELECT 
        temp.id,
        temp.id as temp_poi_id,
        temp.submitted_by_user_id,
        users.name as submitter_name,
        users.email as submitter_email,
        ${editableFieldsSql},
        temp.submission_notes,
        temp.submission_reason,
        temp.admin_review_status,
        temp.admin_review_notes,
        temp.reviewed_by,
        temp.reviewed_at,
        temp.created_at,
        temp.updated_at,
        temp.original_poi_id
      FROM spatial_schema.user_pois_temp temp
      LEFT JOIN backend_schema.nextauth_users users ON temp.submitted_by_user_id = users.id
      ${whereClause}
      ORDER BY temp.${sortField} ${sortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

			queryParams.push(limit, offset);

			const result = await db.query(query, queryParams);

			// Get total count for pagination
			const countQuery = `
      SELECT COUNT(*) as total
      FROM spatial_schema.user_pois_temp temp
      ${whereClause}
    `;

			const countParams = queryParams.slice(0, queryParams.length - 2); // Remove limit and offset
			const countResult = await db.query(countQuery, countParams);
			const totalCount = parseInt(countResult.rows[0].total);

			logger.info(
				`Retrieved ${result.rows.length} POI submissions for agent review`
			);

			return NextResponse.json({
				success: true,
				submissions: result.rows,
				pagination: {
					page,
					limit,
					total: totalCount,
					totalPages: Math.ceil(totalCount / limit),
					hasNext: offset + limit < totalCount,
					hasPrev: page > 1,
				},
			});
		} catch (error) {
			logger.error('Error fetching POI submissions for agent', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch submissions' },
				{ status: 500 }
			);
		}
	}
);
