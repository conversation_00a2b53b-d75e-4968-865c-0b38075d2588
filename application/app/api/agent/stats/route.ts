/** @format */

import {
	AuthenticatedAgentRequest,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/agent/stats - Get dashboard statistics for agent
export const GET = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		try {
			logger.info('Agent fetching dashboard stats', {
				agentId: request.agent.id,
				agentRole: request.agent.role,
			});

			// Get overall statistics
			const statsQuery = `
      SELECT 
        COUNT(*) FILTER (WHERE admin_review_status = 'pending') as total_pending,
        COUNT(*) FILTER (WHERE admin_review_status = 'reviewing') as total_reviewing,
        COUNT(*) FILTER (WHERE admin_review_status = 'approved') as total_approved,
        COUNT(*) FILTER (WHERE admin_review_status = 'rejected') as total_rejected,
        COUNT(*) FILTER (WHERE admin_review_status = 'pending' AND created_at >= NOW() - INTERVAL '7 days') as pending_this_week,
        COUNT(*) FILTER (WHERE admin_review_status = 'approved' AND reviewed_at >= NOW() - INTERVAL '7 days') as approved_this_week
      FROM spatial_schema.user_pois_temp
    `;

			const statsResult = await db.query(statsQuery);
			const stats = statsResult.rows[0];

			// Convert string numbers to integers
			const formattedStats = {
				total_pending: parseInt(stats.total_pending) || 0,
				total_reviewing: parseInt(stats.total_reviewing) || 0,
				total_approved: parseInt(stats.total_approved) || 0,
				total_rejected: parseInt(stats.total_rejected) || 0,
				pending_this_week: parseInt(stats.pending_this_week) || 0,
				approved_this_week: parseInt(stats.approved_this_week) || 0,
			};

			logger.info('Retrieved agent dashboard stats', { stats: formattedStats });

			return NextResponse.json({
				success: true,
				stats: formattedStats,
			});
		} catch (error) {
			logger.error('Error fetching agent dashboard stats', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch stats' },
				{ status: 500 }
			);
		}
	}
);
