/** @format */

import {
	AuthenticatedAgentRequest,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { NextResponse } from 'next/server';

// POST /api/agent/reports/action - Act on a POI report (approve update, approve closed, dismiss)
export const POST = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { report_id, action, update_fields } = body;
			if (!report_id || !action) {
				return NextResponse.json(
					{ success: false, error: 'Missing required fields' },
					{ status: 400 }
				);
			}
			// Fetch the report
			const reportRes = await db.query(
				'SELECT * FROM spatial_schema.poi_reports WHERE id = $1',
				[report_id]
			);
			if (reportRes.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'Report not found' },
					{ status: 404 }
				);
			}
			const report = reportRes.rows[0];
			// Approve info update
			if (action === 'approve_update') {
				// Only allow for info_update reports
				if (report.report_type !== 'info_update') {
					return NextResponse.json(
						{ success: false, error: 'Not an info update report' },
						{ status: 400 }
					);
				}
				// Update the POI (official or user_approved)
				let updateQuery = '';
				const updateParams: string[] = [];
				if (report.poi_type === 'official') {
					updateQuery = 'UPDATE spatial_schema.pois SET ';
					const sets = [];
					let idx = 1;
					if (update_fields?.new_address) {
						sets.push(`full_address = $${idx++}`);
						updateParams.push(update_fields.new_address);
					}
					if (update_fields?.new_phone) {
						sets.push(`phone_number = $${idx++}`);
						updateParams.push(update_fields.new_phone);
					}
					if (update_fields?.new_hours) {
						sets.push(`opening_hours = $${idx++}`);
						updateParams.push(update_fields.new_hours);
					}
					if (sets.length === 0) {
						return NextResponse.json(
							{ success: false, error: 'No fields to update' },
							{ status: 400 }
						);
					}
					updateQuery += sets.join(', ') + ` WHERE id = $${idx}`;
					updateParams.push(report.poi_id);
					await db.query(updateQuery, updateParams);
				} else if (report.poi_type === 'user_approved') {
					updateQuery = 'UPDATE spatial_schema.user_pois_approved SET ';
					const sets = [];
					let idx = 1;
					if (update_fields?.new_address) {
						sets.push(`full_address = $${idx++}`);
						updateParams.push(update_fields.new_address);
					}
					if (update_fields?.new_phone) {
						sets.push(`phone_number = $${idx++}`);
						updateParams.push(update_fields.new_phone);
					}
					if (update_fields?.new_hours) {
						sets.push(`opening_hours = $${idx++}`);
						updateParams.push(update_fields.new_hours);
					}
					if (sets.length === 0) {
						return NextResponse.json(
							{ success: false, error: 'No fields to update' },
							{ status: 400 }
						);
					}
					updateQuery += sets.join(', ') + ` WHERE id = $${idx}`;
					updateParams.push(report.poi_id);
					await db.query(updateQuery, updateParams);
				} else {
					return NextResponse.json(
						{ success: false, error: 'Unsupported POI type for update' },
						{ status: 400 }
					);
				}
				// Mark report as resolved
				await db.query(
					'UPDATE spatial_schema.poi_reports SET status = $1, updated_at = NOW() WHERE id = $2',
					['resolved', report_id]
				);
				return NextResponse.json({ success: true });
			}
			// Approve closed
			if (action === 'approve_closed') {
				// Only allow for closed reports
				if (report.report_type !== 'closed') {
					return NextResponse.json(
						{ success: false, error: 'Not a closure report' },
						{ status: 400 }
					);
				}
				// Deactivate the POI
				if (report.poi_type === 'official') {
					await db.query(
						'UPDATE spatial_schema.pois SET status = $1 WHERE id = $2',
						['inactive', report.poi_id]
					);
				} else if (report.poi_type === 'user_approved') {
					await db.query(
						'UPDATE spatial_schema.user_pois_approved SET status = $1 WHERE id = $2',
						['inactive', report.poi_id]
					);
				} else {
					return NextResponse.json(
						{ success: false, error: 'Unsupported POI type for closure' },
						{ status: 400 }
					);
				}
				// Mark report as resolved
				await db.query(
					'UPDATE spatial_schema.poi_reports SET status = $1, updated_at = NOW() WHERE id = $2',
					['resolved', report_id]
				);
				return NextResponse.json({ success: true });
			}
			// Dismiss
			if (action === 'dismiss') {
				await db.query(
					'UPDATE spatial_schema.poi_reports SET status = $1, updated_at = NOW() WHERE id = $2',
					['dismissed', report_id]
				);
				return NextResponse.json({ success: true });
			}
			return NextResponse.json(
				{ success: false, error: 'Invalid action' },
				{ status: 400 }
			);
		} catch {
			return NextResponse.json(
				{ success: false, error: 'Failed to process report action' },
				{ status: 500 }
			);
		}
	}
);
