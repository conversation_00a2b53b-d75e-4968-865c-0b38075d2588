/** @format */

import { withAgentAuth } from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { NextResponse } from 'next/server';

// GET /api/agent/reports - Get pending POI reports for agent review
export const GET = withAgentAuth(['full_access'])(async () => {
	try {
		const result = await db.query(`
      SELECT r.*, u.name as submitter_name, u.email as submitter_email
      FROM spatial_schema.poi_reports r
      LEFT JOIN backend_schema.nextauth_users u ON r.submitted_by_user_id = u.id
      WHERE r.status = 'pending'
      ORDER BY r.created_at DESC
    `);
		return NextResponse.json({ success: true, reports: result.rows });
	} catch {
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch reports' },
			{ status: 500 }
		);
	}
});
