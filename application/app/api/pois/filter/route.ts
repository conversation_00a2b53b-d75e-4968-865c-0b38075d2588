/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/filter - Get filtered POIs with spatial bounds and advanced filters
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		// Get filter parameters
		const city = searchParams.get('city')?.split(',').filter(Boolean) || [];
		const district =
			searchParams.get('district')?.split(',').filter(Boolean) || [];
		const neighborhood =
			searchParams.get('neighborhood')?.split(',').filter(Boolean) || [];
		const country =
			searchParams.get('country')?.split(',').filter(Boolean) || [];
		const category =
			searchParams.get('category')?.split(',').filter(Boolean) || [];
		const subcategory =
			searchParams.get('subcategory')?.split(',').filter(Boolean) || [];
		const cuisine =
			searchParams.get('cuisine')?.split(',').filter(Boolean) || [];
		const name = searchParams.get('name') || '';
		const bounds = searchParams.get('bounds');
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '30');
		const offset = (page - 1) * limit;

		// New parameter for including interactions
		const includeInteractions =
			searchParams.get('includeInteractions') === 'true';

		// Get user session for favorites
		const session = await getServerSession(authOptions);
		const userId = session?.user?.id;

		logger.info('Filtering POIs', {
			city: city.length,
			district: district.length,
			neighborhood: neighborhood.length,
			country: country.length,
			category: category.length,
			subcategory: subcategory.length,
			cuisine: cuisine.length,
			name,
			bounds,
			limit,
			page,
		});

		// Build WHERE conditions
		const conditions: string[] = [];
		const params: (string[] | string | number | null)[] = [];
		let paramIndex = 1;

		// City filter
		if (city.length > 0) {
			conditions.push(`city = ANY($${paramIndex++})`);
			params.push(city);
		}

		// District filter
		if (district.length > 0) {
			conditions.push(`district = ANY($${paramIndex++})`);
			params.push(district);
		}

		// Neighborhood filter
		if (neighborhood.length > 0) {
			conditions.push(`neighborhood = ANY($${paramIndex++})`);
			params.push(neighborhood);
		}

		// Country filter
		if (country.length > 0) {
			conditions.push(`country = ANY($${paramIndex++})`);
			params.push(country);
		}

		// Category filter
		if (category.length > 0) {
			conditions.push(`category = ANY($${paramIndex++})`);
			params.push(category);
		}

		// Subcategory filter
		if (subcategory.length > 0) {
			conditions.push(`subcategory = ANY($${paramIndex++})`);
			params.push(subcategory);
		}

		// Cuisine filter
		if (cuisine.length > 0) {
			conditions.push(`cuisine = ANY($${paramIndex++})`);
			params.push(cuisine);
		}

		// Name search
		if (name) {
			conditions.push(`name ILIKE $${paramIndex++}`);
			params.push(`%${name}%`);
		}

		// Spatial bounds filter
		if (bounds) {
			const [minLat, minLng, maxLat, maxLng] = bounds.split(',').map(Number);
			conditions.push(`latitude BETWEEN $${paramIndex++} AND $${paramIndex++}`);
			conditions.push(
				`longitude BETWEEN $${paramIndex++} AND $${paramIndex++}`
			);
			params.push(minLat, maxLat, minLng, maxLng);
		}

		const whereClause =
			conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

		// Build the main query - use only spatial_schema.pois table
		const query = `
      SELECT
        'official' as poi_type,
        p.id as poi_id,
        null as temp_id,
        null as approved_id,
        p.name,
        p.name_en,
        p.name_tr,
        p.name_uk,
        p.name_de,
        p.name_ru,
        p.name_ar,
        p.category,
        p.subcategory,
        p.latitude,
        p.longitude,
        p.city,
        p.district,
        p.country,
        p.phone_number,
        p.opening_hours,
        CASE
          WHEN uf.poi_id IS NOT NULL THEN true
          ELSE false
        END as is_favorite,
        p.neighborhood
      FROM spatial_schema.pois p
      LEFT JOIN backend_schema.user_favorites uf ON 
        uf.poi_id = p.id AND uf.user_id = $${paramIndex++}
      ${whereClause}
      ORDER BY p.name ASC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

		// Count query for pagination
		const countQuery = `
      SELECT COUNT(*) as total
      FROM spatial_schema.pois p
      ${whereClause}
    `;

		// Add user ID and pagination params
		const queryParams = [...params, userId || null, limit, offset];
		const countParams = [...params];

		// Execute queries
		const [poisResult, countResult] = await Promise.all([
			db.query(query, queryParams),
			db.query(countQuery, countParams),
		]);

		const totalCount = parseInt(countResult.rows[0]?.total || '0');
		let pois = poisResult.rows;

		// Load interactions if requested
		if (includeInteractions && pois.length > 0 && userId) {
			const interactions = await loadInteractionsForPOIs(pois, userId);

			// Merge interaction data with POI data
			pois = pois.map((poi) => {
				const key = `${poi.poi_id}_${poi.poi_type}`;
				const interactionData = interactions[key];

				return {
					...poi,
					// Add interaction data
					like_count: interactionData?.like?.count || 0,
					favorite_count: interactionData?.favorite?.count || 0,
					visit_count: interactionData?.visit?.count || 0,
					review_count: interactionData?.review?.count || 0,
					user_has_liked: interactionData?.like?.hasInteraction || false,
					user_has_favorited:
						interactionData?.favorite?.hasInteraction || false,
					user_has_visited: interactionData?.visit?.hasInteraction || false,
				};
			});
		} else if (includeInteractions && pois.length > 0) {
			// No user session - just add zero interaction counts
			pois = pois.map((poi) => ({
				...poi,
				like_count: 0,
				favorite_count: 0,
				visit_count: 0,
				review_count: 0,
				user_has_liked: false,
				user_has_favorited: false,
				user_has_visited: false,
			}));
		}

		logger.info(
			`Retrieved ${pois.length} filtered POIs (page ${page})${
				includeInteractions ? ' with interactions' : ''
			}`
		);

		return NextResponse.json({
			success: true,
			pois,
			totalCount,
			page,
			limit,
			totalPages: Math.ceil(totalCount / limit),
			includeInteractions, // Let frontend know if interactions were included
		});
	} catch (error) {
		logger.error('Error filtering POIs', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to filter POIs' },
			{ status: 500 }
		);
	}
}

/**
 * Load interactions for a batch of POIs
 */
async function loadInteractionsForPOIs(
	pois: Array<{ poi_id: number; poi_type: string }>,
	userId: string
): Promise<{
	[key: string]: {
		poi_id: number;
		poi_type: string;
		like: { count: number; hasInteraction: boolean };
		visit: { count: number; hasInteraction: boolean };
		favorite: { count: number; hasInteraction: boolean };
		review: { count: number };
	};
}> {
	try {
		// Extract POI IDs and types
		const poiIds = pois.map((poi) => poi.poi_id).filter(Boolean);
		const poiTypes = pois.map((poi) => poi.poi_type || 'official');

		if (poiIds.length === 0) {
			return {};
		}

		// Get interaction counts and user states for all POIs
		const batchQuery = `
			SELECT
				poi_id,
				poi_type,
				interaction_type,
				COUNT(*) as count,
				BOOL_OR(CASE WHEN user_id = $1 THEN true ELSE false END) as user_has_interaction
			FROM backend_schema.user_interactions
			WHERE poi_id = ANY($2)
				AND poi_type = ANY($3)
				AND interaction_type IN ('like', 'favorite', 'visit')
			GROUP BY poi_id, poi_type, interaction_type
		`;

		const result = await db.query(batchQuery, [userId, poiIds, poiTypes]);

		// Get review counts separately
		const reviewQuery = `
			SELECT
				poi_id,
				poi_type,
				COUNT(*) as review_count
			FROM backend_schema.user_location_reviews
			WHERE poi_id = ANY($1)
				AND poi_type = ANY($2)
			GROUP BY poi_id, poi_type
		`;

		const reviewResult = await db.query(reviewQuery, [poiIds, poiTypes]);

		// Initialize all POIs with zero interactions
		const interactions: {
			[key: string]: {
				poi_id: number;
				poi_type: string;
				like: { count: number; hasInteraction: boolean };
				visit: { count: number; hasInteraction: boolean };
				favorite: { count: number; hasInteraction: boolean };
				review: { count: number };
			};
		} = {};

		pois.forEach((poi) => {
			const key = `${poi.poi_id}_${poi.poi_type}`;
			interactions[key] = {
				poi_id: poi.poi_id,
				poi_type: poi.poi_type,
				like: { count: 0, hasInteraction: false },
				visit: { count: 0, hasInteraction: false },
				favorite: { count: 0, hasInteraction: false },
				review: { count: 0 },
			};
		});

		// Update with actual interaction data
		result.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				const type = row.interaction_type as 'like' | 'visit' | 'favorite';
				if (interactions[key][type]) {
					interactions[key][type].count = parseInt(row.count) || 0;
					interactions[key][type].hasInteraction =
						row.user_has_interaction || false;
				}
			}
		});

		// Update with review counts
		reviewResult.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				interactions[key].review.count = parseInt(row.review_count) || 0;
			}
		});

		logger.info('Loaded interactions for POIs', {
			userId,
			requestedPOIs: pois.length,
			returnedInteractions: Object.keys(interactions).length,
		});

		return interactions;
	} catch (error) {
		logger.error('Error loading interactions for POIs', { error });
		return {};
	}
}
