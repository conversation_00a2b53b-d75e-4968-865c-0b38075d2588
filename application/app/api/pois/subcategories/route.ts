/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/subcategories - Get subcategories filtered by category
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const category = searchParams.get('category');

		logger.info('Fetching POI subcategories', { category });

		let query = `
      SELECT DISTINCT subcategory, COUNT(*) as count
      FROM spatial_schema.pois
      WHERE subcategory IS NOT NULL AND subcategory != '' AND subcategory != 'null'
    `;
		const params: string[] = [];

		// If category is provided, filter subcategories by that category
		if (category) {
			query += ` AND category = $1`;
			params.push(category);
		}

		query += `
      GROUP BY subcategory
      ORDER BY count DESC, subcategory ASC
    `;

		const result = await db.query(query, params);

		const subcategories = result.rows.map(
			(row: { subcategory: string; count: string }) => ({
				subcategory: row.subcategory,
				count: parseInt(row.count),
			})
		);

		logger.info(
			`Retrieved ${subcategories.length} subcategories for category: ${
				category || 'all'
			}`
		);

		return NextResponse.json({
			success: true,
			subcategories,
			category: category || null,
			total: subcategories.length,
		});
	} catch (error) {
		logger.error('Error fetching POI subcategories', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI subcategories' },
			{ status: 500 }
		);
	}
}
