/** @format */

import { POI_EDITABLE_FIELDS } from '@/app/shared/poi';
import { db } from '@/lib/database';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// POST /api/pois/report - Submit a report for a POI (info update or closed)
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}
		const body = await request.json();
		const { poiType, poiId, reportType, ...fields } = body;
		if (!poiType || !poiId || !reportType) {
			return NextResponse.json(
				{ success: false, error: 'Missing required fields' },
				{ status: 400 }
			);
		}
		// Require originalPoiId for info update/removal
		const { originalPoiId } = body;
		if (
			(reportType === 'info_update' || reportType === 'closed') &&
			!originalPoiId
		) {
			return NextResponse.json(
				{ success: false, error: 'Missing original POI ID' },
				{ status: 400 }
			);
		}
		// Prepare columns and values for info update or closed
		const columns = [
			'submitted_by_user_id',
			'admin_review_status',
			'submission_reason',
		];
		const values = [
			session.user.id,
			'pending',
			reportType === 'info_update' ? 'info_update' : 'closed',
		];
		const placeholders = ['$1', '$2', '$3'];
		let paramIndex = 4;
		if (reportType === 'info_update') {
			// Add all editable fields
			for (const field of POI_EDITABLE_FIELDS) {
				if (fields[field] !== undefined) {
					columns.push(field);
					values.push(fields[field]);
					placeholders.push(`$${paramIndex++}`);
				}
			}
		} else if (reportType === 'closed') {
			columns.push('submission_notes');
			values.push(fields.closed_description || '');
			placeholders.push(`$${paramIndex++}`);
		}
		// Always include poi_type and poi_id for reference, but only if not already present
		if (!columns.includes('category')) {
			columns.push('category');
			values.push(poiType);
			placeholders.push(`$${paramIndex++}`);
		}
		if (!columns.includes('street')) {
			columns.push('street');
			values.push(poiId);
			placeholders.push(`$${paramIndex++}`);
		}
		// Always include original POI type and ID for reference
		if (!columns.includes('original_poi_type')) {
			columns.push('original_poi_type');
			values.push(poiType);
			placeholders.push(`$${paramIndex++}`);
		}
		if (!columns.includes('original_poi_id')) {
			columns.push('original_poi_id');
			values.push(originalPoiId || null);
			placeholders.push(`$${paramIndex++}`);
		}

		// For info updates and closures, also set target_poi_id (used by approval logic)
		if (
			(reportType === 'info_update' || reportType === 'closed') &&
			originalPoiId
		) {
			if (!columns.includes('target_poi_id')) {
				columns.push('target_poi_id');
				values.push(originalPoiId);
				placeholders.push(`$${paramIndex++}`);
			}
		}
		// Insert into user_pois_temp
		await db.query(
			`
      INSERT INTO spatial_schema.user_pois_temp (
        ${columns.join(', ')}
      ) VALUES (${placeholders.join(', ')})
    `,
			values
		);
		return NextResponse.json({ success: true });
	} catch {
		return NextResponse.json(
			{ success: false, error: 'Failed to submit report' },
			{ status: 500 }
		);
	}
}
