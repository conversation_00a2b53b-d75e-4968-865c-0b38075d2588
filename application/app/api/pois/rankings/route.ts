/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const {
			locationType,
			locationName,
			center,
			zoom,
			offset = 0,
			limit = 20,
			category,
			city,
			country,
			// 🚀 OPTIMIZATION: Support for including interactions
			includeInteractions = false,
		} = await request.json();

		if (!locationType || !locationName || !center) {
			return NextResponse.json(
				{ success: false, error: 'Missing required parameters' },
				{ status: 400 }
			);
		}

		// Get user session for favorites and interactions
		const session = await getServerSession(authOptions);
		const userId = session?.user?.id;

		logger.info('Rankings API session info', {
			hasSession: !!session,
			userId: userId || 'anonymous',
			includeInteractions,
		});

		// Calculate search radius based on location type and zoom
		let defaultLimit = 20; // Default limit

		if (locationType === 'country') {
			defaultLimit = 50;
		} else if (locationType === 'city') {
			defaultLimit = 30;
		}

		// Adjust based on zoom level
		if (zoom) {
			const zoomFactor = Math.max(0.1, Math.min(2.0, zoom / 200));
			defaultLimit = Math.floor(defaultLimit * zoomFactor);
		}

		// Use provided limit or default
		const finalLimit = limit || defaultLimit;

		// Use the top_locations function for rankings
		// Don't use adminLevel filtering for country-level requests as it causes issues
		let adminLevel: number | null = null;
		if (locationType === 'city') adminLevel = 6;
		else if (locationType === 'region') adminLevel = 8;
		// For country-level requests, use country parameter instead of adminLevel

		// Map category and subcategory with country support
		const topLocationsQuery = `
      SELECT * FROM spatial_schema.get_top_locations($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
    `;
		const topLocationsParams = [
			category || null, // p_category
			null, // p_subcategory
			city || null, // p_city
			null, // p_district
			null, // p_neighborhood
			country || null, // p_country (NEW)
			null, // p_min_rating
			null, // p_max_rating
			adminLevel, // p_admin_level
			null, // p_admin_name
			null, // p_min_lat
			null, // p_max_lat
			null, // p_min_lng
			null, // p_max_lng
			offset, // p_offset
			finalLimit, // p_limit
		];

		const result = await db.query(topLocationsQuery, topLocationsParams);

		// Add poi_type field to all POIs since they come from the official spatial_schema.pois table
		let poisWithType = result.rows.map((poi) => ({
			...poi,
			poi_type: 'official',
			poi_id: poi.id, // Ensure poi_id is set for consistency
		}));

		// 🚀 OPTIMIZATION: Load interactions if requested
		if (includeInteractions && poisWithType.length > 0) {
			try {
				if (userId) {
					const interactions = await loadInteractionsForPOIs(
						poisWithType,
						userId
					);

					// Merge interaction data with POI data
					poisWithType = poisWithType.map((poi) => {
						const key = `${poi.poi_id}_${poi.poi_type}`;
						const interactionData = interactions[key];

						return {
							...poi,
							// Add interaction data
							like_count: interactionData?.like?.count || 0,
							favorite_count: interactionData?.favorite?.count || 0,
							visit_count: interactionData?.visit?.count || 0,
							review_count: interactionData?.review?.count || 0,
							user_has_liked: interactionData?.like?.hasInteraction || false,
							user_has_favorited:
								interactionData?.favorite?.hasInteraction || false,
							user_has_visited: interactionData?.visit?.hasInteraction || false,
						};
					});
				} else {
					// No user session - add zero interaction counts (like filter API)
					poisWithType = poisWithType.map((poi) => ({
						...poi,
						like_count: 0,
						favorite_count: 0,
						visit_count: 0,
						review_count: 0,
						user_has_liked: false,
						user_has_favorited: false,
						user_has_visited: false,
					}));
				}
			} catch (error) {
				logger.error('Error loading interactions for Rankings POIs', { error });
				// Continue without interactions rather than failing
			}
		}

		return NextResponse.json({
			success: true,
			pois: poisWithType,
			includeInteractions, // Let frontend know if interactions were included
			locationType,
			locationName,
			count: poisWithType.length,
			offset,
			limit: finalLimit,
			hasMore: poisWithType.length === finalLimit,
			lastPOIId:
				poisWithType.length > 0
					? poisWithType[poisWithType.length - 1].id
					: null,
		});
	} catch (error) {
		const errorMeta =
			error instanceof Error
				? { message: error.message, stack: error.stack }
				: { error };
		logger.error('Error fetching POI rankings', errorMeta);
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI rankings' },
			{ status: 500 }
		);
	}
}

/**
 * Load interactions for a batch of POIs
 */
async function loadInteractionsForPOIs(
	pois: Array<{ poi_id: number; poi_type: string }>,
	userId: string
): Promise<{
	[key: string]: {
		poi_id: number;
		poi_type: string;
		like: { count: number; hasInteraction: boolean };
		visit: { count: number; hasInteraction: boolean };
		favorite: { count: number; hasInteraction: boolean };
		review: { count: number };
	};
}> {
	try {
		// Extract POI IDs and types
		const poiIds = pois.map((poi) => poi.poi_id).filter(Boolean);
		const poiTypes = pois.map((poi) => poi.poi_type || 'official');

		if (poiIds.length === 0) {
			return {};
		}

		// Get interaction counts and user states for all POIs
		const batchQuery = `
			SELECT
				poi_id,
				poi_type,
				interaction_type,
				COUNT(*) as count,
				BOOL_OR(CASE WHEN user_id = $1 THEN true ELSE false END) as user_has_interaction
			FROM backend_schema.user_interactions
			WHERE poi_id = ANY($2)
				AND poi_type = ANY($3)
				AND interaction_type IN ('like', 'favorite', 'visit')
			GROUP BY poi_id, poi_type, interaction_type
		`;

		const result = await db.query(batchQuery, [userId, poiIds, poiTypes]);

		// Get review counts separately
		const reviewQuery = `
			SELECT
				poi_id,
				poi_type,
				COUNT(*) as review_count
			FROM backend_schema.user_location_reviews
			WHERE poi_id = ANY($1)
				AND poi_type = ANY($2)
			GROUP BY poi_id, poi_type
		`;

		const reviewResult = await db.query(reviewQuery, [poiIds, poiTypes]);

		// Initialize all POIs with zero interactions
		const interactions: {
			[key: string]: {
				poi_id: number;
				poi_type: string;
				like: { count: number; hasInteraction: boolean };
				visit: { count: number; hasInteraction: boolean };
				favorite: { count: number; hasInteraction: boolean };
				review: { count: number };
			};
		} = {};

		pois.forEach((poi) => {
			const key = `${poi.poi_id}_${poi.poi_type}`;
			interactions[key] = {
				poi_id: poi.poi_id,
				poi_type: poi.poi_type,
				like: { count: 0, hasInteraction: false },
				visit: { count: 0, hasInteraction: false },
				favorite: { count: 0, hasInteraction: false },
				review: { count: 0 },
			};
		});

		// Update with actual interaction data
		result.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				const type = row.interaction_type as 'like' | 'visit' | 'favorite';
				if (interactions[key][type]) {
					interactions[key][type].count = parseInt(row.count) || 0;
					interactions[key][type].hasInteraction =
						row.user_has_interaction || false;
				}
			}
		});

		// Update with review counts
		reviewResult.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				interactions[key].review.count = parseInt(row.review_count) || 0;
			}
		});

		logger.info('Loaded interactions for Rankings POIs', {
			userId,
			requestedPOIs: pois.length,
			returnedInteractions: Object.keys(interactions).length,
		});

		return interactions;
	} catch (error) {
		logger.error('Error loading interactions for Rankings POIs', { error });
		return {};
	}
}
