/** @format */

import { POI_EDITABLE_FIELDS } from '@/app/shared/poi';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// POST /api/pois/submit - Submit new POI for admin review
export async function POST(request: NextRequest) {
	try {
		// Get user session - Authentication required for all POI submissions
		const session = await getServerSession(authOptions);

		if (!session?.user?.id) {
			return NextResponse.json(
				{
					success: false,
					error: 'Authentication required for POI submissions',
				},
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { submission_reason = 'new_poi' } = body;

		const { submission_notes, original_poi_id, ...poiData } = body;

		// Extract POI fields using the constants
		const poiFields: Record<string, unknown> = {};
		for (const field of POI_EDITABLE_FIELDS) {
			if (poiData[field] !== undefined) {
				poiFields[field] = poiData[field];
			}
		}

		// Only require original_poi_id for submissions that are updates/reports of existing POIs
		// For new POI submissions, original_poi_id should be null
		if (submission_reason !== 'new_poi' && !original_poi_id) {
			return NextResponse.json(
				{
					success: false,
					error: 'original_poi_id is required for POI updates and reports',
				},
				{ status: 400 }
			);
		}

		// Use userId from session (required for all submissions)
		const userId = session.user.id;

		// Validation

		if (
			!poiFields.name ||
			!poiFields.category ||
			!poiFields.latitude ||
			!poiFields.longitude
		) {
			return NextResponse.json(
				{
					success: false,
					error: 'Name, category, latitude, and longitude are required',
				},
				{ status: 400 }
			);
		}

		const latitude = Number(poiFields.latitude);
		const longitude = Number(poiFields.longitude);

		if (
			latitude < -90 ||
			latitude > 90 ||
			longitude < -180 ||
			longitude > 180
		) {
			return NextResponse.json(
				{ success: false, error: 'Invalid latitude or longitude' },
				{ status: 400 }
			);
		}

		logger.info('Submitting POI for review', {
			name: poiFields.name,
			category: poiFields.category,
			submission_reason,
		});

		try {
			await db.query('BEGIN');

			// Dynamically build columns and values from POI_EDITABLE_FIELDS
			// Insert into temp table for admin review
			// For new POIs, original_poi_id should be null
			const finalOriginalPoiId =
				submission_reason === 'new_poi' ? null : original_poi_id;

			// Build dynamic query using only the fields we have data for
			const columns = [];
			const values = [];

			// Always add submitted_by_user_id (can be null for anonymous submissions)
			columns.push('submitted_by_user_id');
			values.push(userId);

			// Ensure latitude and longitude are always included and are numbers
			if (!poiFields.latitude || !poiFields.longitude) {
				throw new Error(
					'Latitude and longitude are required for POI submission'
				);
			}

			// Add POI field values with proper type conversion
			for (const field of POI_EDITABLE_FIELDS) {
				const value = poiFields[field];
				if (value !== undefined && value !== null && value !== '') {
					columns.push(field);

					// Ensure proper type conversion
					if (field === 'latitude' || field === 'longitude') {
						values.push(parseFloat(value as string));
					} else {
						values.push(String(value));
					}
				}
			}

			// Add submission metadata if provided
			if (submission_notes && submission_notes.trim() !== '') {
				columns.push('submission_notes');
				values.push(String(submission_notes));
			}

			if (finalOriginalPoiId) {
				columns.push('original_poi_id');
				values.push(parseInt(finalOriginalPoiId));

				// For info updates and closures, also set target_poi_id (used by approval logic)
				if (
					submission_reason === 'info_update' ||
					submission_reason === 'closed'
				) {
					columns.push('target_poi_id');
					values.push(parseInt(finalOriginalPoiId));
				}
			}

			// Add geom column
			columns.push('geom');

			// Create placeholders for the query
			const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

			// Find the actual parameter positions for latitude and longitude
			let latParamIndex = -1;
			let lonParamIndex = -1;

			for (let i = 0; i < columns.length - 1; i++) {
				// -1 because geom is last
				if (columns[i] === 'latitude') {
					latParamIndex = i + 1;
				}
				if (columns[i] === 'longitude') {
					lonParamIndex = i + 1;
				}
			}

			if (latParamIndex === -1 || lonParamIndex === -1) {
				throw new Error('Latitude and longitude parameters not found');
			}

			// Extract latitude and longitude values for the geom calculation
			const latValue = values[latParamIndex - 1]; // Convert to 0-based index
			const lonValue = values[lonParamIndex - 1]; // Convert to 0-based index

			const poiResult = await db.query(
				`
        INSERT INTO spatial_schema.user_pois_temp (${columns.join(', ')})
        VALUES (${placeholders}, ST_SetSRID(ST_Point($${values.length + 1}, $${
					values.length + 2
				}), 4326))
        RETURNING id
      `,
				[...values, lonValue, latValue]
			);

			const tempPoiId = poiResult.rows[0].id;

			// ✅ Clean 3-table system: No additional tracking needed
			// The user_pois_temp table contains all submission data
			// Approval tracking will be handled in user_pois_approved when agent approves

			await db.query('COMMIT');

			logger.info('POI submitted successfully', { tempPoiId, userId });

			return NextResponse.json({
				success: true,
				message: 'POI submitted for admin review',
				temp_poi_id: tempPoiId,
				status: 'pending_review',
			});
		} catch (dbError) {
			await db.query('ROLLBACK');
			throw dbError;
		}
	} catch (error) {
		logger.error('Error submitting POI', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to submit POI' },
			{ status: 500 }
		);
	}
}

// GET /api/pois/submit - Get user's POI submissions
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const userId = searchParams.get('userId');
		const status = searchParams.get('status'); // pending, approved, rejected
		const limit = parseInt(searchParams.get('limit') || '50');

		if (!userId) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		logger.info('Fetching user POI submissions', { userId, status, limit });

		let statusFilter = '';
		const queryParams: (string | number)[] = [userId];

		if (status) {
			// Map status to admin_review_status for the clean schema
			if (status === 'pending') {
				statusFilter =
					"AND (t.admin_review_status IS NULL OR t.admin_review_status = 'pending')";
			} else if (status === 'approved') {
				statusFilter = "AND t.admin_review_status = 'approved'";
			} else if (status === 'rejected') {
				statusFilter = "AND t.admin_review_status = 'rejected'";
			}
		}

		// ✅ Clean 3-table system: Query directly from user_pois_temp
		const query = `
      SELECT
        t.id as submission_id,
        t.submission_type,
        t.submission_reason,
        CASE
          WHEN t.admin_review_status = 'approved' THEN 'approved'
          WHEN t.admin_review_status = 'rejected' THEN 'rejected'
          ELSE 'pending'
        END as status,
        t.admin_review_notes as admin_feedback,
        t.created_at as submitted_at,
        t.updated_at as reviewed_at,
        t.id as temp_poi_id,
        t.name,
        t.category,
        t.subcategory,
        t.latitude,
        t.longitude,
        t.city,
        t.district,
        t.phone_number,
        t.opening_hours,
        t.description,
        t.submission_notes,
        t.admin_review_status,
        t.admin_review_notes,
        t.created_at as poi_created_at
      FROM spatial_schema.user_pois_temp t
      WHERE t.submitted_by_user_id = $1
      ${statusFilter}
      ORDER BY t.created_at DESC
      LIMIT $2
    `;

		queryParams.push(limit);

		const result = await db.query(query, queryParams);

		// Group by status for summary
		const statusSummary = result.rows.reduce(
			(acc: Record<string, number>, row: { status: string }) => {
				const status = row.status;
				if (!acc[status]) {
					acc[status] = 0;
				}
				acc[status]++;
				return acc;
			},
			{}
		);

		logger.info(`Retrieved ${result.rows.length} POI submissions`);

		return NextResponse.json({
			success: true,
			submissions: result.rows,
			count: result.rows.length,
			status_summary: statusSummary,
		});
	} catch (error) {
		logger.error('Error fetching POI submissions', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI submissions' },
			{ status: 500 }
		);
	}
}
