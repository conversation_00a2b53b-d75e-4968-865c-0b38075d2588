/** @format */

import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/globe - Get POIs for globe visualization using new top_location function
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const category = searchParams.get('category');
		const subcategory = searchParams.get('subcategory');
		const city = searchParams.get('city');
		const district = searchParams.get('district');
		const neighborhood = searchParams.get('neighborhood');
		const country = searchParams.get('country'); // NEW: Country parameter
		const offset = searchParams.get('offset')
			? parseInt(searchParams.get('offset')!)
			: 0;
		const limit = searchParams.get('limit')
			? parseInt(searchParams.get('limit')!)
			: 100;

		// 🚀 OPTIMIZATION: Support for including interactions
		const includeInteractions =
			searchParams.get('includeInteractions') === 'true';

		// Log the filters
		logger.info('Fetching top locations for globe', {
			category,
			subcategory,
			city,
			district,
			neighborhood,
			country,
			offset,
			limit,
		});

		// Build dynamic SQL and params for get_top_locations
		let sql = 'SELECT * FROM spatial_schema.get_top_locations(';
		const params: (string | number | null)[] = [];
		let paramIndex = 1;
		const addParam = (val: string | number | null) => {
			params.push(val);
			sql += `$${paramIndex}, `;
			paramIndex++;
		};
		// Order: p_category, p_subcategory, p_city, p_district, p_neighborhood, p_country, p_min_rating, p_max_rating, p_admin_level, p_admin_name, p_min_lat, p_max_lat, p_min_lng, p_max_lng, p_offset, p_limit
		addParam(category);
		addParam(subcategory);
		addParam(city);
		addParam(district);
		addParam(neighborhood);
		addParam(country); // NEW: Country parameter
		sql += 'NULL, NULL, NULL, NULL, '; // p_min_rating, p_max_rating, p_admin_level, p_admin_name
		sql += 'NULL, NULL, NULL, NULL, '; // p_min_lat, p_max_lat, p_min_lng, p_max_lng
		addParam(offset);
		addParam(limit);
		sql = sql.replace(/, $/, '') + ')';

		// Remove trailing comma if present
		const result = await db.query(sql, params);

		// Add poi_type and poi_id fields to match expected structure
		let poisWithType = result.rows.map((poi: Record<string, unknown>) => ({
			...poi,
			poi_type: 'official', // All POIs from spatial_schema.pois are official
			poi_id: poi.id, // Map id to poi_id for consistency
		}));

		// 🚀 OPTIMIZATION: Load interactions if requested
		if (includeInteractions && poisWithType.length > 0) {
			try {
				// Get user session for interaction data
				const session = await getServerSession(authOptions);
				const userId = session?.user?.id;

				if (userId) {
					const interactions = await loadInteractionsForPOIs(
						poisWithType,
						userId
					);

					// Merge interaction data with POI data
					poisWithType = poisWithType.map((poi) => {
						const key = `${poi.poi_id}_${poi.poi_type}`;
						const interactionData = interactions[key];

						return {
							...poi,
							// Add interaction data
							like_count: interactionData?.like?.count || 0,
							favorite_count: interactionData?.favorite?.count || 0,
							visit_count: interactionData?.visit?.count || 0,
							review_count: interactionData?.review?.count || 0,
							user_has_liked: interactionData?.like?.hasInteraction || false,
							user_has_favorited:
								interactionData?.favorite?.hasInteraction || false,
							user_has_visited: interactionData?.visit?.hasInteraction || false,
						};
					});
				}
			} catch (error) {
				logger.error('Error loading interactions for Globe POIs', { error });
				// Continue without interactions rather than failing
			}
		}

		return NextResponse.json({
			success: true,
			pois: poisWithType,
			includeInteractions, // Let frontend know if interactions were included
			filters: {
				category,
				subcategory,
				city,
				district,
				neighborhood,
				country,
				offset,
				limit,
			},
		});
	} catch (error) {
		logger.error('Error fetching top locations for globe', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch top locations' },
			{ status: 500 }
		);
	}
}

// POST /api/pois/globe - Advanced globe query with JSON body
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		let { minLat, maxLat, minLng, maxLng, limit = 100 } = body;
		// Force number conversion
		minLat = Number(minLat);
		maxLat = Number(maxLat);
		minLng = Number(minLng);
		maxLng = Number(maxLng);
		limit = Number(limit);

		// If bounding box is provided and valid, use it
		if (!isNaN(minLat) && !isNaN(maxLat) && !isNaN(minLng) && !isNaN(maxLng)) {
			const sql = `SELECT * FROM spatial_schema.get_top_locations(
        NULL, NULL, NULL, NULL, NULL, NULL, -- category, subcategory, city, district, neighborhood, country
        NULL, NULL, NULL, NULL,             -- min_rating, max_rating, admin_level, admin_name
        $1, $2, $3, $4,                     -- min_lat, max_lat, min_lng, max_lng
        0, $5                               -- offset, limit
      )`;
			const params = [minLat, maxLat, minLng, maxLng, limit];
			const result = await db.query(sql, params);

			// Add poi_type and poi_id fields to match expected structure
			const poisWithType = result.rows.map((poi: Record<string, unknown>) => ({
				...poi,
				poi_type: 'official', // All POIs from spatial_schema.pois are official
				poi_id: poi.id, // Map id to poi_id for consistency
			}));

			return NextResponse.json({
				success: true,
				pois: poisWithType,
				filters: { minLat, maxLat, minLng, maxLng, limit },
			});
		} else {
			// Debug: log invalid bounding box
			console.warn(
				'Invalid bounding box params, falling back to legacy logic.',
				{ minLat, maxLat, minLng, maxLng }
			);
		}
		// Fallback: use the GET handler for legacy/side panel
		const { zoom = 10, bounds, center } = body;
		const searchParams = new URLSearchParams();
		searchParams.set('zoom', zoom.toString());
		if (center) {
			searchParams.set('lat', center.lat.toString());
			searchParams.set('lng', center.lng.toString());
		}
		if (bounds) {
			searchParams.set('bounds', bounds);
		}
		searchParams.set('limit', limit.toString());
		const mockRequest = new Request(
			`http://localhost/api/pois/globe?${searchParams.toString()}`
		);
		const { NextRequest } = await import('next/server');
		const nextRequest = new NextRequest(mockRequest.url, mockRequest);
		return GET(nextRequest);
	} catch (error) {
		logger.error('Error in advanced globe POI query', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to process advanced globe query' },
			{ status: 500 }
		);
	}
}

/**
 * Load interactions for a batch of POIs
 */
async function loadInteractionsForPOIs(
	pois: Array<{ poi_id: number; poi_type: string }>,
	userId: string
): Promise<{
	[key: string]: {
		poi_id: number;
		poi_type: string;
		like: { count: number; hasInteraction: boolean };
		visit: { count: number; hasInteraction: boolean };
		favorite: { count: number; hasInteraction: boolean };
		review: { count: number };
	};
}> {
	try {
		// Extract POI IDs and types
		const poiIds = pois.map((poi) => poi.poi_id).filter(Boolean);
		const poiTypes = pois.map((poi) => poi.poi_type || 'official');

		if (poiIds.length === 0) {
			return {};
		}

		// Get interaction counts and user states for all POIs
		const batchQuery = `
			SELECT
				poi_id,
				poi_type,
				interaction_type,
				COUNT(*) as count,
				BOOL_OR(CASE WHEN user_id = $1 THEN true ELSE false END) as user_has_interaction
			FROM backend_schema.user_interactions
			WHERE poi_id = ANY($2)
				AND poi_type = ANY($3)
				AND interaction_type IN ('like', 'favorite', 'visit')
			GROUP BY poi_id, poi_type, interaction_type
		`;

		const result = await db.query(batchQuery, [userId, poiIds, poiTypes]);

		// Get review counts separately
		const reviewQuery = `
			SELECT
				poi_id,
				poi_type,
				COUNT(*) as review_count
			FROM backend_schema.user_location_reviews
			WHERE poi_id = ANY($1)
				AND poi_type = ANY($2)
			GROUP BY poi_id, poi_type
		`;

		const reviewResult = await db.query(reviewQuery, [poiIds, poiTypes]);

		// Initialize all POIs with zero interactions
		const interactions: {
			[key: string]: {
				poi_id: number;
				poi_type: string;
				like: { count: number; hasInteraction: boolean };
				visit: { count: number; hasInteraction: boolean };
				favorite: { count: number; hasInteraction: boolean };
				review: { count: number };
			};
		} = {};

		pois.forEach((poi) => {
			const key = `${poi.poi_id}_${poi.poi_type}`;
			interactions[key] = {
				poi_id: poi.poi_id,
				poi_type: poi.poi_type,
				like: { count: 0, hasInteraction: false },
				visit: { count: 0, hasInteraction: false },
				favorite: { count: 0, hasInteraction: false },
				review: { count: 0 },
			};
		});

		// Update with actual interaction data
		result.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				const type = row.interaction_type as 'like' | 'visit' | 'favorite';
				if (interactions[key][type]) {
					interactions[key][type].count = parseInt(row.count) || 0;
					interactions[key][type].hasInteraction =
						row.user_has_interaction || false;
				}
			}
		});

		// Update with review counts
		reviewResult.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				interactions[key].review.count = parseInt(row.review_count) || 0;
			}
		});

		logger.info('Loaded interactions for Globe POIs', {
			userId,
			requestedPOIs: pois.length,
			returnedInteractions: Object.keys(interactions).length,
		});

		return interactions;
	} catch (error) {
		logger.error('Error loading interactions for Globe POIs', { error });
		return {};
	}
}
