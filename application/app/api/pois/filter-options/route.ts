/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		logger.info('Fetching POI filter options from database');

		// Get unique values for each filter type from the actual POI data
		const [
			citiesResult,
			districtsResult,
			countriesResult,
			categoriesResult,
			subcategoriesResult,
			cuisinesResult,
		] = await Promise.all([
			// Cities - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT city 
        FROM spatial_schema.pois 
        WHERE city IS NOT NULL AND city != '' AND city != 'null'
        ORDER BY city ASC
        LIMIT 100
      `),

			// Districts - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT district
        FROM spatial_schema.pois
        WHERE district IS NOT NULL AND district != '' AND district != 'null'
        ORDER BY district ASC
        LIMIT 100
      `),

			// Countries - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT country
        FROM spatial_schema.pois
        WHERE country IS NOT NULL AND country != '' AND country != 'null'
        ORDER BY country ASC
        LIMIT 100
      `),

			// Categories - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT category 
        FROM spatial_schema.pois 
        WHERE category IS NOT NULL AND category != '' AND category != 'null'
        ORDER BY category ASC
        LIMIT 100
      `),

			// Subcategories - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT subcategory 
        FROM spatial_schema.pois 
        WHERE subcategory IS NOT NULL AND subcategory != '' AND subcategory != 'null'
        ORDER BY subcategory ASC
        LIMIT 100
      `),

			// Cuisines - from spatial_schema.pois
			db.query(`
        SELECT DISTINCT cuisine 
        FROM spatial_schema.pois 
        WHERE cuisine IS NOT NULL AND cuisine != '' AND cuisine != 'null'
        ORDER BY cuisine ASC
        LIMIT 100
      `),
		]);

		// Extract the values from the results
		const cities = citiesResult.rows.map((row) => row.city).filter(Boolean);
		const districts = districtsResult.rows
			.map((row) => row.district)
			.filter(Boolean);
		const countries = countriesResult.rows
			.map((row) => row.country)
			.filter(Boolean);
		const categories = categoriesResult.rows
			.map((row) => row.category)
			.filter(Boolean);
		const subcategories = subcategoriesResult.rows
			.map((row) => row.subcategory)
			.filter(Boolean);
		const cuisines = cuisinesResult.rows
			.map((row) => row.cuisine)
			.filter(Boolean);

		logger.info('Filter options retrieved', {
			cities: cities.length,
			districts: districts.length,
			countries: countries.length,
			categories: categories.length,
			subcategories: subcategories.length,
			cuisines: cuisines.length,
		});

		return NextResponse.json({
			success: true,
			cities,
			districts,
			countries,
			categories,
			subcategories,
			cuisines,
		});
	} catch (error) {
		const errorMeta =
			error instanceof Error
				? { message: error.message, stack: error.stack }
				: { error };
		logger.error('Error fetching filter options', errorMeta);
		console.error('Error fetching filter options:', error);
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch filter options' },
			{ status: 500 }
		);
	}
}
