/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { resolveToUserId } from '@/lib/username-helpers';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/nearby - Get POIs near user location
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		// Parse query parameters
		const latitude = parseFloat(searchParams.get('lat') || '0');
		const longitude = parseFloat(searchParams.get('lng') || '0');
		const radius = parseFloat(searchParams.get('radius') || '1000'); // meters
		const categories =
			searchParams.get('categories')?.split(',').filter(Boolean) || [];
		const subcategories =
			searchParams.get('subcategories')?.split(',').filter(Boolean) || [];
		const limit = parseInt(searchParams.get('limit') || '50');
		const userId = searchParams.get('userId');
		const username = searchParams.get('username');

		if (!latitude || !longitude) {
			return NextResponse.json(
				{ success: false, error: 'Latitude and longitude are required' },
				{ status: 400 }
			);
		}

		// Resolve username to userId if needed
		let resolvedUserId = userId;
		if (!resolvedUserId && username) {
			resolvedUserId = await resolveToUserId(username);
			if (!resolvedUserId) {
				return NextResponse.json(
					{ success: false, error: 'User not found' },
					{ status: 404 }
				);
			}
		}

		logger.info('Finding nearby POIs', {
			latitude,
			longitude,
			radius,
			categories: categories.length,
			subcategories: subcategories.length,
			limit,
		});

		// Build category and subcategory filters
		let categoryFilter = '';
		const queryParams: (number | string[])[] = [latitude, longitude, radius];

		if (categories.length > 0) {
			categoryFilter += 'AND category = ANY($4)';
			queryParams.push(categories);
		}

		if (subcategories.length > 0) {
			const paramIndex = queryParams.length + 1;
			categoryFilter += ` AND subcategory = ANY($${paramIndex})`;
			queryParams.push(subcategories);
		}

		// Add user ID if provided
		if (resolvedUserId) {
			queryParams.push(parseInt(resolvedUserId));
		}

		// Query nearby POIs using spatial distance calculation
		const query = `
      WITH nearby_pois AS (
        -- Official POIs
        SELECT
          'official' as poi_type,
          id as poi_id,
          null as temp_id,
          null as approved_id,
          name,
          category,
          subcategory,
          latitude,
          longitude,
          full_address as address,
          city,
          district,
          phone_number,
          opening_hours,
          ST_Distance(
            ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
            ST_SetSRID(ST_Point($2, $1), 4326)::geography
          ) as distance_meters
        FROM spatial_schema.pois
        WHERE ST_DWithin(
          ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
          ST_SetSRID(ST_Point($2, $1), 4326)::geography,
          $3
        )
        ${categoryFilter}

        UNION ALL

        -- Approved User POIs
        SELECT
          'user_approved' as poi_type,
          null as poi_id,
          null as temp_id,
          id as approved_id,
          name,
          category,
          subcategory,
          latitude,
          longitude,
          full_address as address,
          city,
          district,
          phone_number,
          opening_hours,
          ST_Distance(
            ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
            ST_SetSRID(ST_Point($2, $1), 4326)::geography
          ) as distance_meters
        FROM spatial_schema.user_pois_approved
        WHERE ST_DWithin(
          ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
          ST_SetSRID(ST_Point($2, $1), 4326)::geography,
          $3
        )
        ${categoryFilter}

        ${
					resolvedUserId
						? `
        UNION ALL

        -- User's own temp POIs
        SELECT
          'user_temp' as poi_type,
          null as poi_id,
          id as temp_id,
          null as approved_id,
          name,
          category,
          subcategory,
          latitude,
          longitude,
          full_address as address,
          city,
          district,
          phone_number,
          opening_hours,
          ST_Distance(
            ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
            ST_SetSRID(ST_Point($2, $1), 4326)::geography
          ) as distance_meters
        FROM spatial_schema.user_pois_temp
        WHERE ST_DWithin(
          ST_SetSRID(ST_Point(longitude, latitude), 4326)::geography,
          ST_SetSRID(ST_Point($2, $1), 4326)::geography,
          $3
        )
        ${categoryFilter}
        AND submitted_by_user_id = $${queryParams.length}
        `
						: ''
				}
      )
      SELECT *
      FROM nearby_pois
      ORDER BY distance_meters ASC
      LIMIT $${queryParams.length + (resolvedUserId ? 0 : 1)}
    `;

		queryParams.push(limit);

		const result = await db.query(query, queryParams);

		// Add favorites information if user is provided
		let poisWithFavorites = result.rows;
		if (resolvedUserId && result.rows.length > 0) {
			const poiIds = result.rows
				.filter((poi) => poi.poi_type === 'official' && poi.poi_id)
				.map((poi) => poi.poi_id);

			const approvedIds = result.rows
				.filter((poi) => poi.poi_type === 'user_approved' && poi.approved_id)
				.map((poi) => poi.approved_id);

			if (poiIds.length > 0 || approvedIds.length > 0) {
				const favoritesQuery = `
          SELECT poi_id, user_poi_approved_id, poi_type
          FROM backend_schema.user_favorites
          WHERE user_id = $1 
          AND (
            (poi_id = ANY($2) AND poi_type = 'official') OR
            (user_poi_approved_id = ANY($3) AND poi_type = 'user_approved')
          )
        `;

				const favorites = await db.query(favoritesQuery, [
					resolvedUserId,
					poiIds.length > 0 ? poiIds : [null],
					approvedIds.length > 0 ? approvedIds : [null],
				]);

				const favoriteSet = new Set(
					favorites.rows.map((fav) =>
						fav.poi_type === 'official'
							? `official_${fav.poi_id}`
							: `user_approved_${fav.user_poi_approved_id}`
					)
				);

				poisWithFavorites = result.rows.map((poi) => ({
					...poi,
					is_favorite: favoriteSet.has(
						poi.poi_type === 'official'
							? `official_${poi.poi_id}`
							: poi.poi_type === 'user_approved'
							? `user_approved_${poi.approved_id}`
							: ''
					),
					distance_km: Math.round(poi.distance_meters / 10) / 100, // Convert to km with 2 decimal places
				}));
			}
		} else {
			poisWithFavorites = result.rows.map((poi) => ({
				...poi,
				is_favorite: false,
				distance_km: Math.round(poi.distance_meters / 10) / 100,
			}));
		}

		logger.info(`Found ${poisWithFavorites.length} nearby POIs`);

		return NextResponse.json({
			success: true,
			pois: poisWithFavorites,
			count: poisWithFavorites.length,
			center: { latitude, longitude },
			radius_meters: radius,
		});
	} catch (error) {
		logger.error('Error finding nearby POIs', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to find nearby POIs' },
			{ status: 500 }
		);
	}
}
