/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

/**
 * PUT /api/pois/[poiType]/[poiId]/profile
 * Update POI profile information (description, profile picture, etc.)
 */
export async function PUT(
	request: NextRequest,
	context: { params: Promise<{ poiType: string; poiId: string }> }
) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { poiType, poiId } = await context.params;
		const { description, profile_picture_url } = await request.json();

		// Validate POI type
		if (!['official', 'user_temp', 'user_approved'].includes(poiType)) {
			return NextResponse.json({ error: 'Invalid POI type' }, { status: 400 });
		}

		// Check if user has permission to edit this POI
		let hasPermission = false;
		let tableName = '';
		let idField = '';

		switch (poiType) {
			case 'official':
				tableName = 'spatial_schema.pois';
				idField = 'poi_id';
				// For official POIs, only admins can edit (for now, allow any authenticated user)
				hasPermission = true;
				break;
			case 'user_temp':
				tableName = 'spatial_schema.user_pois_temp';
				idField = 'temp_id';
				// Check if user owns this temp POI
				const tempResult = await db.query(
					`SELECT created_by FROM ${tableName} WHERE ${idField} = $1`,
					[poiId]
				);
				hasPermission = tempResult.rows[0]?.created_by === session.user.id;
				break;
			case 'user_approved':
				tableName = 'spatial_schema.user_pois_approved';
				idField = 'approved_id';
				// Check if user owns this approved POI
				const approvedResult = await db.query(
					`SELECT created_by FROM ${tableName} WHERE ${idField} = $1`,
					[poiId]
				);
				hasPermission = approvedResult.rows[0]?.created_by === session.user.id;
				break;
		}

		if (!hasPermission) {
			return NextResponse.json(
				{ error: 'Forbidden: You do not have permission to edit this POI' },
				{ status: 403 }
			);
		}

		// Build update object
		const updateData: Record<string, string> = {};
		if (description !== undefined) updateData.description = description;
		if (profile_picture_url !== undefined)
			updateData.profile_picture_url = profile_picture_url;

		if (Object.keys(updateData).length === 0) {
			return NextResponse.json(
				{ error: 'No valid fields to update' },
				{ status: 400 }
			);
		}

		// Add updated timestamp
		updateData.updated_at = new Date().toISOString();

		// Build update query
		const setClause = Object.keys(updateData)
			.map((key, index) => `${key} = $${index + 2}`)
			.join(', ');

		const values = [poiId, ...Object.values(updateData)];

		const updateQuery = `
      UPDATE ${tableName} 
      SET ${setClause}
      WHERE ${idField} = $1
      RETURNING *
    `;

		const result = await db.query(updateQuery, values);

		if (result.rows.length === 0) {
			return NextResponse.json({ error: 'POI not found' }, { status: 404 });
		}

		logger.info('POI profile updated successfully', {
			userId: session.user.id,
			poiType,
			poiId,
			updatedFields: Object.keys(updateData),
		});

		return NextResponse.json({
			success: true,
			poi: result.rows[0],
			message: 'POI profile updated successfully',
		});
	} catch (error) {
		logger.error('Error updating POI profile', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});

		return NextResponse.json(
			{ error: 'Failed to update POI profile' },
			{ status: 500 }
		);
	}
}

/**
 * GET /api/pois/[poiType]/[poiId]/profile
 * Get POI profile information with statistics
 */
export async function GET(
	request: NextRequest,
	context: { params: Promise<{ poiType: string; poiId: string }> }
) {
	try {
		const { poiType, poiId } = await context.params;

		// Validate POI type
		if (!['official', 'user_temp', 'user_approved'].includes(poiType)) {
			return NextResponse.json({ error: 'Invalid POI type' }, { status: 400 });
		}

		let tableName = '';
		let idField = '';

		switch (poiType) {
			case 'official':
				tableName = 'spatial_schema.pois';
				idField = 'poi_id';
				break;
			case 'user_temp':
				tableName = 'spatial_schema.user_pois_temp';
				idField = 'temp_id';
				break;
			case 'user_approved':
				tableName = 'spatial_schema.user_pois_approved';
				idField = 'approved_id';
				break;
		}

		// Get POI basic info
		const poiResult = await db.query(
			`SELECT * FROM ${tableName} WHERE ${idField} = $1`,
			[poiId]
		);

		if (poiResult.rows.length === 0) {
			return NextResponse.json({ error: 'POI not found' }, { status: 404 });
		}

		const poi = poiResult.rows[0];

		// Get media count
		const mediaCountResult = await db.query(
			'SELECT COUNT(*) as media_count FROM spatial_schema.poi_media WHERE poi_id = $1',
			[poiId]
		);

		// Get interaction counts
		const statsResult = await db.query(
			`
      SELECT 
        COALESCE(SUM(like_count), 0) as total_likes,
        COALESCE(SUM(favorite_count), 0) as total_favorites,
        COALESCE(SUM(view_count), 0) as total_views
      FROM spatial_schema.poi_media 
      WHERE poi_id = $1
    `,
			[poiId]
		);

		const visitCountResult = await db.query(
			'SELECT COUNT(*) as visit_count FROM spatial_schema.poi_visits WHERE poi_id = $1',
			[poiId]
		);

		const reviewCountResult = await db.query(
			'SELECT COUNT(*) as review_count FROM spatial_schema.poi_reviews WHERE poi_id = $1',
			[poiId]
		);

		// Compile profile data
		const profile = {
			...poi,
			media_count: parseInt(mediaCountResult.rows[0]?.media_count || '0'),
			like_count: parseInt(statsResult.rows[0]?.total_likes || '0'),
			favorite_count: parseInt(statsResult.rows[0]?.total_favorites || '0'),
			view_count: parseInt(statsResult.rows[0]?.total_views || '0'),
			visit_count: parseInt(visitCountResult.rows[0]?.visit_count || '0'),
			reviews_count: parseInt(reviewCountResult.rows[0]?.review_count || '0'),
		};

		return NextResponse.json({
			success: true,
			profile,
		});
	} catch (error) {
		logger.error('Error retrieving POI profile', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});

		return NextResponse.json(
			{ error: 'Failed to retrieve POI profile' },
			{ status: 500 }
		);
	}
}
