/** @format */

import { NextRequest, NextResponse } from 'next/server';
import { Pool } from 'pg';

// Database connection
const pool = new Pool({
	connectionString: process.env.DATABASE_URL,
	ssl:
		process.env.NODE_ENV === 'production'
			? { rejectUnauthorized: false }
			: false,
});

// GET /api/pois/interactions/unified - Get all interaction data for a POI in a single call
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const poiId = searchParams.get('poiId');
		const poiType = searchParams.get('poiType') || 'official';
		const userPoiTempId = searchParams.get('userPoiTempId');
		const userPoiApprovedId = searchParams.get('userPoiApprovedId');
		const userId = searchParams.get('userId');

		// Validate POI identifier
		if (poiType === 'official' && !poiId) {
			return NextResponse.json(
				{ success: false, error: 'POI ID is required for official POIs' },
				{ status: 400 }
			);
		}

		if (poiType === 'user_temp' && !userPoiTempId) {
			return NextResponse.json(
				{
					success: false,
					error: 'User POI temp ID is required for user temp POIs',
				},
				{ status: 400 }
			);
		}

		if (poiType === 'user_approved' && !userPoiApprovedId) {
			return NextResponse.json(
				{
					success: false,
					error: 'User POI approved ID is required for user approved POIs',
				},
				{ status: 400 }
			);
		}

		const client = await pool.connect();

		try {
			// Build WHERE clause based on POI type
			let whereClause = '';
			let poiIdValue: string | null = null;

			if (poiType === 'official') {
				whereClause = 'poi_id = $1 AND poi_type = $2';
				poiIdValue = poiId;
			} else if (poiType === 'user_temp') {
				whereClause = 'user_poi_temp_id = $1 AND poi_type = $2';
				poiIdValue = userPoiTempId;
			} else if (poiType === 'user_approved') {
				whereClause = 'user_poi_approved_id = $1 AND poi_type = $2';
				poiIdValue = userPoiApprovedId;
			}

			// Get interaction counts
			const countsQuery = `
				SELECT
					interaction_type,
					COUNT(*) as count
				FROM backend_schema.user_interactions
				WHERE ${whereClause}
				GROUP BY interaction_type
			`;

			const countsResult = await client.query(countsQuery, [
				poiIdValue,
				poiType,
			]);

			// Initialize counts
			const counts = {
				like_count: 0,
				favorite_count: 0,
				visit_count: 0,
			};

			// Process counts
			countsResult.rows.forEach((row) => {
				if (row.interaction_type === 'like') {
					counts.like_count = parseInt(row.count);
				} else if (row.interaction_type === 'favorite') {
					counts.favorite_count = parseInt(row.count);
				} else if (row.interaction_type === 'visit') {
					counts.visit_count = parseInt(row.count);
				}
			});

			// Get review count
			const reviewCountQuery = `
				SELECT COUNT(*) as review_count
				FROM backend_schema.user_location_reviews
				WHERE ${whereClause}
			`;

			const reviewCountResult = await client.query(reviewCountQuery, [
				poiIdValue,
				poiType,
			]);
			const review_count = parseInt(
				reviewCountResult.rows[0]?.review_count || 0
			);

			// Get user interaction states if userId is provided
			let userStates = {
				user_has_liked: false,
				user_has_favorited: false,
				user_has_visited: false,
			};

			if (userId) {
				const userStatesQuery = `
					SELECT interaction_type
					FROM backend_schema.user_interactions
					WHERE ${whereClause} AND user_id = $3
				`;

				const userStatesResult = await client.query(userStatesQuery, [
					poiIdValue,
					poiType,
					userId,
				]);

				userStatesResult.rows.forEach((row) => {
					if (row.interaction_type === 'like') {
						userStates.user_has_liked = true;
					} else if (row.interaction_type === 'favorite') {
						userStates.user_has_favorited = true;
					} else if (row.interaction_type === 'visit') {
						userStates.user_has_visited = true;
					}
				});
			}

			// Combine all data
			const data = {
				...counts,
				review_count,
				...userStates,
			};

			console.log(
				`[${new Date().toISOString()}] INFO: Unified interactions loaded | Meta:`,
				{
					poiType,
					poiId: poiIdValue,
					userId: userId || 'anonymous',
					data,
				}
			);

			return NextResponse.json({
				success: true,
				data,
			});
		} finally {
			client.release();
		}
	} catch (error) {
		console.error('Error in unified interactions API:', error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Internal server error',
			},
			{ status: 500 }
		);
	}
}
