/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextRequest, NextResponse } from 'next/server';

// Types for the batch media API
interface BatchPOIRequest {
	poi_id: number;
	poi_type: 'official' | 'user_temp' | 'user_approved';
}

interface MediaRecord {
	id: string;
	poi_id: string;
	user_id: string;
	media_type: 'photo' | 'video' | 'audio';
	media_url: string;
	thumbnail_url?: string;
	caption?: string;
	metadata?: Record<string, unknown>;
	is_verified: boolean;
	like_count: number;
	created_at: string;
	updated_at?: string;
}

// POST /api/pois/media/batch - Get media for multiple POIs in a single request
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { pois } = body;

		if (!pois || !Array.isArray(pois) || pois.length === 0) {
			return NextResponse.json(
				{ success: false, error: 'POIs array is required' },
				{ status: 400 }
			);
		}

		// Limit batch size to prevent abuse
		if (pois.length > 50) {
			return NextResponse.json(
				{ success: false, error: 'Maximum 50 POIs per batch' },
				{ status: 400 }
			);
		}

		logger.info('Batch loading POI media', {
			poiCount: pois.length,
		});

		// Extract POI IDs and types
		const poiIds = pois
			.map((poi: BatchPOIRequest) => poi.poi_id)
			.filter(Boolean);
		const poiTypes = pois.map(
			(poi: BatchPOIRequest) => poi.poi_type || 'official'
		);

		logger.info('POI IDs being queried for media', {
			poiIds,
			poiTypes,
		});

		if (poiIds.length === 0) {
			return NextResponse.json({
				success: true,
				media: {},
			});
		}

		// Build batch query for media (matching individual API - no is_verified filter)
		const mediaQuery = `
			SELECT
				pm.*,
				p.name as poi_name,
				p.city as poi_city,
				p.district as poi_district,
				u.username as uploaded_by_username,
				u.name as uploaded_by_name,
				COALESCE(like_counts.like_count, 0) as like_count,
				COALESCE(fav_counts.favorite_count, 0) as favorite_count
			FROM spatial_schema.poi_media pm
			LEFT JOIN spatial_schema.pois p ON pm.poi_id = p.id
			LEFT JOIN backend_schema.nextauth_users u ON pm.user_id = u.id
			LEFT JOIN (
				SELECT media_id, COUNT(*) as like_count
				FROM spatial_schema.poi_media_likes
				GROUP BY media_id
			) like_counts ON pm.id = like_counts.media_id
			LEFT JOIN (
				SELECT media_id, COUNT(*) as favorite_count
				FROM spatial_schema.poi_media_favorites
				GROUP BY media_id
			) fav_counts ON pm.id = fav_counts.media_id
			WHERE pm.poi_id = ANY($1)
			ORDER BY pm.poi_id, pm.created_at DESC
		`;

		const mediaResult = await db.query(mediaQuery, [poiIds]);

		logger.info('Media query result', {
			rowCount: mediaResult.rows.length,
			sampleRows: mediaResult.rows.slice(0, 2), // Log first 2 rows for debugging
		});

		// Group media by POI ID
		const mediaByPoi: Record<string, MediaRecord[]> = {};

		// Initialize all POI IDs with empty arrays
		poiIds.forEach((poiId: number) => {
			mediaByPoi[poiId.toString()] = [];
		});

		// Group the results by POI ID
		mediaResult.rows.forEach((media: MediaRecord) => {
			const poiId = media.poi_id.toString();
			if (!mediaByPoi[poiId]) {
				mediaByPoi[poiId] = [];
			}
			mediaByPoi[poiId].push(media);
		});

		// Limit media per POI (e.g., 5 media items per POI)
		const limitPerPoi = 5;
		Object.keys(mediaByPoi).forEach((poiId) => {
			mediaByPoi[poiId] = mediaByPoi[poiId].slice(0, limitPerPoi);
		});

		logger.info('Batch media loaded successfully', {
			poiCount: pois.length,
			totalMediaCount: mediaResult.rows.length,
		});

		return NextResponse.json({
			success: true,
			media: mediaByPoi,
		});
	} catch (error) {
		logger.error('Error in batch media loading', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to load batch media' },
			{ status: 500 }
		);
	}
}
