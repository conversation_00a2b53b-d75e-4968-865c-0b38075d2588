/** @format */

import { MediaManager } from '@/app/shared/media/utils';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { POISearchService } from '@/lib/poi';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/media - Get media for user or POI
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const userId = searchParams.get('userId');
		const poiId = searchParams.get('poiId');
		const limit = parseInt(searchParams.get('limit') || '50');
		const offset = parseInt(searchParams.get('offset') || '0');

		// Build query based on parameters
		let query = `
      SELECT
        pm.*,
        p.name as poi_name,
        p.city as poi_city,
        p.district as poi_district,
        u.username as uploaded_by_username,
        u.name as uploaded_by_name,
        COALESCE(like_counts.like_count, 0) as like_count,
        COALESCE(fav_counts.favorite_count, 0) as favorite_count
      FROM spatial_schema.poi_media pm
      LEFT JOIN spatial_schema.pois p ON pm.poi_id = p.id
      LEFT JOIN backend_schema.nextauth_users u ON pm.user_id = u.id
      LEFT JOIN (
        SELECT media_id, COUNT(*) as like_count
        FROM spatial_schema.poi_media_likes
        GROUP BY media_id
      ) like_counts ON pm.id = like_counts.media_id
      LEFT JOIN (
        SELECT media_id, COUNT(*) as favorite_count
        FROM spatial_schema.poi_media_favorites
        GROUP BY media_id
      ) fav_counts ON pm.id = fav_counts.media_id
      WHERE 1=1
    `;
		const params: (string | number)[] = [];
		let paramIndex = 1;

		// Filter by user
		if (userId) {
			query += ` AND pm.user_id = $${paramIndex++}`;
			params.push(userId);
		}

		// Filter by POI
		if (poiId) {
			query += ` AND pm.poi_id = $${paramIndex++}`;
			params.push(poiId);
		}

		// Get total count first (without LIMIT/OFFSET)
		let countQuery = `
      SELECT COUNT(*) as total
      FROM spatial_schema.poi_media pm
      WHERE 1=1
    `;
		const countParams: (string | number)[] = [];
		let countParamIndex = 1;

		// Apply same filters for count
		if (userId) {
			countQuery += ` AND pm.user_id = $${countParamIndex++}`;
			countParams.push(userId);
		}

		if (poiId) {
			countQuery += ` AND pm.poi_id = $${countParamIndex++}`;
			countParams.push(poiId);
		}

		const countResult = await db.query(countQuery, countParams);
		const totalCount = parseInt(countResult.rows[0].total);

		// Order by creation date (newest first)
		query += ` ORDER BY pm.created_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
		params.push(limit, offset);

		const result = await db.query(query, params);

		// Calculate pagination info
		const hasMore = offset + result.rows.length < totalCount;

		logger.info('Retrieved POI media', {
			userId,
			poiId,
			count: result.rows.length,
			total: totalCount,
		});

		return NextResponse.json({
			success: true,
			media: result.rows,
			pagination: {
				total: totalCount,
				limit,
				offset,
				hasMore,
				currentCount: result.rows.length,
			},
		});
	} catch (error) {
		logger.error('Error retrieving POI media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to retrieve media' },
			{ status: 500 }
		);
	}
}

// POST /api/pois/media - Upload media for a POI
export async function POST(request: NextRequest) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		// Parse form data
		const formData = await request.formData();
		const file = formData.get('file') as File;
		const poiId = formData.get('poiId') as string;
		const poiType = (formData.get('poiType') as string) || 'official';
		const caption = (formData.get('caption') as string) || '';

		// Validate required fields
		if (!file) {
			return NextResponse.json(
				{ success: false, error: 'File is required' },
				{ status: 400 }
			);
		}

		if (!poiId) {
			return NextResponse.json(
				{ success: false, error: 'POI ID is required' },
				{ status: 400 }
			);
		}

		logger.info('Processing media upload', {
			userId,
			poiId,
			poiType,
			fileName: file.name,
			fileSize: file.size,
			fileType: file.type,
		});

		// Validate POI exists
		const poiValidation = await POISearchService.validatePOIForUpload(
			poiId,
			poiType as 'official' | 'user_temp' | 'user_approved'
		);
		if (!poiValidation.isValid || !poiValidation.poi) {
			return NextResponse.json(
				{ success: false, error: poiValidation.error || 'Invalid POI' },
				{ status: 400 }
			);
		}

		const validatedPOI = poiValidation.poi;

		// Process and save media using MediaManager
		const mediaResult = await MediaManager.savePostMedia(userId, poiId, file);

		if (!mediaResult.success) {
			return NextResponse.json(
				{ success: false, error: mediaResult.error || 'Failed to save media' },
				{ status: 400 }
			);
		}

		// Save media record to database
		const insertQuery = `
      INSERT INTO spatial_schema.poi_media (
        poi_id, user_id, media_type, media_url, thumbnail_url, caption,
        metadata, is_verified, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      RETURNING *
    `;

		const metadata = {
			originalFileName: file.name,
			fileSize: file.size,
			mimeType: file.type,
			poiName: validatedPOI.name,
			poiLocation: [validatedPOI.city, validatedPOI.district]
				.filter(Boolean)
				.join(', '),
			uploadedAt: new Date().toISOString(),
			warnings: mediaResult.warnings || [],
		};

		const thumbnailUrl =
			mediaResult.thumbnails && mediaResult.thumbnails.length > 0
				? mediaResult.thumbnails[0]
				: null;

		// Map 'image' to 'photo' for database constraint compatibility
		const dbMediaType =
			mediaResult.mediaType === 'image' ? 'photo' : mediaResult.mediaType;

		const dbResult = await db.query(insertQuery, [
			poiId,
			userId,
			dbMediaType,
			mediaResult.mediaUrl,
			thumbnailUrl,
			caption,
			JSON.stringify(metadata),
			false, // is_verified - default to false for user uploads
		]);

		const savedMedia = dbResult.rows[0];

		logger.info('Media upload completed successfully', {
			userId,
			poiId,
			mediaId: savedMedia.id,
			mediaUrl: mediaResult.mediaUrl,
		});

		return NextResponse.json({
			success: true,
			media: savedMedia,
			mediaUrl: mediaResult.mediaUrl,
			thumbnails: mediaResult.thumbnails,
			mediaType: mediaResult.mediaType,
			warnings: mediaResult.warnings,
			poi: {
				id: validatedPOI.poi_id,
				name: validatedPOI.name,
				location: [validatedPOI.city, validatedPOI.district]
					.filter(Boolean)
					.join(', '),
			},
		});
	} catch (error) {
		logger.error('Error uploading POI media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to upload media' },
			{ status: 500 }
		);
	}
}

// DELETE /api/pois/media - Delete media
export async function DELETE(request: NextRequest) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;
		const { searchParams } = new URL(request.url);
		const mediaId = searchParams.get('mediaId');

		if (!mediaId) {
			return NextResponse.json(
				{ success: false, error: 'Media ID is required' },
				{ status: 400 }
			);
		}

		// Get media record to verify ownership
		const mediaQuery = `
      SELECT * FROM spatial_schema.poi_media
      WHERE id = $1 AND user_id = $2
    `;
		const mediaResult = await db.query(mediaQuery, [mediaId, userId]);

		if (mediaResult.rows.length === 0) {
			return NextResponse.json(
				{ success: false, error: 'Media not found or access denied' },
				{ status: 404 }
			);
		}

		const media = mediaResult.rows[0];

		// Delete media files from filesystem
		await MediaManager.deleteMedia(media.media_url);

		// Also delete thumbnail if exists
		if (media.thumbnail_url) {
			await MediaManager.deleteMedia(media.thumbnail_url);
		}

		// Delete database record
		await db.query('DELETE FROM spatial_schema.poi_media WHERE id = $1', [
			mediaId,
		]);

		logger.info('Media deleted successfully', {
			userId,
			mediaId,
			mediaUrl: media.media_url,
		});

		return NextResponse.json({
			success: true,
			message: 'Media deleted successfully',
		});
	} catch (error) {
		logger.error('Error deleting POI media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete media' },
			{ status: 500 }
		);
	}
}
