/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/pois/categories - Get available filter categories
export async function GET() {
	try {
		logger.info('Fetching POI categories');

		// Get distinct categories and subcategories from POIs
		const [categoriesResult, subcategoriesResult] = await Promise.all([
			db.query(`
        SELECT DISTINCT category, COUNT(*) as count
        FROM spatial_schema.pois
        WHERE category IS NOT NULL AND category != ''
        GROUP BY category
        ORDER BY count DESC, category ASC
      `),
			db.query(`
        SELECT DISTINCT subcategory, COUNT(*) as count
        FROM spatial_schema.pois
        WHERE subcategory IS NOT NULL AND subcategory != ''
        GROUP BY subcategory
        ORDER BY count DESC, subcategory ASC
      `),
		]);

		const categoriesArray = categoriesResult.rows.map(
			(row: { category: string; count: string }) => ({
				category: row.category,
				count: parseInt(row.count),
			})
		);
		const subcategoriesArray = subcategoriesResult.rows.map(
			(row: { subcategory: string; count: string }) => ({
				subcategory: row.subcategory,
				count: parseInt(row.count),
			})
		);

		logger.info(
			`Retrieved ${categoriesArray.length} categories and ${subcategoriesArray.length} subcategories`
		);

		return NextResponse.json({
			success: true,
			categories: categoriesArray,
			subcategories: subcategoriesArray,
			total_categories: categoriesArray.length,
			total_subcategories: subcategoriesArray.length,
		});
	} catch (error) {
		logger.error('Error fetching POI categories', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI categories' },
			{ status: 500 }
		);
	}
}
