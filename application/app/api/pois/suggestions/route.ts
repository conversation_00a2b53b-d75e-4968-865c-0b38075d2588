/** @format */

import { db } from '@/lib/database';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Unified Location Suggestion API
 * Consolidates: /api/pois/suggest-city, /api/pois/suggest-district, /api/pois/suggest-neighborhood
 * 
 * Usage:
 * - Cities: GET /api/pois/suggestions?field=city&query=istanbul
 * - Districts: GET /api/pois/suggestions?field=district&city=istanbul&query=kad
 * - Neighborhoods: GET /api/pois/suggestions?field=neighborhood&city=istanbul&district=kadikoy&query=moda
 */
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const field = searchParams.get('field');
		const query = searchParams.get('query') || '';
		const city = searchParams.get('city');
		const district = searchParams.get('district');

		// Validate field parameter
		if (!field || !['city', 'district', 'neighborhood'].includes(field)) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Valid field parameter is required (city, district, or neighborhood)' 
				},
				{ status: 400 }
			);
		}

		let sql = '';
		let params: string[] = [];
		let responseKey = '';

		switch (field) {
			case 'city':
				sql = `SELECT DISTINCT city FROM spatial_schema.pois WHERE city IS NOT NULL AND city != '' AND city != 'null' AND status = 'active'`;
				responseKey = 'cities';
				
				if (query) {
					sql += ` AND city ILIKE $1`;
					params.push(query + '%');
					sql += ` ORDER BY city ASC LIMIT 100`;
				} else {
					sql += ` ORDER BY city ASC`;
				}
				break;

			case 'district':
				if (!city) {
					return NextResponse.json(
						{ success: false, error: 'city parameter is required for district suggestions' },
						{ status: 400 }
					);
				}
				
				sql = `SELECT DISTINCT district FROM spatial_schema.pois WHERE city = $1 AND district IS NOT NULL AND district != '' AND district != 'null' AND status = 'active'`;
				params = [city];
				responseKey = 'districts';
				
				if (query) {
					sql += ` AND district ILIKE $2`;
					params.push(query + '%');
					sql += ` ORDER BY district ASC LIMIT 100`;
				} else {
					sql += ` ORDER BY district ASC`;
				}
				break;

			case 'neighborhood':
				if (!city || !district) {
					return NextResponse.json(
						{ success: false, error: 'city and district parameters are required for neighborhood suggestions' },
						{ status: 400 }
					);
				}
				
				sql = `SELECT DISTINCT neighborhood FROM spatial_schema.pois WHERE city = $1 AND district = $2 AND neighborhood IS NOT NULL AND neighborhood != '' AND neighborhood != 'null' AND status = 'active'`;
				params = [city, district];
				responseKey = 'neighborhoods';
				
				if (query) {
					sql += ` AND neighborhood ILIKE $3`;
					params.push(query + '%');
					sql += ` ORDER BY neighborhood ASC LIMIT 100`;
				} else {
					sql += ` ORDER BY neighborhood ASC`;
				}
				break;

			default:
				return NextResponse.json(
					{ success: false, error: 'Invalid field parameter' },
					{ status: 400 }
				);
		}

		const result = await db.query(sql, params);
		const suggestions = result.rows.map((row) => row[field]).filter(Boolean);

		return NextResponse.json({ 
			success: true, 
			[responseKey]: suggestions,
			field,
			count: suggestions.length
		});

	} catch (error) {
		console.error('Error in location suggestions API:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
