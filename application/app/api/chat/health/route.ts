import { NextResponse } from 'next/server'
import { config } from '@/lib/config'

export async function GET() {
  // Test connection to LLM engine API
  const response = await fetch(`${config.llmEngine.url}/health`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`LLM Engine health check failed: ${response.status} - ${errorText}`)
  }

  const data = await response.json()
  return NextResponse.json({
    status: 'healthy',
    llm_engine: data,
    timestamp: new Date().toISOString()
  })
}
