import { NextRequest, NextResponse } from 'next/server'
import { db, table } from '@/lib/database'
import { validatePassword } from '@/lib/security-headers'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  try {
    const { email, password, username, name, age } = await request.json()

    // Validate input
    if (!email || !password || !username) {
      return NextResponse.json(
        { error: 'Email, password, and username are required' },
        { status: 400 }
      )
    }

    if (!name || !age) {
      return NextResponse.json(
        { error: 'Name and age are required' },
        { status: 400 }
      )
    }

    // Validate password with complexity requirements
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.error },
        { status: 400 }
      )
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]{3,50}$/
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { error: 'Username must be 3-50 characters and contain only letters, numbers, underscores, and hyphens' },
        { status: 400 }
      )
    }

    if (parseInt(age) < 13) {
      return NextResponse.json(
        { error: 'You must be at least 13 years old' },
        { status: 400 }
      )
    }

    // Check if user already exists (email or username in nextauth_users)
    const existingUser = await db.getOne(
      `SELECT id FROM ${table('nextauth_users')} WHERE email = $1 OR username = $2`,
      [email, username.toLowerCase()]
    )

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email or username already exists' },
        { status: 409 }
      )
    }

    // Hash password
    const saltRounds = 10
    const passwordHash = await bcrypt.hash(password, saltRounds)

    // Create new user in nextauth_users table with all profile information
    const userId = uuidv4()
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3B82F6&color=fff`
    const newUserResult = await db.query(
      `INSERT INTO ${table('nextauth_users')} (id, username, name, email, email_verified, image, age, avatar_url, profile_completed, password_hash) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *`,
      [userId, username.toLowerCase(), name, email, null, avatarUrl, parseInt(age), avatarUrl, true, passwordHash]
    )
    const newUser = newUserResult.rows[0]

    // Create search preferences (location choices handled client-side only)
    await db.query(
      `INSERT INTO ${table('user_location_settings')} (user_id, search_radius, num_candidates)
       VALUES ($1, $2, $3)`,
      [newUser.id, 5000, 3]
    )

    // Create user credits record
    await db.query(
      `INSERT INTO ${table('user_credits')} (user_id, credits_earned, credits_purchased, credits_used, subscription_type) 
       VALUES ($1, $2, $3, $4, $5)`,
      [newUser.id, 0, 0, 0, 'none']
    )

    // Return user without password
    return NextResponse.json({
      user: {
        id: newUser.id,
        email: newUser.email,
        username: newUser.username,
        name: newUser.name,
        age: newUser.age,
        profile_completed: newUser.profile_completed,
        avatar_url: newUser.avatar_url
      }
    })

  } catch (error) {
    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
