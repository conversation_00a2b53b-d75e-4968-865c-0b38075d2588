/** @format */

import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const session = await getServerSession(authOptions);

		if (!session?.user?.id) {
			return NextResponse.json(
				{
					valid: false,
					error: 'No valid session found',
				},
				{ status: 401 }
			);
		}

		// Return session validation with minimal user info
		return NextResponse.json({
			valid: true,
			user: {
				id: session.user.id,
				email: session.user.email,
				name: session.user.name,
				role: session.user.role,
			},
			expiresAt: session.expires,
		});
	} catch (error) {
		console.error('Session validation error:', error);
		return NextResponse.json(
			{
				valid: false,
				error: 'Session validation failed',
			},
			{ status: 500 }
		);
	}
}
