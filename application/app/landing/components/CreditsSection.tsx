/** @format */

import { colors } from '@/app/colors';
import { useRouter } from 'next/navigation';
import React from 'react';
import { FiGift, FiShield, FiTrendingUp, FiZap } from 'react-icons/fi';

interface CreditsSectionProps {
	onGetStarted: () => void;
}

const CreditsSection: React.FC<CreditsSectionProps> = ({
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	onGetStarted,
}) => {
	const router = useRouter();
	const creditFeatures = [
		{
			icon: <FiZap className='w-6 h-6' />,
			title: 'Pay Per Use',
			description: 'Only pay for what you use with our flexible credit system',
		},
		{
			icon: <FiShield className='w-6 h-6' />,
			title: 'No Expiration',
			description: 'Your credits never expire - use them whenever you want',
		},
		{
			icon: <FiGift className='w-6 h-6' />,
			title: 'Bonus Credits',
			description: 'Earn extra credits by contributing to our POI database',
		},
		{
			icon: <FiTrendingUp className='w-6 h-6' />,
			title: 'Smart Scaling',
			description: 'Upgrade or downgrade anytime based on your usage',
		},
	];

	return (
		<div
			id='credits'
			className='py-12 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-10'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiZap
							className='w-5 h-5'
							style={{ color: colors.brand.green }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Flexible Pricing
						</span>
					</div>
					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Flexible</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Credit System
						</span>
					</h2>
					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-12'
						style={{ color: colors.neutral.slateGray }}>
						Pay only for what you use with our transparent credit system. Start
						free and scale up as you explore more of Istanbul.
					</p>
				</div>

				{/* Simple CTA to Credits Page */}
				<div className='text-center mb-10'>
					<div
						className='inline-block p-12 rounded-3xl border-2 transition-all duration-300'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<div
							className='w-20 h-20 rounded-2xl flex items-center justify-center text-white mb-6 mx-auto shadow-lg'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
							}}>
							<FiZap className='w-10 h-10' />
						</div>

						<h3
							className='text-3xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Ready to Get Started?
						</h3>

						<p
							className='text-lg mb-8 max-w-md mx-auto'
							style={{ color: colors.neutral.slateGray }}>
							Explore our flexible pricing options and find the perfect plan for
							your Istanbul adventures.
						</p>

						<button
							onClick={() => router.push('/credits')}
							className='text-white px-8 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							View Pricing Plans
						</button>
					</div>
				</div>

				{/* Credit Features */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
					{creditFeatures.map((feature, index) => (
						<div
							key={index}
							className='p-6 rounded-2xl bg-white/70 backdrop-blur-sm border transition-all duration-300'
							style={{ borderColor: colors.ui.gray200 }}>
							<div className='text-center'>
								<div
									className='w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4'
									style={{
										backgroundColor:
											index % 3 === 0
												? colors.ui.blue100
												: index % 3 === 1
												? colors.ui.navy100
												: colors.ui.green100,
										color:
											index % 3 === 0
												? colors.brand.blue
												: index % 3 === 1
												? colors.brand.navy
												: colors.brand.green,
									}}>
									{feature.icon}
								</div>
								<h4
									className='font-bold mb-2'
									style={{ color: colors.neutral.textBlack }}>
									{feature.title}
								</h4>
								<p
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									{feature.description}
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default CreditsSection;
