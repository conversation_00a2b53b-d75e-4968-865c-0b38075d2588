/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCamera,
	FiCoffee,
	FiMapPin,
	FiStar,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';
import { POI_CATEGORIES_DATA } from '../../shared/poi/constants';

// Brand colors from your design system
const BRAND_COLORS = ['#33C2FF', '#00D4AA', '#1A365D'];

// Color scheme based on your brand colors
const colors = {
	brand: {
		blue: '#33C2FF',
		green: '#00D4AA',
		navy: '#1A365D',
	},
	supporting: {
		lightBlue: '#87CEEB',
		mintGreen: '#98FB98',
		softNavy: '#2D3748',
	},
	ui: {
		gray100: '#F7FAFC',
		gray200: '#EDF2F7',
		gray300: '#E2E8F0',
		green50: '#F0FFF4',
		blue50: '#EBF8FF',
	},
	neutral: {
		textBlack: '#1A202C',
		slateGray: '#718096',
	},
};

// Helper functions from constants
const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

const getPOISubcategoriesWithCategory = () => {
	const allSubcategories: Array<{ subcategory: string; category: string }> = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData =
			POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
		for (const subcategory of categoryData.subcategories) {
			allSubcategories.push({ subcategory, category });
		}
	}
	return allSubcategories;
};

// Generates a gradient from a base color with 50% transparency
const getGradient = (color: string) => {
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		return result
			? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16),
			  }
			: null;
	};

	const baseRgb = hexToRgb(color);
	if (!baseRgb)
		return 'linear-gradient(45deg, rgba(204, 204, 204, 0.5), rgba(238, 238, 238, 0.5))';

	// Create a lighter shade for the gradient
	const lighterRgb = {
		r: Math.min(255, baseRgb.r + 40),
		g: Math.min(255, baseRgb.g + 40),
		b: Math.min(255, baseRgb.b + 40),
	};

	const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
	const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

	return `linear-gradient(45deg, ${startColor}, ${endColor})`;
};

// Shuffle function
const shuffleArray = <T,>(array: T[]): T[] => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

interface CardData {
	subcategory: string;
	category: string;
	weight?: number;
	x?: number;
	y?: number;
	width?: number;
	height?: number;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CardData) => void;
}

// Treemap algorithm - the core layout engine
const generateTreemap = (
	items: CardData[],
	x: number,
	y: number,
	width: number,
	height: number
): CardData[] => {
	if (items.length === 0) {
		return [];
	}
	if (items.length === 1) {
		return [{ ...items[0], x, y, width, height }];
	}

	const totalWeight = items.reduce((sum, item) => sum + (item.weight || 1), 0);

	// Find a split point that divides the total weight
	let cumulativeWeight = 0;
	let splitIndex = 0;
	for (let i = 0; i < items.length - 1; i++) {
		cumulativeWeight += items[i].weight || 1;
		// Look for a split that is reasonably balanced to avoid tiny slivers
		if (
			cumulativeWeight >= totalWeight / 3 &&
			cumulativeWeight <= (totalWeight * 2) / 3
		) {
			splitIndex = i + 1;
			break;
		}
	}

	// If no "good" split point is found, fall back to splitting near the middle
	if (splitIndex === 0) {
		splitIndex = Math.ceil(items.length / 2);
	}

	const items1 = items.slice(0, splitIndex);
	const items2 = items.slice(splitIndex);

	const weight1 = items1.reduce((sum, item) => sum + (item.weight || 1), 0);
	const ratio1 = weight1 / totalWeight;

	let rects: CardData[] = [];
	if (width > height) {
		// Split vertically
		const width1 = width * ratio1;
		const width2 = width - width1;
		rects = rects.concat(generateTreemap(items1, x, y, width1, height));
		rects = rects.concat(
			generateTreemap(items2, x + width1, y, width2, height)
		);
	} else {
		// Split horizontally
		const height1 = height * ratio1;
		const height2 = height - height1;
		rects = rects.concat(generateTreemap(items1, x, y, width, height1));
		rects = rects.concat(
			generateTreemap(items2, x, y + height1, width, height2)
		);
	}

	return rects;
};

// Get icon for subcategory
const getCardIcon = (subcategory: string) => {
	const iconMap: { [key: string]: JSX.Element } = {
		Cafe: <FiCoffee className='w-full h-full text-white' />,
		Restaurant: <FiSun className='w-full h-full text-white' />,
		'Museum Visit': <FiBook className='w-full h-full text-white' />,
		'Art Gallery Walk': <FiCamera className='w-full h-full text-white' />,
		'Museum Visit2': <FiBook className='w-full h-full text-white' />,
		'Art Gallery Walk2': <FiCamera className='w-full h-full text-white' />,
	};
	return (
		iconMap[subcategory] || <FiMapPin className='w-full h-full text-white' />
	);
};

const CategoryCard: React.FC<{
	cardData: CardData;
	index: number;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ cardData, index, isHovered, onHover, onClick }) => {
	const { x = 0, y = 0, width = 0, height = 0 } = cardData;

	// Get brand color and gradient - exactly like the example
	const color = BRAND_COLORS[index % BRAND_COLORS.length];
	const gradient = getGradient(color);

	return (
		<div
			style={{
				position: 'absolute',
				left: `${x}px`,
				top: `${y}px`,
				width: `${width}px`,
				height: `${height}px`,
				boxSizing: 'border-box',
				border: '2px solid #fff',
				overflow: 'hidden',
				color: 'white',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				padding: '15px',
				textAlign: 'center',
				transition: 'transform 0.3s ease, box-shadow 0.3s ease',
				borderRadius: '6px',
				textShadow: '1px 1px 3px rgba(0,0,0,0.4)',
				background: gradient,
				transform: isHovered ? 'scale(1.02)' : 'scale(1)',
				zIndex: isHovered ? 10 : 1,
				boxShadow: isHovered ? '0 10px 20px rgba(0, 0, 0, 0.2)' : 'none',
				borderColor: isHovered ? '#fafafa' : '#fff',
				cursor: 'pointer',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			<h3
				style={{
					fontWeight: 'bold',
					fontSize: '1.5em',
					margin: 0,
				}}>
				{cardData.subcategory}
			</h3>

			<p
				style={{
					fontSize: '1em',
					opacity: 0.9,
					marginTop: '8px',
					margin: 0,
				}}>
				{cardData.category}
			</p>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	const [categoryFilter, setCategoryFilter] = useState<string>('All');
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
	const [layoutCards, setLayoutCards] = useState<CardData[]>([]);

	// Render layout function using treemap algorithm
	const renderLayout = (filter: string = 'All') => {
		let allSubcategories = getPOISubcategoriesWithCategory();

		if (filter !== 'All') {
			allSubcategories = allSubcategories.filter(
				(sc) => sc.category === filter
			);
		}

		// Deduplicate subcategories to ensure each card is unique
		const uniqueSubcategories: CardData[] = [];
		const seenSubcategories = new Set();
		// Shuffle before deduplicating to vary which category is picked for a shared subcategory
		const shuffledAll = shuffleArray([...allSubcategories]);
		for (const item of shuffledAll) {
			if (!seenSubcategories.has(item.subcategory)) {
				uniqueSubcategories.push(item);
				seenSubcategories.add(item.subcategory);
			}
		}

		// Shuffle and pick a random number of subcategories (max 10)
		const shuffled = shuffleArray(uniqueSubcategories);
		const count = Math.max(
			1,
			Math.min(shuffled.length, Math.floor(Math.random() * 8) + 3)
		); // 3 to 10 cards
		let subcategories = shuffled.slice(0, count);

		if (subcategories.length === 0) return;

		// Assign random weights for more varied treemap sizes
		subcategories = subcategories.map((sc) => ({
			...sc,
			weight: Math.random() * 0.8 + 0.2, // weight between 0.2 and 1.0
		}));
		// Sort by weight to encourage more varied groupings
		subcategories.sort((a, b) => (a.weight || 0) - (b.weight || 0));

		// Container dimensions
		const containerWidth = 1184; // 1200 - 16px padding
		const containerHeight = 784; // 800 - 16px padding

		const treemapLayout = generateTreemap(
			subcategories,
			0,
			0,
			containerWidth,
			containerHeight
		);
		setLayoutCards(treemapLayout);
	};

	// Initialize layout on mount and when filter changes
	React.useEffect(() => {
		renderLayout(categoryFilter);
	}, [categoryFilter]);

	// Function to randomize cards
	const randomizeCards = () => {
		renderLayout(categoryFilter);
	};

	const handleFilterClick = (category: string) => {
		setCategoryFilter(category);
	};

	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{layoutCards.length} Subcategories • {getPOICategories().length}{' '}
							Main Categories
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Explore</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Every Category
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						From adrenaline-pumping adventures to peaceful retreats. Discover
						amazing locations across Istanbul.
					</p>
				</div>

				{/* Randomize Button */}
				<div className='flex justify-center mb-6'>
					<button
						onClick={randomizeCards}
						className='px-6 py-2 bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-lg hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center space-x-2'>
						<FiStar className='w-4 h-4' />
						<span>Randomize Cards</span>
					</button>
				</div>

				{/* Filter Controls */}
				<div className='flex flex-wrap justify-center gap-4 mb-12'>
					<div className='flex items-center space-x-3 flex-wrap'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Category:
						</span>
						<FilterButton
							active={categoryFilter === 'All'}
							onClick={() => handleFilterClick('All')}
							icon={<FiTrendingUp className='w-4 h-4' />}>
							All
						</FilterButton>
						{getPOICategories().map((category) => (
							<FilterButton
								key={category}
								active={categoryFilter === category}
								onClick={() => handleFilterClick(category)}
								icon={<FiActivity className='w-4 h-4' />}>
								{category.split(' ')[0]}
							</FilterButton>
						))}
					</div>
				</div>

				{/* Card Container - Exactly like the example */}
				<div
					style={{
						width: '1200px',
						height: '800px',
						border: '2px solid #ccc',
						position: 'relative',
						boxSizing: 'border-box',
						overflow: 'hidden',
						backgroundColor: '#fff',
						margin: '0 auto',
						boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
						borderRadius: '8px',
					}}>
					{layoutCards.map((card, index) => (
						<CategoryCard
							key={`${card.category}-${card.subcategory}-${index}`}
							cardData={card}
							index={index}
							isHovered={
								hoveredCategory === `${card.category}-${card.subcategory}`
							}
							onHover={(hovered) =>
								setHoveredCategory(
									hovered ? `${card.category}-${card.subcategory}` : null
								)
							}
							onClick={() => onCategorySelect?.(card)}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

// FilterButton component
const FilterButton: React.FC<{
	children: React.ReactNode;
	active: boolean;
	onClick: () => void;
	icon?: React.ReactNode;
}> = ({ children, active, onClick, icon }) => (
	<button
		onClick={onClick}
		className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex items-center space-x-2 ${
			active
				? 'bg-gradient-to-r from-blue-500 to-green-500 text-white shadow-lg'
				: 'bg-gray-100 text-gray-700 hover:bg-gray-200'
		}`}>
		{icon}
		<span>{children}</span>
	</button>
);

export default DynamicCategoryMosaic;
