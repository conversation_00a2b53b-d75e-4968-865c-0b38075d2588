/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCamera,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiStar,
	FiSun,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

// Color scheme based on your brand colors
const colors = {
	brand: {
		blue: '#33C2FF',
		green: '#00D4AA',
		navy: '#1A365D',
	},
	supporting: {
		lightBlue: '#87CEEB',
		mintGreen: '#98FB98',
		softNavy: '#2D3748',
	},
	ui: {
		gray100: '#F7FAFC',
		gray200: '#EDF2F7',
		gray300: '#E2E8F0',
		green50: '#F0FFF4',
		blue50: '#EBF8FF',
	},
	neutral: {
		textBlack: '#1A202C',
		slateGray: '#718096',
	},
};

interface CategoryTile {
	id: string;
	name: string;
	subcategories: string[];
	type: 'athletic' | 'non-athletic';
	mood: 'action' | 'chill';
	locationCount: number;
	previewImage?: string;
	gradient: string;
	size: 'small' | 'medium' | 'large';
	icon: React.ReactNode;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CategoryTile) => void;
}

const FilterButton: React.FC<{
	active: boolean;
	onClick: () => void;
	children: React.ReactNode;
	icon: React.ReactNode;
}> = ({ active, onClick, children, icon }) => (
	<button
		onClick={onClick}
		className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
			active ? 'scale-105' : 'hover:scale-102'
		}`}
		style={{
			background: active
				? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
				: `linear-gradient(135deg, ${colors.ui.gray100} 0%, ${colors.ui.gray200} 100%)`,
			color: active ? 'white' : colors.neutral.textBlack,
			boxShadow: active ? `0 8px 25px rgba(51, 194, 255, 0.3)` : 'none',
		}}>
		{icon}
		<span className='font-medium'>{children}</span>
	</button>
);

const CategoryCard: React.FC<{
	category: any;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
	gridSpan: { cols: number; rows: number };
}> = ({ category, isHovered, onHover, onClick, gridSpan }) => {
	// Dynamic design elements based on grid span size
	const getDesignElements = () => {
		const totalSpan = gridSpan.cols * gridSpan.rows;

		if (totalSpan >= 8) {
			// Large cards (4x2, 3x3, etc.)
			return {
				pattern:
					'bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.3)_2px,transparent_2px)] bg-[length:40px_40px]',
				iconSize: 'w-20 h-20',
				iconBg: 'bg-white/30',
				textSize: 'text-2xl',
				subtextSize: 'text-base',
				decoration: (
					<div className='absolute top-6 right-6 w-32 h-32 rounded-full bg-white/10 blur-2xl'></div>
				),
			};
		} else if (totalSpan >= 4) {
			// Medium cards (2x2, 3x1, etc.)
			return {
				pattern:
					'bg-[linear-gradient(45deg,rgba(255,255,255,0.2)_25%,transparent_25%,transparent_75%,rgba(255,255,255,0.2)_75%)] bg-[length:30px_30px]',
				iconSize: 'w-16 h-16',
				iconBg: 'bg-white/25',
				textSize: 'text-xl',
				subtextSize: 'text-sm',
				decoration: (
					<div className='absolute -right-6 -top-6 w-24 h-24 rounded-full bg-white/8'></div>
				),
			};
		} else if (totalSpan >= 2) {
			// Small-medium cards (2x1, 1x2)
			return {
				pattern:
					'bg-[conic-gradient(from_0deg_at_50%_50%,rgba(255,255,255,0.2)_0deg,transparent_60deg,rgba(255,255,255,0.2)_120deg,transparent_180deg)]',
				iconSize: 'w-12 h-12',
				iconBg: 'bg-white/20',
				textSize: 'text-lg',
				subtextSize: 'text-xs',
				decoration: (
					<div className='absolute bottom-3 left-3 w-16 h-16 rounded-lg bg-white/10 rotate-12'></div>
				),
			};
		} else {
			// Small cards (1x1)
			return {
				pattern:
					'bg-[radial-gradient(circle_at_70%_20%,rgba(255,255,255,0.25)_1px,transparent_1px)] bg-[length:20px_20px]',
				iconSize: 'w-10 h-10',
				iconBg: 'bg-white/15',
				textSize: 'text-base',
				subtextSize: 'text-xs',
				decoration: (
					<div className='absolute top-3 right-3 w-10 h-10 rounded-full bg-white/12'></div>
				),
			};
		}
	};

	const designElements = getDesignElements();

	return (
		<div
			className='rounded-2xl p-6 cursor-pointer transition-all duration-300 relative overflow-hidden group'
			style={{
				gridColumn: `span ${gridSpan.cols}`,
				gridRow: `span ${gridSpan.rows}`,
				background: category.gradient,
				transform: isHovered ? 'scale(1.02) translateY(-4px)' : 'scale(1)',
				boxShadow: isHovered
					? '0 20px 40px rgba(0, 0, 0, 0.15)'
					: '0 4px 15px rgba(0, 0, 0, 0.1)',
				minWidth: 0, // Allow grid to control sizing
				minHeight: 0, // Allow grid to control sizing
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Background Pattern with 50% transparency */}
			<div className='absolute inset-0 opacity-50'>
				<div className={`absolute inset-0 ${designElements.pattern}`}></div>
			</div>

			{/* Decorative elements */}
			{designElements.decoration}

			{/* Content */}
			<div className='relative h-full flex flex-col justify-center text-white text-center'>
				<div className='flex items-center justify-center mb-4'>
					<div
						className={`${designElements.iconSize} rounded-xl ${designElements.iconBg} backdrop-blur-sm flex items-center justify-center`}>
						{category.icon}
					</div>
				</div>

				<div className='space-y-2'>
					<h3 className={`font-bold ${designElements.textSize} leading-tight`}>
						{category.subcategory}
					</h3>
					<p className={`text-white/90 ${designElements.subtextSize}`}>
						{category.category}
					</p>
				</div>
			</div>

			{/* Hover overlay with 50% transparency */}
			<div
				className={`absolute inset-0 bg-white/50 backdrop-blur-sm transition-opacity duration-300 ${
					isHovered ? 'opacity-100' : 'opacity-0'
				}`}></div>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	// Use the actual categories from constants
	const ACTUAL_CATEGORIES = {
		'Food & Drink': {
			subcategories: [
				'Cafe',
				'Restaurant',
				'Bar',
				'Street Food',
				'Fine Dining',
				'Bakery',
			],
		},
		'Cultural & Creative Experiences': {
			subcategories: [
				'Museum Visit',
				'Art Gallery Walk',
				'Theater',
				'Concert',
				'Festival',
				'Workshop',
			],
		},
		'Sports & Fitness': {
			subcategories: [
				'Gym',
				'Yoga Studio',
				'Swimming',
				'Rock Climbing',
				'Tennis',
				'Basketball',
			],
		},
		Entertainment: {
			subcategories: [
				'Cinema',
				'Bowling',
				'Karaoke',
				'Gaming',
				'Comedy Club',
				'Dance Club',
			],
		},
		'Shopping & Markets': {
			subcategories: [
				'Mall',
				'Boutique',
				'Market',
				'Vintage Store',
				'Bookstore',
				'Electronics',
			],
		},
	};

	// Enhanced gradient generation with more variety
	const generateGradients = (count: number) => {
		const baseGradients = [
			// Blue variations
			`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
			`linear-gradient(45deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(225deg, ${colors.brand.blue} 0%, ${colors.supporting.softNavy} 100%)`,

			// Green variations
			`linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
			`linear-gradient(315deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(180deg, ${colors.brand.green} 0%, ${colors.brand.navy} 100%)`,

			// Navy variations
			`linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
			`linear-gradient(90deg, ${colors.supporting.softNavy} 0%, ${colors.brand.navy} 100%)`,

			// Mixed variations
			`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(225deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(45deg, ${colors.brand.navy} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(315deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
		];

		// Shuffle and return
		const shuffled = [...baseGradients].sort(() => Math.random() - 0.5);
		return Array.from(
			{ length: count },
			(_, i) => shuffled[i % shuffled.length]
		);
	};

	// Get different icons for variety
	const getCardIcon = (subcategory: string) => {
		const iconMap: { [key: string]: JSX.Element } = {
			Cafe: <FiCoffee className='w-full h-full text-white' />,
			Restaurant: <FiSun className='w-full h-full text-white' />,
			Bar: <FiMusic className='w-full h-full text-white' />,
			'Street Food': <FiShoppingBag className='w-full h-full text-white' />,
			'Fine Dining': <FiStar className='w-full h-full text-white' />,
			Bakery: <FiHeart className='w-full h-full text-white' />,
			'Museum Visit': <FiBook className='w-full h-full text-white' />,
			'Art Gallery Walk': <FiCamera className='w-full h-full text-white' />,
			Theater: <FiMusic className='w-full h-full text-white' />,
			Concert: <FiZap className='w-full h-full text-white' />,
			Festival: <FiStar className='w-full h-full text-white' />,
			Workshop: <FiActivity className='w-full h-full text-white' />,
			Gym: <FiActivity className='w-full h-full text-white' />,
			'Yoga Studio': <FiHeart className='w-full h-full text-white' />,
			Swimming: <FiSun className='w-full h-full text-white' />,
			'Rock Climbing': <FiTrendingUp className='w-full h-full text-white' />,
			Tennis: <FiZap className='w-full h-full text-white' />,
			Basketball: <FiActivity className='w-full h-full text-white' />,
		};
		return (
			iconMap[subcategory] || <FiMapPin className='w-full h-full text-white' />
		);
	};

	// Grid configuration - like your 12x8 example
	const GRID_COLS = 12; // 12 columns for flexibility
	const GRID_ROWS = 8; // 8 rows for good proportions

	// Define card layouts as grid spans [cols, rows] - like your example
	const getCardLayouts = (cardCount: number) => {
		// Different layout patterns based on card count
		const layoutPatterns = [
			// Pattern 1: Mixed sizes like your example
			[
				[4, 2], // Large card: 4 cols x 2 rows (like 1 1 1 1)
				[3, 2], // Medium-large: 3 cols x 2 rows (like 2 2 2)
				[2, 2], // Square medium: 2 cols x 2 rows (like 3 3)
				[2, 2], // Square medium: 2 cols x 2 rows (like 4 4)
				[2, 1], // Small wide: 2 cols x 1 row (like 5 5)
				[3, 1], // Medium wide: 3 cols x 1 row (like 6 6 6)
				[5, 1], // Very wide: 5 cols x 1 row (like 7 7 7 7 7)
				[2, 1], // Small: 2 cols x 1 row (like 8 8)
				[2, 2], // Square: 2 cols x 2 rows (like 9 9)
				[1, 1], // Tiny: 1 col x 1 row
				[1, 1], // Tiny: 1 col x 1 row
				[1, 1], // Tiny: 1 col x 1 row
			],
			// Pattern 2: More uniform
			[
				[3, 3], // Large square
				[3, 2], // Wide medium
				[2, 3], // Tall medium
				[2, 2], // Square
				[2, 1], // Wide small
				[1, 2], // Tall small
				[2, 1], // Wide small
				[1, 1], // Tiny
				[1, 1], // Tiny
				[1, 1], // Tiny
				[1, 1], // Tiny
				[1, 1], // Tiny
			],
			// Pattern 3: Stock market style
			[
				[5, 2], // Very wide
				[2, 3], // Tall
				[3, 1], // Wide
				[1, 2], // Thin tall
				[2, 2], // Square
				[4, 1], // Very wide thin
				[1, 1], // Tiny
				[2, 1], // Small wide
				[1, 3], // Very tall thin
				[3, 2], // Medium
				[1, 1], // Tiny
				[1, 1], // Tiny
			],
		];

		// Randomly select a pattern
		const pattern =
			layoutPatterns[Math.floor(Math.random() * layoutPatterns.length)];
		return pattern.slice(0, cardCount);
	};

	// Create randomized subcategory cards
	const createSubcategoryCards = () => {
		const allCards: any[] = [];

		// Create all possible cards
		Object.entries(ACTUAL_CATEGORIES).forEach(
			([categoryName, categoryData]) => {
				categoryData.subcategories.forEach((subcategory) => {
					allCards.push({
						id: `${categoryName}-${subcategory}`,
						category: categoryName,
						subcategory: subcategory,
						icon: getCardIcon(subcategory),
					});
				});
			}
		);

		// Randomize and limit to 8-12 cards for better grid layout
		const shuffledCards = [...allCards].sort(() => Math.random() - 0.5);
		const cardCount = Math.floor(Math.random() * 5) + 8; // 8-12 cards
		const selectedCards = shuffledCards.slice(0, cardCount);

		// Get grid layouts and assign gradients
		const cardLayouts = getCardLayouts(selectedCards.length);
		const gradients = generateGradients(selectedCards.length);

		return selectedCards.map((card, index) => ({
			...card,
			gridSpan: { cols: cardLayouts[index][0], rows: cardLayouts[index][1] },
			gradient: gradients[index],
			priority: cardLayouts[index][0] * cardLayouts[index][1], // Larger cards have higher priority
		}));
	};

	const [displayCards, setDisplayCards] = useState(() =>
		createSubcategoryCards()
	);
	const [categoryFilter, setCategoryFilter] = useState<string>('all');
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

	// Function to randomize cards
	const randomizeCards = () => {
		setDisplayCards(createSubcategoryCards());
	};

	const filteredCards = displayCards.filter((card) => {
		return categoryFilter === 'all' || card.category === categoryFilter;
	});

	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{displayCards.length} Subcategories •{' '}
							{Object.keys(ACTUAL_CATEGORIES).length} Main Categories
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Explore</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Every Category
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						From adrenaline-pumping adventures to peaceful retreats. Discover
						amazing locations across Istanbul.
					</p>
				</div>

				{/* Randomize Button */}
				<div className='flex justify-center mb-6'>
					<button
						onClick={randomizeCards}
						className='px-6 py-2 bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-lg hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center space-x-2'>
						<FiStar className='w-4 h-4' />
						<span>Randomize Cards</span>
					</button>
				</div>

				{/* Filter Controls */}
				<div className='flex flex-wrap justify-center gap-4 mb-12'>
					<div className='flex items-center space-x-3 flex-wrap'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Category:
						</span>
						<FilterButton
							active={categoryFilter === 'all'}
							onClick={() => setCategoryFilter('all')}
							icon={<FiTrendingUp className='w-4 h-4' />}>
							All
						</FilterButton>
						{Object.keys(ACTUAL_CATEGORIES).map((category) => (
							<FilterButton
								key={category}
								active={categoryFilter === category}
								onClick={() => setCategoryFilter(category)}
								icon={<FiActivity className='w-4 h-4' />}>
								{category.split(' ')[0]}
							</FilterButton>
						))}
					</div>
				</div>

				{/* Card Container - CSS Grid */}
				<div
					className='relative overflow-hidden rounded-2xl mx-auto'
					style={{
						width: '1200px',
						height: '800px',
						maxWidth: '100%',
						border: '2px solid rgba(0,0,0,0.1)',
						background:
							'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.02) 100%)',
						padding: '8px',
						boxSizing: 'border-box',
					}}>
					{/* Background grid pattern */}
					<div className='absolute inset-0 opacity-10'>
						<div className='absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.1)_1px,transparent_1px)] bg-[size:50px_50px]'></div>
					</div>

					{/* CSS Grid Container */}
					<div
						className='relative h-full w-full'
						style={{
							display: 'grid',
							gridTemplateColumns: `repeat(${GRID_COLS}, 1fr)`,
							gridTemplateRows: `repeat(${GRID_ROWS}, 1fr)`,
							gap: '8px',
							boxSizing: 'border-box',
						}}>
						{filteredCards.map((card) => (
							<CategoryCard
								key={card.id}
								category={card}
								isHovered={hoveredCategory === card.id}
								onHover={(hovered) =>
									setHoveredCategory(hovered ? card.id : null)
								}
								onClick={() => onCategorySelect?.(card)}
								gridSpan={card.gridSpan}
							/>
						))}
					</div>
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
