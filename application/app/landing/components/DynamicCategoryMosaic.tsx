/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCamera,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiStar,
	FiSun,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

// Color scheme based on your brand colors
const colors = {
	brand: {
		blue: '#33C2FF',
		green: '#00D4AA',
		navy: '#1A365D',
	},
	supporting: {
		lightBlue: '#87CEEB',
		mintGreen: '#98FB98',
		softNavy: '#2D3748',
	},
	ui: {
		gray100: '#F7FAFC',
		gray200: '#EDF2F7',
		gray300: '#E2E8F0',
		green50: '#F0FFF4',
		blue50: '#EBF8FF',
	},
	neutral: {
		textBlack: '#1A202C',
		slateGray: '#718096',
	},
};

interface CategoryTile {
	id: string;
	name: string;
	subcategories: string[];
	type: 'athletic' | 'non-athletic';
	mood: 'action' | 'chill';
	locationCount: number;
	previewImage?: string;
	gradient: string;
	size: 'small' | 'medium' | 'large';
	icon: React.ReactNode;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CategoryTile) => void;
}

const FilterButton: React.FC<{
	active: boolean;
	onClick: () => void;
	children: React.ReactNode;
	icon: React.ReactNode;
}> = ({ active, onClick, children, icon }) => (
	<button
		onClick={onClick}
		className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
			active ? 'scale-105' : 'hover:scale-102'
		}`}
		style={{
			background: active
				? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
				: `linear-gradient(135deg, ${colors.ui.gray100} 0%, ${colors.ui.gray200} 100%)`,
			color: active ? 'white' : colors.neutral.textBlack,
			boxShadow: active ? `0 8px 25px rgba(51, 194, 255, 0.3)` : 'none',
		}}>
		{icon}
		<span className='font-medium'>{children}</span>
	</button>
);

const CategoryCard: React.FC<{
	category: any;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ category, isHovered, onHover, onClick }) => {
	// Dynamic design elements based on card area
	const getDesignElements = () => {
		const { width, height } = category.size;
		const area = width * height;

		if (area > 50000) {
			// Very large cards
			return {
				pattern:
					'bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.3)_2px,transparent_2px)] bg-[length:40px_40px]',
				iconSize: 'w-20 h-20',
				iconBg: 'bg-white/30',
				textSize: 'text-2xl',
				subtextSize: 'text-base',
				decoration: (
					<div className='absolute top-6 right-6 w-32 h-32 rounded-full bg-white/10 blur-2xl'></div>
				),
			};
		} else if (area > 30000) {
			// Large cards
			return {
				pattern:
					'bg-[linear-gradient(45deg,rgba(255,255,255,0.2)_25%,transparent_25%,transparent_75%,rgba(255,255,255,0.2)_75%)] bg-[length:30px_30px]',
				iconSize: 'w-16 h-16',
				iconBg: 'bg-white/25',
				textSize: 'text-xl',
				subtextSize: 'text-sm',
				decoration: (
					<div className='absolute -right-6 -top-6 w-24 h-24 rounded-full bg-white/8'></div>
				),
			};
		} else if (area > 15000) {
			// Medium cards
			return {
				pattern:
					'bg-[conic-gradient(from_0deg_at_50%_50%,rgba(255,255,255,0.2)_0deg,transparent_60deg,rgba(255,255,255,0.2)_120deg,transparent_180deg)]',
				iconSize: 'w-12 h-12',
				iconBg: 'bg-white/20',
				textSize: 'text-lg',
				subtextSize: 'text-xs',
				decoration: (
					<div className='absolute bottom-3 left-3 w-16 h-16 rounded-lg bg-white/10 rotate-12'></div>
				),
			};
		} else {
			// Small cards
			return {
				pattern:
					'bg-[radial-gradient(circle_at_70%_20%,rgba(255,255,255,0.25)_1px,transparent_1px)] bg-[length:20px_20px]',
				iconSize: 'w-10 h-10',
				iconBg: 'bg-white/15',
				textSize: 'text-base',
				subtextSize: 'text-xs',
				decoration: (
					<div className='absolute top-3 right-3 w-10 h-10 rounded-full bg-white/12'></div>
				),
			};
		}
	};

	const designElements = getDesignElements();

	return (
		<div
			className='rounded-2xl p-6 cursor-pointer transition-all duration-300 relative overflow-hidden group absolute'
			style={{
				width: `${category.size.width}px`,
				height: `${category.size.height}px`,
				left: `${category.size.x}px`,
				top: `${category.size.y}px`,
				background: category.gradient,
				transform: isHovered ? 'scale(1.02) translateY(-4px)' : 'scale(1)',
				boxShadow: isHovered
					? '0 20px 40px rgba(0, 0, 0, 0.15)'
					: '0 4px 15px rgba(0, 0, 0, 0.1)',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Background Pattern with 50% transparency */}
			<div className='absolute inset-0 opacity-50'>
				<div className={`absolute inset-0 ${designElements.pattern}`}></div>
			</div>

			{/* Decorative elements */}
			{designElements.decoration}

			{/* Content */}
			<div className='relative h-full flex flex-col justify-center text-white text-center'>
				<div className='flex items-center justify-center mb-4'>
					<div
						className={`${designElements.iconSize} rounded-xl ${designElements.iconBg} backdrop-blur-sm flex items-center justify-center`}>
						{category.icon}
					</div>
				</div>

				<div className='space-y-2'>
					<h3 className={`font-bold ${designElements.textSize} leading-tight`}>
						{category.subcategory}
					</h3>
					<p className={`text-white/90 ${designElements.subtextSize}`}>
						{category.category}
					</p>
				</div>
			</div>

			{/* Hover overlay with 50% transparency */}
			<div
				className={`absolute inset-0 bg-white/50 backdrop-blur-sm transition-opacity duration-300 ${
					isHovered ? 'opacity-100' : 'opacity-0'
				}`}></div>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	// Use the actual categories from constants
	const ACTUAL_CATEGORIES = {
		'Food & Drink': {
			subcategories: [
				'Cafe',
				'Restaurant',
				'Bar',
				'Street Food',
				'Fine Dining',
				'Bakery',
			],
		},
		'Cultural & Creative Experiences': {
			subcategories: [
				'Museum Visit',
				'Art Gallery Walk',
				'Theater',
				'Concert',
				'Festival',
				'Workshop',
			],
		},
		'Sports & Fitness': {
			subcategories: [
				'Gym',
				'Yoga Studio',
				'Swimming',
				'Rock Climbing',
				'Tennis',
				'Basketball',
			],
		},
		Entertainment: {
			subcategories: [
				'Cinema',
				'Bowling',
				'Karaoke',
				'Gaming',
				'Comedy Club',
				'Dance Club',
			],
		},
		'Shopping & Markets': {
			subcategories: [
				'Mall',
				'Boutique',
				'Market',
				'Vintage Store',
				'Bookstore',
				'Electronics',
			],
		},
	};

	// Enhanced gradient generation with more variety
	const generateGradients = (count: number) => {
		const baseGradients = [
			// Blue variations
			`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
			`linear-gradient(45deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(225deg, ${colors.brand.blue} 0%, ${colors.supporting.softNavy} 100%)`,

			// Green variations
			`linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
			`linear-gradient(315deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(180deg, ${colors.brand.green} 0%, ${colors.brand.navy} 100%)`,

			// Navy variations
			`linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
			`linear-gradient(90deg, ${colors.supporting.softNavy} 0%, ${colors.brand.navy} 100%)`,

			// Mixed variations
			`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(225deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(45deg, ${colors.brand.navy} 0%, ${colors.brand.green} 100%)`,
			`linear-gradient(315deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
		];

		// Shuffle and return
		const shuffled = [...baseGradients].sort(() => Math.random() - 0.5);
		return Array.from(
			{ length: count },
			(_, i) => shuffled[i % shuffled.length]
		);
	};

	// Get different icons for variety
	const getCardIcon = (subcategory: string) => {
		const iconMap: { [key: string]: JSX.Element } = {
			Cafe: <FiCoffee className='w-full h-full text-white' />,
			Restaurant: <FiSun className='w-full h-full text-white' />,
			Bar: <FiMusic className='w-full h-full text-white' />,
			'Street Food': <FiShoppingBag className='w-full h-full text-white' />,
			'Fine Dining': <FiStar className='w-full h-full text-white' />,
			Bakery: <FiHeart className='w-full h-full text-white' />,
			'Museum Visit': <FiBook className='w-full h-full text-white' />,
			'Art Gallery Walk': <FiCamera className='w-full h-full text-white' />,
			Theater: <FiMusic className='w-full h-full text-white' />,
			Concert: <FiZap className='w-full h-full text-white' />,
			Festival: <FiStar className='w-full h-full text-white' />,
			Workshop: <FiActivity className='w-full h-full text-white' />,
			Gym: <FiActivity className='w-full h-full text-white' />,
			'Yoga Studio': <FiHeart className='w-full h-full text-white' />,
			Swimming: <FiSun className='w-full h-full text-white' />,
			'Rock Climbing': <FiTrendingUp className='w-full h-full text-white' />,
			Tennis: <FiZap className='w-full h-full text-white' />,
			Basketball: <FiActivity className='w-full h-full text-white' />,
		};
		return (
			iconMap[subcategory] || <FiMapPin className='w-full h-full text-white' />
		);
	};

	// Automatic box division algorithm
	const calculateOptimalLayout = (cardCount: number) => {
		const containerWidth = 1184; // 1200 - 16px padding
		const containerHeight = 784; // 800 - 16px padding
		const gap = 8;

		// Step 1: Calculate total available area
		const totalArea = containerWidth * containerHeight;
		const availableArea = totalArea * 0.95; // Use 95% to account for gaps

		// Step 2: Determine base unit size based on card count
		const baseUnitArea = availableArea / cardCount;
		const baseUnitSize = Math.sqrt(baseUnitArea);

		// Step 3: Create size variations (stock market style)
		const sizeMultipliers = [
			2.5, // Very large
			2.0, // Large
			1.8, // Medium-large
			1.5, // Medium
			1.2, // Medium-small
			1.0, // Base
			0.8, // Small
			0.6, // Very small
		];

		// Step 4: Assign sizes ensuring total area coverage
		const cardSizes = [];
		let totalAssignedArea = 0;

		for (let i = 0; i < cardCount; i++) {
			const multiplier = sizeMultipliers[i % sizeMultipliers.length];
			const cardArea = baseUnitArea * multiplier;

			// Calculate width and height with some randomness for variety
			const aspectRatio = 0.7 + Math.random() * 0.6; // 0.7 to 1.3
			const width = Math.sqrt(cardArea / aspectRatio);
			const height = cardArea / width;

			cardSizes.push({
				width: Math.max(100, Math.min(width, containerWidth * 0.6)),
				height: Math.max(80, Math.min(height, containerHeight * 0.6)),
				area: cardArea,
			});

			totalAssignedArea += cardArea;
		}

		// Step 5: Scale all cards to use exactly the available space
		const scaleFactor = Math.sqrt(availableArea / totalAssignedArea);
		cardSizes.forEach((card) => {
			card.width *= scaleFactor;
			card.height *= scaleFactor;
		});

		// Step 6: Pack cards using a simple bin packing algorithm
		const packedCards = [];
		const occupiedSpaces = [];

		// Sort cards by area (largest first) for better packing
		const sortedCards = cardSizes
			.map((size, index) => ({ ...size, originalIndex: index }))
			.sort((a, b) => b.area - a.area);

		for (const card of sortedCards) {
			let placed = false;
			let bestPosition = { x: 0, y: 0 };

			// Try to find the best position
			for (let y = 0; y <= containerHeight - card.height && !placed; y += 20) {
				for (let x = 0; x <= containerWidth - card.width && !placed; x += 20) {
					const newRect = {
						x,
						y,
						width: card.width,
						height: card.height,
					};

					// Check if this position overlaps with existing cards
					const overlaps = occupiedSpaces.some(
						(existing) =>
							!(
								newRect.x + newRect.width + gap <= existing.x ||
								newRect.x >= existing.x + existing.width + gap ||
								newRect.y + newRect.height + gap <= existing.y ||
								newRect.y >= existing.y + existing.height + gap
							)
					);

					if (!overlaps) {
						bestPosition = { x, y };
						placed = true;
					}
				}
			}

			// Add the card to packed cards and occupied spaces
			const finalCard = {
				...card,
				x: bestPosition.x,
				y: bestPosition.y,
			};

			packedCards[card.originalIndex] = finalCard;
			occupiedSpaces.push(finalCard);
		}

		return packedCards;
	};

	const [allCards, setAllCards] = useState(() => {
		// Create all possible cards without layout
		const cards: any[] = [];
		Object.entries(ACTUAL_CATEGORIES).forEach(
			([categoryName, categoryData]) => {
				categoryData.subcategories.forEach((subcategory) => {
					cards.push({
						id: `${categoryName}-${subcategory}`,
						category: categoryName,
						subcategory: subcategory,
						icon: getCardIcon(subcategory),
					});
				});
			}
		);
		return cards.sort(() => Math.random() - 0.5); // Randomize once
	});

	const [categoryFilter, setCategoryFilter] = useState<string>('all');
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

	// Function to randomize cards
	const randomizeCards = () => {
		setAllCards([...allCards].sort(() => Math.random() - 0.5));
	};

	// Calculate filtered and laid out cards whenever filter changes
	const getFilteredAndLayoutCards = () => {
		// First filter the cards
		const filtered = allCards.filter((card) => {
			return categoryFilter === 'all' || card.category === categoryFilter;
		});

		// Limit to max 10 cards
		const limitedCards = filtered.slice(0, Math.min(10, filtered.length));

		// If no cards, return empty array
		if (limitedCards.length === 0) return [];

		// Apply layout algorithm to filtered cards
		const cardLayouts = calculateOptimalLayout(limitedCards.length);
		const gradients = generateGradients(limitedCards.length);

		return limitedCards.map((card, index) => ({
			...card,
			size: cardLayouts[index],
			gradient: gradients[index],
			priority: cardLayouts[index].area,
		}));
	};

	const filteredCards = getFilteredAndLayoutCards();

	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{filteredCards.length} Subcategories •{' '}
							{Object.keys(ACTUAL_CATEGORIES).length} Main Categories
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Explore</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Every Category
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						From adrenaline-pumping adventures to peaceful retreats. Discover
						amazing locations across Istanbul.
					</p>
				</div>

				{/* Randomize Button */}
				<div className='flex justify-center mb-6'>
					<button
						onClick={randomizeCards}
						className='px-6 py-2 bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-lg hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center space-x-2'>
						<FiStar className='w-4 h-4' />
						<span>Randomize Cards</span>
					</button>
				</div>

				{/* Filter Controls */}
				<div className='flex flex-wrap justify-center gap-4 mb-12'>
					<div className='flex items-center space-x-3 flex-wrap'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Category:
						</span>
						<FilterButton
							active={categoryFilter === 'all'}
							onClick={() => setCategoryFilter('all')}
							icon={<FiTrendingUp className='w-4 h-4' />}>
							All
						</FilterButton>
						{Object.keys(ACTUAL_CATEGORIES).map((category) => (
							<FilterButton
								key={category}
								active={categoryFilter === category}
								onClick={() => setCategoryFilter(category)}
								icon={<FiActivity className='w-4 h-4' />}>
								{category.split(' ')[0]}
							</FilterButton>
						))}
					</div>
				</div>

				{/* Card Container - Automatic Layout */}
				<div
					className='relative overflow-hidden rounded-2xl mx-auto'
					style={{
						width: '1200px',
						height: '800px',
						maxWidth: '100%',
						border: '2px solid rgba(0,0,0,0.1)',
						background:
							'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.02) 100%)',
						padding: '8px',
						boxSizing: 'border-box',
					}}>
					{/* Background grid pattern */}
					<div className='absolute inset-0 opacity-10'>
						<div className='absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.1)_1px,transparent_1px)] bg-[size:50px_50px]'></div>
					</div>

					{/* Absolute Positioned Cards */}
					<div className='relative h-full w-full'>
						{filteredCards.map((card) => (
							<CategoryCard
								key={card.id}
								category={card}
								isHovered={hoveredCategory === card.id}
								onHover={(hovered) =>
									setHoveredCategory(hovered ? card.id : null)
								}
								onClick={() => onCategorySelect?.(card)}
							/>
						))}
					</div>
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
