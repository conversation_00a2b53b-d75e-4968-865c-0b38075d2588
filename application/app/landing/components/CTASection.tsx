import React from 'react';
import { colors } from '@/app/colors';
import { FiMapPin, FiMessageCircle, FiStar, FiCoffee, FiShoppingBag } from 'react-icons/fi';

interface CTASectionProps {
  onGetStarted: () => void;
}

const CTASection: React.FC<CTASectionProps> = ({ onGetStarted }) => {
  const examples = [
    {
      text: "Find me a cozy cafe nearby where I can work",
      icon: <FiCoffee className="w-5 h-5" />,
      category: "Work & Study"
    },
    {
      text: "Where's the best sushi restaurant in my area?",
      icon: <FiMapPin className="w-5 h-5" />,
      category: "Dining"
    },
    {
      text: "I need a pharmacy that's actually in my neighborhood",
      icon: <FiShoppingBag className="w-5 h-5" />,
      category: "Healthcare"
    },
    {
      text: "Show me Italian restaurants with outdoor seating nearby",
      icon: <FiStar className="w-5 h-5" />,
      category: "Fine Dining"
    }
  ];

  return (
    <div id="cta" className="py-12 relative overflow-hidden bg-transparent">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div
          className="absolute top-20 left-10 w-40 h-40 rounded-full opacity-20 blur-3xl"
          style={{
            background: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`
          }}
        ></div>
        <div
          className="absolute bottom-20 right-10 w-32 h-32 rounded-full opacity-15 blur-2xl"
          style={{
            background: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`
          }}
        ></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


        {/* Example Queries */}
        <div className="rounded-3xl p-10">
          <div className="text-center mb-12">
            <h3
              className="text-3xl font-bold mb-4"
              style={{ color: colors.neutral.textBlack }}
            >
              Try These Example Queries
            </h3>
            <p
              className="text-lg"
              style={{ color: colors.neutral.slateGray }}
            >
              See how natural and intuitive it is to find exactly what you're looking for
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {examples.map((example, index) => (
              <div
                key={index}
                className="p-6 rounded-2xl border-2 transition-all duration-300 cursor-pointer"
                style={{
                  background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
                  borderColor: colors.ui.gray200
                }}
                onClick={onGetStarted}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = colors.brand.blue
                  e.currentTarget.style.transform = 'translateY(-2px)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = colors.ui.gray200
                  e.currentTarget.style.transform = 'translateY(0)'
                }}
              >
                <div className="flex items-start space-x-4">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center text-white flex-shrink-0 shadow-lg"
                    style={{
                      background: index % 3 === 0 ? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)` :
                                 index % 3 === 1 ? `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.green} 100%)` :
                                 `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`
                    }}
                  >
                    {example.icon}
                  </div>
                  <div className="flex-1">
                    <div
                      className="text-xs font-semibold uppercase tracking-wide mb-2"
                      style={{ color: colors.brand.blue }}
                    >
                      {example.category}
                    </div>
                    <p
                      className="font-medium leading-relaxed"
                      style={{ color: colors.neutral.textBlack }}
                    >
                      "{example.text}"
                    </p>
                  </div>
                  <FiMessageCircle
                    className="w-5 h-5"
                    style={{ color: colors.neutral.slateGray }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Final CTA Button */}
        <div className="text-center">
          <button
            onClick={onGetStarted}
            className="text-white px-10 py-4 rounded-2xl text-lg font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
            style={{
              background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`
            }}
          >
            Get Started Today
          </button>
        </div>
      </div>
    </div>
  );
};

export default CTASection;
