/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState, useEffect } from 'react';
import {
  FiMessageCircle,
  FiSearch,
  FiMapPin,
  FiFilter,
  FiClock,
  FiStar,
  FiZap,
  FiHeart,
  FiTrendingUp,
  FiCheckCircle,
  FiArrowRight
} from 'react-icons/fi';

interface DemoScenario {
  id: string;
  userQuery: string;
  aiResponse: string;
  searchResults: LocationResult[];
  processingSteps: string[];
  conversationContext: string[];
  traditionalSteps: string[];
}

interface LocationResult {
  name: string;
  type: string;
  rating: number;
  distance: string;
  features: string[];
  image?: string;
}

const demoScenarios: DemoScenario[] = [
  {
    id: 'cozy-cafe',
    userQuery: "I want a cozy cafe with good wifi near Galata Tower where I can work for a few hours",
    aiResponse: "I found 8 cozy cafes with excellent wifi near Galata Tower. Based on your work needs, here are the top 3 with quiet atmospheres and reliable internet...",
    searchResults: [
      {
        name: "Karakoy Lokantasi Cafe",
        type: "Cafe",
        rating: 4.8,
        distance: "200m from Galata Tower",
        features: ["Free WiFi", "Quiet", "Power Outlets", "Coffee"]
      },
      {
        name: "Galata Coffee Roasters",
        type: "Coffee Shop",
        rating: 4.6,
        distance: "150m from Galata Tower",
        features: ["High-Speed WiFi", "Work-Friendly", "Specialty Coffee"]
      },
      {
        name: "Minimalist Cafe",
        type: "Cafe",
        rating: 4.7,
        distance: "300m from Galata Tower",
        features: ["Silent Zone", "Fast WiFi", "Laptop-Friendly"]
      }
    ],
    processingSteps: [
      "Understanding: cozy cafe + good wifi + work environment",
      "Location: Galata Tower area (radius: 500m)",
      "Filtering: WiFi quality ratings > 4.0",
      "Ranking: Coziness score + Work-friendliness",
      "Context: Work duration (few hours) = quiet preference"
    ],
    conversationContext: [],
    traditionalSteps: [
      "Select 'Cafe' from category dropdown",
      "Choose 'Galata Tower' area on map",
      "Filter by 'WiFi Available'",
      "Sort by rating or distance",
      "Manually check each result for work suitability"
    ]
  },
  {
    id: 'romantic-dinner',
    userQuery: "Looking for a romantic restaurant with Bosphorus view for anniversary dinner",
    aiResponse: "Perfect! I found several romantic restaurants with stunning Bosphorus views. Here are 3 ideal spots for your anniversary celebration...",
    searchResults: [
      {
        name: "Sunset Grill & Bar",
        type: "Fine Dining",
        rating: 4.9,
        distance: "Ulus, Bosphorus view",
        features: ["Bosphorus View", "Romantic", "Fine Dining", "Reservations"]
      },
      {
        name: "Lacivert Restaurant",
        type: "Seafood",
        rating: 4.7,
        distance: "Anadolu Hisarı",
        features: ["Waterfront", "Intimate", "Seafood", "Sunset View"]
      },
      {
        name: "Feriye Palace Restaurant",
        type: "Ottoman Cuisine",
        rating: 4.8,
        distance: "Ortaköy",
        features: ["Historic", "Bosphorus View", "Elegant", "Special Occasions"]
      }
    ],
    processingSteps: [
      "Understanding: romantic + restaurant + Bosphorus view + anniversary",
      "Location: Bosphorus waterfront areas",
      "Filtering: Romantic atmosphere + water view",
      "Ranking: Romance score + view quality + special occasion suitability",
      "Context: Anniversary = special occasion preferences"
    ],
    conversationContext: [],
    traditionalSteps: [
      "Select 'Restaurant' category",
      "Filter by 'Waterfront' or 'View'",
      "Search multiple areas along Bosphorus",
      "Check individual photos for view quality",
      "Read reviews for romantic atmosphere confirmation"
    ]
  }
];

const ProcessingStep: React.FC<{ step: string; index: number; isActive: boolean }> = ({ 
  step, 
  index, 
  isActive 
}) => (
  <div 
    className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
      isActive ? 'scale-105' : ''
    }`}
    style={{
      background: isActive 
        ? `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`
        : colors.ui.gray50,
      borderLeft: `3px solid ${isActive ? colors.brand.blue : colors.ui.gray300}`
    }}
  >
    <div
      className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
        isActive ? 'text-white' : 'text-gray-500'
      }`}
      style={{
        background: isActive 
          ? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
          : colors.ui.gray300
      }}
    >
      {isActive ? <FiCheckCircle className="w-4 h-4" /> : index + 1}
    </div>
    <span 
      className={`text-sm ${isActive ? 'font-medium' : ''}`}
      style={{ color: isActive ? colors.neutral.textBlack : colors.neutral.slateGray }}
    >
      {step}
    </span>
  </div>
);

const LocationCard: React.FC<{ location: LocationResult; index: number }> = ({ location, index }) => (
  <div
    className="p-4 rounded-xl border transition-all duration-300 hover:scale-102"
    style={{
      background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
      borderColor: colors.ui.gray200,
      animationDelay: `${index * 100}ms`
    }}
  >
    <div className="flex items-start justify-between mb-3">
      <div>
        <h4 className="font-semibold" style={{ color: colors.neutral.textBlack }}>
          {location.name}
        </h4>
        <p className="text-sm" style={{ color: colors.neutral.slateGray }}>
          {location.type} • {location.distance}
        </p>
      </div>
      <div className="flex items-center space-x-1">
        <FiStar className="w-4 h-4" style={{ color: colors.brand.green }} />
        <span className="text-sm font-medium" style={{ color: colors.neutral.textBlack }}>
          {location.rating}
        </span>
      </div>
    </div>
    
    <div className="flex flex-wrap gap-2">
      {location.features.map((feature, idx) => (
        <span
          key={idx}
          className="px-2 py-1 rounded-lg text-xs"
          style={{
            background: colors.ui.green50,
            color: colors.neutral.textBlack
          }}
        >
          {feature}
        </span>
      ))}
    </div>
  </div>
);

const AIPlaygroundDemo: React.FC<{ onGetStarted?: () => void }> = ({ onGetStarted }) => {
  const [activeScenario, setActiveScenario] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showComparison, setShowComparison] = useState(false);

  const currentScenario = demoScenarios[activeScenario];

  useEffect(() => {
    if (isPlaying) {
      const timer = setInterval(() => {
        setActiveStep(prev => {
          if (prev >= currentScenario.processingSteps.length - 1) {
            setIsPlaying(false);
            setShowComparison(true);
            return prev;
          }
          return prev + 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isPlaying, currentScenario.processingSteps.length]);

  const startDemo = () => {
    setActiveStep(0);
    setShowComparison(false);
    setIsPlaying(true);
  };

  return (
    <div className="py-16 bg-transparent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div
            className="inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <FiZap className="w-5 h-5" style={{ color: colors.brand.green }} />
            <span className="text-sm font-medium" style={{ color: colors.neutral.textBlack }}>
              AI vs Traditional Search
            </span>
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span style={{ color: colors.brand.navy }}>See AI</span>
            <br />
            <span
              className="text-transparent bg-clip-text"
              style={{
                backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
              }}
            >
              In Action
            </span>
          </h2>
          
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: colors.neutral.slateGray }}>
            Watch how our AI understands context, processes natural language, 
            and delivers personalized results in real-time.
          </p>
        </div>

        {/* Scenario Selector */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-4">
            {demoScenarios.map((scenario, index) => (
              <button
                key={scenario.id}
                onClick={() => {
                  setActiveScenario(index);
                  setActiveStep(0);
                  setIsPlaying(false);
                  setShowComparison(false);
                }}
                className={`px-6 py-3 rounded-xl transition-all duration-300 ${
                  activeScenario === index ? 'scale-105' : 'hover:scale-102'
                }`}
                style={{
                  background: activeScenario === index
                    ? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
                    : colors.ui.gray100,
                  color: activeScenario === index ? 'white' : colors.neutral.textBlack
                }}
              >
                {scenario.id === 'cozy-cafe' ? 'Work Cafe' : 'Romantic Dinner'}
              </button>
            ))}
          </div>
        </div>

        {/* Main Demo Area */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* AI Processing Side */}
          <div
            className="p-8 rounded-3xl border"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <div className="flex items-center space-x-3 mb-6">
              <div
                className="w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)` }}
              >
                <FiMessageCircle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold" style={{ color: colors.neutral.textBlack }}>
                  AI-Powered Search
                </h3>
                <p className="text-sm" style={{ color: colors.neutral.slateGray }}>
                  Natural conversation understanding
                </p>
              </div>
            </div>

            {/* User Query */}
            <div className="mb-6">
              <div className="flex justify-end mb-4">
                <div
                  className="max-w-sm p-4 rounded-2xl rounded-tr-sm"
                  style={{ background: colors.brand.blue, color: 'white' }}
                >
                  <p className="text-sm">{currentScenario.userQuery}</p>
                </div>
              </div>
            </div>

            {/* Processing Steps */}
            <div className="space-y-3 mb-6">
              <h4 className="font-semibold" style={{ color: colors.neutral.textBlack }}>
                AI Processing Steps:
              </h4>
              {currentScenario.processingSteps.map((step, index) => (
                <ProcessingStep
                  key={index}
                  step={step}
                  index={index}
                  isActive={isPlaying ? index <= activeStep : false}
                />
              ))}
            </div>

            {/* Start Demo Button */}
            <button
              onClick={startDemo}
              disabled={isPlaying}
              className="w-full py-3 rounded-xl font-semibold transition-all duration-300 disabled:opacity-50"
              style={{
                background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
                color: 'white'
              }}
            >
              {isPlaying ? 'Processing...' : 'Start AI Demo'}
            </button>
          </div>

          {/* Traditional Search Side */}
          <div
            className="p-8 rounded-3xl border"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.gray50} 0%, ${colors.neutral.cloudWhite} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <div className="flex items-center space-x-3 mb-6">
              <div
                className="w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ background: colors.ui.gray300 }}
              >
                <FiSearch className="w-6 h-6" style={{ color: colors.neutral.textBlack }} />
              </div>
              <div>
                <h3 className="text-xl font-bold" style={{ color: colors.neutral.textBlack }}>
                  Traditional Search
                </h3>
                <p className="text-sm" style={{ color: colors.neutral.slateGray }}>
                  Manual filtering and browsing
                </p>
              </div>
            </div>

            {/* Traditional Steps */}
            <div className="space-y-3 mb-6">
              <h4 className="font-semibold" style={{ color: colors.neutral.textBlack }}>
                Manual Steps Required:
              </h4>
              {currentScenario.traditionalSteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 rounded-lg" style={{ background: colors.ui.gray100 }}>
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                    style={{ background: colors.ui.gray300, color: colors.neutral.textBlack }}
                  >
                    {index + 1}
                  </div>
                  <span className="text-sm" style={{ color: colors.neutral.slateGray }}>
                    {step}
                  </span>
                </div>
              ))}
            </div>

            <div className="text-center p-4 rounded-xl" style={{ background: colors.ui.gray100 }}>
              <p className="text-sm" style={{ color: colors.neutral.slateGray }}>
                Multiple steps, manual filtering, time-consuming process
              </p>
            </div>
          </div>
        </div>

        {/* Results Comparison */}
        {showComparison && (
          <div
            className="p-8 rounded-3xl border animate-fade-in"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold mb-2" style={{ color: colors.neutral.textBlack }}>
                AI Results
              </h3>
              <p style={{ color: colors.neutral.slateGray }}>
                Personalized recommendations based on context understanding
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {currentScenario.searchResults.map((location, index) => (
                <LocationCard key={index} location={location} index={index} />
              ))}
            </div>

            <div className="text-center">
              <button
                onClick={onGetStarted}
                className="inline-flex items-center space-x-2 px-8 py-4 rounded-2xl font-semibold transition-all duration-300 hover:scale-105"
                style={{
                  background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
                  color: 'white'
                }}
              >
                <span>Try It Yourself</span>
                <FiArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIPlaygroundDemo;
