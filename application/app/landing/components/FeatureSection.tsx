import React from 'react';
import { colors } from '@/app/colors';
import { FiMapPin, FiMessageSquare, FiZap, FiTarget, FiHeart, FiTrendingUp, FiShield, FiUsers, FiGlobe, FiLayers } from 'react-icons/fi';

const FeatureSection: React.FC = () => {
  const mainFeatures = [
    {
      icon: <FiGlobe className="w-8 h-8" />,
      title: "Interactive 3D Globe",
      description: "Explore Istanbul through our stunning 3D globe interface. Seamlessly transition from global view to detailed street-level maps with smooth animations and real-time location tracking."
    },
    {
      icon: <FiMessageSquare className="w-8 h-8" />,
      title: "Advanced Conversational AI",
      description: "Chat naturally with our AI that understands context, remembers your preferences, and provides personalized recommendations. No keywords or complex searches needed."
    },
    {
      icon: <FiTarget className="w-8 h-8" />,
      title: "Intelligent POI Management",
      description: "Discover verified locations with our three-tier POI system. Add your own discoveries, favorite places, and contribute to the community database."
    }
  ];

  const secondaryFeatures = [
    {
      icon: <FiMapPin className="w-6 h-6" />,
      title: "Comprehensive Coverage",
      description: "Complete Istanbul coverage with official and community-verified locations"
    },
    {
      icon: <FiLayers className="w-6 h-6" />,
      title: "Multi-Layer Mapping",
      description: "Switch between globe, satellite, and street views seamlessly"
    },
    {
      icon: <FiZap className="w-6 h-6" />,
      title: "Lightning Performance",
      description: "Sub-second responses with real-time location processing"
    },
    {
      icon: <FiHeart className="w-6 h-6" />,
      title: "Smart Favorites",
      description: "AI-powered recommendations based on your saved places"
    },
    {
      icon: <FiUsers className="w-6 h-6" />,
      title: "Community Driven",
      description: "User-contributed locations with admin review system"
    },
    {
      icon: <FiShield className="w-6 h-6" />,
      title: "Privacy First",
      description: "Secure authentication with optional location sharing"
    }
  ];

  return (
    <div id="features" className="py-12 bg-transparent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-10">
          <div
            className="inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <FiTrendingUp
              className="w-5 h-5"
              style={{ color: colors.supporting.lightBlue }}
            />
            <span
              className="text-sm font-medium"
              style={{ color: colors.neutral.textBlack }}
            >
              Revolutionary Technology
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span style={{ color: colors.brand.navy }}>Why Choose</span>
            <br />
            <span
              className="text-transparent bg-clip-text"
              style={{
                backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
              }}
            >
              Wizlop?
            </span>
          </h2>
          <p
            className="text-xl max-w-3xl mx-auto leading-relaxed"
            style={{ color: colors.neutral.slateGray }}
          >
            We've revolutionized location discovery with AI that truly understands geography,
            conversation, and the nuances of urban exploration.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className="group relative p-8 rounded-3xl border-2 transition-all duration-300 overflow-hidden"
              style={{
                background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
                borderColor: colors.ui.gray200
              }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:20px_20px] opacity-50"></div>

              <div className="relative">
                <div
                  className="w-20 h-20 rounded-2xl flex items-center justify-center text-white mb-8 shadow-lg"
                  style={{
                    background: index === 0 ? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)` :
                               index === 1 ? `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.green} 100%)` :
                               `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`
                  }}
                >
                  {feature.icon}
                </div>
                <h3
                  className="text-2xl font-bold mb-4 group-hover:opacity-80 transition-opacity"
                  style={{ color: colors.neutral.textBlack }}
                >
                  {feature.title}
                </h3>
                <p
                  className="leading-relaxed text-lg"
                  style={{ color: colors.neutral.slateGray }}
                >
                  {feature.description}
                </p>
              </div>

              {/* Hover Effect */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"
                style={{
                  background: `linear-gradient(135deg, ${colors.supporting.mintGreen}20 0%, ${colors.neutral.cloudWhite}20 100%)`
                }}
              ></div>
            </div>
          ))}
        </div>

        {/* Secondary Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {secondaryFeatures.map((feature, index) => (
            <div
              key={index}
              className="p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300"
              style={{
                backgroundColor: `${colors.neutral.cloudWhite}CC`,
                borderColor: colors.ui.gray200
              }}
            >
              <div className="flex items-start space-x-4">
                <div
                  className="w-12 h-12 rounded-xl flex items-center justify-center"
                  style={{
                    background: index % 3 === 0 ? colors.ui.blue100 :
                               index % 3 === 1 ? colors.ui.navy100 : colors.ui.green100,
                    color: index % 3 === 0 ? colors.brand.blue :
                           index % 3 === 1 ? colors.brand.navy : colors.brand.green
                  }}
                >
                  {feature.icon}
                </div>
                <div>
                  <h4
                    className="font-bold mb-2"
                    style={{ color: colors.neutral.textBlack }}
                  >
                    {feature.title}
                  </h4>
                  <p
                    className="text-sm leading-relaxed"
                    style={{ color: colors.neutral.slateGray }}
                  >
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Stats Section */}
        <div className="relative mt-10">
          {/* Background */}
          <div
            className="absolute inset-0 rounded-3xl"
            style={{
              background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`
            }}
          ></div>
          <div
            className="absolute inset-0 rounded-3xl bg-[size:30px_30px]"
            style={{
              backgroundImage: 'linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)'
            }}
          ></div>

          <div className="relative p-12 text-white">
            <div className="text-center mb-12">
              {/* Launch Badge */}
              <div className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-semibold text-white">Coming Soon</span>
              </div>

              <h3 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                <span className="text-transparent bg-clip-text" style={{
                  backgroundImage: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.neutral.cloudWhite} 100%)`
                }}>
                  Revolutionary
                </span>
                <br />
                <span className="text-white">AI Location Platform</span>
              </h3>

              <p
                className="text-xl leading-relaxed max-w-2xl mx-auto"
                style={{ color: colors.neutral.cloudWhite }}
              >
                Experience the future of location discovery with cutting-edge AI technology,
                immersive 3D visualization, and intelligent conversation-based search.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
              <div className="group p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl mb-4">🤖</div>
                <div
                  className="text-xl font-bold mb-3 text-transparent bg-clip-text group-hover:scale-105 transition-transform duration-300"
                  style={{
                    backgroundImage: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.neutral.cloudWhite} 100%)`
                  }}
                >
                  Smart AI Engine
                </div>
                <div
                  className="text-sm leading-relaxed"
                  style={{ color: colors.neutral.cloudWhite }}
                >
                  Advanced conversational AI that understands natural language and context
                </div>
              </div>

              <div className="group p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl mb-4">🎯</div>
                <div
                  className="text-xl font-bold mb-3 text-transparent bg-clip-text group-hover:scale-105 transition-transform duration-300"
                  style={{
                    backgroundImage: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.neutral.cloudWhite} 100%)`
                  }}
                >
                  Precision Mapping
                </div>
                <div
                  className="text-sm leading-relaxed"
                  style={{ color: colors.neutral.cloudWhite }}
                >
                  Accurate location data with real-time updates and verified POI database
                </div>
              </div>

              <div className="group p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl mb-4">🏘️</div>
                <div
                  className="text-xl font-bold mb-3 text-transparent bg-clip-text group-hover:scale-105 transition-transform duration-300"
                  style={{
                    backgroundImage: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.neutral.cloudWhite} 100%)`
                  }}
                >
                  Local Discovery
                </div>
                <div
                  className="text-sm leading-relaxed"
                  style={{ color: colors.neutral.cloudWhite }}
                >
                  Neighborhood-level exploration with insider tips and hidden gems
                </div>
              </div>

              <div className="group p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl mb-4">🌍</div>
                <div
                  className="text-xl font-bold mb-3 text-transparent bg-clip-text group-hover:scale-105 transition-transform duration-300"
                  style={{
                    backgroundImage: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.neutral.cloudWhite} 100%)`
                  }}
                >
                  3D Visualization
                </div>
                <div
                  className="text-sm leading-relaxed"
                  style={{ color: colors.neutral.cloudWhite }}
                >
                  Interactive globe with seamless transitions to detailed street maps
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureSection;
