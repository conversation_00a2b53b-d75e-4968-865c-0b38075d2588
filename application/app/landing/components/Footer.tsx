/** @format */

import { colors } from '@/app/colors';
import Image from 'next/image';
import React from 'react';
import { FiHeart } from 'react-icons/fi';

const Footer: React.FC = () => {
	return (
		<footer className='py-12 relative bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
					{/* Brand Section */}
					<div className='col-span-1 md:col-span-2'>
						<div className='flex items-center space-x-3 mb-4'>
							<div className='w-10 h-10 relative'>
								<Image
									src='/logo/512x512.png'
									alt='Wizlop Logo'
									width={40}
									height={40}
									className='rounded-xl'
								/>
							</div>
							<h3
								className='text-2xl font-bold'
								style={{ color: colors.neutral.textBlack }}>
								Wizlop
							</h3>
						</div>
						<p
							className='mb-4 max-w-md leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Revolutionizing location discovery through conversational AI. Find
							exactly what you're looking for in Istanbul.
						</p>
						<div
							className='flex items-center'
							style={{ color: colors.neutral.slateGray }}>
							<span>Made with</span>
							<FiHeart
								className='mx-2'
								style={{ color: colors.brand.green }}
							/>
							<span>in Istanbul</span>
						</div>
					</div>

					{/* Product Links */}
					<div>
						<h4
							className='text-lg font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Product
						</h4>
						<ul className='space-y-2'>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Features
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									How it Works
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Credits
								</button>
							</li>
						</ul>
					</div>

					{/* Company Links */}
					<div>
						<h4
							className='text-lg font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Company
						</h4>
						<ul className='space-y-2'>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									About Us
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Contact
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-sm'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Privacy Policy
								</button>
							</li>
						</ul>
					</div>
				</div>

				{/* Bottom Section */}
				<div
					className='border-t mt-8 pt-6 text-center'
					style={{ borderColor: colors.ui.gray200 }}>
					<p
						className='text-sm'
						style={{ color: colors.neutral.slateGray }}>
						© 2024 Wizlop. All rights reserved. • Intelligent Location Discovery
						Platform
					</p>
				</div>
			</div>
		</footer>
	);
};

export default Footer;
