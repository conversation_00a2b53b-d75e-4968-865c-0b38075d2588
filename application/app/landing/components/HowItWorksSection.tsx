import React from 'react';
import { FiMessageCircle, FiGlobe, FiArrowRight, FiTarget, FiZap, FiCpu } from 'react-icons/fi';
import { colors } from '@/app/colors';

const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      number: "01",
      icon: <FiMessageCircle className="w-6 h-6 sm:w-8 sm:h-8" />,
      title: "Start a Conversation",
      description: "Simply ask what you're looking for in natural language. No complex searches or keywords needed.",
      example: "Find me a cozy cafe with WiFi near Galata Tower",
      color: colors.brand.blue,
      bgColor: colors.ui.blue50
    },
    {
      number: "02",
      icon: <FiCpu className="w-6 h-6 sm:w-8 sm:h-8" />,
      title: "AI Processes Your Request",
      description: "Our advanced AI understands context, location preferences, and searches through verified POIs.",
      example: "Analyzing: cafe + WiFi + cozy + near Galata Tower",
      color: colors.brand.navy,
      bgColor: colors.ui.navy50
    },
    {
      number: "03",
      icon: <FiGlobe className="w-6 h-6 sm:w-8 sm:h-8" />,
      title: "Explore on Interactive Globe",
      description: "View results on our stunning 3D globe, then seamlessly zoom into detailed street-level maps.",
      example: "3D visualization with smooth transitions",
      color: colors.brand.green,
      bgColor: colors.ui.green50
    },
    {
      number: "04",
      icon: <FiTarget className="w-6 h-6 sm:w-8 sm:h-8" />,
      title: "Get Precise Results",
      description: "Receive personalized recommendations with exact locations, directions, and insider insights.",
      example: "5 perfect matches with walking directions",
      color: colors.brand.blue,
      bgColor: colors.ui.blue50
    }
  ];



  return (
    <div id="how-it-works" className="py-12 bg-transparent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-10">
          <div
            className="inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border"
            style={{
              background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
              borderColor: colors.ui.gray200
            }}
          >
            <FiZap
              className="w-5 h-5"
              style={{ color: colors.brand.green }}
            />
            <span
              className="text-sm font-medium"
              style={{ color: colors.neutral.textBlack }}
            >
              Simple & Powerful
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span style={{ color: colors.brand.navy }}>How</span>
            <br />
            <span
              className="text-transparent bg-clip-text"
              style={{
                backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
              }}
            >
              Wizlop Works
            </span>
          </h2>
          <p
            className="text-xl max-w-3xl mx-auto leading-relaxed"
            style={{ color: colors.neutral.slateGray }}
          >
            From conversation to discovery in four simple steps. 
            Experience the future of location search powered by AI and beautiful 3D visualization.
          </p>
        </div>

        {/* Steps Flow */}
        <div className="relative mb-10">
          {/* Connection Lines */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-gray-300 to-transparent transform -translate-y-1/2"></div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                {/* Step Card */}
                <div
                  className="relative p-8 pt-16 rounded-3xl border-2 transition-all duration-300 bg-white/90 backdrop-blur-sm h-[450px] flex flex-col"
                  style={{
                    borderColor: colors.ui.gray200
                  }}
                >
                  {/* Step Number - Fixed positioning */}
                  <div
                    className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-lg"
                    style={{ backgroundColor: step.color }}
                  >
                    {step.number}
                  </div>

                  {/* Icon */}
                  <div
                    className="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl flex items-center justify-center text-white mb-8 shadow-lg mx-auto"
                    style={{ backgroundColor: step.color }}
                  >
                    {step.icon}
                  </div>

                  {/* Content - Flex grow to ensure equal height */}
                  <div className="flex-grow flex flex-col text-center mt-4">
                    <h3
                      className="text-lg font-bold mb-4"
                      style={{ color: colors.neutral.textBlack }}
                    >
                      {step.title}
                    </h3>
                    <p
                      className="text-xs leading-relaxed mb-6 flex-grow"
                      style={{ color: colors.neutral.slateGray }}
                    >
                      {step.description}
                    </p>

                    {/* Example */}
                    <div
                      className="p-3 rounded-xl text-xs font-medium mt-auto"
                      style={{
                        backgroundColor: step.bgColor,
                        color: step.color
                      }}
                    >
                      {step.example}
                    </div>
                  </div>
                </div>

                {/* Arrow (hidden on mobile) */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: colors.neutral.cloudWhite }}
                    >
                      <FiArrowRight
                        className="w-4 h-4"
                        style={{ color: colors.supporting.lightBlue }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>


      </div>
    </div>
  );
};

export default HowItWorksSection;
