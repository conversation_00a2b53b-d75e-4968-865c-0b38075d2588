/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import {
	FiMapPin,
	FiMessageCircle,
	FiStar,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	return (
		<div className='relative min-h-screen overflow-hidden bg-transparent'>
			{/* Complex Background Patterns */}
			<div className='absolute inset-0'>
				{/* Geometric shapes */}
				<div
					className='absolute top-20 left-10 w-32 h-32 rounded-full opacity-20 blur-xl'
					style={{
						background: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
					}}></div>
				<div
					className='absolute top-40 right-20 w-48 h-48 rounded-full opacity-15 blur-2xl'
					style={{
						background: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
					}}></div>
				<div
					className='absolute bottom-20 left-1/4 w-24 h-24 rounded-full opacity-25 blur-lg'
					style={{
						background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
					}}></div>

				{/* Grid pattern */}
				<div
					className='absolute inset-0 bg-[size:50px_50px]'
					style={{
						backgroundImage: `linear-gradient(${colors.brand.green}08 1px, transparent 1px), linear-gradient(90deg, ${colors.brand.green}08 1px, transparent 1px)`,
					}}></div>

				{/* Floating elements with better positioning */}
				<div
					className='absolute top-32 left-16 w-3 h-3 rounded-full animate-bounce-gentle'
					style={{ backgroundColor: colors.brand.green }}></div>
				<div
					className='absolute top-64 right-32 w-2 h-2 rounded-full animate-bounce-gentle animate-delay-500'
					style={{ backgroundColor: colors.supporting.lightBlue }}></div>
				<div
					className='absolute bottom-48 left-32 w-4 h-4 rounded-full animate-bounce-gentle animate-delay-1000'
					style={{ backgroundColor: colors.supporting.mintGreen }}></div>
				<div
					className='absolute top-80 left-1/3 w-2 h-2 rounded-full animate-bounce-gentle animate-delay-1500'
					style={{ backgroundColor: colors.brand.blue }}></div>
			</div>

			<div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
				<div className='grid lg:grid-cols-2 gap-16 items-center'>
					{/* Left Column - Content */}
					<div className='space-y-8'>
						{/* Badge */}
						<div className='animate-fade-in'>
							<div
								className='inline-flex items-center space-x-2 rounded-full px-4 py-2 border'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
									borderColor: colors.ui.gray200,
								}}>
								<FiStar
									className='w-4 h-4'
									style={{ color: colors.supporting.lightBlue }}
								/>
								<span
									className='text-sm font-medium'
									style={{ color: colors.neutral.textBlack }}>
									AI-Powered Location Discovery
								</span>
							</div>
						</div>

						{/* Main Headline */}
						<div className='animate-slide-up'>
							<h1 className='text-5xl md:text-7xl font-bold leading-tight'>
								<span style={{ color: colors.brand.navy }}>Discover</span>
								<br />
								<span
									className='text-transparent bg-clip-text'
									style={{
										backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
									}}>
									Istanbul
								</span>
								<br />
								<span style={{ color: colors.brand.navy }}>Through</span>
								<br />
								<span
									className='text-transparent bg-clip-text'
									style={{
										backgroundImage: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
									}}>
									Conversation
								</span>
							</h1>
						</div>

						{/* Subtitle */}
						<div className='animate-slide-up animate-delay-100'>
							<p
								className='text-xl leading-relaxed max-w-lg'
								style={{ color: colors.neutral.slateGray }}>
								Stop searching with keywords. Start talking to our AI that
								understands geography and finds exactly what you're looking for
								in Istanbul.
							</p>
						</div>

						{/* Platform Features */}
						<div className='animate-slide-up animate-delay-200 flex items-center space-x-8'>
							<div className='text-center'>
								<div
									className='text-2xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									AI-Powered
								</div>
								<div
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									Discovery Engine
								</div>
							</div>
							<div className='text-center'>
								<div
									className='text-2xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									Map-Based
								</div>
								<div
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									Precision
								</div>
							</div>
							<div className='text-center'>
								<div
									className='text-2xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									Neighborhood
								</div>
								<div
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									Level Exploration
								</div>
							</div>
						</div>

						{/* CTA Buttons */}
						<div className='animate-slide-up animate-delay-300 flex flex-col sm:flex-row gap-4'>
							<button
								onClick={onGetStarted}
								className='group text-white px-8 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
								}}>
								<span>Start Exploring</span>
								<FiTrendingUp className='w-5 h-5 group-hover:translate-x-1 transition-transform' />
							</button>
						</div>

						{/* Example Query */}
						<div className='animate-slide-up animate-delay-400'>
							<div
								className='backdrop-blur-sm border rounded-2xl p-4 shadow-sm'
								style={{
									backgroundColor: `${colors.neutral.cloudWhite}CC`,
									borderColor: colors.ui.gray200,
								}}>
								<div className='flex items-center space-x-3'>
									<div
										className='w-8 h-8 rounded-full flex items-center justify-center'
										style={{
											background: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
										}}>
										<FiMessageCircle className='w-4 h-4 text-white' />
									</div>
									<p
										className='italic'
										style={{ color: colors.neutral.textBlack }}>
										"Find me a cozy cafe nearby where I can work"
									</p>
								</div>
							</div>
						</div>
					</div>

					{/* Right Column - Visual Elements */}
					<div className='relative animate-fade-in animate-delay-200'>
						{/* Main Visual Container */}
						<div className='relative'>
							{/* Background Card */}
							<div
								className='rounded-3xl p-8 shadow-2xl border'
								style={{
									background: `linear-gradient(135deg, ${colors.neutral.cloudWhite} 0%, ${colors.ui.green50} 100%)`,
									borderColor: colors.ui.gray200,
								}}>
								{/* Feature Cards */}
								<div className='space-y-6'>
									<div
										className='flex items-center space-x-4 p-4 rounded-2xl border'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
											borderColor: colors.ui.gray200,
										}}>
										<div
											className='w-12 h-12 rounded-xl flex items-center justify-center'
											style={{
												background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
											}}>
											<FiMessageCircle className='w-6 h-6 text-white' />
										</div>
										<div>
											<h3
												className='font-semibold'
												style={{ color: colors.neutral.textBlack }}>
												Natural Language
											</h3>
											<p
												className='text-sm'
												style={{ color: colors.neutral.slateGray }}>
												Talk like a human
											</p>
										</div>
									</div>

									<div
										className='flex items-center space-x-4 p-4 rounded-2xl border'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.blue100} 0%, ${colors.ui.blue50} 100%)`,
											borderColor: colors.ui.gray200,
										}}>
										<div
											className='w-12 h-12 rounded-xl flex items-center justify-center'
											style={{
												background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.green} 100%)`,
											}}>
											<FiMapPin className='w-6 h-6 text-white' />
										</div>
										<div>
											<h3
												className='font-semibold'
												style={{ color: colors.neutral.textBlack }}>
												Geographic Intelligence
											</h3>
											<p
												className='text-sm'
												style={{ color: colors.neutral.slateGray }}>
												Precise boundaries
											</p>
										</div>
									</div>

									<div
										className='flex items-center space-x-4 p-4 rounded-2xl border'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.neutral.cloudWhite} 100%)`,
											borderColor: colors.ui.gray200,
										}}>
										<div
											className='w-12 h-12 rounded-xl flex items-center justify-center'
											style={{
												background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
											}}>
											<FiZap className='w-6 h-6 text-white' />
										</div>
										<div>
											<h3
												className='font-semibold'
												style={{ color: colors.neutral.textBlack }}>
												Lightning Fast
											</h3>
											<p
												className='text-sm'
												style={{ color: colors.neutral.slateGray }}>
												Sub-second results
											</p>
										</div>
									</div>
								</div>
							</div>

							{/* Floating Elements */}
							<div
								className='absolute -top-4 -right-4 w-16 h-16 rounded-2xl opacity-80 animate-bounce-gentle'
								style={{
									background: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
								}}></div>
							<div
								className='absolute -bottom-4 -left-4 w-12 h-12 rounded-xl opacity-70 animate-bounce-gentle animate-delay-500'
								style={{
									background: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
								}}></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
