/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useEffect, useRef, useState } from 'react';
import {
	FiGlobe,
	FiLayers,
	FiMapPin,
	FiMessageSquare,
	FiShield,
	FiTrendingUp,
	FiUsers,
	FiZap,
} from 'react-icons/fi';

interface FeatureLayer {
	depth: number; // 0-1, where 0 is background
	scrollMultiplier: number; // How fast it moves with scroll
	content: React.ReactNode;
	blendMode?: 'normal' | 'multiply' | 'overlay';
}

interface Feature {
	icon: React.ReactNode;
	title: string;
	description: string;
	details: string[];
	gradient: string;
	stats?: { label: string; value: string };
}

const features: Feature[] = [
	{
		icon: <FiGlobe className='w-8 h-8' />,
		title: 'Interactive 3D Globe',
		description:
			'Explore Istanbul through our stunning 3D globe interface with seamless transitions from global to street-level views.',
		details: [
			'Real-time location tracking',
			'Smooth zoom animations',
			'Street-level detail',
			'Interactive pin clustering',
		],
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
		stats: { label: 'Zoom Levels', value: '18+' },
	},
	{
		icon: <FiMessageSquare className='w-8 h-8' />,
		title: 'Advanced Conversational AI',
		description:
			'Chat naturally with AI that understands context, remembers preferences, and provides personalized recommendations.',
		details: [
			'Natural language processing',
			'Context memory',
			'Personalized suggestions',
			'Multi-language support',
		],
		gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		stats: { label: 'Languages', value: '47' },
	},
	{
		icon: <FiMapPin className='w-8 h-8' />,
		title: 'Intelligent Location Discovery',
		description:
			'Advanced algorithms that understand geography, user preferences, and real-time data to find perfect matches.',
		details: [
			'Smart location matching',
			'Real-time availability',
			'Preference learning',
			'Geographic intelligence',
		],
		gradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
		stats: { label: 'Locations', value: '2,847' },
	},
	{
		icon: <FiUsers className='w-8 h-8' />,
		title: 'Community-Driven Database',
		description:
			'User-contributed locations with democratic categorization and admin-reviewed quality assurance.',
		details: [
			'User contributions',
			'Democratic voting',
			'Quality assurance',
			'Real user reviews',
		],
		gradient: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
		stats: { label: 'Contributors', value: '1,200+' },
	},
	{
		icon: <FiShield className='w-8 h-8' />,
		title: 'Privacy-First Design',
		description:
			'Secure authentication with optional location sharing and transparent data practices.',
		details: [
			'Secure authentication',
			'Optional location sharing',
			'Data transparency',
			'Privacy controls',
		],
		gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
		stats: { label: 'Security', value: '100%' },
	},
	{
		icon: <FiZap className='w-8 h-8' />,
		title: 'Real-Time Intelligence',
		description:
			'Live data integration with opening hours, crowd levels, and dynamic recommendations.',
		details: [
			'Live opening hours',
			'Crowd level data',
			'Dynamic recommendations',
			'Real-time updates',
		],
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
		stats: { label: 'Updates/Min', value: '150+' },
	},
];

const GeometricPatterns: React.FC = () => (
	<div className='absolute inset-0 opacity-3'>
		<svg
			width='100%'
			height='100%'
			viewBox='0 0 1000 1000'
			className='absolute inset-0'>
			<defs>
				<pattern
					id='grid'
					width='50'
					height='50'
					patternUnits='userSpaceOnUse'>
					<path
						d='M 50 0 L 0 0 0 50'
						fill='none'
						stroke={colors.brand.blue}
						strokeWidth='1'
					/>
				</pattern>
			</defs>
			<rect
				width='100%'
				height='100%'
				fill='url(#grid)'
			/>
		</svg>
	</div>
);

const IstanbulMapOutline: React.FC = () => (
	<div className='absolute inset-0 flex items-center justify-center opacity-5'>
		<svg
			width='400'
			height='300'
			viewBox='0 0 400 300'
			className='text-current'>
			<path
				d='M50 150 Q100 100 150 120 Q200 140 250 130 Q300 120 350 140 Q380 160 350 180 Q300 200 250 190 Q200 180 150 200 Q100 220 50 180 Z'
				fill='none'
				stroke={colors.brand.blue}
				strokeWidth='2'
				className='animate-pulse'
			/>
			{/* Location pins */}
			{Array.from({ length: 8 }).map((_, i) => (
				<circle
					key={i}
					cx={80 + i * 35}
					cy={140 + Math.sin(i) * 20}
					r='3'
					fill={colors.brand.green}
					className='animate-pulse'
					style={{ animationDelay: `${i * 0.2}s` }}
				/>
			))}
		</svg>
	</div>
);

const AnimatedLocationPins: React.FC = () => (
	<div className='absolute inset-0'>
		{Array.from({ length: 12 }).map((_, i) => (
			<div
				key={i}
				className='absolute w-4 h-4 rounded-full animate-pulse'
				style={{
					left: `${20 + ((i * 7) % 60)}%`,
					top: `${30 + ((i * 11) % 40)}%`,
					background:
						i % 3 === 0
							? colors.brand.blue
							: i % 3 === 1
							? colors.brand.green
							: colors.supporting.lightBlue,
					animationDelay: `${i * 0.3}s`,
					animationDuration: `${2 + Math.random()}s`,
				}}>
				<div
					className='absolute inset-0 rounded-full animate-ping'
					style={{
						background:
							i % 3 === 0
								? colors.brand.blue
								: i % 3 === 1
								? colors.brand.green
								: colors.supporting.lightBlue,
						animationDelay: `${i * 0.3}s`,
					}}></div>
			</div>
		))}
	</div>
);

const FeatureCard: React.FC<{
	feature: Feature;
	index: number;
	isVisible: boolean;
}> = ({ feature, index, isVisible }) => {
	const [isHovered, setIsHovered] = useState(false);

	return (
		<div
			className={`group relative p-8 rounded-3xl border-2 transition-all duration-700 overflow-hidden ${
				isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
			}`}
			style={{
				background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
				borderColor: isHovered ? colors.brand.blue : colors.ui.gray200,
				transform: isHovered
					? 'translateY(-8px) scale(1.02)'
					: 'translateY(0) scale(1)',
				transitionDelay: `${index * 100}ms`,
				boxShadow: isHovered
					? '0 25px 50px rgba(51, 194, 255, 0.15)'
					: '0 4px 15px rgba(0, 0, 0, 0.05)',
			}}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}>
			{/* Background Pattern */}
			<div className='absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:20px_20px] opacity-50'></div>

			{/* Gradient Overlay */}
			<div
				className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
				style={{ background: feature.gradient }}></div>

			{/* Content */}
			<div className='relative'>
				{/* Header */}
				<div className='flex items-start justify-between mb-6'>
					<div
						className='w-16 h-16 rounded-2xl flex items-center justify-center text-white transition-transform duration-300 group-hover:scale-110'
						style={{ background: feature.gradient }}>
						{feature.icon}
					</div>

					{feature.stats && (
						<div className='text-right'>
							<div
								className='text-2xl font-bold'
								style={{ color: colors.brand.blue }}>
								{feature.stats.value}
							</div>
							<div
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								{feature.stats.label}
							</div>
						</div>
					)}
				</div>

				{/* Title and Description */}
				<h3
					className='text-2xl font-bold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					{feature.title}
				</h3>
				<p
					className='text-lg leading-relaxed mb-6'
					style={{ color: colors.neutral.slateGray }}>
					{feature.description}
				</p>

				{/* Feature Details */}
				<div className='space-y-3'>
					{feature.details.map((detail, idx) => (
						<div
							key={idx}
							className='flex items-center space-x-3'>
							<div
								className='w-2 h-2 rounded-full'
								style={{ background: colors.brand.green }}></div>
							<span
								className='text-sm'
								style={{ color: colors.neutral.textBlack }}>
								{detail}
							</span>
						</div>
					))}
				</div>

				{/* Hover Effect Arrow */}
				<div
					className={`absolute bottom-6 right-6 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
						isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
					}`}
					style={{ background: feature.gradient }}>
					<FiTrendingUp className='w-4 h-4 text-white' />
				</div>
			</div>
		</div>
	);
};

const ParallaxFeatureSection: React.FC = () => {
	const sectionRef = useRef<HTMLDivElement>(null);
	const [scrollY, setScrollY] = useState(0);
	const [visibleCards, setVisibleCards] = useState<boolean[]>(
		new Array(features.length).fill(false)
	);

	useEffect(() => {
		const handleScroll = () => {
			if (sectionRef.current) {
				const rect = sectionRef.current.getBoundingClientRect();
				const scrollProgress = Math.max(
					0,
					Math.min(1, (window.innerHeight - rect.top) / window.innerHeight)
				);
				setScrollY(scrollProgress);

				// Check which cards should be visible
				const newVisibleCards = features.map((_, index) => {
					const cardProgress = scrollProgress - index * 0.1;
					return cardProgress > 0.2;
				});
				setVisibleCards(newVisibleCards);
			}
		};

		window.addEventListener('scroll', handleScroll);
		handleScroll(); // Initial check
		return () => window.removeEventListener('scroll', handleScroll);
	}, []);

	const featureLayers: FeatureLayer[] = [
		{
			depth: 0,
			scrollMultiplier: 0.1,
			content: <GeometricPatterns />,
			blendMode: 'overlay',
		},
		{
			depth: 0.3,
			scrollMultiplier: 0.3,
			content: <IstanbulMapOutline />,
			blendMode: 'multiply',
		},
		{
			depth: 0.7,
			scrollMultiplier: 0.7,
			content: <AnimatedLocationPins />,
		},
	];

	return (
		<div
			ref={sectionRef}
			className='absolute inset-0 bg-transparent overflow-hidden pointer-events-none'
			style={{ zIndex: -1 }}>
			{/* Parallax Layers */}
			{featureLayers.map((layer, index) => (
				<div
					key={index}
					className='absolute inset-0'
					style={{
						transform: `translateY(${
							scrollY * layer.scrollMultiplier * 100
						}px)`,
						zIndex: layer.depth * 10,
						mixBlendMode: layer.blendMode || 'normal',
					}}>
					{layer.content}
				</div>
			))}

			{/* Main Content */}
			<div className='relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-6'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiLayers
							className='w-5 h-5'
							style={{ color: colors.supporting.lightBlue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Revolutionary Technology
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Why Choose</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Wizlop?
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						We've revolutionized location discovery with AI that truly
						understands geography, conversation, and the nuances of urban
						exploration.
					</p>
				</div>

				{/* Features Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8'>
					{features.map((feature, index) => (
						<FeatureCard
							key={index}
							feature={feature}
							index={index}
							isVisible={visibleCards[index]}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default ParallaxFeatureSection;
