/** @format */

'use client';

import { useState, useEffect } from 'react';

interface CategoryData {
  category: string;
  count: number;
}

interface SubcategoryData {
  subcategory: string;
  count: number;
}

interface CategoriesResponse {
  success: boolean;
  categories: CategoryData[];
  subcategories: SubcategoryData[];
  total_categories: number;
  total_subcategories: number;
}

interface UseCategoriesReturn {
  categories: CategoryData[];
  subcategories: SubcategoryData[];
  totalCategories: number;
  totalSubcategories: number;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useCategories = (): UseCategoriesReturn => {
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [subcategories, setSubcategories] = useState<SubcategoryData[]>([]);
  const [totalCategories, setTotalCategories] = useState(0);
  const [totalSubcategories, setTotalSubcategories] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/pois/categories');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: CategoriesResponse = await response.json();

      if (data.success) {
        setCategories(data.categories);
        setSubcategories(data.subcategories);
        setTotalCategories(data.total_categories);
        setTotalSubcategories(data.total_subcategories);
      } else {
        throw new Error('Failed to fetch categories');
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const refetch = () => {
    fetchCategories();
  };

  return {
    categories,
    subcategories,
    totalCategories,
    totalSubcategories,
    loading,
    error,
    refetch
  };
};
