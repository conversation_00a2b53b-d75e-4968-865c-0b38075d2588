/** @format */

import { config } from '@/lib/config';
import { db } from '@/lib/database';
import { ConflictError, handleApiError, ValidationError } from '@/lib/errors';
import { logger } from '@/lib/logger';
import { withPerformanceMonitoring } from '@/lib/performance';
import { authRateLimit } from '@/lib/rateLimit';
import {
	sanitizeObject,
	validateAge,
	validateEmail,
	validateName,
	validatePassword,
	validateUsername,
} from '@/lib/security-headers';
import bcrypt from 'bcryptjs';
import { NextRequest, NextResponse } from 'next/server';

// Test the validation function
console.log('validatePassword function:', typeof validatePassword);
console.log('validatePassword test:', validatePassword('test'));

async function signupHandler(request: NextRequest) {
	// Apply rate limiting
	const rateLimitResponse = await authRateLimit(request);
	if (rateLimitResponse) {
		return rateLimitResponse;
	}

	try {
		const body = await request.json();

		// Sanitize all input
		const sanitizedBody = sanitizeObject(body);
		const {
			email,
			password,
			username,
			name,
			age,
			allowDataUsage = false,
		} = sanitizedBody;

		// Enhanced input validation
		if (!email || !validateEmail(email)) {
			throw new ValidationError('Valid email is required');
		}

		if (!username || !validateUsername(username)) {
			throw new ValidationError(
				'Username must be 3-20 characters, alphanumeric and underscores only'
			);
		}

		if (!name || !validateName(name)) {
			throw new ValidationError(
				'Name must be 2-50 characters, letters, spaces, hyphens, and apostrophes only'
			);
		}

		if (!age || !validateAge(parseInt(age))) {
			throw new ValidationError('Age must be between 13 and 120');
		}

		// Enhanced password validation
		console.log('Password validation:', { password, length: password.length });
		console.log('validatePassword function exists:', typeof validatePassword);
		console.log('validatePassword function:', validatePassword);
		const passwordValidation = validatePassword(password);
		console.log('Password validation result:', passwordValidation);
		if (!passwordValidation.isValid) {
			console.log('Password validation failed, throwing error');
			throw new ValidationError(passwordValidation.error!);
		}
		console.log('Password validation passed');

		// Check if user already exists (email)
		const existingUserByEmail = await db.getOne(
			'SELECT id FROM users WHERE email = $1',
			[email]
		);

		if (existingUserByEmail) {
			logger.authEvent('signup', email, false, { reason: 'user_exists_email' });
			throw new ConflictError('User with this email already exists');
		}

		// Check if username already exists
		const existingUserByUsername = await db.getOne(
			'SELECT id FROM users WHERE username = $1',
			[username]
		);

		if (existingUserByUsername) {
			logger.authEvent('signup', username, false, {
				reason: 'user_exists_username',
			});
			throw new ConflictError('Username already taken');
		}

		// Hash password with configured rounds
		const passwordHash = await bcrypt.hash(
			password,
			config.security.bcryptRounds
		);

		// Create new user and profile in a transaction
		const results = await db.transaction([
			{
				text: 'INSERT INTO users (email, username, password_hash) VALUES ($1, $2, $3) RETURNING *',
				params: [email, username, passwordHash],
			},
			{
				text: `INSERT INTO user_profiles (user_id, name, age, avatar_url, profile_completed, allow_data_usage)
               VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
				params: [
					null, // Will be replaced with user.id
					name,
					parseInt(age),
					`https://ui-avatars.com/api/?name=${encodeURIComponent(
						name
					)}&background=3B82F6&color=fff`,
					true,
					allowDataUsage,
				],
			},
		]);

		const newUser = results[0].rows[0];

		// Update the profile query with the actual user ID
		const profile = await db.insert('user_profiles', {
			user_id: newUser.id,
			name,
			age: parseInt(age),
			avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(
				name
			)}&background=3B82F6&color=fff`,
			profile_completed: true,
			allow_data_usage: allowDataUsage,
		});

		logger.authEvent('signup', newUser.id, true, {
			name,
			age: parseInt(age),
			profile_completed: true,
		});

		// Return user without password
		return NextResponse.json({
			user: {
				id: newUser.id,
				email: newUser.email,
				username: newUser.username,
			},
			profile: {
				id: profile.id,
				name: profile.name,
				age: profile.age,
				profile_completed: profile.profile_completed,
			},
		});
	} catch (error) {
		return handleApiError(error);
	}
}

export const POST = withPerformanceMonitoring(signupHandler, 'auth/signup');
