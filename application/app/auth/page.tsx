/** @format */

'use client';

import { LoadingSpinner } from '@/app/shared/system';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect } from 'react';
import { AuthPageLayout, IntegratedAuth } from './components';

function AuthPageContent() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { status } = useSession();

	// Get parameters from URL
	const mode = searchParams.get('mode') as 'signin' | 'signup' | null;
	const callbackUrl = searchParams.get('callbackUrl');
	const source = searchParams.get('source');

	// Redirect if already authenticated
	useEffect(() => {
		if (status === 'authenticated') {
			// Use callbackUrl if provided, otherwise default to current page or chat
			if (callbackUrl) {
				router.push(decodeURIComponent(callbackUrl));
			} else if (source === 'start-exploring') {
				router.push('/chat');
			} else {
				// Default: stay on current page (go back to where they came from)
				router.back();
			}
		}
	}, [status, router, callbackUrl, source]);

	// Show loading while checking auth
	if (status === 'loading') {
		return (
			<AuthPageLayout>
				<div className='flex justify-center items-center min-h-[50vh]'>
					<LoadingSpinner text='Checking authentication...' />
				</div>
			</AuthPageLayout>
		);
	}

	// Don't render if authenticated (will redirect)
	if (status === 'authenticated') {
		return null;
	}

	return (
		<AuthPageLayout>
			<IntegratedAuth defaultMode={mode || 'signup'} />
		</AuthPageLayout>
	);
}

export default function AuthPageModule() {
	return (
		<Suspense
			fallback={
				<AuthPageLayout>
					<div className='flex justify-center items-center min-h-[50vh]'>
						<LoadingSpinner text='Loading...' />
					</div>
				</AuthPageLayout>
			}>
			<AuthPageContent />
		</Suspense>
	);
}
