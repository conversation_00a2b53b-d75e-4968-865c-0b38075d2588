/** @format */

'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { LandingPage } from './landing/components';

export default function Home() {
	const router = useRouter();
	const { status } = useSession();

	if (status === 'loading') {
		return (
			<div className='min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center'>
				<div className='text-center'>
					<div className='w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4'></div>
					<p className='text-gray-600'>Loading...</p>
				</div>
			</div>
		);
	}

	const handleGetStarted = () => {
		if (status === 'authenticated') {
			router.push('/chat');
		} else {
			// Special handling for "Start Exploring" button - should go to chat after auth
			router.push('/auth?callbackUrl=%2Fchat&source=start-exploring');
		}
	};

	return <LandingPage onGetStarted={handleGetStarted} />;
}
