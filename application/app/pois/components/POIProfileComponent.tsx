/** @format */

'use client';

import { colors } from '@/app/colors';
import UnifiedInteractionButtons from '@/app/shared/userInteractions/components/UnifiedInteractionButtons';
import { POIType } from '@/app/shared/userInteractions/shared/types/base';
import { useSession } from 'next-auth/react';
import React from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import { FiClock, FiMapPin, FiPhone, FiStar } from 'react-icons/fi';
import { POIMediaGallery } from './POIMediaGallery';

/**
 * POI profile data interface
 */
interface POIProfile {
	poi_id: number | string;
	poi_type: string;
	name: string;
	category?: string;
	subcategory?: string;
	description?: string;
	profile_picture_url?: string;
	full_address?: string;
	country?: string;
	phone_number?: string;
	opening_hours?: string;
	rating?: number;
	reviews_count?: number;
	media_count?: number;
	visit_count?: number;
	like_count?: number;
	favorite_count?: number;
	created_by?: string;
	created_at?: string;
}

/**
 * POI profile component props
 */
interface POIProfileComponentProps {
	poi: POIProfile;
	isOwner?: boolean;
	onReportClick?: () => void;
	onWriteReview?: () => void;
	className?: string;
}

/**
 * Enhanced POI profile component with media gallery and management
 */
export const POIProfileComponent: React.FC<POIProfileComponentProps> = ({
	poi,
	isOwner = false,
	onReportClick,
	onWriteReview,
	className = '',
}) => {
	const { data: session } = useSession();

	/**
	 * Check if current user is the POI owner
	 */
	const checkOwnership = () => {
		return session?.user?.id === poi.created_by || isOwner;
	};

	/**
	 * Get category icon
	 */
	const getCategoryIcon = (category?: string) => {
		const icons: { [key: string]: string } = {
			restaurant: '🍽️',
			cafe: '☕',
			bar: '🍺',
			shop: '🛍️',
			hotel: '🏨',
			park: '🌳',
			hospital: '🏥',
			school: '🎓',
		};
		return icons[category || ''] || '📍';
	};

	/**
	 * Format category display name
	 */
	const formatCategoryName = (category?: string) => {
		if (!category) return '';
		return category
			.split('_')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	};

	const isOwnerOrAdmin = checkOwnership();

	return (
		<div
			className={`bg-white rounded-2xl shadow-lg border overflow-hidden ${className}`}
			style={{ borderColor: colors.ui.gray200 }}>
			{/* Clean Brand Header */}
			<div
				className='relative h-32 overflow-hidden'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
				}}>
				{/* Subtle Pattern */}
				<div className='absolute inset-0 opacity-10'>
					<div
						className='absolute inset-0'
						style={{
							backgroundImage: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
							backgroundSize: '60px 60px',
						}}></div>
				</div>
			</div>

			{/* Clean Profile Content */}
			<div className='relative p-6'>
				{/* Clean POI Header */}
				<div className='mb-6'>
					{/* POI Info */}
					<div className='space-y-4'>
						{/* Title Row */}
						<div className='flex items-center gap-4 mb-4'>
							<div
								className='w-12 h-12 rounded-xl flex items-center justify-center text-xl text-white flex-shrink-0 shadow-md'
								style={{ backgroundColor: colors.brand.blue }}>
								{getCategoryIcon(poi.category)}
							</div>
							<div className='flex-1'>
								<h1
									className='text-2xl lg:text-3xl font-bold leading-tight'
									style={{ color: colors.neutral.textBlack }}>
									{poi.name}
								</h1>
							</div>
							{/* Report Button */}
							{onReportClick && (
								<button
									onClick={onReportClick}
									className='flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 bg-gradient-to-r from-white via-red-50 to-white hover:from-red-50 hover:via-red-100 hover:to-red-50 hover:scale-105 shadow-sm hover:shadow-md'
									style={{
										color: colors.utility.error,
										borderColor: colors.utility.error,
									}}
									title='Report an issue with this place'>
									<FaExclamationTriangle className='w-4 h-4' />
									<span className='text-sm font-medium'>Report</span>
								</button>
							)}
						</div>

						{/* Category badges and User Interactions - Same Row */}
						<div className='flex justify-between items-center gap-4'>
							{/* Left side - Category badges */}
							<div className='flex flex-wrap items-center gap-3'>
								<div
									className='inline-flex items-center gap-2 px-3 py-1 rounded-lg border'
									style={{
										backgroundColor: colors.ui.blue50,
										borderColor: colors.ui.blue200,
									}}>
									<div
										className='w-2 h-2 rounded-full'
										style={{ backgroundColor: colors.brand.blue }}></div>
									<span
										className='text-sm font-medium'
										style={{ color: colors.brand.blue }}>
										{formatCategoryName(poi.category)}
									</span>
								</div>
								{poi.subcategory && (
									<div
										className='inline-flex items-center gap-2 px-3 py-1 rounded-lg border'
										style={{
											backgroundColor: colors.ui.gray50,
											borderColor: colors.ui.gray200,
										}}>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.slateGray }}>
											{formatCategoryName(poi.subcategory)}
										</span>
									</div>
								)}
								{poi.rating && (
									<div
										className='inline-flex items-center gap-2 px-3 py-1 rounded-lg border'
										style={{
											backgroundColor: colors.ui.green50,
											borderColor: colors.ui.green200,
										}}>
										<FiStar
											className='w-4 h-4'
											style={{ color: colors.brand.green }}
										/>
										<span
											className='text-sm font-medium'
											style={{ color: colors.brand.green }}>
											{poi.rating.toFixed(1)}
										</span>
									</div>
								)}
							</div>

							{/* Right side - User Interaction Buttons */}
							<div>
								<UnifiedInteractionButtons
									poiId={typeof poi.poi_id === 'number' ? poi.poi_id : null}
									poiType={poi.poi_type as POIType}
									userPoiTempId={
										poi.poi_type === 'user_temp'
											? typeof poi.poi_id === 'number'
												? poi.poi_id
												: null
											: null
									}
									userPoiApprovedId={
										poi.poi_type === 'user_approved'
											? typeof poi.poi_id === 'number'
												? poi.poi_id
												: null
											: null
									}
									initialData={{
										like_count: poi.like_count || 0,
										favorite_count: poi.favorite_count || 0,
										visit_count: poi.visit_count || 0,
										review_count: poi.reviews_count || 0,
										user_has_liked: false, // Will be loaded by the hook
										user_has_favorited: false, // Will be loaded by the hook
										user_has_visited: false, // Will be loaded by the hook
									}}
									layout='horizontal'
									showCounts={true}
									showLabels={true}
									size='md'
									enableOptimisticUpdates={true}
									autoLoad={true}
									onWriteReview={onWriteReview}
								/>
							</div>
						</div>
					</div>
				</div>

				{/* Clean Contact Information */}
				<div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
					{poi.full_address && (
						<div
							className='flex items-start gap-3 p-4 bg-gradient-to-br from-white via-blue-50 to-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02]'
							style={{ borderColor: colors.ui.gray200 }}>
							<div
								className='w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.blue50}, ${colors.brand.blue}20, ${colors.ui.blue50})`,
								}}>
								<FiMapPin
									className='w-4 h-4'
									style={{ color: colors.brand.blue }}
								/>
							</div>
							<div className='flex-1 min-w-0'>
								<p
									className='text-sm font-medium mb-1'
									style={{ color: colors.neutral.textBlack }}>
									Address
								</p>
								<p
									className='text-sm break-words'
									style={{ color: colors.neutral.slateGray }}>
									{poi.full_address}
								</p>
							</div>
						</div>
					)}
					{poi.phone_number && (
						<div
							className='flex items-start gap-3 p-4 bg-gradient-to-br from-white via-green-50 to-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02]'
							style={{ borderColor: colors.ui.gray200 }}>
							<div
								className='w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.green50}, ${colors.brand.green}20, ${colors.ui.green50})`,
								}}>
								<FiPhone
									className='w-4 h-4'
									style={{ color: colors.brand.green }}
								/>
							</div>
							<div className='flex-1 min-w-0'>
								<p
									className='text-sm font-medium mb-1'
									style={{ color: colors.neutral.textBlack }}>
									Phone
								</p>
								<a
									href={`tel:${poi.phone_number}`}
									className='text-sm font-medium hover:underline transition-colors duration-200'
									style={{ color: colors.brand.blue }}>
									{poi.phone_number}
								</a>
							</div>
						</div>
					)}
					{poi.opening_hours && (
						<div
							className='flex items-start gap-3 p-4 bg-gradient-to-br from-white via-indigo-50 to-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02]'
							style={{ borderColor: colors.ui.gray200 }}>
							<div
								className='w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.navy50}, ${colors.brand.navy}20, ${colors.ui.navy50})`,
								}}>
								<FiClock
									className='w-4 h-4'
									style={{ color: colors.brand.navy }}
								/>
							</div>
							<div className='flex-1 min-w-0'>
								<p
									className='text-sm font-medium mb-1'
									style={{ color: colors.neutral.textBlack }}>
									Hours
								</p>
								<p
									className='text-sm whitespace-pre-line'
									style={{ color: colors.neutral.slateGray }}>
									{poi.opening_hours}
								</p>
							</div>
						</div>
					)}
				</div>

				{/* Clean Description Section */}
				<div className='mb-6'>
					<div
						className='bg-white rounded-lg border shadow-sm p-4'
						style={{ borderColor: colors.ui.gray200 }}>
						<div className='mb-4'>
							<h3
								className='text-lg font-bold flex items-center gap-2'
								style={{ color: colors.neutral.textBlack }}>
								<div
									className='w-5 h-5 rounded flex items-center justify-center'
									style={{ backgroundColor: colors.brand.blue }}>
									<span className='text-white text-xs'>ℹ️</span>
								</div>
								About this place
							</h3>
						</div>

						<p className='text-gray-700 leading-relaxed text-base'>
							{poi.description ||
								'No description available yet. Be the first to share what makes this place special!'}
						</p>
					</div>
				</div>
			</div>

			{/* Clean Media Gallery Section */}
			<div style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<POIMediaGallery
					poiId={poi.poi_id}
					poiType={poi.poi_type}
					poiName={poi.name}
					showUploadButton={isOwnerOrAdmin}
					allowUploads={isOwnerOrAdmin}
				/>
			</div>
		</div>
	);
};
