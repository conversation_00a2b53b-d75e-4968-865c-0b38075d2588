/** @format */

'use client';

import { Session } from 'next-auth';
import { useEffect, useState } from 'react';
import {
	<PERSON>a<PERSON>row<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Fa<PERSON>ilter,
	FaSearch,
	FaTimes,
	FaUserPlus,
	FaUsers,
	FaUserShield,
} from 'react-icons/fa';

interface User {
	id: string;
	email: string;
	name: string;
	username: string;
	role: 'user' | 'agent' | 'superuser';
	permissions: string[];
	created_at: string;
	agent_activated_at: string | null;
	superuser_activated_at: string | null;
}

interface DashboardStats {
	total_users: number;
	total_agents: number;
	total_superusers: number;
	new_users_this_week: number;
}

const roleColors = {
	user: 'bg-gray-100 text-gray-800',
	agent: 'bg-blue-100 text-blue-800',
	superuser: 'bg-purple-100 text-purple-800',
};

const roleIcons = {
	user: FaUsers,
	agent: FaUserShield,
	superuser: FaCrown,
};

interface AdminDashboardClientProps {
	session: Session;
}

export default function AdminDashboardClient({
	session,
}: AdminDashboardClientProps) {
	// State
	const [users, setUsers] = useState<User[]>([]);
	const [stats, setStats] = useState<DashboardStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState('');

	// Search and filters
	const [searchTerm, setSearchTerm] = useState('');
	const [roleFilter, setRoleFilter] = useState<string>('all');
	const [currentPage] = useState(1);
	const [itemsPerPage] = useState(20);

	// Promotion modal
	const [showPromotionModal, setShowPromotionModal] = useState(false);
	const [selectedUser, setSelectedUser] = useState<User | null>(null);
	const [promotionType, setPromotionType] = useState<'agent' | 'superuser'>(
		'agent'
	);

	// User details modal state
	const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
	const [selectedUserDetails, setSelectedUserDetails] = useState<User | null>(
		null
	);
	const [userDetailsLoading, setUserDetailsLoading] = useState(false);
	const [userDetailsError, setUserDetailsError] = useState('');

	useEffect(() => {
		loadDashboardData();
	}, [searchTerm, roleFilter, currentPage]);

	const loadDashboardData = async () => {
		try {
			setLoading(true);

			// Load stats
			const statsResponse = await fetch('/api/admin/stats');
			if (statsResponse.ok) {
				const statsData = await statsResponse.json();
				setStats(statsData.stats);
			}

			// Load users
			const params = new URLSearchParams({
				search: searchTerm,
				role: roleFilter,
				page: currentPage.toString(),
				limit: itemsPerPage.toString(),
			});

			const usersResponse = await fetch(`/api/admin/users?${params}`);
			if (usersResponse.ok) {
				const usersData = await usersResponse.json();
				setUsers(usersData.users);
			} else {
				setError('Failed to load users');
			}
		} catch (error) {
			console.error('Error loading dashboard data:', error);
			setError('Failed to load dashboard data');
		} finally {
			setLoading(false);
		}
	};

	const handlePromoteUser = async (user: User, type: 'agent' | 'superuser') => {
		setSelectedUser(user);
		setPromotionType(type);
		setShowPromotionModal(true);
	};

	const confirmPromotion = async () => {
		if (!selectedUser) return;

		try {
			const endpoint =
				promotionType === 'agent'
					? '/api/admin/agents'
					: '/api/admin/superusers';
			const response = await fetch(endpoint, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					user_id: selectedUser.id,
					permissions:
						promotionType === 'agent' ? ['full_access'] : ['full_access'],
				}),
			});

			if (response.ok) {
				setShowPromotionModal(false);
				setSelectedUser(null);
				loadDashboardData(); // Reload data
			} else {
				const errorData = await response.json();
				setError(errorData.error || 'Failed to promote user');
			}
		} catch (error) {
			console.error('Error promoting user:', error);
			setError('Failed to promote user');
		}
	};

	// Fetch user details for modal
	const handleViewUserDetails = async (userId: string) => {
		setUserDetailsLoading(true);
		setUserDetailsError('');
		setShowUserDetailsModal(true);
		try {
			// Try to find user in already loaded users
			const user = users.find((u) => u.id === userId);
			if (user) {
				setSelectedUserDetails(user);
				setUserDetailsLoading(false);
				return;
			}
			// Otherwise, fetch from API (if you have a user details endpoint)
			const response = await fetch(`/api/admin/users?search=${userId}`);
			if (response.ok) {
				const data = await response.json();
				if (data.users && data.users.length > 0) {
					setSelectedUserDetails(data.users[0]);
				} else {
					setUserDetailsError('User not found');
				}
			} else {
				setUserDetailsError('Failed to fetch user details');
			}
		} catch {
			setUserDetailsError('Failed to fetch user details');
		} finally {
			setUserDetailsLoading(false);
		}
	};

	const filteredUsers = users.filter((user) => {
		const matchesSearch =
			!searchTerm ||
			user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.id.toLowerCase().includes(searchTerm.toLowerCase());

		const matchesRole = roleFilter === 'all' || user.role === roleFilter;

		return matchesSearch && matchesRole;
	});

	if (loading) {
		return (
			<div className='min-h-screen bg-gray-50 flex items-center justify-center'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto'></div>
					<p className='mt-4 text-gray-600'>Loading admin dashboard...</p>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			{/* Header */}
			<div className='bg-white shadow-sm border-b'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
					<div className='flex justify-between items-center'>
						<div>
							<h1 className='text-3xl font-bold text-gray-900'>
								Admin Dashboard
							</h1>
							<p className='text-gray-600 mt-1'>
								Manage users and system settings
							</p>
						</div>
						<div className='flex items-center space-x-4'>
							<span className='text-sm text-gray-500'>
								Welcome, {session?.user?.name || session?.user?.email}
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Stats Cards */}
			{stats && (
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
						<div className='bg-white rounded-lg shadow p-6'>
							<div className='flex items-center'>
								<FaUsers className='text-blue-500 text-2xl mr-3' />
								<div>
									<p className='text-sm font-medium text-gray-600'>
										Total Users
									</p>
									<p className='text-2xl font-bold text-gray-900'>
										{stats.total_users}
									</p>
								</div>
							</div>
						</div>
						<div className='bg-white rounded-lg shadow p-6'>
							<div className='flex items-center'>
								<FaUserShield className='text-green-500 text-2xl mr-3' />
								<div>
									<p className='text-sm font-medium text-gray-600'>Agents</p>
									<p className='text-2xl font-bold text-gray-900'>
										{stats.total_agents}
									</p>
								</div>
							</div>
						</div>
						<div className='bg-white rounded-lg shadow p-6'>
							<div className='flex items-center'>
								<FaCrown className='text-purple-500 text-2xl mr-3' />
								<div>
									<p className='text-sm font-medium text-gray-600'>
										Superusers
									</p>
									<p className='text-2xl font-bold text-gray-900'>
										{stats.total_superusers}
									</p>
								</div>
							</div>
						</div>
						<div className='bg-white rounded-lg shadow p-6'>
							<div className='flex items-center'>
								<FaUserPlus className='text-orange-500 text-2xl mr-3' />
								<div>
									<p className='text-sm font-medium text-gray-600'>
										New This Week
									</p>
									<p className='text-2xl font-bold text-gray-900'>
										{stats.new_users_this_week}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Error Message */}
			{error && (
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6'>
					<div className='bg-red-50 border border-red-200 rounded-lg p-4'>
						<p className='text-red-800'>{error}</p>
					</div>
				</div>
			)}

			{/* Main Content */}
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12'>
				{/* Search and Filters */}
				<div className='bg-white rounded-lg shadow mb-6 p-6'>
					<div className='flex flex-wrap items-center justify-between gap-4'>
						<div className='flex items-center space-x-4'>
							<div className='relative'>
								<FaSearch className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400' />
								<input
									type='text'
									placeholder='Search by email, name, username, or ID...'
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className='pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-80'
								/>
							</div>
							<div className='flex items-center space-x-2'>
								<FaFilter className='text-gray-400' />
								<select
									value={roleFilter}
									onChange={(e) => setRoleFilter(e.target.value)}
									className='border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'>
									<option value='all'>All Roles</option>
									<option value='user'>Users</option>
									<option value='agent'>Agents</option>
									<option value='superuser'>Superusers</option>
								</select>
							</div>
						</div>
					</div>
				</div>

				{/* Users Table */}
				<div className='bg-white rounded-lg shadow overflow-hidden'>
					<div className='px-6 py-4 border-b border-gray-200'>
						<h3 className='text-lg font-medium text-gray-900'>
							Users ({filteredUsers.length})
						</h3>
					</div>

					<div className='overflow-x-auto'>
						<table className='min-w-full divide-y divide-gray-200'>
							<thead className='bg-gray-50'>
								<tr>
									<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
										User
									</th>
									<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
										Role
									</th>
									<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
										Joined
									</th>
									<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
										Actions
									</th>
								</tr>
							</thead>
							<tbody className='bg-white divide-y divide-gray-200'>
								{filteredUsers.map((user) => {
									const RoleIcon = roleIcons[user.role];
									return (
										<tr
											key={user.id}
											className='hover:bg-gray-50'>
											<td className='px-6 py-4'>
												<div>
													<div className='text-sm font-medium text-gray-900'>
														{user.name}
													</div>
													<div className='text-sm text-gray-500'>
														{user.email}
													</div>
													<div className='text-xs text-gray-400'>
														@{user.username} • ID: {user.id.slice(0, 8)}...
													</div>
												</div>
											</td>
											<td className='px-6 py-4'>
												<span
													className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
														roleColors[user.role]
													}`}>
													<RoleIcon className='mr-1' />
													{user.role}
												</span>
											</td>
											<td className='px-6 py-4 text-sm text-gray-500'>
												{new Date(user.created_at).toLocaleDateString()}
											</td>
											<td className='px-6 py-4'>
												<div className='flex items-center space-x-2'>
													<button
														onClick={() => handleViewUserDetails(user.id)}
														className='text-blue-600 hover:text-blue-900'
														title='View Details'>
														<FaEye />
													</button>
													{user.role === 'user' && (
														<>
															<button
																onClick={() => handlePromoteUser(user, 'agent')}
																className='text-green-600 hover:text-green-900'
																title='Promote to Agent'>
																<FaUserShield />
															</button>
															<button
																onClick={() =>
																	handlePromoteUser(user, 'superuser')
																}
																className='text-purple-600 hover:text-purple-900'
																title='Promote to Superuser'>
																<FaCrown />
															</button>
														</>
													)}
													{user.role === 'agent' && (
														<button
															onClick={() =>
																handlePromoteUser(user, 'superuser')
															}
															className='text-purple-600 hover:text-purple-900'
															title='Promote to Superuser'>
															<FaCrown />
														</button>
													)}
												</div>
											</td>
										</tr>
									);
								})}
							</tbody>
						</table>
					</div>
				</div>
			</div>

			{/* User Details Modal */}
			{showUserDetailsModal && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4 relative'>
						<button
							onClick={() => setShowUserDetailsModal(false)}
							className='absolute top-2 right-2 text-gray-400 hover:text-gray-700'
							title='Close'>
							<FaTimes />
						</button>
						<h3 className='text-lg font-medium text-gray-900 mb-4'>
							User Details
						</h3>
						{userDetailsLoading ? (
							<div className='text-center py-8'>Loading...</div>
						) : userDetailsError ? (
							<div className='text-red-600 py-8'>{userDetailsError}</div>
						) : selectedUserDetails ? (
							<div className='space-y-3'>
								<div>
									<strong>Name:</strong> {selectedUserDetails.name}
								</div>
								<div>
									<strong>Email:</strong> {selectedUserDetails.email}
								</div>
								<div>
									<strong>Username:</strong> {selectedUserDetails.username}
								</div>
								<div>
									<strong>Role:</strong> {selectedUserDetails.role}
								</div>
								<div>
									<strong>Permissions:</strong>{' '}
									{selectedUserDetails.permissions?.join(', ') || 'None'}
								</div>
								<div>
									<strong>Joined:</strong>{' '}
									{new Date(
										selectedUserDetails.created_at
									).toLocaleDateString()}
								</div>
								<div>
									<strong>ID:</strong> {selectedUserDetails.id}
								</div>
							</div>
						) : (
							<div className='text-gray-600 py-8'>
								No user details available.
							</div>
						)}
					</div>
				</div>
			)}

			{/* Promotion Modal */}
			{showPromotionModal && selectedUser && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
						<h3 className='text-lg font-medium text-gray-900 mb-4'>
							Promote User to{' '}
							{promotionType === 'agent' ? 'Agent' : 'Superuser'}
						</h3>

						<div className='mb-4'>
							<p className='text-sm text-gray-600'>
								You are about to promote <strong>{selectedUser.name}</strong> (
								{selectedUser.email}) to{' '}
								{promotionType === 'agent' ? 'Agent' : 'Superuser'} role.
							</p>

							{promotionType === 'agent' && (
								<div className='mt-3 p-3 bg-blue-50 rounded-lg'>
									<p className='text-sm text-blue-800'>
										<strong>Agent permissions:</strong> POI review, access to
										agent dashboard
									</p>
								</div>
							)}

							{promotionType === 'superuser' && (
								<div className='mt-3 p-3 bg-purple-50 rounded-lg'>
									<p className='text-sm text-purple-800'>
										<strong>Superuser permissions:</strong> Full admin access,
										user management, agent management
									</p>
								</div>
							)}
						</div>

						<div className='flex justify-end space-x-3'>
							<button
								onClick={() => setShowPromotionModal(false)}
								className='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200'>
								Cancel
							</button>
							<button
								onClick={confirmPromotion}
								className={`px-4 py-2 text-sm font-medium text-white rounded-lg ${
									promotionType === 'agent'
										? 'bg-blue-600 hover:bg-blue-700'
										: 'bg-purple-600 hover:bg-purple-700'
								}`}>
								Promote to {promotionType === 'agent' ? 'Agent' : 'Superuser'}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
