/** @format */

'use client';

import { LoadingSpinner, useAuthGuard } from '@/app/shared/system';
import AdminDashboardClient from './AdminDashboardClient';

export default function AdminDashboard() {
	// Allow access to superusers with either full_access or user_management
	const { isAuthenticated, isAuthorized, isLoading, user, hasPermission } =
		useAuthGuard({
			requireAuth: true,
			requireRole: 'superuser',
		});

	// Only allow if user has full_access or user_management
	const canAccess =
		hasPermission('full_access') || hasPermission('user_management');

	if (isLoading) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<LoadingSpinner text='Loading admin dashboard...' />
			</div>
		);
	}

	if (!isAuthenticated || !isAuthorized || !canAccess || !user) {
		return null; // Auth guard will handle redirect
	}

	return (
		<AdminDashboardClient
			session={{
				user: user as {
					id: string;
					email: string;
					username: string;
					name?: string | null;
					image?: string | null;
					profile_picture_url?: string | null;
					role: 'user' | 'superuser' | 'agent';
					permissions: string[];
				},
				expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
			}}
		/>
	);
}
