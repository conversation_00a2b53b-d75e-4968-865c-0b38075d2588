/** @format */

'use client';

import { LoadingSpinner, useAuthGuard } from '@/app/shared/system';
import AgentDashboardClient from './AgentDashboardClient';

export default function AgentDashboard() {
	// Allow access to agents or superusers with full_access
	const { isAuthenticated, isAuthorized, isLoading, user } = useAuthGuard({
		requireAuth: true,
		allowedRoles: ['agent', 'superuser'],
		requirePermission: 'full_access',
	});

	if (isLoading) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<LoadingSpinner text='Loading agent dashboard...' />
			</div>
		);
	}

	if (!isAuthenticated || !isAuthorized || !user) {
		return null; // Auth guard will handle redirect
	}

	return (
		<AgentDashboardClient
			session={{
				user: user as {
					id: string;
					email: string;
					username: string;
					name?: string | null;
					image?: string | null;
					profile_picture_url?: string | null;
					role: 'user' | 'superuser' | 'agent';
					permissions: string[];
				},
				expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
			}}
		/>
	);
}
