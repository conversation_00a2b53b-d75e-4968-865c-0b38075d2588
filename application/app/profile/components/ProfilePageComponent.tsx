/** @format */

import { colors } from '@/app/colors';
import { UserProfile } from '@/app/shared/profile';
import { UserInteractionsProvider } from '@/app/shared/userInteractions/shared/context/UserInteractionsProvider';
import { useSession } from 'next-auth/react';
import React from 'react';
import { EnhancedProfileHeader } from './EnhancedProfileHeader';
import MyMediaGallery from './MyMediaGallery';
import { UserInteractionsSection } from './UserInteractionsSection';

const ProfilePageComponent: React.FC = () => {
	const { data: session } = useSession();

	// Handler for profile updates from enhanced components
	const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
		// Profile updates will be handled by the individual components
		console.log('Profile updated:', updatedProfile);
	};

	return (
		<div
			className='min-h-screen'
			style={{ backgroundColor: colors.neutral.cloudWhite }}>
			{/* Clean Layout Container */}
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
				{/* Shared User Interactions Provider */}
				{session?.user?.id && (
					<UserInteractionsProvider userId={session.user.id}>
						{/* Beautiful Modern Profile Header */}
						<div
							className='bg-white rounded-2xl shadow-lg border mb-8 overflow-hidden'
							style={{ borderColor: colors.ui.gray200 }}>
							{/* Elegant Cover Section */}
							<div
								className='relative h-40 overflow-hidden'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
								}}>
								{/* Sophisticated Pattern */}
								<div className='absolute inset-0 opacity-15'>
									<div
										className='absolute inset-0'
										style={{
											backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.4) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
											backgroundSize: '80px 80px, 120px 120px',
										}}></div>
								</div>

								{/* Floating Elements */}
								<div className='absolute top-6 right-6 w-16 h-16 rounded-full bg-white/10 backdrop-blur-sm'></div>
								<div className='absolute bottom-8 left-8 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm'></div>
							</div>

							{/* Main Profile Content */}
							<div className='relative px-8 pb-8'>
								{/* Profile Info Section */}
								<div className='flex flex-col sm:flex-row sm:items-end sm:justify-between gap-6 -mt-16 mb-8'>
									{/* Avatar and Basic Info */}
									<div className='flex flex-col sm:flex-row sm:items-end gap-6'>
										<EnhancedProfileHeader
											user={session?.user}
											onProfileUpdate={handleProfileUpdate}
											integrated={true}
										/>
									</div>
								</div>

								{/* Interactions Section */}
								<div
									className='bg-gradient-to-br from-gray-50 to-white rounded-xl border p-6'
									style={{ borderColor: colors.ui.gray200 }}>
									<UserInteractionsSection
										userId={session.user.id}
										integrated={true}
									/>
								</div>
							</div>
						</div>

						{/* Second Row: Full Width Media Gallery */}
						<div>
							<MyMediaGallery />
						</div>
					</UserInteractionsProvider>
				)}
			</div>
		</div>
	);
};

export default ProfilePageComponent;
