/** @format */

import { colors } from '@/app/colors';
import {
	MediaDeleteButton,
	MediaUploadComponent,
} from '@/app/shared/media/components';
import { UploadResult } from '@/app/shared/media/types';
import { useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';
import {
	FiCalendar,
	FiMapPin,
	FiRefreshCw,
	FiUpload,
	FiUser,
	FiX,
} from 'react-icons/fi';

const MyMediaGallery: React.FC = () => {
	const { data: session } = useSession();
	const [media, setMedia] = useState<
		{
			id: string;
			media_url: string;
			thumbnail_url?: string;
			media_type: 'photo' | 'video';
			caption?: string;
			created_at: string;
			like_count: number;
			favorite_count: number;
			poi_name?: string;
			poi_city?: string;
			poi_district?: string;
			poi_category?: string;
			poi_subcategory?: string;
			uploaded_by_username?: string;
			uploaded_by_name?: string;
			metadata?: Record<string, unknown>;
			is_verified?: boolean;
			poiId?: string;
			poi_id?: string;
		}[]
	>([]);
	const [showUpload, setShowUpload] = useState(false);
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		if (session?.user?.id) {
			fetchMedia();
		}
	}, [session?.user?.id]);

	const fetchMedia = async () => {
		if (!session?.user?.id) return;
		setLoading(true);
		try {
			const res = await fetch(`/api/pois/media?userId=${session.user.id}`);
			const data = await res.json();
			if (data.success) {
				setMedia(data.media || []);
			}
		} catch (error) {
			console.error('Error fetching media:', error);
		} finally {
			setLoading(false);
		}
	};

	const handleUploadComplete = (results: UploadResult[]) => {
		console.log('Media upload completed:', results);
		setShowUpload(false);
		fetchMedia(); // Refresh the media gallery
	};

	const handleUploadError = (error: string) => {
		console.error('Media upload error:', error);
		alert('Failed to upload media: ' + error);
	};

	return (
		<div
			className='bg-white rounded-2xl shadow-lg border overflow-hidden'
			style={{ borderColor: colors.ui.gray200 }}>
			{/* Clean Media Gallery Section */}
			<div style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<div className='space-y-8 p-8'>
					{/* Modern Header */}
					<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6'>
						<div className='space-y-2'>
							<div className='flex items-center gap-3'>
								<div className='w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center'>
									<span className='text-white text-lg'>📸</span>
								</div>
								<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent'>
									My Photo Gallery
								</h3>
							</div>
							<p className='text-gray-600 font-medium'>
								{media.length} {media.length === 1 ? 'photo' : 'photos'} •
								Latest first
							</p>
						</div>

						<div className='flex items-center gap-4'>
							{/* Upload Button */}
							<button
								onClick={() => setShowUpload(true)}
								className='flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#33C2FF] via-[#01034F] to-[#80ED99] hover:from-[#01034F] hover:via-[#33C2FF] hover:to-[#80ED99] text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-105'>
								<FiUpload className='h-5 w-5' />
								<span>Add Photos</span>
							</button>
						</div>
					</div>

					{/* Modern Upload Modal */}
					{showUpload && (
						<div className='fixed inset-0 bg-gradient-to-br from-black/40 via-black/60 to-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
							<div className='bg-gradient-to-br from-white via-white to-gray-50 backdrop-blur-sm rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100'>
								<div className='p-8'>
									<div className='flex items-center justify-between mb-8'>
										<div className='flex items-center gap-3'>
											<div className='w-10 h-10 bg-gradient-to-br from-[#33C2FF] via-[#01034F] to-[#80ED99] rounded-xl flex items-center justify-center shadow-lg'>
												<FiUpload className='h-5 w-5 text-white' />
											</div>
											<div>
												<h3 className='text-xl font-bold bg-gradient-to-r from-[#01034F] via-[#33C2FF] to-[#01034F] bg-clip-text text-transparent'>
													Add Photos
												</h3>
												<p className='text-sm text-gray-600'>
													Share your experiences
												</p>
											</div>
										</div>
										<button
											onClick={() => setShowUpload(false)}
											className='p-2 rounded-xl bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 hover:from-gray-100 hover:via-gray-200 hover:to-gray-100 text-gray-600 hover:text-gray-800 transition-all duration-200 border border-gray-200 hover:border-gray-300'>
											<FiX className='h-6 w-6' />
										</button>
									</div>

									<MediaUploadComponent
										uploadType='poi_media'
										onUploadComplete={handleUploadComplete}
										onUploadError={handleUploadError}
										maxFiles={10}
										showPOISearch={true}
									/>
								</div>
							</div>
						</div>
					)}

					{/* Modern Media Content */}
					{loading && media.length === 0 ? (
						<div className='flex flex-col items-center justify-center py-20'>
							<div className='relative'>
								<FiRefreshCw className='h-12 w-12 animate-spin text-blue-500' />
								<div className='absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur-xl opacity-30 animate-pulse'></div>
							</div>
							<p className='text-gray-600 font-medium mt-4'>
								Loading amazing photos...
							</p>
						</div>
					) : media.length === 0 ? (
						<div className='text-center py-20'>
							<div className='relative inline-block mb-8'>
								<div className='text-8xl mb-4 relative z-10'>📷</div>
								<div className='absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-2xl opacity-20 animate-pulse'></div>
							</div>
							<div className='max-w-md mx-auto space-y-4'>
								<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent'>
									No photos yet
								</h3>
								<p className='text-gray-600 leading-relaxed'>
									Start building your photo collection. Share your experiences
									with the community!
								</p>
								<button
									onClick={() => setShowUpload(true)}
									className='inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'>
									<FiUpload className='w-5 h-5' />
									Add First Photo
								</button>
							</div>
						</div>
					) : (
						<>
							{/* Modern Photo Grid - Latest first, left to right, then next row */}
							<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'>
								{media.map((item, index) => (
									<div
										key={item.id || index}
										className='group relative bg-gradient-to-br from-white via-white to-gray-50 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 hover:border-[#33C2FF] overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-[1.02]'
										style={{
											animationName: 'fadeInUp',
											animationDuration: '0.6s',
											animationTimingFunction: 'ease-out',
											animationFillMode: 'forwards',
											animationDelay: `${index * 100}ms`,
										}}>
										{/* Media Content */}
										<div className='relative aspect-square overflow-hidden'>
											{item.media_type === 'photo' ? (
												<img
													src={item.thumbnail_url || item.media_url}
													alt={item.caption || 'Photo'}
													className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
													loading='lazy'
												/>
											) : (
												<div className='relative'>
													<video
														src={item.media_url}
														className='w-full h-full object-cover'
														poster={item.thumbnail_url}
													/>
													<div className='absolute top-3 left-3 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-medium'>
														📹 Video
													</div>
												</div>
											)}

											{/* Hover Overlay */}
											<div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

											{/* Action Buttons */}
											<div className='absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300'>
												<div className='flex items-center justify-between'>
													<div className='flex items-center gap-2'>
														<FiMapPin className='w-3 h-3 text-white' />
														<span className='text-xs text-white font-medium'>
															{item.poi_name ||
																`POI ${item.poiId || item.poi_id || 'Unknown'}`}
														</span>
													</div>
													<MediaDeleteButton
														mediaId={item.id}
														size='small'
														onDeleteSuccess={() => {
															console.log('Media deleted successfully');
															fetchMedia(); // Refresh the gallery
														}}
														onDeleteError={(error) => {
															console.error('Delete error:', error);
															alert('Failed to delete media: ' + error);
														}}
													/>
												</div>
											</div>
										</div>

										{/* Caption */}
										{item.caption && (
											<div className='p-3'>
												<p className='text-sm text-gray-700 line-clamp-2 leading-relaxed'>
													{item.caption}
												</p>
											</div>
										)}

										{/* User Info */}
										<div className='px-3 pb-3'>
											<div className='flex items-center gap-2 text-xs text-gray-500'>
												<FiUser className='w-3 h-3' />
												<span>{session?.user?.name || 'You'}</span>
												<span>•</span>
												<FiCalendar className='w-3 h-3' />
												<span>
													{new Date(item.created_at).toLocaleDateString()}
												</span>
											</div>
										</div>
									</div>
								))}
							</div>
						</>
					)}
				</div>
			</div>
		</div>
	);
};

export default MyMediaGallery;
