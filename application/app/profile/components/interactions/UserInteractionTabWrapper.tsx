/** @format */

'use client';

import { UserInteractionTab } from '@/app/shared/userInteractions/shared/components/UserInteractionTab';
import React from 'react';

type InteractionType = 'likes' | 'favorites' | 'visits' | 'reviews';

interface UserInteractionTabWrapperProps {
	interactionType: InteractionType;
	userId?: string; // Optional for backward compatibility, but not used by UserInteractionTab
}

export const UserInteractionTabWrapper: React.FC<UserInteractionTabWrapperProps> = ({ 
	interactionType 
}) => {
	return <UserInteractionTab interactionType={interactionType} />;
};

// Export individual components for backward compatibility
export const UserLikesTab: React.FC<{ userId?: string }> = () => (
	<UserInteractionTabWrapper interactionType="likes" />
);

export const UserFavoritesTab: React.FC<{ userId?: string }> = () => (
	<UserInteractionTabWrapper interactionType="favorites" />
);

export const UserVisitsTab: React.FC<{ userId?: string }> = () => (
	<UserInteractionTabWrapper interactionType="visits" />
);

export const UserReviewsTab: React.FC<{ userId?: string }> = () => (
	<UserInteractionTabWrapper interactionType="reviews" />
);
