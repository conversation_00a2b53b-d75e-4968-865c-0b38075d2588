/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { FaEdit, FaHeart, FaMapMarkerAlt, FaStar } from 'react-icons/fa';
import {
	UserFavoritesTab,
	UserLikesTab,
	UserReviewsTab,
	UserVisitsTab,
} from './interactions';

interface UserInteractionsSectionProps {
	userId: string;
	integrated?: boolean;
}

type TabType = 'likes' | 'favorites' | 'visits' | 'reviews';

interface TabConfig {
	id: TabType;
	label: string;
	icon: React.ReactNode;
	color: string;
	hoverColor: string;
	activeColor: string;
}

const tabs: TabConfig[] = [
	{
		id: 'likes',
		label: 'Likes',
		icon: <FaHeart className='w-4 h-4' />,
		color: colors.utility.error,
		hoverColor: colors.utility.errorLight,
		activeColor: colors.utility.error,
	},
	{
		id: 'favorites',
		label: 'Favorites',
		icon: <FaStar className='w-4 h-4' />,
		color: colors.brand.blue,
		hoverColor: colors.supporting.lightBlue,
		activeColor: colors.brand.blue,
	},
	{
		id: 'visits',
		label: 'Visits',
		icon: <FaMapMarkerAlt className='w-4 h-4' />,
		color: colors.brand.green,
		hoverColor: colors.supporting.mintGreen,
		activeColor: colors.brand.green,
	},
	{
		id: 'reviews',
		label: 'Reviews',
		icon: <FaEdit className='w-4 h-4' />,
		color: colors.brand.navy,
		hoverColor: colors.supporting.softNavy,
		activeColor: colors.brand.navy,
	},
];

export const UserInteractionsSection: React.FC<
	UserInteractionsSectionProps
> = ({ userId, integrated = false }) => {
	const [activeTab, setActiveTab] = useState<TabType>('likes');

	const renderTabContent = () => {
		switch (activeTab) {
			case 'likes':
				return <UserLikesTab userId={userId} />;
			case 'favorites':
				return <UserFavoritesTab userId={userId} />;
			case 'visits':
				return <UserVisitsTab userId={userId} />;
			case 'reviews':
				return <UserReviewsTab userId={userId} />;
			default:
				return null;
		}
	};

	if (integrated) {
		// Integrated mode - elegant modern layout
		return (
			<div>
				{/* Beautiful Section Header */}
				<div className='mb-6'>
					<div className='flex items-center gap-3 mb-2'>
						<div className='w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center'>
							<span className='text-white text-lg'>⚡</span>
						</div>
						<h3 className='text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent'>
							My Activity
						</h3>
					</div>
					<p className='text-sm text-gray-600 ml-11'>
						Track your interactions and engagement
					</p>
				</div>

				{/* Modern Tab Navigation */}
				<div className='grid grid-cols-2 lg:grid-cols-4 gap-3 mb-6'>
					{tabs.map((tab) => {
						const isActive = activeTab === tab.id;

						return (
							<button
								key={tab.id}
								onClick={() => setActiveTab(tab.id)}
								className={`flex items-center justify-center gap-2 px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-0.5 ${
									isActive ? 'shadow-lg' : 'hover:shadow-md'
								}`}
								style={{
									color: isActive ? 'white' : tab.color,
									borderColor: isActive ? tab.activeColor : colors.ui.gray200,
									backgroundColor: isActive ? tab.activeColor : 'white',
								}}>
								<div
									className={`transition-transform duration-300 ${
										isActive ? 'scale-110' : ''
									}`}>
									{tab.icon}
								</div>
								<span className='font-semibold text-sm'>{tab.label}</span>
							</button>
						);
					})}
				</div>

				{/* Elegant Content Area with Hidden Scrollbar */}
				<div
					className='max-h-96 overflow-y-auto rounded-xl border p-4 scrollbar-hide'
					style={{
						borderColor: colors.ui.gray200,
						backgroundColor: 'rgba(249, 250, 251, 0.5)',
						scrollbarWidth: 'none' /* Firefox */,
						msOverflowStyle: 'none' /* IE and Edge */,
					}}>
					<style jsx>{`
						.scrollbar-hide::-webkit-scrollbar {
							display: none; /* Chrome, Safari, Opera */
						}
					`}</style>
					{renderTabContent()}
				</div>
			</div>
		);
	}

	// Standalone mode
	return (
		<div
			className='bg-white rounded-2xl shadow-lg border overflow-hidden'
			style={{ borderColor: colors.ui.gray200 }}>
			{/* Clean Header */}
			<div
				className='px-6 py-4 border-b'
				style={{
					backgroundColor: colors.ui.blue50,
					borderColor: colors.ui.gray200,
				}}>
				<h2
					className='text-xl font-bold'
					style={{ color: colors.neutral.textBlack }}>
					My Interactions
				</h2>
				<p
					className='text-sm mt-1'
					style={{ color: colors.neutral.slateGray }}>
					Manage your likes, favorites, visits, and reviews
				</p>
			</div>

			{/* Clean Tab Navigation */}
			<div
				className='flex border-b'
				style={{ borderColor: colors.ui.gray200 }}>
				{tabs.map((tab) => {
					const isActive = activeTab === tab.id;

					return (
						<button
							key={tab.id}
							onClick={() => setActiveTab(tab.id)}
							className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 ${
								isActive ? 'border-b-2' : ''
							}`}
							style={{
								color: isActive ? tab.activeColor : colors.neutral.slateGray,
								borderBottomColor: isActive ? tab.activeColor : 'transparent',
								backgroundColor: isActive ? `${tab.color}10` : 'transparent',
							}}>
							<div style={{ color: isActive ? tab.activeColor : tab.color }}>
								{tab.icon}
							</div>
							<span>{tab.label}</span>
						</button>
					);
				})}
			</div>

			{/* Clean Tab Content */}
			<div className='p-6'>{renderTabContent()}</div>
		</div>
	);
};
