/** @format */

'use client';

import { colors } from '@/app/colors';
import { useSession } from 'next-auth/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
	FiCamera,
	FiEdit3,
	FiRotateCw,
	FiTrash2,
	<PERSON><PERSON>ser,
	FiX,
} from 'react-icons/fi';

/**
 * Profile picture upload history item
 */
interface ProfilePictureHistory {
	id: string;
	url: string;
	uploadedAt: string;
	isActive: boolean;
}

/**
 * Enhanced profile picture upload component with crop, resize, and history
 */
interface ProfilePictureUploadProps {
	currentProfilePicture?: string;
	onUploadComplete?: (result: { url: string; message: string }) => void;
	onUploadError?: (error: string) => void;
	className?: string;
	size?: 'small' | 'medium' | 'large';
	showHistory?: boolean;
	allowCrop?: boolean;
}

export const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
	currentProfilePicture,
	onUploadComplete,
	onUploadError,
	className = '',
	size = 'medium',
	showHistory = true,
	allowCrop = true,
}) => {
	const { data: session, update: updateSession } = useSession();
	const fileInputRef = useRef<HTMLInputElement>(null);

	// State management
	const [isUploading, setIsUploading] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [showCropModal, setShowCropModal] = useState(false);
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [previewUrl, setPreviewUrl] = useState<string | null>(null);

	const [zoom, setZoom] = useState(1);
	const [rotation, setRotation] = useState(0);
	const [history, setHistory] = useState<ProfilePictureHistory[]>([]);
	const [showHistoryModal, setShowHistoryModal] = useState(false);

	// Size configurations
	const sizeConfig = {
		small: { width: 64, height: 64, className: 'w-16 h-16' },
		medium: { width: 120, height: 120, className: 'w-30 h-30' },
		large: { width: 200, height: 200, className: 'w-50 h-50' },
	};

	const currentSize = sizeConfig[size];

	/**
	 * Load profile picture history
	 */
	const loadHistory = useCallback(async () => {
		if (!session?.user?.id) return;

		try {
			const response = await fetch(
				`/api/profile/picture-history?userId=${session.user.id}`
			);
			if (response.ok) {
				const data = await response.json();
				setHistory(data.history || []);
			}
		} catch (error) {
			console.error('Failed to load profile picture history:', error);
		}
	}, [session?.user?.id]);

	useEffect(() => {
		if (showHistory) {
			loadHistory();
		}
	}, [showHistory, loadHistory]);

	/**
	 * Handle file selection
	 */
	const handleFileSelect = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0];
			if (!file) return;

			// Validate file type
			if (!file.type.startsWith('image/')) {
				onUploadError?.('Please select an image file');
				return;
			}

			// Validate file size (5MB limit)
			if (file.size > 5 * 1024 * 1024) {
				onUploadError?.('File size must be less than 5MB');
				return;
			}

			setSelectedFile(file);

			// Create preview URL
			const url = URL.createObjectURL(file);
			setPreviewUrl(url);

			if (allowCrop) {
				setShowCropModal(true);
			} else {
				// Upload directly without cropping
				uploadFile(file);
			}
		},
		[allowCrop, onUploadError]
	);

	/**
	 * Upload file to server
	 */
	const uploadFile = useCallback(
		async (file: File) => {
			if (!session?.user?.id) return;

			setIsUploading(true);
			try {
				const formData = new FormData();
				formData.append('file', file);
				formData.append('uploadType', 'profile');

				const response = await fetch('/api/media/upload', {
					method: 'POST',
					body: formData,
				});

				const result = await response.json();

				if (result.success) {
					// Update the session to include the new profile picture
					await updateSession();

					onUploadComplete?.(result);
					loadHistory(); // Refresh history
					setShowCropModal(false);
					setSelectedFile(null);
					if (previewUrl) {
						URL.revokeObjectURL(previewUrl);
						setPreviewUrl(null);
					}
				} else {
					throw new Error(result.error || 'Upload failed');
				}
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : 'Upload failed';
				onUploadError?.(errorMessage);
			} finally {
				setIsUploading(false);
			}
		},
		[
			session?.user?.id,
			onUploadComplete,
			onUploadError,
			loadHistory,
			previewUrl,
			updateSession,
		]
	);

	/**
	 * Handle profile picture deletion
	 */
	const handleDelete = useCallback(async () => {
		if (!session?.user?.id) return;

		setIsDeleting(true);
		try {
			const response = await fetch('/api/profile/picture/delete', {
				method: 'DELETE',
			});

			const result = await response.json();

			if (result.success) {
				// Update the session to remove the profile picture
				await updateSession();

				// Notify parent component
				onUploadComplete?.({
					url: '',
					message: 'Profile picture deleted successfully',
				});

				// Refresh history
				loadHistory();

				setShowDeleteConfirm(false);
			} else {
				throw new Error(result.error || 'Delete failed');
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Delete failed';
			onUploadError?.(errorMessage);
		} finally {
			setIsDeleting(false);
		}
	}, [
		session?.user?.id,
		onUploadComplete,
		onUploadError,
		loadHistory,
		updateSession,
	]);

	/**
	 * Generate default avatar with user initials
	 */
	const generateDefaultAvatar = useCallback(() => {
		if (!session?.user?.name) return null;

		const initials = session.user.name
			.split(' ')
			.map((name) => name.charAt(0))
			.join('')
			.toUpperCase()
			.slice(0, 2);

		return (
			<div
				className={`${currentSize.className} rounded-full flex items-center justify-center text-white font-bold`}
				style={{
					backgroundColor: colors.brand.blue,
					fontSize:
						size === 'small' ? '14px' : size === 'medium' ? '18px' : '24px',
				}}>
				{initials}
			</div>
		);
	}, [session?.user?.name, currentSize.className, size]);

	/**
	 * Render profile picture or default avatar
	 */
	const renderProfilePicture = () => {
		if (currentProfilePicture) {
			return (
				<img
					src={currentProfilePicture}
					alt='Profile'
					className={`${currentSize.className} rounded-full object-cover`}
				/>
			);
		}

		return (
			generateDefaultAvatar() || (
				<div
					className={`${currentSize.className} rounded-full flex items-center justify-center bg-gray-200`}>
					<FiUser
						className='text-gray-400'
						size={size === 'small' ? 24 : size === 'medium' ? 32 : 48}
					/>
				</div>
			)
		);
	};

	return (
		<div className={`relative ${className}`}>
			{/* Profile Picture Display */}
			<div className='relative group'>
				{renderProfilePicture()}

				{/* Hover Overlay */}
				<div className='absolute inset-0 bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center'>
					<div className='flex space-x-2'>
						<button
							onClick={() => fileInputRef.current?.click()}
							className='p-2 bg-white rounded-full hover:bg-gray-100 transition-colors'
							title='Upload new picture'>
							<FiCamera className='w-4 h-4 text-gray-700' />
						</button>

						{showHistory && (
							<button
								onClick={() => setShowHistoryModal(true)}
								className='p-2 bg-white rounded-full hover:bg-gray-100 transition-colors'
								title='View history'>
								<FiEdit3 className='w-4 h-4 text-gray-700' />
							</button>
						)}

						{currentProfilePicture && (
							<button
								onClick={() => setShowDeleteConfirm(true)}
								className='p-2 bg-red-500 rounded-full hover:bg-red-600 transition-colors'
								title='Delete profile picture'>
								<FiTrash2 className='w-4 h-4 text-white' />
							</button>
						)}
					</div>
				</div>
			</div>

			{/* Hidden File Input */}
			<input
				ref={fileInputRef}
				type='file'
				accept='image/*'
				onChange={handleFileSelect}
				className='hidden'
			/>

			{/* Upload Status */}
			{isUploading && (
				<div className='absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center'>
					<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-white'></div>
				</div>
			)}

			{/* Crop Modal */}
			{showCropModal && previewUrl && (
				<div className='fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4'>
					<div className='bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden'>
						<div className='p-6'>
							<div className='flex items-center justify-between mb-6'>
								<h3 className='text-lg font-semibold text-gray-900'>
									Crop Profile Picture
								</h3>
								<button
									onClick={() => {
										setShowCropModal(false);
										setSelectedFile(null);
										if (previewUrl) {
											URL.revokeObjectURL(previewUrl);
											setPreviewUrl(null);
										}
									}}
									className='text-gray-400 hover:text-gray-600'>
									<FiX className='h-6 w-6' />
								</button>
							</div>

							{/* Crop Area */}
							<div className='mb-6'>
								<div
									className='relative bg-gray-100 rounded-lg overflow-hidden'
									style={{ height: '400px' }}>
									<img
										src={previewUrl}
										alt='Preview'
										className='w-full h-full object-contain'
										style={{
											transform: `scale(${zoom}) rotate(${rotation}deg)`,
											transformOrigin: 'center',
										}}
									/>
									{/* Crop overlay would go here - simplified for now */}
								</div>
							</div>

							{/* Crop Controls */}
							<div className='space-y-4 mb-6'>
								<div className='flex items-center space-x-4'>
									<label className='text-sm font-medium text-gray-700'>
										Zoom:
									</label>
									<input
										type='range'
										min='0.5'
										max='3'
										step='0.1'
										value={zoom}
										onChange={(e) => setZoom(parseFloat(e.target.value))}
										className='flex-1'
									/>
									<span className='text-sm text-gray-500'>
										{Math.round(zoom * 100)}%
									</span>
								</div>

								<div className='flex items-center space-x-4'>
									<label className='text-sm font-medium text-gray-700'>
										Rotation:
									</label>
									<button
										onClick={() => setRotation((prev) => prev - 90)}
										className='p-2 border border-gray-300 rounded hover:bg-gray-50'>
										<FiRotateCw className='w-4 h-4 transform rotate-180' />
									</button>
									<button
										onClick={() => setRotation((prev) => prev + 90)}
										className='p-2 border border-gray-300 rounded hover:bg-gray-50'>
										<FiRotateCw className='w-4 h-4' />
									</button>
									<span className='text-sm text-gray-500'>{rotation}°</span>
								</div>
							</div>

							{/* Action Buttons */}
							<div className='flex justify-end space-x-3'>
								<button
									onClick={() => {
										setShowCropModal(false);
										setSelectedFile(null);
										if (previewUrl) {
											URL.revokeObjectURL(previewUrl);
											setPreviewUrl(null);
										}
									}}
									className='px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50'>
									Cancel
								</button>
								<button
									onClick={() => selectedFile && uploadFile(selectedFile)}
									disabled={isUploading}
									className='px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50'>
									{isUploading ? 'Uploading...' : 'Upload'}
								</button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* History Modal */}
			{showHistoryModal && (
				<div className='fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4'>
					<div className='bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-hidden'>
						<div className='p-6'>
							<div className='flex items-center justify-between mb-6'>
								<h3 className='text-lg font-semibold text-gray-900'>
									Profile Picture History
								</h3>
								<button
									onClick={() => setShowHistoryModal(false)}
									className='text-gray-400 hover:text-gray-600'>
									<FiX className='h-6 w-6' />
								</button>
							</div>

							<div className='space-y-3 max-h-96 overflow-y-auto'>
								{history.length === 0 ? (
									<p className='text-gray-500 text-center py-8'>
										No previous profile pictures
									</p>
								) : (
									history.map((item) => (
										<div
											key={item.id}
											className={`flex items-center space-x-3 p-3 rounded-lg border ${
												item.isActive
													? 'border-blue-500 bg-blue-50'
													: 'border-gray-200 hover:bg-gray-50'
											}`}>
											<img
												src={item.url}
												alt='Previous profile'
												className='w-12 h-12 rounded-full object-cover'
											/>
											<div className='flex-1'>
												<p className='text-sm text-gray-600'>
													{new Date(item.uploadedAt).toLocaleDateString()}
												</p>
												{item.isActive && (
													<span className='text-xs text-blue-600 font-medium'>
														Current
													</span>
												)}
											</div>
											{!item.isActive && (
												<button
													onClick={() => {
														// TODO: Implement restore functionality
														console.log('Restore picture:', item.id);
													}}
													className='text-sm text-blue-600 hover:text-blue-800'>
													Restore
												</button>
											)}
										</div>
									))
								)}
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Delete Confirmation Modal */}
			{showDeleteConfirm && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
						<h3 className='text-lg font-semibold text-gray-900 mb-4'>
							Delete Profile Picture
						</h3>
						<p className='text-gray-600 mb-6'>
							Are you sure you want to delete your profile picture? This action
							cannot be undone.
						</p>
						<div className='flex justify-end space-x-3'>
							<button
								onClick={() => setShowDeleteConfirm(false)}
								className='px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors'
								disabled={isDeleting}>
								Cancel
							</button>
							<button
								onClick={handleDelete}
								disabled={isDeleting}
								className='px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2'>
								{isDeleting ? (
									<>
										<div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
										<span>Deleting...</span>
									</>
								) : (
									<>
										<FiTrash2 className='w-4 h-4' />
										<span>Delete</span>
									</>
								)}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default ProfilePictureUpload;
