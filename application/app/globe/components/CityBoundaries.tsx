'use client'

import { useEffect, useState } from 'react'

interface CityBoundary {
  id: string
  name: string
  coordinates: [number, number][]
  properties: {
    name: string
    type: string
    district?: string
  }
}

interface CityBoundariesProps {
  currentZoom: number
  currentLocation: {lat: number, lng: number} | null
  onCityClick: (city: CityBoundary) => void
}

// Istanbul districts data (simplified)
const ISTANBUL_DISTRICTS: CityBoundary[] = [
  {
    id: 'istanbul-kadikoy',
    name: 'Kadıköy',
    coordinates: [
      [29.02, 40.98],
      [29.04, 40.98],
      [29.04, 40.96],
      [29.02, 40.96],
      [29.02, 40.98]
    ],
    properties: {
      name: 'Kadıköy',
      type: 'district',
      district: 'Kadıköy'
    }
  },
  {
    id: 'istanbul-besiktas',
    name: 'Be<PERSON><PERSON><PERSON><PERSON>',
    coordinates: [
      [28.98, 41.04],
      [29.00, 41.04],
      [29.00, 41.02],
      [28.98, 41.02],
      [28.98, 41.04]
    ],
    properties: {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      type: 'district',
      district: 'Beşiktaş'
    }
  },
  {
    id: 'istanbul-fatih',
    name: '<PERSON>ih',
    coordinates: [
      [28.96, 41.02],
      [28.98, 41.02],
      [28.98, 41.00],
      [28.96, 41.00],
      [28.96, 41.02]
    ],
    properties: {
      name: 'Fatih',
      type: 'district',
      district: 'Fatih'
    }
  },
  {
    id: 'istanbul-sisli',
    name: 'Şişli',
    coordinates: [
      [28.98, 41.06],
      [29.00, 41.06],
      [29.00, 41.04],
      [28.98, 41.04],
      [28.98, 41.06]
    ],
    properties: {
      name: 'Şişli',
      type: 'district',
      district: 'Şişli'
    }
  }
]

export default function CityBoundaries({
  currentZoom,
  currentLocation,
  onCityClick
}: CityBoundariesProps) {
  const [visibleDistricts, setVisibleDistricts] = useState<CityBoundary[]>([])

  useEffect(() => {
    // Show city boundaries when zoomed in enough and in Istanbul area
    if (currentZoom > 150 && currentLocation) {
      const isInIstanbul = 
        currentLocation.lat >= 40.8 && currentLocation.lat <= 41.2 &&
        currentLocation.lng >= 28.5 && currentLocation.lng <= 29.5
      
      if (isInIstanbul) {
        setVisibleDistricts(ISTANBUL_DISTRICTS)
      } else {
        setVisibleDistricts([])
      }
    } else {
      setVisibleDistricts([])
    }
  }, [currentZoom, currentLocation])

  if (visibleDistricts.length === 0) return null

  return (
    <div className="city-boundaries-overlay">
      {visibleDistricts.map((district) => (
        <div
          key={district.id}
          className="city-boundary-item"
          data-district={district.id.split('-')[1]}
          onClick={() => onCityClick(district)}
          title={`Click to explore ${district.name}`}
        >
          <div className="city-boundary-label">
            {district.name}
          </div>
        </div>
      ))}
    </div>
  )
} 