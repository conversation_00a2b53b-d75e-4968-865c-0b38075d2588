/** @format */

'use client';

import 'leaflet/dist/leaflet.css';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import GlobalMapManager from './GlobalMapManager';

// Fix for default markers in React Leaflet
import L from 'leaflet';
delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)
	._getIconUrl;
L.Icon.Default.mergeOptions({
	iconRetinaUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
	iconUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
	shadowUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapContainerProps {
	center: [number, number];
	zoom: number;
	children?: ReactNode;
	className?: string;
	style?: React.CSSProperties;
	onMapReady?: (map: L.Map) => void;
	onBoundsChange?: (
		bounds: L.LatLngBounds,
		center: L.LatLng,
		zoom: number
	) => void;
	minZoom?: number;
	maxZoom?: number;
	zoomControl?: boolean;
	scrollWheelZoom?: boolean | 'center';
	doubleClickZoom?: boolean | 'center';
	touchZoom?: boolean | 'center';
	dragging?: boolean;
	keyboard?: boolean;
	boxZoom?: boolean;
	zoomSnap?: number;
	zoomDelta?: number;
	wheelPxPerZoomLevel?: number;
}

const MapContainer: React.FC<MapContainerProps> = ({
	center,
	zoom,
	children,
	className = '',
	style = { height: '100%', width: '100%' },
	onMapReady,
	onBoundsChange,
	minZoom = 3,
	maxZoom = 18,
	zoomControl = true,
	scrollWheelZoom = 'center',
	doubleClickZoom = 'center',
	touchZoom = 'center',
	dragging = true,
	keyboard = true,
	boxZoom = true,
	zoomSnap = 0.5,
	zoomDelta = 0.5,
	wheelPxPerZoomLevel = 8000,
}) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const mapRef = useRef<L.Map | null>(null);
	const [isReady, setIsReady] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [containerId] = useState(
		() => `map-container-${Date.now()}-${Math.random()}`
	);
	const boundsChangeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const manager = GlobalMapManager.getInstance();

	// Initialize map once when component mounts
	useEffect(() => {
		if (!containerRef.current || mapRef.current) return;

		try {
			// Get or create map using global manager
			const mapInstance = manager.getContainer(
				containerId,
				containerRef.current,
				{
					center,
					zoom,
					minZoom,
					maxZoom,
					zoomControl,
					scrollWheelZoom,
					doubleClickZoom,
					touchZoom,
					dragging,
					keyboard,
					boxZoom,
					zoomSnap,
					zoomDelta,
					wheelPxPerZoomLevel,
				}
			);

			mapRef.current = mapInstance;
			setIsLoading(false);

			// Set up bounds change handler
			if (onBoundsChange) {
				const handleBoundsChange = () => {
					if (boundsChangeTimeoutRef.current) {
						clearTimeout(boundsChangeTimeoutRef.current);
					}

					boundsChangeTimeoutRef.current = setTimeout(() => {
						if (mapRef.current) {
							const bounds = mapRef.current.getBounds();
							const center = mapRef.current.getCenter();
							const zoom = mapRef.current.getZoom();
							onBoundsChange(bounds, center, zoom);
						}
					}, 300);
				};

				mapInstance.on('moveend', handleBoundsChange);
				mapInstance.on('zoomend', handleBoundsChange);
			}

			// Trigger ready callback
			setTimeout(() => {
				setIsReady(true);
				onMapReady?.(mapInstance);
			}, 100);
		} catch (error) {
			console.error('Error initializing map:', error);
			setIsLoading(false);
		}
	}, [
		center,
		zoom,
		minZoom,
		maxZoom,
		zoomControl,
		scrollWheelZoom,
		doubleClickZoom,
		touchZoom,
		dragging,
		keyboard,
		boxZoom,
		zoomSnap,
		zoomDelta,
		wheelPxPerZoomLevel,
		onMapReady,
		onBoundsChange,
		containerId,
		manager,
	]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (boundsChangeTimeoutRef.current) {
				clearTimeout(boundsChangeTimeoutRef.current);
			}
			manager.removeContainer(containerId);
		};
	}, [containerId, manager]);

	// Update map view when center or zoom changes
	useEffect(() => {
		if (mapRef.current && isReady) {
			mapRef.current.setView(center, zoom);
		}
	}, [center, zoom, isReady]);

	if (isLoading) {
		return (
			<div
				ref={containerRef}
				className={`relative ${className}`}
				style={style}>
				<div className='flex items-center justify-center h-full'>
					<div className='text-gray-500'>Loading map...</div>
				</div>
			</div>
		);
	}

	return (
		<div
			ref={containerRef}
			className={`relative ${className}`}
			style={style}>
			{/* Children are rendered when map is ready */}
			{isReady && children}
		</div>
	);
};

export default MapContainer;
