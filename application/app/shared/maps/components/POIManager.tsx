/** @format */

'use client';

import L from 'leaflet';
import { useCallback, useRef } from 'react';

interface POI {
	id: number;
	poi_type: string;
	poi_id: number;
	name: string;
	category: string;
	subcategory: string;
	city: string;
	district: string;
	country?: string;
	latitude: number;
	longitude: number;
	random_score: number;
	phone_number?: string;
	opening_hours?: string;
}

interface POIManagerProps {
	map: L.Map | null;
	pois: POI[];
	onPOIClick: (poi: POI) => void;
	userLocation?: { latitude: number; longitude: number } | null;
}

// POI cache for better performance
class POICache {
	private cache = new Map<string, POI[]>();
	private maxCacheSize = 50;

	getCacheKey(bounds: L.LatLngBounds, zoom: number): string {
		const sw = bounds.getSouthWest();
		const ne = bounds.getNorthEast();
		return `${Math.round(sw.lat * 1000)},${Math.round(
			sw.lng * 1000
		)},${Math.round(ne.lat * 1000)},${Math.round(ne.lng * 1000)},${zoom}`;
	}

	get(key: string): POI[] | null {
		return this.cache.get(key) || null;
	}

	set(key: string, pois: POI[]): void {
		if (this.cache.size >= this.maxCacheSize) {
			// Remove oldest entry
			const firstKey = this.cache.keys().next().value;
			if (firstKey !== undefined) {
				this.cache.delete(firstKey);
			}
		}
		this.cache.set(key, pois);
	}

	clear(): void {
		this.cache.clear();
	}
}

// Singleton POI cache instance
const poiCache = new POICache();

// Custom POI marker with clustering
const createPOIMarker = (poi: POI, onClick: (poi: POI) => void, map: L.Map) => {
	// Ensure POI pane exists and has higher z-index
	if (!map.getPane('poi-pane')) {
		const poiPane = map.createPane('poi-pane');
		poiPane.style.zIndex = '650'; // Higher than default marker pane (600)
		console.log('Created POI pane with z-index 650');
	}

	const marker = L.circleMarker([poi.latitude, poi.longitude], {
		radius: 8,
		fillColor: '#ef4444',
		color: '#ffffff',
		weight: 2,
		opacity: 1,
		fillOpacity: 0.8,
		pane: 'poi-pane', // Use custom pane for better layering control
	});

	// Add click handler to show detailed card with actions immediately
	marker.on('click', () => {
		console.log('POI marker clicked:', poi.name);
		onClick(poi);
	});

	return marker;
};

// Create user location marker
const createUserLocationMarker = (
	userLocation: { latitude: number; longitude: number },
	map: L.Map
) => {
	// Ensure user location pane exists and has lower z-index
	if (!map.getPane('user-location-pane')) {
		const userPane = map.createPane('user-location-pane');
		userPane.style.zIndex = '550'; // Lower than POI pane but higher than default
		console.log('Created user location pane with z-index 550');
	}

	const marker = L.circleMarker(
		[userLocation.latitude, userLocation.longitude],
		{
			radius: 12,
			fillColor: '#3b82f6',
			color: '#ffffff',
			weight: 3,
			opacity: 1,
			fillOpacity: 0.8,
			pane: 'user-location-pane', // Use custom pane for better layering control
		}
	);

	const popupContent = `
    <div style="font-family: system-ui; font-size: 12px;">
      <strong style="color: #1f2937;">📍 Your Location</strong><br/>
      <span style="color: #6b7280;">${userLocation.latitude.toFixed(
				4
			)}°, ${userLocation.longitude.toFixed(4)}°</span>
    </div>
  `;

	marker.bindPopup(popupContent);
	return marker;
};

export const usePOIManager = ({
	map,
	pois,
	onPOIClick,
	userLocation,
}: POIManagerProps) => {
	const markersRef = useRef<L.LayerGroup | null>(null);
	const userMarkerRef = useRef<L.CircleMarker | null>(null);

	// Initialize marker layer group
	const initializeMarkerLayer = useCallback(() => {
		if (!map || markersRef.current) return;

		markersRef.current = L.layerGroup().addTo(map);
	}, [map]);

	// Clear all markers
	const clearMarkers = useCallback(() => {
		if (markersRef.current) {
			markersRef.current.clearLayers();
		}
		if (userMarkerRef.current && map) {
			map.removeLayer(userMarkerRef.current);
			userMarkerRef.current = null;
		}
	}, [map]);

	// Update POI markers
	const updatePOIMarkers = useCallback(() => {
		if (!map || !markersRef.current) return;

		// Clear existing POI markers
		markersRef.current.clearLayers();

		// Filter and validate POIs
		const validPois = pois.filter(
			(poi) =>
				poi &&
				typeof poi.latitude === 'number' &&
				typeof poi.longitude === 'number' &&
				!isNaN(poi.latitude) &&
				!isNaN(poi.longitude) &&
				poi.name &&
				poi.latitude >= -90 &&
				poi.latitude <= 90 &&
				poi.longitude >= -180 &&
				poi.longitude <= 180
		);

		// Get current map bounds for viewport culling
		const bounds = map.getBounds();
		const visiblePois = validPois.filter((poi) =>
			bounds.contains([poi.latitude, poi.longitude])
		);

		// Limit POIs for performance (show max 100 at once)
		const limitedPois = visiblePois.slice(0, 100);

		// Create markers for visible POIs
		limitedPois.forEach((poi) => {
			try {
				const marker = createPOIMarker(poi, onPOIClick, map);
				markersRef.current?.addLayer(marker);
			} catch (error) {
				console.warn('Error creating POI marker:', error, poi);
			}
		});

		console.log(
			`Rendered ${limitedPois.length} POI markers (${validPois.length} total POIs)`
		);
	}, [map, pois, onPOIClick]);

	// Update user location marker
	const updateUserLocationMarker = useCallback(() => {
		if (!map || !userLocation) return;

		// Remove existing user marker
		if (userMarkerRef.current) {
			map.removeLayer(userMarkerRef.current);
		}

		// Create new user marker
		try {
			userMarkerRef.current = createUserLocationMarker(userLocation, map);
			map.addLayer(userMarkerRef.current);
		} catch (error) {
			console.warn('Error creating user location marker:', error);
		}
	}, [map, userLocation]);

	// Initialize and update markers
	const updateMarkers = useCallback(() => {
		initializeMarkerLayer();
		// Add user location marker first (lower layer)
		updateUserLocationMarker();
		// Then add POI markers (higher layer)
		updatePOIMarkers();
		console.log(
			'Markers updated - User location added first, then POI markers'
		);
	}, [initializeMarkerLayer, updatePOIMarkers, updateUserLocationMarker]);

	// Cleanup function
	const cleanup = useCallback(() => {
		clearMarkers();
		if (markersRef.current && map) {
			map.removeLayer(markersRef.current);
			markersRef.current = null;
		}
	}, [clearMarkers, map]);

	return {
		updateMarkers,
		clearMarkers,
		cleanup,
		poiCache,
	};
};

export { createPOIMarker, createUserLocationMarker, POICache };
export type { POI };
