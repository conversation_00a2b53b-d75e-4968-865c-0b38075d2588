/** @format */

'use client';

import L from 'leaflet';
import React, { useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';
import { useMap } from 'react-leaflet';

// Component to handle map resize events
export const MapResizeHandler: React.FC = () => {
	const map = useMap();

	useEffect(() => {
		const handleResize = () => {
			setTimeout(() => {
				map.invalidateSize();
			}, 100);
		};

		// Handle window resize
		window.addEventListener('resize', handleResize);

		// Initial resize after mount
		setTimeout(() => {
			map.invalidateSize();
		}, 100);

		return () => {
			window.removeEventListener('resize', handleResize);
		};
	}, [map]);

	return null;
};

// Component to fit map bounds to markers
interface FitBoundsProps {
	markers: Array<[number, number]>;
	padding?: number;
	maxZoom?: number;
}

export const FitBounds: React.FC<FitBoundsProps> = ({
	markers,
	padding = 0.1,
	maxZoom = 15,
}) => {
	const map = useMap();

	useEffect(() => {
		if (markers.length === 0) return;

		if (markers.length === 1) {
			// Single marker - just center on it
			map.setView(markers[0], maxZoom);
		} else {
			// Multiple markers - fit bounds
			try {
				const validMarkers = markers.filter(
					(coords) =>
						Array.isArray(coords) &&
						coords.length === 2 &&
						typeof coords[0] === 'number' &&
						typeof coords[1] === 'number' &&
						!isNaN(coords[0]) &&
						!isNaN(coords[1])
				);

				if (validMarkers.length > 1) {
					const group = new L.FeatureGroup(
						validMarkers.map((coords) => L.marker(coords))
					);
					const bounds = group.getBounds();

					if (bounds && bounds.isValid()) {
						map.fitBounds(bounds.pad(padding), { maxZoom });
					} else {
						map.setView(validMarkers[0], maxZoom);
					}
				} else if (validMarkers.length === 1) {
					map.setView(validMarkers[0], maxZoom);
				}
			} catch (error) {
				console.warn('Error fitting bounds:', error);
				// Fallback to first marker
				if (markers.length > 0) {
					map.setView(markers[0], maxZoom);
				}
			}
		}
	}, [map, markers, padding, maxZoom]);

	return null;
};

// Component to handle map center changes
interface MapCenterControllerProps {
	center: [number, number];
	zoom?: number;
}

export const MapCenterController: React.FC<MapCenterControllerProps> = ({
	center,
	zoom,
}) => {
	const map = useMap();

	useEffect(() => {
		if (center && center.length === 2) {
			if (zoom !== undefined) {
				map.setView(center, zoom);
			} else {
				map.panTo(center);
			}
		}
	}, [map, center, zoom]);

	return null;
};

// Component to add custom controls
interface CustomControlProps {
	position: 'topleft' | 'topright' | 'bottomleft' | 'bottomright';
	children: React.ReactNode;
}

export const CustomControl: React.FC<CustomControlProps> = ({
	position,
	children,
}) => {
	const map = useMap();
	const containerRef = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		const CustomControlClass = L.Control.extend({
			onAdd: function () {
				const div = L.DomUtil.create('div', 'custom-control');
				div.style.background = 'white';
				div.style.padding = '8px';
				div.style.borderRadius = '4px';
				div.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

				// Prevent map events on control
				L.DomEvent.disableClickPropagation(div);
				L.DomEvent.disableScrollPropagation(div);

				containerRef.current = div;
				return div;
			},
		});

		const control = new CustomControlClass({ position });

		map.addControl(control);

		return () => {
			map.removeControl(control);
			containerRef.current = null;
		};
	}, [map, position]);

	// Render children into the control container
	useEffect(() => {
		if (containerRef.current && children) {
			const root = createRoot(containerRef.current);
			root.render(children);

			return () => {
				root.unmount();
			};
		}
	}, [children]);

	return null;
};

// Re-export location utilities from central location
export {
	calculateDistance,
	formatDistance,
	validateCoordinates,
} from 'app/shared/locationManager/utils/locationUtils';

// Default map configuration
export const DEFAULT_MAP_CONFIG = {
	center: [41.0082, 28.9784] as [number, number], // Istanbul
	zoom: 15,
	minZoom: 3,
	maxZoom: 18,
	zoomControl: true,
	scrollWheelZoom: false, // Disable scroll wheel zoom
	doubleClickZoom: false, // Disable double-click zoom
	touchZoom: false, // Disable touch zoom
	dragging: true,
	keyboard: false, // Disable keyboard zoom
	boxZoom: false, // Disable box zoom
	zoomSnap: 0.5,
	zoomDelta: 0.5,
	wheelPxPerZoomLevel: 8000, // Ultra-conservative zoom sensitivity
};
