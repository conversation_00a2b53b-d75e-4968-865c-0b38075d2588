# Global Map System

This directory contains a new global map system that solves the "Map container is already initialized" error by using a single Leaflet map instance across the entire application.

## 🎯 Problem Solved

The previous approach created multiple map instances, which caused conflicts in React Strict Mode. The new global map system:

- **Single Map Instance**: Only one Leaflet map exists at any time
- **Container Management**: Maps are moved between different DOM containers
- **React Strict Mode Compatible**: No more initialization conflicts
- **Memory Efficient**: Reduces memory usage and improves performance
- **Scalable**: Can handle millions of users without issues

## 🏗️ Architecture

### Core Components

1. **GlobalMapManager** (`GlobalMapManager.ts`)
   - Singleton pattern for global map management
   - Handles map instance creation and container switching
   - Provides debugging and cleanup utilities

2. **GlobalMapContainer** (`GlobalMapContainer.tsx`)
   - React component that manages a map container
   - Handles initialization and cleanup
   - Provides loading states and error handling

3. **GlobalMapComponents** (`GlobalMapComponents.tsx`)
   - React Leaflet compatible components
   - Markers, popups, circles, polylines, polygons
   - Hooks for accessing the global map instance

## 🚀 Usage

### Basic Map Container

```tsx
import { GlobalMapContainer, GlobalMapMarker, GlobalMapPopup } from '../shared/components/maps'

function MyMapComponent() {
  return (
    <GlobalMapContainer
      containerId="my-map"
      center={[41.0082, 28.9784]}
      zoom={10}
      onMapReady={(map) => console.log('Map ready!')}
    >
      <GlobalMapMarker
        position={[41.0082, 28.9784]}
        popup={
          <GlobalMapPopup>
            <div>Hello World!</div>
          </GlobalMapPopup>
        }
      />
    </GlobalMapContainer>
  )
}
```

### Using Hooks

```tsx
import { useGlobalMap, useGlobalMapDebug } from '../shared/components/maps'

function MyComponent() {
  const map = useGlobalMap()
  const debugInfo = useGlobalMapDebug()

  useEffect(() => {
    if (map) {
      // Access the global map instance
      map.setView([41.0082, 28.9784], 10)
    }
  }, [map])

  return <div>Map Debug: {JSON.stringify(debugInfo)}</div>
}
```

### Available Components

- `GlobalMapContainer` - Main map container
- `GlobalMapMarker` - Map markers
- `GlobalMapPopup` - Popup content
- `GlobalMapTileLayer` - Custom tile layers
- `GlobalMapCircle` - Circle overlays
- `GlobalMapPolyline` - Line overlays
- `GlobalMapPolygon` - Polygon overlays

### Available Hooks

- `useGlobalMap()` - Get the current map instance
- `useGlobalMapDebug()` - Get debug information

## 🔧 Migration from React Leaflet

### Before (React Leaflet)
```tsx
import { MapContainer, Marker, Popup } from 'react-leaflet'

<MapContainer center={[41.0082, 28.9784]} zoom={10}>
  <Marker position={[41.0082, 28.9784]}>
    <Popup>Hello World!</Popup>
  </Marker>
</MapContainer>
```

### After (Global Map System)
```tsx
import { GlobalMapContainer, GlobalMapMarker, GlobalMapPopup } from '../shared/components/maps'

<GlobalMapContainer
  containerId="my-map"
  center={[41.0082, 28.9784]}
  zoom={10}
>
  <GlobalMapMarker
    position={[41.0082, 28.9784]}
    popup={<GlobalMapPopup>Hello World!</GlobalMapPopup>}
  />
</GlobalMapContainer>
```

## 🎛️ Configuration

### Map Configuration Options

```tsx
interface MapConfig {
  center: [number, number]
  zoom: number
  minZoom?: number
  maxZoom?: number
  zoomControl?: boolean
  scrollWheelZoom?: boolean | 'center'
  doubleClickZoom?: boolean | 'center'
  touchZoom?: boolean | 'center'
  dragging?: boolean
  keyboard?: boolean
  boxZoom?: boolean
  zoomSnap?: number
  zoomDelta?: number
  wheelPxPerZoomLevel?: number
}
```

## 🐛 Debugging

### Debug Information

```tsx
const debugInfo = useGlobalMapDebug()
// Returns:
// {
//   isInitialized: boolean
//   containerCount: number
//   hasGlobalMap: boolean
//   currentContainerId: string | null
// }
```

### Console Logs

The system provides detailed console logs:
- `🌍 Global map instance created`
- `🌍 Global map moved to new container`
- `🗑️ Container {id} removed`
- `🧹 Global map manager cleaned up`

## 🔄 Container Management

The global map system automatically manages containers:

1. **Container Creation**: When a new `GlobalMapContainer` mounts
2. **Container Switching**: When moving between different map views
3. **Container Cleanup**: When a `GlobalMapContainer` unmounts

### Container Lifecycle

```tsx
// 1. Component mounts
<GlobalMapContainer containerId="map-1" />

// 2. Map is created or moved to this container
// 3. Component unmounts
// 4. Container is removed from manager
```

## 🚨 Important Notes

1. **Unique Container IDs**: Each `GlobalMapContainer` must have a unique `containerId`
2. **Single Active Container**: Only one container can be active at a time
3. **Automatic Cleanup**: Containers are automatically cleaned up on unmount
4. **React Strict Mode**: Fully compatible with React Strict Mode

## 🧪 Testing

Use the test page at `/test-map` to verify the global map system is working correctly.

## 📈 Performance Benefits

- **Memory**: Single map instance reduces memory usage
- **CPU**: No map recreation on component re-renders
- **Network**: Reduced tile loading overhead
- **Scalability**: Can handle many concurrent users

## 🔮 Future Enhancements

- [ ] Map state persistence
- [ ] Advanced container management
- [ ] Performance monitoring
- [ ] Custom tile layer support
- [ ] Map event system 