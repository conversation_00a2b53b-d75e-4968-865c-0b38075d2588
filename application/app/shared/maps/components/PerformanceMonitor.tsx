/** @format */

'use client';

import L, { TileEvent } from 'leaflet';
import React, { useEffect, useRef, useState } from 'react';
import GlobalMapManager from './GlobalMapManager';

// Extend HTMLImageElement to include our custom timing property
interface TileWithTiming extends HTMLImageElement {
	_loadStartTime?: number;
}

interface PerformanceMetrics {
	tileLoadTime: number;
	zoomTime: number;
	renderTime: number;
	fps: number;
	memoryUsage: number;
	tileCacheHitRate: number;
	averageTileLoadTime: number;
	totalTilesLoaded: number;
	failedTiles: number;
}

interface PerformanceMonitorProps {
	enabled?: boolean;
	onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
	showUI?: boolean;
}

export const useMapPerformance = () => {
	const [metrics, setMetrics] = useState<PerformanceMetrics>({
		tileLoadTime: 0,
		zoomTime: 0,
		renderTime: 0,
		fps: 0,
		memoryUsage: 0,
		tileCacheHitRate: 0,
		averageTileLoadTime: 0,
		totalTilesLoaded: 0,
		failedTiles: 0,
	});

	const frameCountRef = useRef(0);
	const lastTimeRef = useRef(performance.now());
	const tileLoadTimesRef = useRef<number[]>([]);
	const tileCacheHitsRef = useRef(0);
	const tileCacheMissesRef = useRef(0);
	const totalTilesRef = useRef(0);
	const failedTilesRef = useRef(0);

	useEffect(() => {
		const manager = GlobalMapManager.getInstance();
		const map = manager.getGlobalMap();

		if (!map) return;

		// FPS monitoring
		const updateFPS = () => {
			frameCountRef.current++;
			const currentTime = performance.now();

			if (currentTime - lastTimeRef.current >= 1000) {
				const fps = Math.round(
					(frameCountRef.current * 1000) / (currentTime - lastTimeRef.current)
				);
				setMetrics((prev) => ({ ...prev, fps }));
				frameCountRef.current = 0;
				lastTimeRef.current = currentTime;
			}

			requestAnimationFrame(updateFPS);
		};

		// Tile loading monitoring
		// FIXED: Update event handler signatures for Leaflet compatibility
		const handleTileLoadStart = (e: TileEvent) => {
			const startTime = performance.now();
			if (e.tile) (e.tile as TileWithTiming)._loadStartTime = startTime;
			totalTilesRef.current++;
		};

		const handleTileLoad = (e: TileEvent) => {
			const endTime = performance.now();
			const loadTime =
				endTime - ((e.tile as TileWithTiming)._loadStartTime || endTime);

			tileLoadTimesRef.current.push(loadTime);

			// Keep only last 100 measurements
			if (tileLoadTimesRef.current.length > 100) {
				tileLoadTimesRef.current.shift();
			}

			const averageLoadTime =
				tileLoadTimesRef.current.reduce((a, b) => a + b, 0) /
				tileLoadTimesRef.current.length;

			setMetrics((prev) => ({
				...prev,
				tileLoadTime: loadTime,
				averageTileLoadTime: averageLoadTime,
				totalTilesLoaded: totalTilesRef.current,
			}));
		};

		// FIXED: Use correct LeafletEvent type for event handlers
		const handleTileError = () => {
			failedTilesRef.current++;
			setMetrics((prev) => ({
				...prev,
				failedTiles: failedTilesRef.current,
			}));
		};

		// Zoom monitoring
		const handleZoomStart = () => {
			const startTime = performance.now();
			(map as L.Map & { _zoomStartTime?: number })._zoomStartTime = startTime;
		};

		const handleZoomEnd = () => {
			const endTime = performance.now();
			const zoomTime =
				endTime -
				((map as L.Map & { _zoomStartTime?: number })._zoomStartTime ||
					endTime);

			setMetrics((prev) => ({
				...prev,
				zoomTime,
			}));
		};

		// Memory monitoring
		const updateMemoryUsage = () => {
			if ('memory' in performance) {
				const memory = (
					performance as Performance & { memory?: { usedJSHeapSize: number } }
				).memory;
				if (memory) {
					const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB

					setMetrics((prev) => ({
						...prev,
						memoryUsage,
					}));
				}
			}
		};

		// Cache hit rate monitoring
		const updateCacheHitRate = () => {
			const totalRequests =
				tileCacheHitsRef.current + tileCacheMissesRef.current;
			const hitRate =
				totalRequests > 0
					? (tileCacheHitsRef.current / totalRequests) * 100
					: 0;

			setMetrics((prev) => ({
				...prev,
				tileCacheHitRate: Math.round(hitRate),
			}));
		};

		// Event listeners
		map.on('tileloadstart', handleTileLoadStart);
		map.on('tileload', handleTileLoad);
		map.on('tileerror', handleTileError);
		map.on('zoomstart', handleZoomStart);
		map.on('zoomend', handleZoomEnd);

		// Start monitoring
		updateFPS();

		const memoryInterval = setInterval(updateMemoryUsage, 2000);
		const cacheInterval = setInterval(updateCacheHitRate, 1000);

		return () => {
			map.off('tileloadstart', handleTileLoadStart);
			map.off('tileload', handleTileLoad);
			map.off('tileerror', handleTileError);
			map.off('zoomstart', handleZoomStart);
			map.off('zoomend', handleZoomEnd);

			clearInterval(memoryInterval);
			clearInterval(cacheInterval);
		};
	}, []);

	return metrics;
};

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
	enabled = true,
	onMetricsUpdate,
	showUI = false,
}) => {
	const metrics = useMapPerformance();

	useEffect(() => {
		if (enabled && onMetricsUpdate) {
			onMetricsUpdate(metrics);
		}
	}, [metrics, enabled, onMetricsUpdate]);

	if (!showUI) return null;

	return (
		<div className='fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg font-mono text-xs z-50'>
			<div className='space-y-1'>
				<div>FPS: {metrics.fps}</div>
				<div>Memory: {metrics.memoryUsage}MB</div>
				<div>Tile Load: {Math.round(metrics.averageTileLoadTime)}ms</div>
				<div>Zoom Time: {Math.round(metrics.zoomTime)}ms</div>
				<div>Cache Hit: {metrics.tileCacheHitRate}%</div>
				<div>Tiles: {metrics.totalTilesLoaded}</div>
				<div>Failed: {metrics.failedTiles}</div>
			</div>
		</div>
	);
};

// Performance optimization utilities
export const optimizeMapPerformance = () => {
	const manager = GlobalMapManager.getInstance();
	const map = manager.getGlobalMap();

	if (!map) return;

	// Optimize rendering
	map.getPanes().tilePane.style.willChange = 'transform';

	// Reduce repaints during zoom
	const originalSetView = map.setView;
	map.setView = function (
		center: L.LatLngExpression,
		zoom: number,
		options?: L.ZoomPanOptions
	) {
		if (options?.animate) {
			this.getPanes().tilePane.style.transition = 'transform 0.3s ease-out';
		}
		return originalSetView.call(this, center, zoom, options);
	};

	// Optimize tile loading - use a different approach since getLayers doesn't exist
	const tilePane = map.getPanes().tilePane;
	const tileElements = tilePane.querySelectorAll('img');
	tileElements.forEach(
		(img: HTMLImageElement & { _leaflet_layer?: L.TileLayer }) => {
			if (img._leaflet_layer && img._leaflet_layer instanceof L.TileLayer) {
				const layer = img._leaflet_layer;
				layer.options.keepBuffer = 4;
				layer.options.updateWhenIdle = true;
				layer.options.updateWhenZooming = false;
			}
		}
	);
};

// Performance recommendations based on metrics
export const getPerformanceRecommendations = (
	metrics: PerformanceMetrics
): string[] => {
	const recommendations: string[] = [];

	if (metrics.fps < 30) {
		recommendations.push('Consider reducing tile quality or zoom levels');
	}

	if (metrics.averageTileLoadTime > 1000) {
		recommendations.push('Tile loading is slow - check network connection');
	}

	if (metrics.memoryUsage > 100) {
		recommendations.push('High memory usage - consider clearing tile cache');
	}

	if (metrics.tileCacheHitRate < 50) {
		recommendations.push('Low cache hit rate - increase tile buffer');
	}

	if (metrics.failedTiles > 10) {
		recommendations.push('Many failed tiles - check tile server availability');
	}

	return recommendations;
};
