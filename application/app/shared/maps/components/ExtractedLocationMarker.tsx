'use client'

import React from 'react'
import { Marker, Popup } from 'react-leaflet'
import L from 'leaflet'

interface ExtractedLocation {
  name: string
  lat: number
  lng: number
  confidence: number
  latitude?: number
  longitude?: number
  walk_route_distance_m?: number
  address?: string
}

interface ExtractedLocationMarkerProps {
  location: ExtractedLocation
  onClick?: (location: ExtractedLocation) => void
  showPopup?: boolean
  isSelected?: boolean
}

// Create custom extracted location icon
const createExtractedLocationIcon = (isSelected: boolean = false) => {
  const size = isSelected ? 20 : 16
  const color = isSelected ? '#ef4444' : '#ef4444' // Red for extracted locations
  
  return L.divIcon({
    className: 'extracted-location-marker',
    html: `
      <div style="
        width: ${size}px; 
        height: ${size}px; 
        background: ${color}; 
        border: ${isSelected ? 3 : 2}px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${size * 0.6}px;
        color: white;
        font-weight: bold;
        ${isSelected ? 'animation: pulse 2s infinite;' : ''}
      ">
        🎯
      </div>
      ${isSelected ? `
        <style>
          @keyframes pulse {
            0% {
              transform: scale(0.95);
              box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            70% {
              transform: scale(1);
              box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
            100% {
              transform: scale(0.95);
              box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
            }
          }
        </style>
      ` : ''}
    `,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  })
}

const ExtractedLocationMarker: React.FC<ExtractedLocationMarkerProps> = ({
  location,
  onClick,
  showPopup = false,
  isSelected = false
}) => {
  // Use lat/lng or latitude/longitude
  const lat = location.lat || location.latitude
  const lng = location.lng || location.longitude

  // Validate coordinates
  if (
    typeof lat !== 'number' || 
    typeof lng !== 'number' ||
    isNaN(lat) || 
    isNaN(lng)
  ) {
    console.warn('Invalid extracted location coordinates:', location)
    return null
  }

  const locationIcon = createExtractedLocationIcon(isSelected)

  const handleClick = () => {
    onClick?.(location)
  }

  const formatDistance = (distanceM: number): string => {
    if (distanceM < 1000) {
      return `${Math.round(distanceM)}m walk`
    }
    return `${(distanceM / 1000).toFixed(1)}km walk`
  }

  return (
    <Marker
      position={[lat, lng]}
      icon={locationIcon}
      eventHandlers={{
        click: handleClick
      }}
    >
      {showPopup && (
        <Popup>
          <div style={{ fontFamily: 'system-ui', fontSize: '12px', minWidth: '180px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '14px' }}>
              🎯 {location.name}
            </div>
            
            <div style={{ marginBottom: '4px' }}>
              <strong>Coordinates:</strong><br/>
              {lat.toFixed(4)}°, {lng.toFixed(4)}°
            </div>
            
            <div style={{ marginBottom: '4px' }}>
              <strong>Confidence:</strong> {Math.round(location.confidence * 100)}%
            </div>
            
            {location.address && (
              <div style={{ marginBottom: '4px' }}>
                <strong>Address:</strong> {location.address}
              </div>
            )}
            
            {location.walk_route_distance_m && (
              <div style={{ marginBottom: '4px', color: '#3b82f6', fontWeight: 'bold' }}>
                {formatDistance(location.walk_route_distance_m)}
              </div>
            )}
            
            <div style={{ marginTop: '8px', fontSize: '11px', color: '#666' }}>
              Extracted from conversation
            </div>
          </div>
        </Popup>
      )}
    </Marker>
  )
}

export default ExtractedLocationMarker
