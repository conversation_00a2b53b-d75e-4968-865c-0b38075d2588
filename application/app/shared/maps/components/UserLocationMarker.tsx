'use client'

import React from 'react'
import { Marker, Popup } from 'react-leaflet'
import L from 'leaflet'

interface UserLocationMarkerProps {
  latitude: number
  longitude: number
  accuracy?: number
  showPopup?: boolean
}

// Create custom user location icon
const createUserLocationIcon = () => {
  return L.divIcon({
    className: 'user-location-marker',
    html: `
      <div style="
        width: 20px; 
        height: 20px; 
        background: #3b82f6; 
        border: 3px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
      ">
        <div style="
          position: absolute;
          top: -5px;
          left: -5px;
          width: 30px;
          height: 30px;
          background: rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          animation: pulse 2s infinite;
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
          }
          70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
          }
          100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
          }
        }
      </style>
    `,
    iconSize: [20, 20],
    iconAnchor: [10, 10],
    popupAnchor: [0, -10]
  })
}

const UserLocationMarker: React.FC<UserLocationMarkerProps> = ({
  latitude,
  longitude,
  accuracy,
  showPopup = false
}) => {
  // Validate coordinates
  if (
    typeof latitude !== 'number' || 
    typeof longitude !== 'number' ||
    isNaN(latitude) || 
    isNaN(longitude)
  ) {
    console.warn('Invalid user location coordinates:', { latitude, longitude })
    return null
  }

  const userIcon = createUserLocationIcon()

  return (
    <Marker
      position={[latitude, longitude]}
      icon={userIcon}
    >
      {showPopup && (
        <Popup>
          <div style={{ fontFamily: 'system-ui', fontSize: '12px' }}>
            <strong>📍 Your Location</strong><br/>
            {latitude.toFixed(4)}°, {longitude.toFixed(4)}°
            {accuracy && (
              <>
                <br/>Accuracy: ±{Math.round(accuracy)}m
              </>
            )}
          </div>
        </Popup>
      )}
    </Marker>
  )
}

export default UserLocationMarker
