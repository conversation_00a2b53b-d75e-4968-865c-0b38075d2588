'use client'

import React, { useEffect, useRef } from 'react'
import L from 'leaflet'

// User Location Marker Component
interface UnifiedUserLocationMarkerProps {
  latitude: number
  longitude: number
  accuracy?: number
  showPopup?: boolean
  map?: L.Map
}

const createUserLocationIcon = () => {
  return L.divIcon({
    className: 'user-location-marker',
    html: `
      <div style="
        width: 20px; 
        height: 20px; 
        background: #3b82f6; 
        border: 3px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
      ">
        <div style="
          position: absolute;
          top: -5px;
          left: -5px;
          width: 30px;
          height: 30px;
          background: rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          animation: pulse 2s infinite;
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
          }
          70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
          }
          100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
          }
        }
      </style>
    `,
    iconSize: [20, 20],
    iconAnchor: [10, 10],
    popupAnchor: [0, -10]
  })
}

export const UnifiedUserLocationMarker: React.FC<UnifiedUserLocationMarkerProps> = ({
  latitude,
  longitude,
  accuracy,
  showPopup = false,
  map
}) => {
  const markerRef = useRef<L.Marker | null>(null)

  useEffect(() => {
    if (!map || typeof latitude !== 'number' || typeof longitude !== 'number' || isNaN(latitude) || isNaN(longitude)) {
      return
    }

    // Create marker
    const userIcon = createUserLocationIcon()
    markerRef.current = L.marker([latitude, longitude], { icon: userIcon })

    if (showPopup) {
      const popupContent = `
        <div style="font-family: system-ui; font-size: 12px;">
          <strong>📍 Your Location</strong><br/>
          ${latitude.toFixed(4)}°, ${longitude.toFixed(4)}°
          ${accuracy ? `<br/>Accuracy: ±${Math.round(accuracy)}m` : ''}
        </div>
      `
      markerRef.current.bindPopup(popupContent)
    }

    markerRef.current.addTo(map)

    return () => {
      if (markerRef.current && map) {
        map.removeLayer(markerRef.current)
      }
    }
  }, [map, latitude, longitude, accuracy, showPopup])

  return null
}

// Extracted Location Marker Component
interface ExtractedLocation {
  name: string
  lat?: number
  lng?: number
  latitude?: number
  longitude?: number
  confidence: number
  walk_route_distance_m?: number
  address?: string
}

interface UnifiedExtractedLocationMarkerProps {
  location: ExtractedLocation
  onClick?: (location: ExtractedLocation) => void
  showPopup?: boolean
  isSelected?: boolean
  map?: L.Map
}

const createExtractedLocationIcon = (isSelected: boolean = false) => {
  const size = isSelected ? 20 : 16
  const color = isSelected ? '#ef4444' : '#ef4444'
  
  return L.divIcon({
    className: 'extracted-location-marker',
    html: `
      <div style="
        width: ${size}px; 
        height: ${size}px; 
        background: ${color}; 
        border: ${isSelected ? 3 : 2}px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${size * 0.6}px;
        color: white;
        font-weight: bold;
        ${isSelected ? 'animation: pulse 2s infinite;' : ''}
      ">
        🎯
      </div>
      ${isSelected ? `
        <style>
          @keyframes pulse {
            0% {
              transform: scale(0.95);
              box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            70% {
              transform: scale(1);
              box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
            100% {
              transform: scale(0.95);
              box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
            }
          }
        </style>
      ` : ''}
    `,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  })
}

export const UnifiedExtractedLocationMarker: React.FC<UnifiedExtractedLocationMarkerProps> = ({
  location,
  onClick,
  showPopup = false,
  isSelected = false,
  map
}) => {
  const markerRef = useRef<L.Marker | null>(null)

  useEffect(() => {
    if (!map) return

    const lat = location.lat || location.latitude
    const lng = location.lng || location.longitude

    if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
      console.warn('Invalid extracted location coordinates:', location)
      return
    }

    // Create marker
    const locationIcon = createExtractedLocationIcon(isSelected)
    markerRef.current = L.marker([lat, lng], { icon: locationIcon })

    // Add click handler
    if (onClick) {
      markerRef.current.on('click', () => onClick(location))
    }

    if (showPopup) {
      const formatDistance = (distanceM: number): string => {
        if (distanceM < 1000) {
          return `${Math.round(distanceM)}m walk`
        }
        return `${(distanceM / 1000).toFixed(1)}km walk`
      }

      const popupContent = `
        <div style="font-family: system-ui; font-size: 12px; min-width: 180px;">
          <div style="font-weight: bold; margin-bottom: 8px; font-size: 14px;">
            🎯 ${location.name}
          </div>
          
          <div style="margin-bottom: 4px;">
            <strong>Coordinates:</strong><br/>
            ${lat.toFixed(4)}°, ${lng.toFixed(4)}°
          </div>
          
          <div style="margin-bottom: 4px;">
            <strong>Confidence:</strong> ${Math.round(location.confidence * 100)}%
          </div>
          
          ${location.address ? `
            <div style="margin-bottom: 4px;">
              <strong>Address:</strong> ${location.address}
            </div>
          ` : ''}
          
          ${location.walk_route_distance_m ? `
            <div style="margin-bottom: 4px; color: #3b82f6; font-weight: bold;">
              ${formatDistance(location.walk_route_distance_m)}
            </div>
          ` : ''}
          
          <div style="margin-top: 8px; font-size: 11px; color: #666;">
            Extracted from conversation
          </div>
        </div>
      `
      markerRef.current.bindPopup(popupContent)
    }

    markerRef.current.addTo(map)

    return () => {
      if (markerRef.current && map) {
        map.removeLayer(markerRef.current)
      }
    }
  }, [map, location, onClick, showPopup, isSelected])

  return null
}

// Map Resize Handler Component
interface UnifiedMapResizeHandlerProps {
  map?: L.Map
}

export const UnifiedMapResizeHandler: React.FC<UnifiedMapResizeHandlerProps> = ({ map }) => {
  useEffect(() => {
    if (!map) return

    const handleResize = () => {
      setTimeout(() => {
        map.invalidateSize()
      }, 100)
    }

    // Handle window resize
    window.addEventListener('resize', handleResize)

    // Initial resize after mount
    setTimeout(() => {
      map.invalidateSize()
    }, 100)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [map])

  return null
}

// Fit Bounds Component
interface UnifiedFitBoundsProps {
  markers: Array<[number, number]>
  padding?: number
  maxZoom?: number
  map?: L.Map
}

export const UnifiedFitBounds: React.FC<UnifiedFitBoundsProps> = ({ 
  markers, 
  padding = 0.1, 
  maxZoom = 15,
  map
}) => {
  useEffect(() => {
    if (!map || markers.length === 0) return

    if (markers.length === 1) {
      // Single marker - just center on it
      map.setView(markers[0], maxZoom)
    } else {
      // Multiple markers - fit bounds
      try {
        const validMarkers = markers.filter(coords => 
          Array.isArray(coords) && 
          coords.length === 2 && 
          typeof coords[0] === 'number' && 
          typeof coords[1] === 'number' &&
          !isNaN(coords[0]) && !isNaN(coords[1])
        )

        if (validMarkers.length > 1) {
          const group = new L.FeatureGroup(
            validMarkers.map(coords => L.marker(coords))
          )
          const bounds = group.getBounds()

          if (bounds && bounds.isValid()) {
            map.fitBounds(bounds.pad(padding), { maxZoom })
          } else {
            map.setView(validMarkers[0], maxZoom)
          }
        } else if (validMarkers.length === 1) {
          map.setView(validMarkers[0], maxZoom)
        }
      } catch (error) {
        console.warn('Error fitting bounds:', error)
        // Fallback to first marker
        if (markers.length > 0) {
          map.setView(markers[0], maxZoom)
        }
      }
    }
  }, [map, markers, padding, maxZoom])

  return null
}
