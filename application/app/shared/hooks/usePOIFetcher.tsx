/** @format */

'use client';

import L from 'leaflet';
import { useCallback, useEffect, useRef, useState } from 'react';

interface POI {
	id: number; // BIGINT from spatial schema
	poi_type: string;
	poi_id: number; // BIGINT from spatial_schema.pois.id
	name: string;
	category: string;
	subcategory: string;
	city: string;
	district: string;
	country?: string;
	latitude: number;
	longitude: number;
	random_score: number;
	phone_number?: string;
	opening_hours?: string;
}

interface FetchPOIsParams {
	minLat: number;
	maxLat: number;
	minLng: number;
	maxLng: number;
	limit?: number;
}

interface UsePOIFetcherReturn {
	pois: POI[];
	isLoading: boolean;
	error: string | null;
	fetchPOIs: (bounds: L.LatLngBounds, zoom: number) => Promise<void>;
	clearPOIs: () => void;
	loadingProgress: number;
}

// POI cache with spatial indexing
class SpatialPOICache {
	private cache = new Map<string, { pois: POI[]; timestamp: number }>();
	private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
	private readonly MAX_CACHE_SIZE = 100;

	private getCacheKey(bounds: L.LatLngBounds, zoom: number): string {
		const sw = bounds.getSouthWest();
		const ne = bounds.getNorthEast();
		// Round to reduce cache fragmentation
		const precision = zoom > 12 ? 1000 : zoom > 8 ? 100 : 10;
		return `${Math.round(sw.lat * precision)},${Math.round(
			sw.lng * precision
		)},${Math.round(ne.lat * precision)},${Math.round(
			ne.lng * precision
		)},${Math.floor(zoom)}`;
	}

	get(bounds: L.LatLngBounds, zoom: number): POI[] | null {
		const key = this.getCacheKey(bounds, zoom);
		const cached = this.cache.get(key);

		if (!cached) return null;

		// Check if cache is still valid
		if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
			this.cache.delete(key);
			return null;
		}

		return cached.pois;
	}

	set(bounds: L.LatLngBounds, zoom: number, pois: POI[]): void {
		const key = this.getCacheKey(bounds, zoom);

		// Remove oldest entries if cache is full
		if (this.cache.size >= this.MAX_CACHE_SIZE) {
			const oldestKey = Array.from(this.cache.entries()).sort(
				([, a], [, b]) => a.timestamp - b.timestamp
			)[0][0];
			this.cache.delete(oldestKey);
		}

		this.cache.set(key, {
			pois: [...pois], // Create a copy
			timestamp: Date.now(),
		});
	}

	clear(): void {
		this.cache.clear();
	}

	getStats(): { size: number; keys: string[] } {
		return {
			size: this.cache.size,
			keys: Array.from(this.cache.keys()),
		};
	}
}

// Singleton cache instance
const spatialCache = new SpatialPOICache();

export const usePOIFetcher = (): UsePOIFetcherReturn => {
	const [pois, setPois] = useState<POI[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [loadingProgress, setLoadingProgress] = useState(0);

	const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const abortControllerRef = useRef<AbortController | null>(null);
	const lastFetchBoundsRef = useRef<string | null>(null);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (fetchTimeoutRef.current) {
				clearTimeout(fetchTimeoutRef.current);
			}
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, []);

	const fetchPOIsFromAPI = useCallback(
		async (params: FetchPOIsParams): Promise<POI[]> => {
			const response = await fetch('/api/pois/globe', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(params),
				signal: abortControllerRef.current?.signal,
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();

			if (!data.success || !Array.isArray(data.pois)) {
				throw new Error('Invalid response format');
			}

			return data.pois;
		},
		[]
	);

	const validatePOI = useCallback((poi: unknown): poi is POI => {
		if (!poi || typeof poi !== 'object') return false;
		const p = poi as Record<string, unknown>;
		return (
			typeof p.latitude === 'number' &&
			typeof p.longitude === 'number' &&
			!isNaN(p.latitude) &&
			!isNaN(p.longitude) &&
			p.latitude >= -90 &&
			p.latitude <= 90 &&
			p.longitude >= -180 &&
			p.longitude <= 180 &&
			typeof p.name === 'string' &&
			p.name.length > 0
		);
	}, []);

	const fetchPOIs = useCallback(
		async (bounds: L.LatLngBounds, zoom: number): Promise<void> => {
			// Cancel any pending fetch
			if (fetchTimeoutRef.current) {
				clearTimeout(fetchTimeoutRef.current);
			}
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}

			// Create cache key for deduplication
			const sw = bounds.getSouthWest();
			const ne = bounds.getNorthEast();
			const boundsKey = `${sw.lat.toFixed(4)},${sw.lng.toFixed(
				4
			)},${ne.lat.toFixed(4)},${ne.lng.toFixed(4)},${zoom}`;

			// Skip if same bounds as last fetch
			if (lastFetchBoundsRef.current === boundsKey) {
				return;
			}

			// Check cache first
			const cachedPOIs = spatialCache.get(bounds, zoom);
			if (cachedPOIs) {
				console.log('Using cached POIs:', cachedPOIs.length);
				setPois(cachedPOIs);
				setError(null);
				setLoadingProgress(100);
				lastFetchBoundsRef.current = boundsKey;
				return;
			}

			// Debounce the fetch
			fetchTimeoutRef.current = setTimeout(async () => {
				try {
					setIsLoading(true);
					setError(null);
					setLoadingProgress(0);
					lastFetchBoundsRef.current = boundsKey;

					// Create new abort controller
					abortControllerRef.current = new AbortController();

					// Simulate progress
					const progressInterval = setInterval(() => {
						setLoadingProgress((prev) => Math.min(prev + 10, 90));
					}, 100);

					const params: FetchPOIsParams = {
						minLat: sw.lat,
						maxLat: ne.lat,
						minLng: sw.lng,
						maxLng: ne.lng,
						limit: zoom > 12 ? 200 : zoom > 8 ? 100 : 50, // Adaptive limit based on zoom
					};

					const fetchedPOIs = await fetchPOIsFromAPI(params);
					clearInterval(progressInterval);

					// Validate and filter POIs
					const validPOIs = fetchedPOIs.filter(validatePOI);

					// Cache the results
					spatialCache.set(bounds, zoom, validPOIs);

					setPois(validPOIs);
					setLoadingProgress(100);
					setError(null);

					console.log(
						`Fetched ${validPOIs.length} valid POIs (${fetchedPOIs.length} total)`
					);
				} catch (err) {
					if (err instanceof Error && err.name === 'AbortError') {
						console.log('POI fetch aborted');
						return;
					}

					console.error('Error fetching POIs:', err);
					setError(err instanceof Error ? err.message : 'Failed to fetch POIs');
					setPois([]);
					setLoadingProgress(0);
				} finally {
					setIsLoading(false);
				}
			}, 500); // 500ms debounce
		},
		[fetchPOIsFromAPI, validatePOI]
	);

	const clearPOIs = useCallback(() => {
		setPois([]);
		setError(null);
		setLoadingProgress(0);
		lastFetchBoundsRef.current = null;

		// Cancel any pending fetch
		if (fetchTimeoutRef.current) {
			clearTimeout(fetchTimeoutRef.current);
		}
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}
	}, []);

	return {
		pois,
		isLoading,
		error,
		fetchPOIs,
		clearPOIs,
		loadingProgress,
	};
};

export { spatialCache };
export type { FetchPOIsParams, POI };
