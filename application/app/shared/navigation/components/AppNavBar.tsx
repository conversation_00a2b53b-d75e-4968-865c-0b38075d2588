/** @format */

'use client';

import { colors, gradients } from '@/app/colors';
import { CreditsDisplay } from '@/app/shared/credits';
import { useLocationManager } from '@/app/shared/locationManager';
import { useSessionManager } from '@/app/shared/system';
import { Z_INDEX_LAYERS } from '@/lib/navigation-config';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import {
	FaBars,
	FaCog,
	FaComments,
	FaGlobe,
	FaMap,
	FaMapMarkerAlt,
	FaSignOutAlt,
	FaStar,
	FaUser,
} from 'react-icons/fa';
import { FiRefreshCw, FiZap } from 'react-icons/fi';

interface AppNavBarProps {
	variant?: 'default' | 'transparent' | 'minimal';
}

const AppNavBar: React.FC<AppNavBarProps> = ({ variant = 'default' }) => {
	const router = useRouter();
	const pathname = usePathname();
	const { data: session } = useSession();
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const mobileMenuRef = useRef<HTMLDivElement>(null);
	const {
		location: userLocation,
		error: locationError,
		isLoading: locationLoading,
		requestAutoLocation,
	} = useLocationManager();

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setDropdownOpen(false);
			}
			if (
				mobileMenuRef.current &&
				!mobileMenuRef.current.contains(event.target as Node)
			) {
				setMobileMenuOpen(false);
			}
		};
		if (dropdownOpen || mobileMenuOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [dropdownOpen, mobileMenuOpen]);

	const [isLoggingOut, setIsLoggingOut] = useState(false);
	const { clearSession } = useSessionManager();

	const handleLogout = async () => {
		try {
			setIsLoggingOut(true);
			setDropdownOpen(false);
			await clearSession();
		} catch (error) {
			console.error('Logout failed:', error);
			// Force refresh as fallback
			if (typeof window !== 'undefined') {
				window.location.href = '/';
			}
		} finally {
			setIsLoggingOut(false);
		}
	};

	const getUserInitials = () => {
		if (session?.user?.username) {
			return session.user.username.substring(0, 2).toUpperCase();
		}
		if (session?.user?.email) {
			return session.user.email.substring(0, 2).toUpperCase();
		}
		return 'U';
	};

	// Permission-aware role checks
	const user = session?.user;
	const isSuperuser = user?.role === 'superuser';
	const isAgent = user?.role === 'agent';
	const hasFullAccess = user?.permissions?.includes('full_access');
	const hasUserManagement = user?.permissions?.includes('user_management');

	// Show admin dashboard if superuser with full_access or user_management
	const showAdminDashboard =
		isSuperuser && (hasFullAccess || hasUserManagement);
	// Show agent dashboard if agent with full_access or superuser with full_access
	const showAgentDashboard =
		(isAgent && hasFullAccess) || (isSuperuser && hasFullAccess);

	// Navigation helpers
	const handleNavLink = (id: string) => {
		try {
			if (pathname === '/') {
				const section = document.getElementById(id);
				if (section) {
					section.scrollIntoView({ behavior: 'smooth' });
					return;
				}
			}
			router.push(`/#${id}`);
		} catch (error) {
			console.error('Navigation failed:', error);
		}
	};

	const handleRouteNavigation = (route: string) => {
		try {
			// Check if user needs authentication for protected routes
			if (
				(route === '/chat' || route === '/profile' || route === '/settings') &&
				!session
			) {
				router.push(`/auth?callbackUrl=${encodeURIComponent(route)}`);
				return;
			}
			router.push(route);
		} catch (error) {
			console.error('Route navigation failed:', error);
		}
	};

	const handleSignIn = () => {
		try {
			// Pass current page as callback URL so user returns to where they were
			const callbackUrl = encodeURIComponent(pathname);
			router.push(`/auth?mode=signin&callbackUrl=${callbackUrl}`);
		} catch (error) {
			console.error('Sign in navigation failed:', error);
		}
	};

	const formatLocation = () => {
		if (!userLocation) return 'Location unavailable';
		const latDir = userLocation.latitude >= 0 ? 'N' : 'S';
		const lngDir = userLocation.longitude >= 0 ? 'E' : 'W';
		const timeAgo = Math.floor((Date.now() - userLocation.timestamp) / 1000);
		const timeText =
			timeAgo < 60 ? 'just now' : `${Math.floor(timeAgo / 60)}m ago`;
		const accuracy = userLocation.accuracy
			? Math.round(userLocation.accuracy)
			: 'unknown';
		return `${Math.abs(userLocation.latitude).toFixed(4)}°${latDir}, ${Math.abs(
			userLocation.longitude
		).toFixed(4)}°${lngDir} (±${accuracy}m) • ${timeText}`;
	};

	// Get navigation styles based on variant
	const getNavigationStyles = () => {
		const baseClasses =
			'fixed top-0 left-0 right-0 flex items-center justify-between px-4 md:px-8 h-16 transition-all duration-300';
		const zIndex = Z_INDEX_LAYERS.navigation;

		switch (variant) {
			case 'transparent':
				return {
					className: `${baseClasses} ${zIndex} backdrop-blur-md border-b border-opacity-20`,
					style: {
						background: 'rgba(255, 255, 255, 0.1)',
						borderColor: 'rgba(255, 255, 255, 0.2)',
					},
				};
			case 'minimal':
				return {
					className: `${baseClasses} ${zIndex} bg-white/90 backdrop-blur-sm shadow-sm`,
					style: { borderBottom: `1px solid ${colors.ui.gray100}` },
				};
			default:
				return {
					className: `${baseClasses} ${zIndex} border-b shadow-sm animate-fade-in bg-white`,
					style: {
						background: gradients.background,
						borderColor: colors.ui.gray200,
					},
				};
		}
	};

	const navStyles = getNavigationStyles();

	return (
		<nav
			className={navStyles.className}
			style={navStyles.style}>
			{/* Logo and Company Name */}
			<div
				className='flex items-center cursor-pointer h-full space-x-3 flex-shrink-0'
				onClick={() => router.push('/')}>
				<div className='flex items-center justify-center w-12 h-12 rounded-xl bg-white/10 backdrop-blur-sm'>
					<Image
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						width={40}
						height={40}
						className='object-contain'
						priority
					/>
				</div>
				<div className='hidden sm:block'>
					<h1
						className='text-xl font-bold tracking-tight'
						style={{ color: colors.brand.navy }}>
						Wizlop
					</h1>
				</div>
			</div>
			{/* Main Nav Links - Responsive */}
			<div className='flex-1 flex justify-center'>
				<div
					className='hidden lg:flex items-center space-x-6'
					style={{ color: colors.neutral.slateGray }}>
					{pathname === '/' && (
						<>
							<button
								onClick={() => handleNavLink('features')}
								className='transition-colors font-medium text-sm'
								style={{ color: colors.neutral.slateGray }}
								onMouseEnter={(e) =>
									(e.currentTarget.style.color = colors.supporting.lightBlue)
								}
								onMouseLeave={(e) =>
									(e.currentTarget.style.color = colors.neutral.slateGray)
								}>
								Features
							</button>
							<button
								onClick={() => handleNavLink('how-it-works')}
								className='transition-colors font-medium text-sm'
								style={{ color: colors.neutral.slateGray }}
								onMouseEnter={(e) =>
									(e.currentTarget.style.color = colors.supporting.lightBlue)
								}
								onMouseLeave={(e) =>
									(e.currentTarget.style.color = colors.neutral.slateGray)
								}>
								How it Works
							</button>
							<button
								onClick={() => handleNavLink('credits')}
								className='transition-colors font-medium text-sm'
								style={{ color: colors.neutral.slateGray }}
								onMouseEnter={(e) =>
									(e.currentTarget.style.color = colors.supporting.lightBlue)
								}
								onMouseLeave={(e) =>
									(e.currentTarget.style.color = colors.neutral.slateGray)
								}>
								Credits
							</button>
						</>
					)}
					<button
						onClick={() => handleRouteNavigation('/chat')}
						className='flex items-center space-x-2 px-3 py-2 rounded-xl transition-colors text-sm'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
						}}
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue100)
						}>
						<FaComments className='w-4 h-4' />
						<span>Chat</span>
					</button>
					<button
						onClick={() => handleRouteNavigation('/globe')}
						className='flex items-center space-x-2 px-3 py-2 rounded-xl transition-colors text-sm'
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
						}}
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.green200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.green100)
						}>
						<FaGlobe className='w-4 h-4' />
						<span>Globe</span>
					</button>
					<button
						onClick={() => handleRouteNavigation('/pois')}
						className='flex items-center space-x-2 px-3 py-2 rounded-xl transition-colors text-sm'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.brand.teal,
						}}
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.gray200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.gray100)
						}>
						<FaMapMarkerAlt className='w-4 h-4' />
						<span>POIs</span>
					</button>
				</div>

				{/* Compact nav for medium screens */}
				<div className='hidden md:flex lg:hidden items-center space-x-2'>
					<button
						onClick={() => handleRouteNavigation('/chat')}
						className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
						}}
						title='Chat'
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue100)
						}>
						<FaComments className='w-4 h-4' />
					</button>
					<button
						onClick={() => handleRouteNavigation('/globe')}
						className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
						}}
						title='Globe'
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.green200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.green100)
						}>
						<FaGlobe className='w-4 h-4' />
					</button>
					<button
						onClick={() => handleRouteNavigation('/pois')}
						className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.brand.teal,
						}}
						title='POIs'
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.gray200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.gray100)
						}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>
				</div>
			</div>

			{/* Right Side - Credits and Profile Dropdown */}
			<div className='flex items-center space-x-2 flex-shrink-0'>
				{/* Add POI button, only on /pois - hidden on small screens */}
				{pathname === '/pois' && (
					<button
						onClick={() => router.push('/pois/submit')}
						className='hidden sm:flex px-3 py-2 rounded-xl bg-green-600 text-white font-semibold shadow-md hover:bg-green-700 transition-colors text-sm'
						style={{ minWidth: 100 }}>
						+ Add POI
					</button>
				)}

				{session ? (
					<div className='flex items-center space-x-2'>
						{/* Credits - hidden on small screens */}
						<div className='hidden md:block'>
							<CreditsDisplay
								size='medium'
								showAddButton={false}
							/>
						</div>

						{/* Profile Dropdown */}
						<div
							className='relative'
							ref={dropdownRef}>
							<div
								onClick={() => setDropdownOpen(!dropdownOpen)}
								className='w-10 h-10 text-white rounded-xl flex items-center justify-center cursor-pointer select-none transition-all duration-300 shadow-lg'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
								}}
								onMouseEnter={(e) =>
									(e.currentTarget.style.background = `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.green} 100%)`)
								}
								onMouseLeave={(e) =>
									(e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`)
								}>
								{getUserInitials()}
							</div>
							{dropdownOpen && (
								<div className='absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-xl z-50 py-2 text-sm overflow-hidden'>
									{/* Location Display */}
									<div className='px-4 py-3 bg-gray-50 border-b border-gray-100'>
										<div className='flex items-start gap-3'>
											<FaMapMarkerAlt className='text-blue-600 mt-0.5 flex-shrink-0' />
											<div className='min-w-0 flex-1'>
												<div className='flex items-center justify-between mb-1'>
													<div className='flex items-center gap-2'>
														<span className='font-medium text-gray-800'>
															Your Location
														</span>
														{userLocation && (
															<div
																className='w-2 h-2 bg-green-500 rounded-full animate-pulse'
																title='Location active'></div>
														)}
													</div>
													<button
														onClick={requestAutoLocation}
														disabled={locationLoading}
														className='text-blue-600 hover:text-blue-800 disabled:opacity-50 transition-colors'
														title='Refresh location'>
														<FiRefreshCw
															className={`w-3 h-3 ${
																locationLoading ? 'animate-spin' : ''
															}`}
														/>
													</button>
												</div>
												<div className='text-xs text-gray-600 break-all'>
													{locationLoading ? (
														<span className='text-blue-500'>
															📍 Getting location...
														</span>
													) : locationError ? (
														<span className='text-red-600'>
															⚠️ {locationError}
														</span>
													) : (
														formatLocation()
													)}
												</div>
											</div>
										</div>
									</div>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/profile');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-blue-50 transition-colors text-blue-700 hover:text-blue-900'>
										<FaUser className='text-base' /> Profile
									</button>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/credits');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-yellow-50 transition-colors text-yellow-700 hover:text-yellow-900'>
										<FiZap className='text-base' /> Credits
									</button>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/settings');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors text-gray-700 hover:text-gray-900'>
										<FaCog className='text-base' /> Settings
									</button>
									{showAdminDashboard && (
										<button
											onClick={() => {
												setDropdownOpen(false);
												router.push('/admin/dashboard');
											}}
											className='w-full flex items-center gap-3 px-4 py-3 hover:bg-green-50 transition-colors text-green-700 hover:text-green-900'>
											<FaStar className='text-base' /> Admin Dashboard
										</button>
									)}
									{showAgentDashboard && (
										<button
											onClick={() => {
												setDropdownOpen(false);
												router.push('/agent/dashboard');
											}}
											className='w-full flex items-center gap-3 px-4 py-3 hover:bg-green-50 transition-colors text-green-700 hover:text-green-900'>
											<FaMap className='text-base' /> Agent Dashboard
										</button>
									)}
									<div className='border-t border-gray-200 my-1' />
									<button
										onClick={handleLogout}
										disabled={isLoggingOut}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-red-600 hover:text-red-700 disabled:opacity-50'>
										<FaSignOutAlt className='text-base' />
										{isLoggingOut ? 'Logging out...' : 'Logout'}
									</button>
								</div>
							)}
						</div>
					</div>
				) : (
					<button
						onClick={handleSignIn}
						className='hidden sm:flex px-4 py-2 rounded-xl bg-blue-600 text-white font-semibold shadow-md hover:bg-blue-700 transition-colors text-sm'
						style={{ minWidth: 100 }}>
						Sign In
					</button>
				)}

				{/* Mobile Menu Button */}
				<button
					onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
					className='md:hidden p-2 rounded-lg transition-colors'
					style={{
						backgroundColor: mobileMenuOpen ? colors.ui.gray200 : 'transparent',
						color: colors.neutral.slateGray,
					}}
					aria-label='Toggle mobile menu'>
					<FaBars className='w-5 h-5' />
				</button>
			</div>

			{/* Mobile Menu Dropdown */}
			{mobileMenuOpen && (
				<div
					ref={mobileMenuRef}
					className={`absolute top-16 left-0 right-0 bg-white border-b shadow-lg md:hidden ${Z_INDEX_LAYERS.dropdown}`}
					style={{
						background: gradients.background,
						borderColor: colors.ui.gray200,
					}}>
					<div className='px-4 py-4 space-y-3 max-h-[80vh] overflow-y-auto'>
						{/* Credits display for mobile */}
						{session && (
							<div
								className='mb-4 p-3 rounded-lg'
								style={{ backgroundColor: colors.ui.blue50 }}>
								<CreditsDisplay
									size='medium'
									showAddButton={false}
								/>
							</div>
						)}

						{/* Add POI button, only on /pois */}
						{pathname === '/pois' && (
							<button
								onClick={() => {
									handleRouteNavigation('/pois/submit');
									setMobileMenuOpen(false);
								}}
								className='w-full py-3 px-3 rounded-lg bg-green-600 text-white font-semibold transition-colors mb-2'>
								+ Add POI
							</button>
						)}

						{/* Landing page navigation links */}
						{pathname === '/' && (
							<>
								<button
									onClick={() => {
										handleNavLink('features');
										setMobileMenuOpen(false);
									}}
									className='w-full text-left py-3 px-3 rounded-lg transition-colors'
									style={{ color: colors.neutral.slateGray }}>
									Features
								</button>
								<button
									onClick={() => {
										handleNavLink('how-it-works');
										setMobileMenuOpen(false);
									}}
									className='w-full text-left py-3 px-3 rounded-lg transition-colors'
									style={{ color: colors.neutral.slateGray }}>
									How it Works
								</button>
								<button
									onClick={() => {
										handleNavLink('credits');
										setMobileMenuOpen(false);
									}}
									className='w-full text-left py-2 px-3 rounded-lg transition-colors'
									style={{ color: colors.neutral.slateGray }}>
									Credits
								</button>
								<div className='border-t border-gray-200 my-2' />
							</>
						)}

						{/* Main navigation buttons */}
						<button
							onClick={() => {
								handleRouteNavigation('/chat');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors'
							style={{
								backgroundColor: colors.ui.blue100,
								color: colors.brand.blue,
							}}>
							<FaComments className='w-4 h-4' />
							<span>Chat</span>
						</button>

						<button
							onClick={() => {
								handleRouteNavigation('/globe');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors'
							style={{
								backgroundColor: colors.ui.green100,
								color: colors.brand.green,
							}}>
							<FaGlobe className='w-4 h-4' />
							<span>Globe</span>
						</button>

						<button
							onClick={() => {
								handleRouteNavigation('/pois');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors'
							style={{
								backgroundColor: colors.ui.gray100,
								color: colors.brand.teal,
							}}>
							<FaMapMarkerAlt className='w-4 h-4' />
							<span>POIs</span>
						</button>

						{/* User section */}
						{session ? (
							<>
								<div className='border-t border-gray-200 my-2' />
								<div className='py-2'>
									<CreditsDisplay
										size='small'
										showAddButton={false}
									/>
								</div>
								<button
									onClick={() => {
										handleRouteNavigation('/profile');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors'
									style={{ color: colors.neutral.slateGray }}>
									<FaUser className='w-4 h-4' />
									<span>Profile</span>
								</button>
								<button
									onClick={() => {
										handleRouteNavigation('/settings');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors'
									style={{ color: colors.neutral.slateGray }}>
									<FaCog className='w-4 h-4' />
									<span>Settings</span>
								</button>
								<button
									onClick={() => {
										handleLogout();
										setMobileMenuOpen(false);
									}}
									disabled={isLoggingOut}
									className='w-full flex items-center space-x-3 py-3 px-3 rounded-lg transition-colors text-red-600 disabled:opacity-50'>
									<FaSignOutAlt className='w-4 h-4' />
									<span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
								</button>
							</>
						) : (
							<>
								<div className='border-t border-gray-200 my-2' />
								<button
									onClick={() => {
										handleSignIn();
										setMobileMenuOpen(false);
									}}
									className='w-full py-3 px-3 rounded-lg bg-blue-600 text-white font-semibold transition-colors'>
									Sign In
								</button>
							</>
						)}
					</div>
				</div>
			)}
		</nav>
	);
};

export default AppNavBar;
