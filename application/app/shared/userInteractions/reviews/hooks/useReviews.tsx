/** @format */

'use client';

import { ReviewsService } from '@/app/shared/userInteractions/reviews/services';
import {
	POIReviewInteraction,
	ReviewRequest,
	ReviewWithUser,
	UseReviewsOptions,
	UseReviewsResult,
	UseUserReviewsOptions,
	UseUserReviewsResult,
} from '@/app/shared/userInteractions/reviews/types';
import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useRef, useState } from 'react';

export const useReviews = ({
	poi_identifier,
	user_id,
	auto_load = true,
	sort_by = 'created_at',
	sort_order = 'desc',
	limit = 20,
}: UseReviewsOptions = {}): UseReviewsResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [reviews, setReviews] = useState<ReviewWithUser[]>([]);
	const [userReview, setUserReview] = useState<POIReviewInteraction | null>(
		null
	);
	const [reviewCount, setReviewCount] = useState(0);
	const [averageRating, setAverageRating] = useState(0);
	const [ratingDistribution, setRatingDistribution] = useState<{
		1: number;
		2: number;
		3: number;
		4: number;
		5: number;
	}>({ 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 });
	const [hasReviewed, setHasReviewed] = useState(false);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);

	// Track if we've loaded initially to prevent infinite loops
	const hasLoadedInitially = useRef(false);
	const lastPoiIdentifier = useRef<unknown>(null);

	// Load reviews for the POI
	const loadReviews = useCallback(
		async (resetOffset = false, customOffset?: number) => {
			if (!poi_identifier) return;

			setLoading(true);
			setError(null);

			try {
				const response = await ReviewsService.getPOIReviews(poi_identifier, {
					limit,
					offset:
						customOffset !== undefined
							? customOffset
							: resetOffset
							? 0
							: undefined,
					sortBy: sort_by,
					sortOrder: sort_order,
				});

				if (response.success) {
					if (resetOffset || customOffset === 0) {
						setReviews(response.reviews);
					} else {
						setReviews((prev) => [...prev, ...response.reviews]);
					}

					setReviewCount(response.total_count || 0);
					setAverageRating(response.average_rating || 0);
					setRatingDistribution(
						response.rating_distribution || { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
					);
					setHasMore(response.has_more || false);
				} else {
					throw new Error(response.error || 'Failed to load reviews');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load reviews';
				setError(errorMessage);
				console.error('Error loading reviews:', err);
			} finally {
				setLoading(false);
			}
		},
		[poi_identifier, limit, sort_by, sort_order]
	);

	// Load user's review for this POI
	const loadUserReview = useCallback(async () => {
		if (!poi_identifier || !currentUserId) return;

		try {
			const response = await ReviewsService.getUserReviewForPOI(
				poi_identifier,
				currentUserId
			);

			if (response.success) {
				setUserReview(response.review);
				setHasReviewed(response.hasReviewed);
			} else {
				throw new Error('Failed to load user review');
			}
		} catch (err) {
			console.error('Error loading user review:', err);
			// Don't set error state for user review loading as it's not critical
		}
	}, [poi_identifier, currentUserId]);

	// Add review
	const addReview = useCallback(
		async (reviewData: Omit<ReviewRequest, 'poi_identifier' | 'action'>) => {
			if (!poi_identifier || !currentUserId) {
				setError('POI identifier and user ID are required');
				return;
			}

			if (hasReviewed) {
				setError('You have already reviewed this POI');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await ReviewsService.addReview(
					poi_identifier,
					reviewData
				);

				if (response.success) {
					// Refresh reviews and user review
					await Promise.all([loadReviews(true), loadUserReview()]);
				} else {
					throw new Error(response.error || 'Failed to add review');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to add review';
				setError(errorMessage);
				console.error('Error adding review:', err);
			} finally {
				setLoading(false);
			}
		},
		[poi_identifier, currentUserId, hasReviewed, loadReviews, loadUserReview]
	);

	// Update review
	const updateReview = useCallback(
		async (reviewId: string | number, reviewData: Partial<ReviewRequest>) => {
			if (!currentUserId) {
				setError('User ID is required');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await ReviewsService.updateReview(
					reviewId,
					reviewData
				);

				if (response.success) {
					// Refresh reviews and user review
					await Promise.all([loadReviews(true), loadUserReview()]);
				} else {
					throw new Error(response.error || 'Failed to update review');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to update review';
				setError(errorMessage);
				console.error('Error updating review:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, loadReviews, loadUserReview]
	);

	// Remove review
	const removeReview = useCallback(
		async (reviewId: string | number) => {
			if (!currentUserId) {
				setError('User ID is required');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await ReviewsService.removeReview(reviewId);

				if (response.success) {
					// Refresh reviews and user review
					await Promise.all([loadReviews(true), loadUserReview()]);
				} else {
					throw new Error(response.error || 'Failed to remove review');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to remove review';
				setError(errorMessage);
				console.error('Error removing review:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, loadReviews, loadUserReview]
	);

	// Toggle helpful
	const toggleHelpful = useCallback(
		async (reviewId: string | number) => {
			if (!currentUserId) {
				setError('User ID is required');
				return;
			}

			// Find the review to check current helpful status
			const review = reviews.find((r) => r.review_id === reviewId);
			if (!review) return;

			const isCurrentlyHelpful = review.is_helpful || false;

			try {
				const response = await ReviewsService.toggleReviewHelpful(
					reviewId,
					isCurrentlyHelpful
				);

				if (response.success) {
					// Update the review in the local state
					setReviews((prev) =>
						prev.map((r) =>
							r.review_id === reviewId
								? {
										...r,
										is_helpful: !isCurrentlyHelpful,
										helpful_votes: response.helpful_votes || r.helpful_votes,
								  }
								: r
						)
					);
				} else {
					throw new Error(response.error || 'Failed to toggle helpful');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to toggle helpful';
				setError(errorMessage);
				console.error('Error toggling helpful:', err);
			}
		},
		[currentUserId, reviews]
	);

	// Load more reviews
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadReviews(false);
	}, [hasMore, loading, loadReviews]);

	// Refresh data
	const refresh = useCallback(async () => {
		await Promise.all([
			loadReviews(true),
			currentUserId ? loadUserReview() : Promise.resolve(),
		]);
	}, [loadReviews, loadUserReview, currentUserId]);

	// Auto-load on mount and when POI identifier changes
	useEffect(() => {
		// Check if POI identifier has actually changed
		const poiChanged =
			JSON.stringify(lastPoiIdentifier.current) !==
			JSON.stringify(poi_identifier);

		if (
			auto_load &&
			poi_identifier &&
			(!hasLoadedInitially.current || poiChanged)
		) {
			// Reset state when POI changes
			if (poiChanged) {
				setReviews([]);
				setUserReview(null);
				setReviewCount(0);
				setAverageRating(0);
				setRatingDistribution({ 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 });
				setHasReviewed(false);
				setError(null);
			}

			hasLoadedInitially.current = true;
			lastPoiIdentifier.current = poi_identifier;

			// Call loadReviews directly without dependency to avoid infinite loop
			if (!poi_identifier) return;

			setLoading(true);
			setError(null);

			const offsetToUse = 0;

			ReviewsService.getPOIReviews(poi_identifier, {
				limit,
				offset: offsetToUse,
				sortBy: sort_by,
				sortOrder: sort_order,
			})
				.then((response) => {
					if (response.success) {
						setReviews(response.reviews);
						setReviewCount(response.total_count || 0);
						setAverageRating(response.average_rating || 0);
						setRatingDistribution(
							response.rating_distribution || { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
						);
						setHasMore(response.has_more || false);
					} else {
						throw new Error(response.error || 'Failed to load reviews');
					}
				})
				.catch((err) => {
					const errorMessage =
						err instanceof Error ? err.message : 'Failed to load reviews';
					setError(errorMessage);
					console.error('Error loading reviews:', err);
				})
				.finally(() => {
					setLoading(false);
				});
		}
	}, [auto_load, poi_identifier, limit, sort_by, sort_order]);

	return {
		reviews,
		userReview,
		reviewCount,
		averageRating,
		ratingDistribution,
		hasReviewed,
		loading,
		error,
		hasMore,
		addReview,
		updateReview,
		removeReview,
		toggleHelpful,
		loadReviews,
		loadMore,
		refresh,
		loadUserReview,
	};
};

// Hook for managing user's reviews across all POIs
export const useUserReviews = ({
	user_id,
	auto_load = true,
	limit = 20,
	offset = 0,
}: UseUserReviewsOptions = {}): UseUserReviewsResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [reviews, setReviews] = useState<POIReviewInteraction[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [totalCount, setTotalCount] = useState(0);
	const [currentOffset, setCurrentOffset] = useState(offset);

	// Load reviews
	const loadReviews = useCallback(
		async (
			targetUserId?: string,
			resetOffset = false,
			customOffset?: number
		) => {
			const userIdToUse = targetUserId || currentUserId;

			if (!userIdToUse) {
				setError('User ID is required');
				return;
			}

			setLoading(true);
			setError(null);

			const offsetToUse =
				customOffset !== undefined
					? customOffset
					: resetOffset
					? 0
					: currentOffset;

			try {
				const response = await ReviewsService.getUserReviews(
					userIdToUse,
					limit,
					offsetToUse
				);

				if (response.success) {
					if (resetOffset || offsetToUse === 0) {
						setReviews(response.reviews);
					} else {
						setReviews((prev) => [...prev, ...response.reviews]);
					}

					setTotalCount(response.total_count || 0);
					setHasMore(response.has_more || false);
					setCurrentOffset(offsetToUse + response.reviews.length);
				} else {
					throw new Error(response.error || 'Failed to load reviews');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load reviews';
				setError(errorMessage);
				console.error('Error loading reviews:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, limit]
	);

	// Load more reviews
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadReviews(undefined, false, currentOffset);
	}, [hasMore, loading, loadReviews, currentOffset]);

	// Refresh data
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await loadReviews(undefined, true, 0);
	}, [loadReviews]);

	// Remove a review
	const removeReview = useCallback(
		async (reviewId: string | number) => {
			try {
				// Find the review to remove
				const reviewToRemove = reviews.find((review) => review.id === reviewId);
				if (!reviewToRemove) {
					throw new Error('Review not found');
				}

				const response = await ReviewsService.removeReview(reviewId);

				if (response.success) {
					// Remove from local state
					setReviews((prev) => prev.filter((review) => review.id !== reviewId));
					setTotalCount((prev) => Math.max(0, prev - 1));
				} else {
					throw new Error(response.error || 'Failed to remove review');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to remove review';
				setError(errorMessage);
				console.error('Error removing review:', err);
				throw err;
			}
		},
		[reviews]
	);

	// Auto-load on mount and when dependencies change
	useEffect(() => {
		if (auto_load && currentUserId) {
			loadReviews(currentUserId, true);
		}
	}, [auto_load, currentUserId]);

	return {
		reviews,
		loading,
		error,
		hasMore,
		totalCount,
		loadReviews,
		loadMore,
		refresh,
		removeReview,
	};
};
