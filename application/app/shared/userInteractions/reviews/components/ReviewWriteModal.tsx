/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { FaCalendar, FaStar, FaTimes } from 'react-icons/fa';

interface ReviewWriteModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: () => void;
	poiName: string;
	poiId?: number | null;
	poiType: string;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;
	existingReview?: ReviewDataWithId | null;
}

export interface ReviewData {
	rating: number;
	review_title?: string;
	review_text: string;
	visit_date: string;
	photos: string[];
	tags?: string[];
}

// Add a type for editing
export interface ReviewDataWithId extends ReviewData {
	id?: string | number;
}

export default function ReviewWriteModal({
	isOpen,
	onClose,
	onSubmit,
	poiName,
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	existingReview,
}: ReviewWriteModalProps) {
	const [rating, setRating] = useState(existingReview?.rating || 0);
	const [hoveredRating, setHoveredRating] = useState(0);
	const [reviewTitle, setReviewTitle] = useState(
		existingReview?.review_title || ''
	);
	const [reviewText, setReviewText] = useState(
		existingReview?.review_text || ''
	);
	const [visitDate, setVisitDate] = useState(existingReview?.visit_date || '');
	const [tags, setTags] = useState<string[]>(existingReview?.tags || []);
	const [newTag, setNewTag] = useState('');
	const [photos, setPhotos] = useState<string[]>(existingReview?.photos || []);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (rating === 0) {
			alert('Please select a rating');
			return;
		}

		if (!reviewText.trim()) {
			alert('Please write a review');
			return;
		}

		setIsSubmitting(true);

		try {
			const reviewData: Record<string, unknown> = {
				poiId: poiType === 'official' ? poiId?.toString() : null,
				userPoiTempId:
					poiType === 'user_temp' ? userPoiTempId?.toString() : null,
				userPoiApprovedId:
					poiType === 'user_approved' ? userPoiApprovedId?.toString() : null,
				poiType,
				rating,
				review_title: reviewTitle.trim(),
				review_text: reviewText.trim(),
				visit_date: visitDate,
				photos,
				tags,
			};

			const url = `/api/pois/reviews`;
			const method = existingReview ? 'PUT' : 'POST';

			if (existingReview) {
				reviewData.reviewId = existingReview.id;
			}

			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(reviewData),
			});

			const data = await response.json();

			if (data.success) {
				onSubmit(); // Notify parent component
				onClose();
				// Reset form
				setRating(0);
				setReviewTitle('');
				setReviewText('');
				setVisitDate('');
				setTags([]);
				setPhotos([]);
			} else {
				alert(data.error || 'Failed to submit review');
			}
		} catch (error) {
			console.error('Error submitting review:', error);
			alert('Failed to submit review');
		} finally {
			setIsSubmitting(false);
		}
	};

	const addTag = () => {
		if (newTag.trim() && !tags.includes(newTag.trim())) {
			setTags([...tags, newTag.trim()]);
			setNewTag('');
		}
	};

	const removeTag = (tagToRemove: string) => {
		setTags(tags.filter((tag) => tag !== tagToRemove));
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			addTag();
		}
	};

	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'>
			<div className='bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden'>
				{/* Header */}
				<div
					className='flex items-center justify-between p-6 border-b'
					style={{ borderColor: colors.ui.gray200 }}>
					<div>
						<h2
							className='text-xl font-bold'
							style={{ color: colors.neutral.textBlack }}>
							{existingReview ? 'Edit Review' : 'Write a Review'}
						</h2>
						<p
							className='text-sm'
							style={{ color: colors.neutral.slateGray }}>
							Share your experience at {poiName}
						</p>
					</div>
					<button
						onClick={onClose}
						className='p-2 rounded-lg transition-colors hover:bg-gray-100'>
						<FaTimes
							className='w-5 h-5'
							style={{ color: colors.neutral.slateGray }}
						/>
					</button>
				</div>

				{/* Content */}
				<div className='p-6 overflow-y-auto max-h-[calc(90vh-140px)]'>
					<form
						onSubmit={handleSubmit}
						className='space-y-6'>
						{/* Rating */}
						<div>
							<label
								className='block text-sm font-medium mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Rating *
							</label>
							<div className='flex items-center gap-1'>
								{[1, 2, 3, 4, 5].map((star) => (
									<button
										key={star}
										type='button'
										onClick={() => setRating(star)}
										onMouseEnter={() => setHoveredRating(star)}
										onMouseLeave={() => setHoveredRating(0)}
										className='p-1 transition-colors'>
										<FaStar
											className={`w-8 h-8 ${
												star <= (hoveredRating || rating)
													? 'text-yellow-400'
													: 'text-gray-300'
											}`}
										/>
									</button>
								))}
								<span
									className='ml-3 text-sm'
									style={{ color: colors.neutral.slateGray }}>
									{rating > 0 && (
										<>
											{rating} star{rating !== 1 ? 's' : ''} -{' '}
											{rating === 1
												? 'Poor'
												: rating === 2
												? 'Fair'
												: rating === 3
												? 'Good'
												: rating === 4
												? 'Very Good'
												: 'Excellent'}
										</>
									)}
								</span>
							</div>
						</div>

						{/* Review Title */}
						<div>
							<label
								className='block text-sm font-medium mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Review Title (Optional)
							</label>
							<input
								type='text'
								value={reviewTitle}
								onChange={(e) => setReviewTitle(e.target.value)}
								placeholder='Summarize your experience...'
								className='w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
								style={{ borderColor: colors.ui.gray300 }}
								maxLength={200}
							/>
						</div>

						{/* Review Text */}
						<div>
							<label
								className='block text-sm font-medium mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Your Review
							</label>
							<textarea
								value={reviewText}
								onChange={(e) => setReviewText(e.target.value)}
								placeholder='Tell others about your experience...'
								rows={4}
								className='w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none'
								style={{ borderColor: colors.ui.gray300 }}
								maxLength={1000}
							/>
							<div
								className='text-right text-xs mt-1'
								style={{ color: colors.neutral.slateGray }}>
								{reviewText.length}/1000 characters
							</div>
						</div>

						{/* Visit Date */}
						<div>
							<label
								className='block text-sm font-medium mb-2'
								style={{ color: colors.neutral.textBlack }}>
								<FaCalendar className='inline w-4 h-4 mr-2' />
								When did you visit?
							</label>
							<input
								type='date'
								value={visitDate}
								onChange={(e) => setVisitDate(e.target.value)}
								className='px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
								style={{ borderColor: colors.ui.gray300 }}
								max={new Date().toISOString().split('T')[0]}
							/>
						</div>

						{/* Tags */}
						<div>
							<label
								className='block text-sm font-medium mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Tags (Optional)
							</label>
							<div className='flex flex-wrap gap-2 mb-3'>
								{tags.map((tag, index) => (
									<span
										key={index}
										className='inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm'>
										{tag}
										<button
											type='button'
											onClick={() => removeTag(tag)}
											className='hover:text-blue-600'>
											<FaTimes className='w-3 h-3' />
										</button>
									</span>
								))}
							</div>
							<div className='flex gap-2'>
								<input
									type='text'
									value={newTag}
									onChange={(e) => setNewTag(e.target.value)}
									onKeyDown={handleKeyDown}
									placeholder='Add a tag...'
									className='flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
									style={{ borderColor: colors.ui.gray300 }}
									maxLength={30}
								/>
								<button
									type='button'
									onClick={addTag}
									className='px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
									Add
								</button>
							</div>
						</div>

						{/* Submit Buttons */}
						<div className='flex gap-3 pt-4'>
							<button
								type='button'
								onClick={onClose}
								className='flex-1 px-6 py-3 border rounded-lg transition-colors'
								style={{
									borderColor: colors.ui.gray300,
									color: colors.neutral.slateGray,
								}}>
								Cancel
							</button>
							<button
								type='submit'
								disabled={rating === 0 || isSubmitting}
								className='flex-1 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
								{isSubmitting
									? 'Submitting...'
									: existingReview
									? 'Update Review'
									: 'Submit Review'}
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	);
}
