/** @format */

'use client';

import { colors } from '@/app/colors';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import {
	FaCalendar,
	FaEdit,
	FaStar,
	FaThumbsDown,
	FaThumbsUp,
	FaTrash,
	FaUser,
} from 'react-icons/fa';

export interface Review {
	id: string;
	user_id: string;
	username?: string;
	user_name?: string;
	user_avatar?: string;
	rating: number;
	review_title?: string;
	review_text: string;
	visit_date: string;
	photos: string[];
	tags?: string[];
	helpful_votes: number;
	is_verified?: boolean;
	created_at: string;
	updated_at?: string;
	is_own_review?: boolean;
	user_helpful_vote?: 'up' | 'down' | null;
}

interface ReviewDisplayProps {
	poiId?: number | null;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;
	poiType: string;
	refreshTrigger?: number;
	onEditReview?: (review: Review) => void;
	showUserActions?: boolean;
}

export default function ReviewDisplay({
	poiId,
	userPoiTempId,
	userPoiApprovedId,
	poiType,
	refreshTrigger = 0,
	onEditReview,
	showUserActions = true,
}: ReviewDisplayProps) {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [loading, setLoading] = useState(true);
	const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
	const { data: session } = useSession();

	// Fetch reviews when component mounts or refreshTrigger changes
	useEffect(() => {
		if (poiType === 'all' && session?.user?.id) {
			// Fetch all reviews by the user
			loadUserReviews();
		} else {
			loadReviews();
		}
	}, [
		poiId,
		userPoiTempId,
		userPoiApprovedId,
		poiType,
		refreshTrigger,
		session?.user?.id,
	]);

	const loadReviews = async () => {
		setLoading(true);

		try {
			const params = new URLSearchParams({
				poiType,
				limit: '50',
				sortBy: 'created_at',
				sortOrder: 'desc',
			});

			if (poiType === 'official' && poiId) {
				params.append('poiId', poiId.toString());
			} else if (poiType === 'user_temp' && userPoiTempId) {
				params.append('userPoiTempId', userPoiTempId.toString());
			} else if (poiType === 'user_approved' && userPoiApprovedId) {
				params.append('userPoiApprovedId', userPoiApprovedId.toString());
			}

			const response = await fetch(`/api/pois/reviews?${params}`);
			const data = await response.json();

			if (data.success) {
				setReviews(data.reviews || []);
			} else {
				// setError(data.error || 'Failed to load reviews')
			}
		} catch (error) {
			console.error('Error loading reviews:', error);
			// setError('Failed to load reviews')
		} finally {
			setLoading(false);
		}
	};

	const loadUserReviews = async () => {
		setLoading(true);
		try {
			const response = await fetch(
				`/api/pois/reviews?userId=${session?.user?.id}&limit=50&sortBy=created_at&sortOrder=desc`
			);
			const data = await response.json();
			if (data.success) {
				setReviews(data.reviews || []);
			} else {
				// setError(data.error || 'Failed to load reviews')
			}
		} catch {
			// setError('Failed to load reviews')
		} finally {
			setLoading(false);
		}
	};

	const handleHelpfulVote = async (
		reviewId: string,
		voteType: 'up' | 'down'
	) => {
		if (votingStates[reviewId]) return;

		setVotingStates((prev) => ({ ...prev, [reviewId]: true }));
		try {
			// TODO: Implement helpful vote API
			console.log('Helpful vote:', reviewId, voteType);
		} catch (error) {
			console.error('Error voting on review:', error);
		} finally {
			setVotingStates((prev) => ({ ...prev, [reviewId]: false }));
		}
	};

	const handleDeleteReview = async (reviewId: string) => {
		if (!confirm('Are you sure you want to delete this review?')) return;

		try {
			const response = await fetch(`/api/pois/reviews?reviewId=${reviewId}`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
			});

			if (response.ok) {
				setReviews((prev) => prev.filter((review) => review.id !== reviewId));
			} else {
				const data = await response.json();
				alert(data.error || 'Failed to delete review');
			}
		} catch (error) {
			console.error('Error deleting review:', error);
			alert('Failed to delete review');
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	const getTimeAgo = (dateString: string) => {
		const now = new Date();
		const date = new Date(dateString);
		const diffInMs = now.getTime() - date.getTime();
		const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

		if (diffInDays === 0) return 'Today';
		if (diffInDays === 1) return 'Yesterday';
		if (diffInDays < 7) return `${diffInDays} days ago`;
		if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
		if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
		return `${Math.floor(diffInDays / 365)} years ago`;
	};

	const renderStars = (rating: number) => {
		return (
			<div className='flex items-center gap-1'>
				{[1, 2, 3, 4, 5].map((star) => (
					<FaStar
						key={star}
						className={`w-4 h-4 ${
							star <= rating ? 'text-yellow-400' : 'text-gray-300'
						}`}
					/>
				))}
				<span
					className='ml-2 text-sm font-medium'
					style={{ color: colors.neutral.textBlack }}>
					{rating}.0
				</span>
			</div>
		);
	};

	if (loading) {
		return (
			<div className='space-y-4'>
				{[1, 2, 3].map((i) => (
					<div
						key={i}
						className='animate-pulse'>
						<div className='bg-gray-200 rounded-lg p-6'>
							<div className='flex items-start gap-4'>
								<div className='w-10 h-10 bg-gray-300 rounded-full'></div>
								<div className='flex-1 space-y-2'>
									<div className='h-4 bg-gray-300 rounded w-1/4'></div>
									<div className='h-4 bg-gray-300 rounded w-1/2'></div>
									<div className='h-16 bg-gray-300 rounded'></div>
								</div>
							</div>
						</div>
					</div>
				))}
			</div>
		);
	}

	if (!reviews || reviews.length === 0) {
		return (
			<div className='text-center py-12'>
				<FaStar className='w-12 h-12 mx-auto mb-4 text-gray-300' />
				<h3
					className='text-lg font-medium mb-2'
					style={{ color: colors.neutral.textBlack }}>
					No reviews yet
				</h3>
				<p className='text-gray-500'>
					Be the first to share your experience at this place!
				</p>
			</div>
		);
	}

	return (
		<div className='space-y-6'>
			{reviews.map((review) => (
				<div
					key={review.id}
					className='bg-white border rounded-lg p-6 shadow-sm'
					style={{ borderColor: colors.ui.gray200 }}>
					{/* Review Header */}
					<div className='flex items-start justify-between mb-4'>
						<div className='flex items-start gap-4'>
							{/* User Avatar */}
							<div
								className='w-10 h-10 rounded-full flex items-center justify-center'
								style={{ backgroundColor: colors.ui.gray100 }}>
								<FaUser
									className='w-5 h-5'
									style={{ color: colors.neutral.slateGray }}
								/>
							</div>

							{/* User Info & Rating */}
							<div>
								<div className='flex items-center gap-3 mb-1'>
									<h4
										className='font-medium'
										style={{ color: colors.neutral.textBlack }}>
										{review.user_name || review.username || 'Anonymous User'}
									</h4>
									{review.is_own_review && (
										<span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
											Your Review
										</span>
									)}
								</div>
								{renderStars(review.rating)}
								<div
									className='flex items-center gap-4 mt-1 text-sm'
									style={{ color: colors.neutral.slateGray }}>
									<span className='flex items-center gap-1'>
										<FaCalendar className='w-3 h-3' />
										{review.visit_date
											? `Visited ${formatDate(review.visit_date)}`
											: 'Visit date not specified'}
									</span>
									<span>{getTimeAgo(review.created_at)}</span>
								</div>
							</div>
						</div>

						{/* Action Buttons */}
						{showUserActions && review.is_own_review && (
							<div className='flex items-center gap-2'>
								{onEditReview && (
									<button
										onClick={() => onEditReview(review)}
										className='p-2 rounded-lg transition-colors hover:bg-gray-100'
										title='Edit review'>
										<FaEdit
											className='w-4 h-4'
											style={{ color: colors.neutral.slateGray }}
										/>
									</button>
								)}
								<button
									onClick={() => handleDeleteReview(review.id)}
									className='p-2 rounded-lg transition-colors hover:bg-red-50 hover:text-red-600'
									title='Delete review'>
									<FaTrash
										className='w-4 h-4'
										style={{ color: colors.neutral.slateGray }}
									/>
								</button>
							</div>
						)}
					</div>

					{/* Review Title */}
					{review.review_title && (
						<h5
							className='font-semibold mb-2'
							style={{ color: colors.neutral.textBlack }}>
							{review.review_title}
						</h5>
					)}

					{/* Review Text */}
					{review.review_text && (
						<p className='text-gray-700 leading-relaxed mb-4'>
							{review.review_text}
						</p>
					)}

					{/* Tags */}
					{review.tags && review.tags.length > 0 && (
						<div className='flex flex-wrap gap-2 mb-4'>
							{review.tags.map((tag, index) => (
								<span
									key={index}
									className='px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-sm'>
									{tag}
								</span>
							))}
						</div>
					)}

					{/* Photos */}
					{review.photos && review.photos.length > 0 && (
						<div className='grid grid-cols-2 md:grid-cols-4 gap-2 mb-4'>
							{review.photos.map((photo, index) => (
								<img
									key={index}
									src={photo}
									alt={`Review photo ${index + 1}`}
									className='w-full h-20 object-cover rounded-lg'
								/>
							))}
						</div>
					)}

					{/* Helpful Votes */}
					{showUserActions && !review.is_own_review && session?.user?.id && (
						<div
							className='flex items-center gap-4 pt-4 border-t'
							style={{ borderColor: colors.ui.gray100 }}>
							<span
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								Was this review helpful?
							</span>
							<div className='flex items-center gap-2'>
								<button
									onClick={() => handleHelpfulVote(review.id, 'up')}
									disabled={votingStates[review.id]}
									className={`flex items-center gap-1 px-3 py-1 rounded-lg transition-colors ${
										review.user_helpful_vote === 'up'
											? 'bg-green-100 text-green-700'
											: 'hover:bg-gray-100'
									}`}>
									<FaThumbsUp className='w-3 h-3' />
									<span className='text-sm'>Yes</span>
								</button>
								<button
									onClick={() => handleHelpfulVote(review.id, 'down')}
									disabled={votingStates[review.id]}
									className={`flex items-center gap-1 px-3 py-1 rounded-lg transition-colors ${
										review.user_helpful_vote === 'down'
											? 'bg-red-100 text-red-700'
											: 'hover:bg-gray-100'
									}`}>
									<FaThumbsDown className='w-3 h-3' />
									<span className='text-sm'>No</span>
								</button>
								{review.helpful_votes > 0 && (
									<span
										className='text-sm ml-2'
										style={{ color: colors.neutral.slateGray }}>
										{review.helpful_votes} found this helpful
									</span>
								)}
							</div>
						</div>
					)}
				</div>
			))}
		</div>
	);
}
