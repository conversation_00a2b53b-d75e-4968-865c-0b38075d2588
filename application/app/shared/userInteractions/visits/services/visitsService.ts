/** @format */

// Visit-specific service operations

import { BaseInteractionService } from '@/app/shared/userInteractions/shared/services';
import { POIIdentifier } from '@/app/shared/userInteractions/shared/types';
import {
	POIVisitInteraction,
	VisitListResponse,
	VisitRequest,
	VisitResponse,
} from '@/app/shared/userInteractions/visits/types';

export class VisitsService extends BaseInteractionService {
	// Add a visit to a POI (simplified like likes)
	static async addVisit(poi: POIIdentifier): Promise<VisitResponse> {
		return this.updateInteraction(poi, 'visit', 'add');
	}

	// Remove a visit from a POI (simplified like likes)
	static async removeVisit(poi: POIIdentifier): Promise<VisitResponse> {
		return this.updateInteraction(poi, 'visit', 'remove');
	}

	// Toggle visit status
	static async toggleVisit(
		poi: POIIdentifier,
		currentlyVisited: boolean
	): Promise<VisitResponse> {
		return currentlyVisited ? this.removeVisit(poi) : this.addVisit(poi);
	}

	// Get user's visit status for a POI (like likes service)
	static async getUserVisitStatus(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		hasVisited: boolean;
		visitCount: number;
	}> {
		try {
			// Get user's visit status
			const userResponse = await this.getUserInteractions(poi, userId, 'visit');

			if (!userResponse.success) {
				// If it's an authentication error, return default values instead of throwing
				if (
					userResponse.error?.includes('Authentication required') ||
					userResponse.error?.includes('Access denied')
				) {
					// Still try to get the public visit count
					const countResponse = await this.getPOIVisitCount(poi);
					const visitCount = countResponse.success
						? countResponse.visitCount
						: 0;

					return {
						success: true,
						hasVisited: false,
						visitCount,
					};
				}
				throw new Error(userResponse.error || 'Failed to get visit status');
			}

			// Check if user has visited this POI
			const userVisit = userResponse.interactions?.find(
				(interaction) =>
					interaction.user_id === userId &&
					interaction.interaction_type === 'visit'
			);

			// Get total visit count
			const countResponse = await this.getPOIVisitCount(poi);
			const visitCount = countResponse.success ? countResponse.visitCount : 0;

			return {
				success: true,
				hasVisited: !!userVisit,
				visitCount,
			};
		} catch (error) {
			// Handle HTTP errors gracefully
			const errorMessage = super.handleApiError(error);
			if (
				errorMessage.includes('401') ||
				errorMessage.includes('403') ||
				errorMessage.includes('Authentication required')
			) {
				// Still try to get the public visit count
				try {
					const countResponse = await this.getPOIVisitCount(poi);
					const visitCount = countResponse.success
						? countResponse.visitCount
						: 0;

					return {
						success: true,
						hasVisited: false,
						visitCount,
					};
				} catch {
					return {
						success: true,
						hasVisited: false,
						visitCount: 0,
					};
				}
			}
			throw new Error(errorMessage);
		}
	}

	// Get public visit count for a POI (no authentication required)
	static async getPOIVisitCount(poi: POIIdentifier): Promise<{
		success: boolean;
		visitCount: number;
	}> {
		try {
			const response = await this.getPOIVisits(poi, { limit: 1, offset: 0 }); // Just get count, not actual data

			if (!response.success) {
				throw new Error(response.error || 'Failed to get visit count');
			}

			return {
				success: true,
				visitCount: response.total_count || 0,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Add a visit with metadata (for detailed visit tracking)
	static async addVisitWithData(
		poi: POIIdentifier,
		visitData: Omit<VisitRequest, 'poi_identifier' | 'action'> = {}
	): Promise<VisitResponse> {
		const body = {
			...this.buildPOIBody(poi),
			interactionType: 'visit',
			action: 'add',
			metadata: {
				visit_date: visitData.visit_date || new Date().toISOString(),
				visit_duration_minutes: visitData.visit_duration_minutes,
				visit_type: visitData.visit_type || 'spontaneous',
				notes: visitData.notes || '',
			},
		};

		return this.post<VisitResponse>('/interactions', body);
	}

	// Remove a specific visit by ID (for detailed visit tracking)
	static async removeVisitById(
		poi: POIIdentifier,
		visitId: string | number
	): Promise<VisitResponse> {
		const body = {
			...this.buildPOIBody(poi),
			interactionType: 'visit',
			action: 'remove',
			visitId,
		};

		return this.post<VisitResponse>('/interactions', body);
	}

	// Get visits for a POI
	static async getPOIVisits(
		poi: POIIdentifier,
		options: {
			limit?: number;
			offset?: number;
			sortBy?: 'created_at' | 'visit_date';
			sortOrder?: 'asc' | 'desc';
		} = {}
	): Promise<VisitListResponse> {
		const params = {
			...this.buildPOIParams(poi),
			interactionType: 'visit',
			limit: options.limit || 20,
			offset: options.offset || 0,
			sortBy: options.sortBy || 'created_at',
			sortOrder: options.sortOrder || 'desc',
		};

		try {
			const response = await this.get<unknown>('/interactions', params);
			console.log('getPOIVisits response:', response);

			if (
				typeof response === 'object' &&
				response !== null &&
				'success' in response
			) {
				const typedResponse = response as {
					success: boolean;
					interactions?: unknown[];
					total_count?: number;
					has_more?: boolean;
					error?: string;
				};

				return {
					success: typedResponse.success,
					interactions:
						(typedResponse.interactions as POIVisitInteraction[]) || [],
					total_count: typedResponse.total_count || 0,
					has_more: typedResponse.has_more || false,
					error: typedResponse.error,
				};
			}
			console.error('Unexpected response format from /interactions:', response);
			throw new Error('Unexpected response format from /interactions');
		} catch (error) {
			return {
				success: false,
				interactions: [],
				total_count: 0,
				has_more: false,
				error: super.handleApiError(error),
			};
		}
	}

	// Get user's visit for a specific POI
	static async getUserVisitForPOI(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		visit: POIVisitInteraction | null;
		hasVisited: boolean;
	}> {
		try {
			const params = {
				...this.buildPOIParams(poi),
				interactionType: 'visit',
				userId,
			};

			const response = await this.get<VisitListResponse>(
				'/interactions',
				params
			);

			if (!response.success) {
				// If it's an authentication error, return default values instead of throwing
				if (
					response.error?.includes('Authentication required') ||
					response.error?.includes('Access denied')
				) {
					return {
						success: true,
						visit: null,
						hasVisited: false,
					};
				}
				throw new Error(response.error || 'Failed to get user visit');
			}

			const userVisit = response.interactions?.find(
				(visit: POIVisitInteraction) => visit.user_id === userId
			);

			return {
				success: true,
				visit: userVisit || null,
				hasVisited: !!userVisit,
			};
		} catch (error) {
			// Handle HTTP errors gracefully
			const errorMessage = super.handleApiError(error);
			if (
				errorMessage.includes('401') ||
				errorMessage.includes('403') ||
				errorMessage.includes('Authentication required')
			) {
				return {
					success: true,
					visit: null,
					hasVisited: false,
				};
			}
			throw new Error(errorMessage);
		}
	}

	// Get user's visits across all POIs
	static async getUserVisits(
		userId: string,
		limit: number = 20,
		offset: number = 0
	): Promise<VisitListResponse> {
		const params = {
			userId,
			interactionType: 'visit',
			limit,
			offset,
		};

		return this.get<VisitListResponse>('/interactions', params);
	}

	// Get visit statistics for a POI
	static async getVisitStats(poi: POIIdentifier): Promise<{
		success: boolean;
		totalVisits: number;
		uniqueVisitors: number;
		recentVisits: number; // visits in last 30 days
	}> {
		try {
			const response = await this.getPOIVisits(poi, { limit: 1 });

			if (!response.success) {
				throw new Error(response.error || 'Failed to get visit stats');
			}

			return {
				success: true,
				totalVisits: response.total_count || 0,
				uniqueVisitors: response.total_count || 0, // For now, assume each visit is unique
				recentVisits: 0, // Would need additional API endpoint for this
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Check if user has visited a POI
	static async checkUserVisit(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		hasVisited: boolean;
		visitCount: number;
	}> {
		try {
			const result = await this.getUserVisitForPOI(poi, userId);
			const stats = await this.getVisitStats(poi);

			return {
				success: true,
				hasVisited: result.hasVisited,
				visitCount: stats.totalVisits,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}
}
