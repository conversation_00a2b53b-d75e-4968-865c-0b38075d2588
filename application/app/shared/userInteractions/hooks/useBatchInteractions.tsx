/** @format */

/**
 * Optimized batch interactions hook for loading multiple POI interactions efficiently
 * Works seamlessly with useUnifiedInteractions for consistent data format
 *
 * Features:
 * - Batch loading for performance
 * - Deduplication to prevent duplicate requests
 * - Caching with smart invalidation
 * - Compatible data format with unified system
 * - Optimistic updates support
 */

'use client';

import { useCallback, useRef, useState } from 'react';
import {
	BaseInteractionOptions,
	InteractionData as BatchInteractionData,
	Batch<PERSON>I,
	SharedCache,
	generatePOIK<PERSON>,
	isCacheEntryValid,
} from './types';

interface BatchInteractionsState {
	[key: string]: BatchInteractionData; // key format: "poiType_poiId"
}

export interface UseBatchInteractionsOptions extends BaseInteractionOptions {
	batch_size?: number;
	shared_cache?: SharedCache;
}

export interface UseBatchInteractionsResult {
	// Data
	interactions: BatchInteractionsState;

	// Loading states
	isLoading: boolean;
	loadingPOIs: Set<string>;

	// Error state
	error: string | null;

	// Actions
	loadInteractions: (pois: BatchPOI[]) => Promise<void>;
	getInteractionData: (poi: BatchPOI) => BatchInteractionData | null;
	refreshInteractions: (pois?: BatchPOI[]) => Promise<void>;
	clearCache: () => void;

	// Utilities
	isLoaded: (poi: BatchPOI) => boolean;
	isPending: (poi: BatchPOI) => boolean;
}

/**
 * Optimized batch interactions hook
 */
// Shared cache for cross-hook optimization
const globalBatchCache = new Map<
	string,
	{ data: BatchInteractionData; timestamp: number }
>();

export const useBatchInteractions = ({
	enable_caching = true,
	cache_duration = 5 * 60 * 1000, // 5 minutes
	enable_optimistic_updates = true,
	batch_size = 50,
	shared_cache = globalBatchCache,
}: UseBatchInteractionsOptions = {}): UseBatchInteractionsResult => {
	// State
	const [interactions, setInteractions] = useState<BatchInteractionsState>({});
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Refs for tracking
	const loadingPOIs = useRef<Set<string>>(new Set());
	const loadedPOIs = useRef<Set<string>>(new Set());
	const requestQueue = useRef<Set<string>>(new Set());

	/**
	 * Generate a unique key for a POI using shared utility
	 */
	const getPOIKey = useCallback((poi: BatchPOI): string => {
		return generatePOIKey(poi);
	}, []);

	/**
	 * Check if POI data is cached and still valid using shared utility
	 */
	const isCacheValid = useCallback(
		(key: string): boolean => {
			if (!enable_caching) return false;

			const cached = shared_cache.get(key);
			return isCacheEntryValid(cached, cache_duration);
		},
		[enable_caching, cache_duration, shared_cache]
	);

	/**
	 * Check if POI is already loaded
	 */
	const isLoaded = useCallback(
		(poi: BatchPOI): boolean => {
			const key = getPOIKey(poi);
			return loadedPOIs.current.has(key) && isCacheValid(key);
		},
		[getPOIKey, isCacheValid]
	);

	/**
	 * Check if POI is currently being loaded
	 */
	const isPending = useCallback(
		(poi: BatchPOI): boolean => {
			const key = getPOIKey(poi);
			return loadingPOIs.current.has(key);
		},
		[getPOIKey]
	);

	/**
	 * Convert API response to batch interaction data format
	 */
	const convertApiResponse = useCallback(
		(apiData: any): BatchInteractionData => {
			return {
				poi_id: apiData.poi_id,
				poi_type: apiData.poi_type,
				like_count: apiData.like?.count || 0,
				favorite_count: apiData.favorite?.count || 0,
				visit_count: apiData.visit?.count || 0,
				review_count: apiData.review?.count || 0, // Now included in batch API
				user_has_liked: apiData.like?.hasInteraction || false,
				user_has_favorited: apiData.favorite?.hasInteraction || false,
				user_has_visited: apiData.visit?.hasInteraction || false,
			};
		},
		[]
	);

	/**
	 * Load interactions for a batch of POIs with smart deduplication
	 */
	const loadInteractions = useCallback(
		async (pois: BatchPOI[]) => {
			if (!pois || pois.length === 0) return;

			// Filter out POIs that are already loaded/loading or cached
			const poisToLoad = pois.filter((poi) => {
				const key = getPOIKey(poi);
				return (
					!isLoaded(poi) && !isPending(poi) && !requestQueue.current.has(key)
				);
			});

			if (poisToLoad.length === 0) return;

			// Split into batches if needed
			const batches: BatchPOI[][] = [];
			for (let i = 0; i < poisToLoad.length; i += batch_size) {
				batches.push(poisToLoad.slice(i, i + batch_size));
			}

			// Process batches sequentially to avoid overwhelming the API
			for (const batch of batches) {
				await processBatch(batch);
			}
		},
		[getPOIKey, isLoaded, isPending, batch_size]
	);

	/**
	 * Process a single batch of POIs
	 */
	const processBatch = useCallback(
		async (batch: BatchPOI[]) => {
			// Mark POIs as loading and add to request queue
			batch.forEach((poi) => {
				const key = getPOIKey(poi);
				loadingPOIs.current.add(key);
				requestQueue.current.add(key);
			});

			setIsLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/pois/interactions', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						batch: true,
						pois: batch,
					}),
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();

				if (data.success) {
					// Update interactions state
					setInteractions((prev) => {
						const newInteractions = { ...prev };
						const now = Date.now();

						data.interactions.forEach((interaction: any) => {
							const key = getPOIKey({
								poi_id: interaction.poi_id,
								poi_type: interaction.poi_type,
							});

							// Convert and store interaction data
							const interactionData = convertApiResponse(interaction);
							newInteractions[key] = interactionData;

							// Update cache tracking
							loadedPOIs.current.add(key);

							// Store in shared cache
							if (enable_caching) {
								shared_cache.set(key, {
									data: interactionData,
									timestamp: now,
								});
							}
						});

						return newInteractions;
					});
				} else {
					throw new Error(data.error || 'Failed to load batch interactions');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Unknown error';
				setError(errorMessage);
				console.error('Error loading batch interactions:', err);
			} finally {
				// Clean up loading state
				batch.forEach((poi) => {
					const key = getPOIKey(poi);
					loadingPOIs.current.delete(key);
					requestQueue.current.delete(key);
				});
				setIsLoading(false);
			}
		},
		[getPOIKey, convertApiResponse, enable_caching, shared_cache]
	);

	/**
	 * Get interaction data for a specific POI
	 */
	const getInteractionData = useCallback(
		(poi: BatchPOI): BatchInteractionData | null => {
			const key = getPOIKey(poi);

			// Check if data exists and is still valid
			if (!isCacheValid(key)) {
				return null;
			}

			return interactions[key] || null;
		},
		[interactions, getPOIKey, isCacheValid]
	);

	/**
	 * Refresh interactions for specific POIs or all loaded POIs
	 */
	const refreshInteractions = useCallback(
		async (pois?: BatchPOI[]) => {
			let poisToRefresh: BatchPOI[];

			if (pois) {
				poisToRefresh = pois;
			} else {
				// Refresh all loaded POIs
				const loadedKeys = Array.from(loadedPOIs.current);
				if (loadedKeys.length === 0) return;

				poisToRefresh = loadedKeys.map((key) => {
					const [poi_type, poi_id] = key.split('_');
					return {
						poi_id: parseInt(poi_id),
						poi_type,
					};
				});
			}

			// Clear cache for POIs being refreshed
			poisToRefresh.forEach((poi) => {
				const key = getPOIKey(poi);
				loadedPOIs.current.delete(key);

				// Remove from shared cache
				if (enable_caching) {
					shared_cache.delete(key);
				}
			});

			// Reload the interactions
			await loadInteractions(poisToRefresh);
		},
		[loadInteractions, getPOIKey, enable_caching, shared_cache]
	);

	/**
	 * Clear all cached data
	 */
	const clearCache = useCallback(() => {
		setInteractions({});
		loadedPOIs.current.clear();
		requestQueue.current.clear();

		// Clear shared cache
		if (enable_caching) {
			shared_cache.clear();
		}

		setError(null);
	}, [enable_caching, shared_cache]);

	return {
		// Data
		interactions,

		// Loading states
		isLoading,
		loadingPOIs: loadingPOIs.current,

		// Error state
		error,

		// Actions
		loadInteractions,
		getInteractionData,
		refreshInteractions,
		clearCache,

		// Utilities
		isLoaded,
		isPending,
	};
};
