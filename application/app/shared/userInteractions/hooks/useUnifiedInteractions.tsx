/** @format */

/**
 * Optimized unified interactions hook for single POI interactions
 * Works seamlessly with useBatchInteractions for consistent data format
 *
 * Features:
 * - Single POI interaction management
 * - Optimistic updates for better UX
 * - Shared cache integration with batch system
 * - Consistent data format across systems
 * - Smart loading and error handling
 */

'use client';

import { POIIdentifier } from '@/app/shared/userInteractions/shared/types/base';
import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';

// Types for the unified interaction system
export interface UnifiedInteractionData {
	like_count: number;
	favorite_count: number;
	visit_count: number;
	review_count: number;
	user_has_liked: boolean;
	user_has_favorited: boolean;
	user_has_visited: boolean;
}

export interface UseUnifiedInteractionsOptions {
	poi_identifier: POIIdentifier;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
	initial_data?: Partial<UnifiedInteractionData>;
	enable_caching?: boolean;
	cache_duration?: number; // in milliseconds
	shared_cache?: Map<
		string,
		{ data: UnifiedInteractionData; timestamp: number }
	>;
}

export interface UseUnifiedInteractionsResult {
	// Data
	data: UnifiedInteractionData;

	// Loading states
	loading: boolean;
	actionLoading: {
		like: boolean;
		favorite: boolean;
		visit: boolean;
	};

	// Error state
	error: string | null;

	// Ready state
	ready: boolean;

	// Actions
	toggleLike: () => Promise<void>;
	toggleFavorite: () => Promise<void>;
	toggleVisit: () => Promise<void>;

	// Utility
	refresh: () => Promise<void>;
}

/**
 * Unified hook for managing POI interactions (like, favorite, visit)
 * Makes a single API call to get all interaction data and provides
 * individual action methods with optimistic updates
 */
// Shared cache for cross-hook optimization
const globalInteractionCache = new Map<
	string,
	{ data: UnifiedInteractionData; timestamp: number }
>();

export const useUnifiedInteractions = ({
	poi_identifier,
	auto_load = true,
	enable_optimistic_updates = true,
	initial_data,
	enable_caching = true,
	cache_duration = 5 * 60 * 1000, // 5 minutes
	shared_cache = globalInteractionCache,
}: UseUnifiedInteractionsOptions): UseUnifiedInteractionsResult => {
	const { data: session } = useSession();
	const userId = session?.user?.id;

	// Cache key for this POI
	const cacheKey = useCallback(() => {
		if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
			return `official_${poi_identifier.poi_id}`;
		} else if (
			poi_identifier.poi_type === 'user_temp' &&
			poi_identifier.user_poi_temp_id
		) {
			return `user_temp_${poi_identifier.user_poi_temp_id}`;
		} else if (
			poi_identifier.poi_type === 'user_approved' &&
			poi_identifier.user_poi_approved_id
		) {
			return `user_approved_${poi_identifier.user_poi_approved_id}`;
		}
		return '';
	}, [poi_identifier]);

	// Check if cached data is still valid
	const isCacheValid = useCallback(
		(key: string): boolean => {
			if (!enable_caching || !key) return false;

			const cached = shared_cache.get(key);
			if (!cached) return false;

			return Date.now() - cached.timestamp < cache_duration;
		},
		[enable_caching, cache_duration, shared_cache]
	);

	// State
	const [data, setData] = useState<UnifiedInteractionData>({
		like_count: initial_data?.like_count ?? 0,
		favorite_count: initial_data?.favorite_count ?? 0,
		visit_count: initial_data?.visit_count ?? 0,
		review_count: initial_data?.review_count ?? 0,
		user_has_liked: initial_data?.user_has_liked ?? false,
		user_has_favorited: initial_data?.user_has_favorited ?? false,
		user_has_visited: initial_data?.user_has_visited ?? false,
	});

	const [loading, setLoading] = useState(false);
	const [actionLoading, setActionLoading] = useState({
		like: false,
		favorite: false,
		visit: false,
	});
	const [error, setError] = useState<string | null>(null);
	const [ready, setReady] = useState(false);

	// Helper to build API URL
	const buildApiUrl = useCallback(() => {
		const params = new URLSearchParams();

		if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
			params.set('poiId', poi_identifier.poi_id.toString());
		} else if (
			poi_identifier.poi_type === 'user_temp' &&
			poi_identifier.user_poi_temp_id
		) {
			params.set('userPoiTempId', poi_identifier.user_poi_temp_id.toString());
		} else if (
			poi_identifier.poi_type === 'user_approved' &&
			poi_identifier.user_poi_approved_id
		) {
			params.set(
				'userPoiApprovedId',
				poi_identifier.user_poi_approved_id.toString()
			);
		}

		params.set('poiType', poi_identifier.poi_type);
		if (userId) {
			params.set('userId', userId);
		}

		return `/api/pois/interactions/unified?${params.toString()}`;
	}, [poi_identifier, userId]);

	// Load interaction data with caching
	const loadData = useCallback(
		async (force_refresh = false) => {
			if (!poi_identifier) return;

			const key = cacheKey();

			// Check cache first (unless force refresh)
			if (!force_refresh && isCacheValid(key)) {
				const cached = shared_cache.get(key);
				if (cached) {
					console.log(`🎯 Using cached data for POI: ${key}`);
					setData(cached.data);
					setReady(true);
					return;
				}
			}

			setLoading(true);
			setError(null);

			try {
				console.log(`📡 Loading fresh data for POI: ${key}`);
				const response = await fetch(buildApiUrl());

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (result.success) {
					const interactionData = result.data;
					setData(interactionData);

					// Cache the result
					if (enable_caching && key) {
						shared_cache.set(key, {
							data: interactionData,
							timestamp: Date.now(),
						});
						console.log(`💾 Cached interaction data for POI: ${key}`);
					}
				} else {
					throw new Error(result.error || 'Failed to load interaction data');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error
						? err.message
						: 'Failed to load interaction data';
				setError(errorMessage);
				console.error('Error loading unified interactions:', err);
			} finally {
				setLoading(false);
				setReady(true);
			}
		},
		[
			poi_identifier,
			buildApiUrl,
			cacheKey,
			isCacheValid,
			shared_cache,
			enable_caching,
		]
	);

	// Auto-load on mount
	useEffect(() => {
		if (auto_load && poi_identifier) {
			loadData();
		} else {
			setReady(true);
		}
	}, [auto_load, poi_identifier, loadData]);

	// Helper for making interaction API calls
	const makeInteractionCall = useCallback(
		async (
			interactionType: 'like' | 'favorite' | 'visit',
			action: 'add' | 'remove'
		) => {
			if (!poi_identifier || !userId) {
				throw new Error('POI identifier and user authentication required');
			}

			const body: Record<string, unknown> = {
				poiType: poi_identifier.poi_type,
				interactionType,
				action,
			};

			// Add the correct POI ID field based on POI type
			if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
				body.poiId = poi_identifier.poi_id;
			} else if (
				poi_identifier.poi_type === 'user_temp' &&
				poi_identifier.user_poi_temp_id
			) {
				body.userPoiTempId = poi_identifier.user_poi_temp_id;
			} else if (
				poi_identifier.poi_type === 'user_approved' &&
				poi_identifier.user_poi_approved_id
			) {
				body.userPoiApprovedId = poi_identifier.user_poi_approved_id;
			}

			const response = await fetch('/api/pois/interactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(body),
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const result = await response.json();

			if (!result.success) {
				throw new Error(result.error || 'Failed to update interaction');
			}

			return result;
		},
		[poi_identifier, userId]
	);

	// Cache invalidation helper for actions
	const invalidateCache = useCallback(() => {
		const key = cacheKey();
		if (key && enable_caching) {
			shared_cache.delete(key);
			console.log(`🔄 Cache invalidated after action for POI: ${key}`);
		}
	}, [cacheKey, enable_caching, shared_cache]);

	// Toggle like action
	const toggleLike = useCallback(async () => {
		if (!ready || actionLoading.like) return;

		const currentlyLiked = data.user_has_liked;
		const action = currentlyLiked ? 'remove' : 'add';

		setActionLoading((prev) => ({ ...prev, like: true }));
		setError(null);

		// Optimistic update
		if (enable_optimistic_updates) {
			setData((prev) => ({
				...prev,
				user_has_liked: !currentlyLiked,
				like_count: currentlyLiked
					? Math.max(0, prev.like_count - 1)
					: prev.like_count + 1,
			}));
		}

		try {
			await makeInteractionCall('like', action);

			// Invalidate cache after successful action
			invalidateCache();

			// If not using optimistic updates, refresh data
			if (!enable_optimistic_updates) {
				await loadData();
			}
		} catch (err) {
			// Rollback optimistic update on error
			if (enable_optimistic_updates) {
				setData((prev) => ({
					...prev,
					user_has_liked: currentlyLiked,
					like_count: currentlyLiked
						? prev.like_count + 1
						: Math.max(0, prev.like_count - 1),
				}));
			}

			const errorMessage =
				err instanceof Error ? err.message : 'Failed to toggle like';
			setError(errorMessage);
			console.error('Error toggling like:', err);
		} finally {
			setActionLoading((prev) => ({ ...prev, like: false }));
		}
	}, [
		ready,
		actionLoading.like,
		data.user_has_liked,
		enable_optimistic_updates,
		makeInteractionCall,
		loadData,
		invalidateCache,
	]);

	// Toggle favorite action
	const toggleFavorite = useCallback(async () => {
		if (!ready || actionLoading.favorite) return;

		const currentlyFavorited = data.user_has_favorited;
		const action = currentlyFavorited ? 'remove' : 'add';

		setActionLoading((prev) => ({ ...prev, favorite: true }));
		setError(null);

		// Optimistic update
		if (enable_optimistic_updates) {
			setData((prev) => ({
				...prev,
				user_has_favorited: !currentlyFavorited,
				favorite_count: currentlyFavorited
					? Math.max(0, prev.favorite_count - 1)
					: prev.favorite_count + 1,
			}));
		}

		try {
			await makeInteractionCall('favorite', action);

			// Invalidate cache after successful action
			invalidateCache();

			// If not using optimistic updates, refresh data
			if (!enable_optimistic_updates) {
				await loadData();
			}
		} catch (err) {
			// Rollback optimistic update on error
			if (enable_optimistic_updates) {
				setData((prev) => ({
					...prev,
					user_has_favorited: currentlyFavorited,
					favorite_count: currentlyFavorited
						? prev.favorite_count + 1
						: Math.max(0, prev.favorite_count - 1),
				}));
			}

			const errorMessage =
				err instanceof Error ? err.message : 'Failed to toggle favorite';
			setError(errorMessage);
			console.error('Error toggling favorite:', err);
		} finally {
			setActionLoading((prev) => ({ ...prev, favorite: false }));
		}
	}, [
		ready,
		actionLoading.favorite,
		data.user_has_favorited,
		enable_optimistic_updates,
		makeInteractionCall,
		loadData,
		invalidateCache,
	]);

	// Toggle visit action
	const toggleVisit = useCallback(async () => {
		if (!ready || actionLoading.visit) return;

		const currentlyVisited = data.user_has_visited;
		const action = currentlyVisited ? 'remove' : 'add';

		setActionLoading((prev) => ({ ...prev, visit: true }));
		setError(null);

		// Optimistic update
		if (enable_optimistic_updates) {
			setData((prev) => ({
				...prev,
				user_has_visited: !currentlyVisited,
				visit_count: currentlyVisited
					? Math.max(0, prev.visit_count - 1)
					: prev.visit_count + 1,
			}));
		}

		try {
			await makeInteractionCall('visit', action);

			// Invalidate cache after successful action
			invalidateCache();

			// If not using optimistic updates, refresh data
			if (!enable_optimistic_updates) {
				await loadData();
			}
		} catch (err) {
			// Rollback optimistic update on error
			if (enable_optimistic_updates) {
				setData((prev) => ({
					...prev,
					user_has_visited: currentlyVisited,
					visit_count: currentlyVisited
						? prev.visit_count + 1
						: Math.max(0, prev.visit_count - 1),
				}));
			}

			const errorMessage =
				err instanceof Error ? err.message : 'Failed to toggle visit';
			setError(errorMessage);
			console.error('Error toggling visit:', err);
		} finally {
			setActionLoading((prev) => ({ ...prev, visit: false }));
		}
	}, [
		ready,
		actionLoading.visit,
		data.user_has_visited,
		enable_optimistic_updates,
		makeInteractionCall,
		loadData,
		invalidateCache,
	]);

	// Refresh data with cache invalidation
	const refresh = useCallback(async () => {
		const key = cacheKey();
		if (key && enable_caching) {
			shared_cache.delete(key);
			console.log(`🗑️ Invalidated cache for POI: ${key}`);
		}
		await loadData(true); // Force refresh
	}, [loadData, cacheKey, enable_caching, shared_cache]);

	return {
		data,
		loading,
		actionLoading,
		error,
		ready,
		toggleLike,
		toggleFavorite,
		toggleVisit,
		refresh,
	};
};
