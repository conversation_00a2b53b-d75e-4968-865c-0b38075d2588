/** @format */

// Export all user interactions functionality

// === SHARED BASE ===
export * from '@/app/shared/userInteractions/shared';

// === INTERACTION MODULES (for profile system) ===
export * from '@/app/shared/userInteractions/favorites'; // Includes unified saves/favorites (services/types only)
export * from '@/app/shared/userInteractions/reviews';
export * from '@/app/shared/userInteractions/visits'; // Services/types only

// === UNIFIED COMPONENTS ===
export * from '@/app/shared/userInteractions/components';

// === UNIFIED HOOKS ===
export * from '@/app/shared/userInteractions/hooks';
