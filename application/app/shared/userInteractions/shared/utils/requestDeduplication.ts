/**
 * Request deduplication utility to prevent duplicate API calls
 * This helps improve performance by caching ongoing requests and preventing
 * multiple simultaneous requests for the same data
 *
 * @format
 */

export interface CachedRequest<T = unknown> {
	promise: Promise<T>;
	timestamp: number;
	key: string;
}

export class RequestDeduplicator {
	private static instance: RequestDeduplicator;
	private cache = new Map<string, CachedRequest>();
	private readonly defaultTimeout = 5000; // 5 seconds
	private cleanupInterval: NodeJS.Timeout | null = null;

	private constructor() {
		// Start cleanup interval
		this.startCleanup();
	}

	public static getInstance(): RequestDeduplicator {
		if (!RequestDeduplicator.instance) {
			RequestDeduplicator.instance = new RequestDeduplicator();
		}
		return RequestDeduplicator.instance;
	}

	/**
	 * Execute a request with deduplication
	 * If the same request is already in progress, return the existing promise
	 */
	public async execute<T>(
		key: string,
		requestFn: () => Promise<T>,
		timeout: number = this.defaultTimeout
	): Promise<T> {
		// Check if request is already in progress
		const existing = this.cache.get(key);
		if (existing && this.isValid(existing, timeout)) {
			return existing.promise as Promise<T>;
		}

		// Create new request
		const promise = requestFn();
		const cachedRequest: CachedRequest<T> = {
			promise,
			timestamp: Date.now(),
			key,
		};

		// Store in cache
		this.cache.set(key, cachedRequest);

		try {
			const result = await promise;
			return result;
		} catch (error) {
			// Remove failed request from cache immediately
			this.cache.delete(key);
			throw error;
		}
	}

	/**
	 * Check if a cached request is still valid
	 */
	private isValid(cachedRequest: CachedRequest, timeout: number): boolean {
		return Date.now() - cachedRequest.timestamp < timeout;
	}

	/**
	 * Clear a specific request from cache
	 */
	public clear(key: string): void {
		this.cache.delete(key);
	}

	/**
	 * Clear all requests matching a pattern
	 */
	public clearPattern(pattern: string): void {
		for (const key of this.cache.keys()) {
			if (key.includes(pattern)) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * Clear all cached requests
	 */
	public clearAll(): void {
		this.cache.clear();
	}

	/**
	 * Get cache statistics
	 */
	public getStats(): { size: number; keys: string[] } {
		return {
			size: this.cache.size,
			keys: Array.from(this.cache.keys()),
		};
	}

	/**
	 * Start automatic cleanup of expired requests
	 */
	private startCleanup(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
		}

		this.cleanupInterval = setInterval(() => {
			this.cleanupExpired();
		}, 10000); // Cleanup every 10 seconds
	}

	/**
	 * Clean up expired requests
	 */
	private cleanupExpired(): void {
		for (const [key, cachedRequest] of this.cache.entries()) {
			if (!this.isValid(cachedRequest, this.defaultTimeout)) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * Stop cleanup interval (for cleanup)
	 */
	public destroy(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
			this.cleanupInterval = null;
		}
		this.cache.clear();
	}
}

// Singleton instance
export const requestDeduplicator = RequestDeduplicator.getInstance();

/**
 * Helper function to create consistent cache keys for user interactions
 */
export const createUserInteractionKey = (
	userId: string,
	interactionType: string,
	offset: number = 0,
	limit: number = 20,
	additionalParams?: Record<string, unknown>
): string => {
	const baseKey = `user-interactions-${userId}-${interactionType}-${offset}-${limit}`;

	if (additionalParams) {
		const paramString = Object.entries(additionalParams)
			.sort(([a], [b]) => a.localeCompare(b)) // Sort for consistency
			.map(([key, value]) => `${key}:${value}`)
			.join('-');
		return `${baseKey}-${paramString}`;
	}

	return baseKey;
};

/**
 * Helper function to create cache keys for POI interactions
 */
export const createPOIInteractionKey = (
	poiId: string,
	poiType: string,
	interactionType: string,
	userId?: string,
	additionalParams?: Record<string, unknown>
): string => {
	const baseKey = `poi-interactions-${poiType}-${poiId}-${interactionType}`;
	const userPart = userId ? `-user-${userId}` : '';

	if (additionalParams) {
		const paramString = Object.entries(additionalParams)
			.sort(([a], [b]) => a.localeCompare(b))
			.map(([key, value]) => `${key}:${value}`)
			.join('-');
		return `${baseKey}${userPart}-${paramString}`;
	}

	return `${baseKey}${userPart}`;
};

/**
 * Decorator function for automatic request deduplication
 */
export function withDeduplication<T extends unknown[], R>(
	keyGenerator: (...args: T) => string,
	timeout?: number
) {
	return function (
		target: unknown,
		propertyName: string,
		descriptor: PropertyDescriptor
	) {
		const method = descriptor.value;

		descriptor.value = async function (...args: T): Promise<R> {
			const key = keyGenerator(...args);
			return requestDeduplicator.execute(
				key,
				() => method.apply(this, args),
				timeout
			);
		};

		return descriptor;
	};
}

/**
 * React hook for request deduplication
 */
export const useRequestDeduplication = () => {
	return {
		execute: requestDeduplicator.execute.bind(requestDeduplicator),
		clear: requestDeduplicator.clear.bind(requestDeduplicator),
		clearPattern: requestDeduplicator.clearPattern.bind(requestDeduplicator),
		clearAll: requestDeduplicator.clearAll.bind(requestDeduplicator),
		getStats: requestDeduplicator.getStats.bind(requestDeduplicator),
	};
};
