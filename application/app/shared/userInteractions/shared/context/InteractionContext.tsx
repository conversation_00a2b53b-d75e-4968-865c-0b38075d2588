'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  InteractionContextState,
  InteractionContextValue,
  InteractionCounts,
  UserInteractionStates,
  POIIdentifier,
  POIInteraction,
  InteractionCacheEntry,
  createPOIKey,
  getDefaultCounts,
  getDefaultStates
} from '@/app/shared/userInteractions/shared/types'
import { BaseInteractionService } from '@/app/shared/userInteractions/shared/services'

// Action types for the reducer
type InteractionAction =
  | { type: 'SET_GLOBAL_LOADING'; payload: boolean }
  | { type: 'UPDATE_CACHE'; payload: { poiKey: string; data: Partial<InteractionCacheEntry> } }
  | { type: 'CLEAR_CACHE'; payload?: string }
  | { type: 'SET_USER_INTERACTIONS'; payload: { userId: string; interactions: POIInteraction[] } }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<Pick<InteractionContextState, 'cacheTimeout' | 'enableOptimisticUpdates'>> }

// Initial state
const initialState: InteractionContextState = {
  interactionCache: new Map(),
  globalLoading: false,
  userInteractions: new Map(),
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  enableOptimisticUpdates: true
}

// Reducer function
function interactionReducer(state: InteractionContextState, action: InteractionAction): InteractionContextState {
  switch (action.type) {
    case 'SET_GLOBAL_LOADING':
      return { ...state, globalLoading: action.payload }

    case 'UPDATE_CACHE': {
      const newCache = new Map(state.interactionCache)
      const existing = newCache.get(action.payload.poiKey) || {
        counts: getDefaultCounts(),
        states: getDefaultStates(),
        lastUpdated: Date.now()
      }
      
      newCache.set(action.payload.poiKey, {
        ...existing,
        ...action.payload.data,
        lastUpdated: Date.now()
      })
      
      return { ...state, interactionCache: newCache }
    }

    case 'CLEAR_CACHE': {
      if (action.payload) {
        const newCache = new Map(state.interactionCache)
        newCache.delete(action.payload)
        return { ...state, interactionCache: newCache }
      } else {
        return { ...state, interactionCache: new Map() }
      }
    }

    case 'SET_USER_INTERACTIONS': {
      const newUserInteractions = new Map(state.userInteractions)
      newUserInteractions.set(action.payload.userId, action.payload.interactions)
      return { ...state, userInteractions: newUserInteractions }
    }

    case 'UPDATE_SETTINGS':
      return { ...state, ...action.payload }

    default:
      return state
  }
}

// Create context
const InteractionContext = createContext<InteractionContextValue | null>(null)

// Provider component
interface InteractionProviderProps {
  children: React.ReactNode
}

export const InteractionProvider: React.FC<InteractionProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(interactionReducer, initialState)
  const { data: session } = useSession()

  // Actions
  const updateInteractionCache = useCallback((poiKey: string, data: Partial<InteractionCacheEntry>) => {
    dispatch({ type: 'UPDATE_CACHE', payload: { poiKey, data } })
  }, [])

  const clearInteractionCache = useCallback((poiKey?: string) => {
    dispatch({ type: 'CLEAR_CACHE', payload: poiKey })
  }, [])

  const loadUserInteractions = useCallback(async (userId: string) => {
    dispatch({ type: 'SET_GLOBAL_LOADING', payload: true })

    try {
      // Load user's interactions across all POIs using the correct endpoint
      const response = await BaseInteractionService.get<{ success: boolean; interactions: POIInteraction[] }>('/interactions', { userId })
      
      if (response.success) {
        dispatch({ type: 'SET_USER_INTERACTIONS', payload: { userId, interactions: response.interactions } })
        
        // Update cache with user's interaction states
        const interactionsByPOI = new Map<string, POIInteraction[]>()
        response.interactions.forEach(interaction => {
          const poiKey = createPOIKey({
            poi_id: interaction.poi_id,
            user_poi_temp_id: interaction.user_poi_temp_id,
            user_poi_approved_id: interaction.user_poi_approved_id,
            poi_type: interaction.poi_type
          })
          
          if (!interactionsByPOI.has(poiKey)) {
            interactionsByPOI.set(poiKey, [])
          }
          interactionsByPOI.get(poiKey)!.push(interaction)
        })

        // Update cache for each POI
        interactionsByPOI.forEach((interactions, poiKey) => {
          const states: UserInteractionStates = {
            isLiked: interactions.some(i => i.interaction_type === 'like'),
            hasVisited: interactions.some(i => i.interaction_type === 'visit'),
            isFavorited: interactions.some(i => i.interaction_type === 'favorite'),
            hasReviewed: interactions.some(i => i.interaction_type === 'review'),
            hasShared: interactions.some(i => i.interaction_type === 'share')
          }

          updateInteractionCache(poiKey, { states })
        })
      }
    } catch (error) {
      console.error('Error loading user interactions:', error)
    } finally {
      dispatch({ type: 'SET_GLOBAL_LOADING', payload: false })
    }
  }, [updateInteractionCache])

  const updateSettings = useCallback((settings: Partial<Pick<InteractionContextState, 'cacheTimeout' | 'enableOptimisticUpdates'>>) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
  }, [])

  // Auto-load user interactions when user logs in
  useEffect(() => {
    if (session?.user?.id) {
      loadUserInteractions(session.user.id)
    }
  }, [session?.user?.id, loadUserInteractions])

  // Cache cleanup - remove expired entries
  useEffect(() => {
    const cleanup = () => {
      const now = Date.now()
      const newCache = new Map(state.interactionCache)
      let hasChanges = false

      for (const [key, value] of newCache.entries()) {
        if (now - value.lastUpdated > state.cacheTimeout) {
          newCache.delete(key)
          hasChanges = true
        }
      }

      if (hasChanges) {
        dispatch({ type: 'CLEAR_CACHE' })
        newCache.forEach((value, key) => {
          dispatch({ type: 'UPDATE_CACHE', payload: { poiKey: key, data: value } })
        })
      }
    }

    const interval = setInterval(cleanup, 60000) // Run cleanup every minute
    return () => clearInterval(interval)
  }, [state.interactionCache, state.cacheTimeout])

  const contextValue: InteractionContextValue = {
    ...state,
    updateInteractionCache,
    clearInteractionCache,
    loadUserInteractions,
    updateSettings
  }

  return (
    <InteractionContext.Provider value={contextValue}>
      {children}
    </InteractionContext.Provider>
  )
}

// Hook to use the interaction context
export const useInteractionContext = (): InteractionContextValue => {
  const context = useContext(InteractionContext)
  if (!context) {
    throw new Error('useInteractionContext must be used within an InteractionProvider')
  }
  return context
}

// Hook to get cached interaction data for a POI
export const useCachedInteractionData = (poi: POIIdentifier) => {
  const { interactionCache } = useInteractionContext()
  const poiKey = createPOIKey(poi)
  
  return interactionCache.get(poiKey) || {
    counts: getDefaultCounts(),
    states: getDefaultStates(),
    lastUpdated: 0
  }
}

// Hook to update interaction data in cache
export const useUpdateInteractionCache = () => {
  const { updateInteractionCache } = useInteractionContext()
  
  return useCallback((poi: POIIdentifier, data: Partial<{ counts: InteractionCounts; states: UserInteractionStates }>) => {
    const poiKey = createPOIKey(poi)
    updateInteractionCache(poiKey, data)
  }, [updateInteractionCache])
}
