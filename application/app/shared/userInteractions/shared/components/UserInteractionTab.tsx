/** @format */

'use client';

import { colors } from '@/app/colors';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import {
	FaCalendarAlt,
	FaEdit,
	FaExternalLinkAlt,
	FaHeart,
	FaMapMarkerAlt,
	FaSpinner,
	FaStar,
	FaTrash,
} from 'react-icons/fa';
import { useInteractionType } from '../context/UserInteractionsProvider';
import {
	InteractionItem,
	InteractionType,
} from '../hooks/useSharedUserInteractions';

interface UserInteractionTabProps {
	interactionType: InteractionType;
}

interface InteractionConfig {
	icon: React.ReactNode;
	emptyTitle: string;
	emptyMessage: string;
	loadingMessage: string;
	itemTitle: (item: InteractionItem) => string;
	itemSubtitle: (item: InteractionItem) => string;
	itemDetails?: (item: InteractionItem) => string;
	color: string;
	backgroundColor: string;
}

const getInteractionConfig = (type: InteractionType): InteractionConfig => {
	switch (type) {
		case 'favorites':
			return {
				icon: <FaStar className='w-4 h-4' />,
				emptyTitle: 'No Favorites Yet',
				emptyMessage:
					'Start exploring and favorite places you want to remember!',
				loadingMessage: 'Loading your favorites...',
				itemTitle: (item) => item.poi_name || 'Unnamed Location',
				itemSubtitle: (item) =>
					`ID: ${
						item.poi_id || item.user_poi_temp_id || item.user_poi_approved_id
					}`,
				itemDetails: (item) => `Favorited on ${formatDate(item.created_at)}`,
				color: colors.brand.blue,
				backgroundColor: colors.ui.blue100,
			};
		case 'likes':
			return {
				icon: <FaHeart className='w-4 h-4' />,
				emptyTitle: 'No Likes Yet',
				emptyMessage: 'Start exploring and like places you enjoy!',
				loadingMessage: 'Loading your likes...',
				itemTitle: (item) => item.poi_name || 'Unnamed Location',
				itemSubtitle: (item) =>
					`ID: ${
						item.poi_id || item.user_poi_temp_id || item.user_poi_approved_id
					}`,
				itemDetails: (item) => `Liked on ${formatDate(item.created_at)}`,
				color: colors.utility.error,
				backgroundColor: colors.utility.errorLight,
			};
		case 'visits':
			return {
				icon: <FaMapMarkerAlt className='w-4 h-4' />,
				emptyTitle: 'No Visits Yet',
				emptyMessage: "Start exploring and mark places you've visited!",
				loadingMessage: 'Loading your visits...',
				itemTitle: (item) => item.poi_name || 'Unnamed Location',
				itemSubtitle: (item) =>
					`ID: ${
						item.poi_id || item.user_poi_temp_id || item.user_poi_approved_id
					}`,
				itemDetails: (item) =>
					`Visited on ${formatDate(item.visit_date || item.created_at)}`,
				color: colors.brand.green,
				backgroundColor: colors.ui.green100,
			};
		case 'reviews':
			return {
				icon: <FaEdit className='w-4 h-4' />,
				emptyTitle: 'No Reviews Yet',
				emptyMessage: 'Start exploring and share your experiences!',
				loadingMessage: 'Loading your reviews...',
				itemTitle: (item) => item.poi_name || 'Unnamed Location',
				itemSubtitle: (item) =>
					`Rating: ${
						item.rating
							? '★'.repeat(item.rating) + '☆'.repeat(5 - item.rating)
							: 'No rating'
					}`,
				itemDetails: (item) => `Reviewed on ${formatDate(item.created_at)}`,
				color: colors.brand.navy,
				backgroundColor: colors.ui.navy100,
			};
		default:
			throw new Error(`Unknown interaction type: ${type}`);
	}
};

const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	});
};

export const UserInteractionTab: React.FC<UserInteractionTabProps> = ({
	interactionType,
}) => {
	const router = useRouter();
	const [removingId, setRemovingId] = useState<string | null>(null);

	// Use the context-based hook for this interaction type
	const {
		items: interactionItems,
		loading,
		error,
		totalCount,
		hasMore,
		refresh,
		removeInteraction: removeInteractionFromContext,
	} = useInteractionType(interactionType);

	const config = getInteractionConfig(interactionType);

	// Create a mock state object for compatibility
	const interactionState = {
		items: interactionItems,
		loading,
		error,
		hasMore,
		totalCount,
		currentOffset: 0,
	};

	// Remove an interaction
	const handleRemove = async (item: InteractionItem) => {
		setRemovingId(item.id);

		try {
			await removeInteractionFromContext(item.id);
		} catch (err) {
			console.error(`Error removing ${interactionType}:`, err);
		} finally {
			setRemovingId(null);
		}
	};

	// Navigate to POI
	const navigateToPOI = (item: InteractionItem) => {
		const poiId =
			item.poi_id || item.user_poi_temp_id || item.user_poi_approved_id;
		router.push(`/pois/${item.poi_type}/${poiId}`);
	};

	// Handle refresh
	const handleRefresh = () => {
		refresh();
	};

	if (interactionState.loading) {
		return (
			<div className='flex items-center justify-center py-12'>
				<FaSpinner
					className='w-6 h-6 animate-spin mr-3'
					style={{ color: config.color }}
				/>
				<span style={{ color: colors.neutral.slateGray }}>
					{config.loadingMessage}
				</span>
			</div>
		);
	}

	if (interactionState.error) {
		return (
			<div
				className='p-4 rounded-lg'
				style={{ backgroundColor: colors.utility.errorLight }}>
				<p style={{ color: colors.utility.error }}>{interactionState.error}</p>
				<button
					onClick={handleRefresh}
					className='mt-2 px-4 py-2 rounded-lg text-sm font-medium'
					style={{
						backgroundColor: colors.utility.error,
						color: 'white',
					}}>
					Try Again
				</button>
			</div>
		);
	}

	if (interactionState.items.length === 0) {
		return (
			<div className='text-center py-12'>
				<div
					className='w-12 h-12 mx-auto mb-4 opacity-30 flex items-center justify-center'
					style={{ color: config.color }}>
					{config.icon}
				</div>
				<h3
					className='text-lg font-medium mb-2'
					style={{ color: colors.neutral.textBlack }}>
					{config.emptyTitle}
				</h3>
				<p
					className='text-sm'
					style={{ color: colors.neutral.slateGray }}>
					{config.emptyMessage}
				</p>
			</div>
		);
	}

	return (
		<div className='space-y-4'>
			<div className='flex items-center justify-between mb-4'>
				<h3
					className='text-lg font-semibold'
					style={{ color: colors.neutral.textBlack }}>
					Your{' '}
					{interactionType.charAt(0).toUpperCase() + interactionType.slice(1)} (
					{interactionState.totalCount})
				</h3>
				<button
					onClick={handleRefresh}
					className='text-sm px-3 py-1 rounded-lg transition-colors'
					style={{
						color: config.color,
						backgroundColor: colors.ui.blue50,
					}}>
					Refresh
				</button>
			</div>

			<div className='grid gap-3'>
				{interactionState.items.map((item) => (
					<div
						key={item.id}
						className='flex items-center justify-between p-4 rounded-lg border transition-all duration-200 hover:shadow-md'
						style={{
							backgroundColor: colors.neutral.cloudWhite,
							borderColor: colors.ui.gray200,
						}}>
						<div className='flex items-center gap-3 flex-1'>
							<div
								className='w-10 h-10 rounded-full flex items-center justify-center'
								style={{ backgroundColor: config.backgroundColor }}>
								<div style={{ color: config.color }}>{config.icon}</div>
							</div>

							<div className='flex-1'>
								<h4
									className='font-medium'
									style={{ color: colors.neutral.textBlack }}>
									{config.itemTitle(item)}
								</h4>
								<p
									className='text-sm flex items-center gap-1 mt-1'
									style={{ color: colors.neutral.slateGray }}>
									<FaMapMarkerAlt className='w-3 h-3' />
									{config.itemSubtitle(item)}
								</p>
								{config.itemDetails && (
									<p
										className='text-xs flex items-center gap-1 mt-1'
										style={{ color: colors.neutral.slateGray }}>
										<FaCalendarAlt className='w-3 h-3' />
										{config.itemDetails(item)}
									</p>
								)}
							</div>
						</div>

						<div className='flex items-center gap-2'>
							<button
								onClick={() => navigateToPOI(item)}
								className='p-2 rounded-lg transition-colors'
								style={{
									color: config.color,
									backgroundColor: colors.ui.blue50,
								}}
								title='View POI'>
								<FaExternalLinkAlt className='w-4 h-4' />
							</button>

							<button
								onClick={() => handleRemove(item)}
								disabled={removingId === item.id}
								className='p-2 rounded-lg transition-colors disabled:opacity-50'
								style={{
									color: colors.utility.error,
									backgroundColor: colors.utility.errorLight,
								}}
								title={`Remove ${interactionType.slice(0, -1)}`}>
								{removingId === item.id ? (
									<FaSpinner className='w-4 h-4 animate-spin' />
								) : (
									<FaTrash className='w-4 h-4' />
								)}
							</button>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};
