/** @format */

// Base types shared across all interactions

export type POIType = 'official' | 'user_temp' | 'user_approved';

export type InteractionType =
	| 'like'
	| 'visit'
	| 'share'
	| 'favorite'
	| 'review';

export type InteractionAction = 'add' | 'remove' | 'update';

// Base POI identifier interface
// Note: All spatial IDs are BIGINT in DB but handled as numbers in JS (safe up to 9+ quadrillion)
export interface POIIdentifier {
	poi_id?: number | null; // BIGINT from spatial_schema.pois.id
	user_poi_temp_id?: number | null; // BIGINT from spatial_schema.user_pois_temp.id
	user_poi_approved_id?: number | null; // BIGINT from spatial_schema.user_pois_approved.id
	poi_type: POIType;
}

// Base interaction interface
export interface BaseInteraction {
	id?: string | number;
	user_id: string;
	interaction_type: InteractionType;
	created_at: string;
	updated_at?: string;
	metadata?: Record<string, unknown>;
}

// Combined POI and interaction interface
export interface POIInteraction extends BaseInteraction, POIIdentifier {}

// Interaction counts interface
export interface InteractionCounts {
	like_count: number;
	visit_count: number;
	favorite_count: number;
	share_count: number;
	review_count: number;
}

// User interaction states (for current user)
export interface UserInteractionStates {
	isLiked: boolean;
	hasVisited: boolean;
	isFavorited: boolean;
	hasReviewed: boolean;
	hasShared: boolean;
}

// API response interfaces
export interface BaseInteractionResponse {
	success: boolean;
	message?: string;
	error?: string;
	already_exists?: boolean;
}

export interface InteractionResponse extends BaseInteractionResponse {
	interaction?: POIInteraction;
	count?: number;
	already_exists?: boolean;
}

export interface InteractionListResponse extends BaseInteractionResponse {
	interactions: POIInteraction[];
	total_count?: number;
	has_more?: boolean;
}

// Bulk operations
export interface BulkInteractionRequest {
	interactions: Array<{
		poi_identifier: POIIdentifier;
		interaction_type: InteractionType;
		action: InteractionAction;
		metadata?: Record<string, unknown>;
	}>;
}

export interface BulkInteractionResponse extends BaseInteractionResponse {
	results: Array<{
		poi_identifier: POIIdentifier;
		interaction_type: InteractionType;
		success: boolean;
		error?: string;
		count?: number;
	}>;
}

// Hook interfaces
export interface BaseInteractionOptions {
	poi_identifier?: POIIdentifier;
	user_id?: string;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
	cache_duration?: number; // in milliseconds
}

// Context interfaces
export interface InteractionCacheEntry {
	counts: InteractionCounts;
	states: UserInteractionStates;
	lastUpdated: number;
}

export interface InteractionContextState {
	// Global interaction cache
	interactionCache: Map<string, InteractionCacheEntry>;

	// Global loading states
	globalLoading: boolean;

	// User's all interactions (for quick lookups)
	userInteractions: Map<string, POIInteraction[]>;

	// Settings
	cacheTimeout: number;
	enableOptimisticUpdates: boolean;
}

export interface InteractionContextActions {
	// Cache management
	updateInteractionCache: (
		poiKey: string,
		data: Partial<InteractionCacheEntry>
	) => void;
	clearInteractionCache: (poiKey?: string) => void;

	// Bulk operations
	loadUserInteractions: (userId: string) => Promise<void>;

	// Settings
	updateSettings: (
		settings: Partial<
			Pick<InteractionContextState, 'cacheTimeout' | 'enableOptimisticUpdates'>
		>
	) => void;
}

export interface InteractionContextValue
	extends InteractionContextState,
		InteractionContextActions {}

// Utility functions
export const createPOIKey = (poi: POIIdentifier): string => {
	if (poi.poi_type === 'official' && poi.poi_id) {
		return `official:${poi.poi_id}`;
	} else if (poi.poi_type === 'user_temp' && poi.user_poi_temp_id) {
		return `user_temp:${poi.user_poi_temp_id}`;
	} else if (poi.poi_type === 'user_approved' && poi.user_poi_approved_id) {
		return `user_approved:${poi.user_poi_approved_id}`;
	}
	throw new Error('Invalid POI identifier');
};

export const getDefaultCounts = (): InteractionCounts => ({
	like_count: 0,
	visit_count: 0,
	favorite_count: 0,
	share_count: 0,
	review_count: 0,
});

export const getDefaultStates = (): UserInteractionStates => ({
	isLiked: false,
	hasVisited: false,
	isFavorited: false,
	hasReviewed: false,
	hasShared: false,
});

export const getDefaultLoading = (): Record<InteractionType, boolean> => ({
	like: false,
	visit: false,
	favorite: false,
	share: false,
	review: false,
});
