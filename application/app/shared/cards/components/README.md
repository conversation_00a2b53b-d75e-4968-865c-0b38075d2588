# Shared POI Cards System

This directory contains reusable POI (Point of Interest) card components that provide consistent styling, behavior, and functionality across the entire application.

## 🎯 Purpose

The POI cards system was created to:
- **Eliminate code duplication** across different pages
- **Ensure consistent UI/UX** for all POI-related cards
- **Provide shared utilities** for common POI operations
- **Enable easy customization** through configuration props

## 📦 Components

### BaseCard
The foundation component that provides:
- Consistent styling and animations
- Click-outside-to-close functionality
- Escape key handling
- Flexible positioning (floating, modal, inline)
- Configurable animations (fade, slide, scale)

### LocationHoverCard
Used for hovering over location mentions in chat messages:
- Shows location name, coordinates, and distance
- Provides quick actions (Open Maps, Copy Coordinates, View Details)
- Handles hover state management
- Optimized for mouse interaction

### POICard
Used for displaying detailed POI information:
- Shows comprehensive POI details (name, category, rating, etc.)
- Supports multiple variants (modal, inline, hover)
- Configurable action buttons
- Favorite toggle functionality

## 🔧 Usage Examples

### LocationHoverCard (Chat Page)
```tsx
import { LocationHoverCard } from '@/app/shared/components/poi-cards'

<LocationHoverCard
  location={{
    id: "loc-123",
    name: "Central Park",
    lat: 40.7829,
    lng: -73.9654,
    walk_route_distance_m: 500
  }}
  isVisible={showCard}
  position={{ x: 100, y: 200 }}
  onClose={() => setShowCard(false)}
  onCardHoverChange={(hovering) => setIsHovering(hovering)}
/>
```

### POICard (Flat Map)
```tsx
import { POICard } from '@/app/shared/components/poi-cards'

<POICard
  poi={{
    id: 123,
    name: "Amazing Restaurant",
    category: "restaurant",
    latitude: 40.7829,
    longitude: -73.9654,
    user_rating_avg: 4.5,
    user_rating_count: 120
  }}
  isVisible={showPOI}
  onClose={() => setShowPOI(false)}
  onNavigate={(poi) => router.push(`/pois/${poi.id}`)}
  variant="modal"
/>
```

### Inline POI Card (POI Listing)
```tsx
import { POICard } from '@/app/shared/components/poi-cards'

<POICard
  poi={poi}
  isVisible={true}
  onClose={() => {}}
  onNavigate={handleNavigate}
  variant="inline"
  showActions={true}
/>
```

## 🛠 Utilities

The system includes shared utilities for common operations:

```tsx
import { 
  formatDistance, 
  formatRating, 
  getCategoryIcon,
  generateMapsUrl,
  copyCoordinates,
  calculateCardPosition
} from '@/app/shared/components/poi-cards'

// Format distance for display
const distanceText = formatDistance(1.5) // "1.5km away"

// Get category icon
const icon = getCategoryIcon("restaurant") // "🍽️"

// Generate Google Maps URL
const mapsUrl = generateMapsUrl(40.7829, -73.9654)

// Calculate optimal card position
const position = calculateCardPosition(mouseX, mouseY, cardWidth, cardHeight)
```

## 🎨 Customization

### Card Configuration
```tsx
const config = {
  maxWidth: '400px',
  animation: 'slide', // 'fade', 'slide', 'scale'
  showDistance: true,
  showRating: true,
  showActions: true
}

<POICard config={config} ... />
```

### Custom Actions
```tsx
const customActions = [
  {
    label: 'Share',
    icon: FaShare,
    onClick: () => handleShare(),
    variant: 'secondary'
  },
  {
    label: 'Report',
    icon: FaFlag,
    onClick: () => handleReport(),
    variant: 'danger'
  }
]
```

## 🔄 Migration Guide

### From Chat LocationHoverCard
**Before:**
```tsx
import LocationHoverCard from 'app/chat/LocationHoverCard'
```

**After:**
```tsx
import { LocationHoverCard } from '@/app/shared/components/poi-cards'
```

### From Globe POICard
**Before:**
```tsx
import POICard from './POICard'
```

**After:**
```tsx
import { POICard } from '@/app/shared/components/poi-cards'
```

## 📱 Responsive Design

All cards are fully responsive and include:
- Mobile-optimized layouts
- Touch-friendly interactions
- Adaptive sizing based on screen size
- Proper z-index management

## 🎭 Animations

Three animation types are available:
- **fade**: Gentle fade-in with slight scale (default)
- **slide**: Slide up from bottom (good for modals)
- **scale**: Scale in from center (good for quick interactions)

## 🔍 Type Safety

All components are fully typed with TypeScript:
- `BasePOI` interface for POI data
- `LocationData` interface for location data
- `CardAction` interface for action buttons
- `CardConfig` interface for configuration options

## 🧪 Testing

The shared components can be tested consistently across all pages:
```tsx
// Test LocationHoverCard
const mockLocation = {
  id: "test-loc",
  name: "Test Location",
  lat: 40.7829,
  lng: -73.9654
}

// Test POICard
const mockPOI = {
  id: 123,
  name: "Test POI",
  category: "restaurant",
  latitude: 40.7829,
  longitude: -73.9654
}
```

## 🚀 Benefits

1. **Consistency**: All POI cards look and behave the same
2. **Maintainability**: Changes in one place affect all pages
3. **Performance**: Shared utilities reduce bundle size
4. **Developer Experience**: Easy to use and extend
5. **Accessibility**: Built-in keyboard and screen reader support
