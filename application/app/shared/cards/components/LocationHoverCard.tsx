/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useEffect, useRef } from 'react';
import BaseCard from './BaseCard';
import { CardAction, HoverCardProps } from './types';
import { copyCoordinates, formatDistance, generateMapsUrl } from './utils';

const LocationHoverCard: React.FC<HoverCardProps> = ({
	location,
	isVisible,
	position,
	onClose,
	onCardHoverChange,
}) => {
	const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			if (hoverTimeoutRef.current) {
				clearTimeout(hoverTimeoutRef.current);
			}
		};
	}, []);

	// Handle mouse events for hover state
	const handleMouseEnter = () => {
		if (hoverTimeoutRef.current) {
			clearTimeout(hoverTimeoutRef.current);
			hoverTimeoutRef.current = null;
		}
		onCardHoverChange?.(true);
	};

	const handleMouseLeave = () => {
		// Add small delay before notifying parent to allow for mouse movement tolerance
		if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current);
		hoverTimeoutRef.current = setTimeout(() => {
			onCardHoverChange?.(false);
		}, 100);
	};

	// Action handlers
	const handleOpenMaps = () => {
		const mapsUrl = generateMapsUrl(location.lat, location.lng);
		window.open(mapsUrl, '_blank');
		onClose();
	};

	const handleCopyCoordinates = async () => {
		try {
			await copyCoordinates(location.lat, location.lng);
			onClose();
		} catch (error) {
			console.error('Failed to copy coordinates:', error);
		}
	};

	const handleViewDetails = () => {
		window.open(`/pois/official/${location.id}`, '_blank');
		onClose();
	};

	// Define actions
	const actions: CardAction[] = [
		{
			label: '🗺️ Open Maps',
			onClick: handleOpenMaps,
			variant: 'primary',
		},
		{
			label: '📋 Copy Coords',
			onClick: handleCopyCoordinates,
			variant: 'secondary',
		},
		{
			label: 'View Details',
			onClick: handleViewDetails,
			variant: 'primary',
		},
	];

	return (
		<BaseCard
			isVisible={isVisible}
			onClose={onClose}
			position={position}
			config={{ maxWidth: '260px', animation: 'fade' }}>
			{/* Invisible bridge area to help mouse movement from location to card */}
			<div
				className='absolute z-[100] pointer-events-auto'
				style={{
					left: '-20px',
					top: '-20px',
					width: '60px',
					height: '60px',
					backgroundColor: 'transparent',
				}}
				onMouseEnter={handleMouseEnter}
			/>

			{/* Card Content */}
			<div
				className='p-4'
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}>
				{/* Header */}
				<div className='mb-3'>
					<h3 className='font-bold text-lg text-gray-900 leading-tight'>
						📍 {location.name}
					</h3>
					{location.address && (
						<p className='text-sm text-gray-600 mt-1'>{location.address}</p>
					)}
				</div>

				{/* Coordinates */}
				<div className='mb-3 text-sm text-gray-700'>
					<div className='font-mono bg-gray-50 px-2 py-1 rounded text-xs'>
						{location.lat.toFixed(4)}°, {location.lng.toFixed(4)}°
					</div>
				</div>

				{/* Distance */}
				{location.walk_route_distance_m && (
					<div className='mb-3'>
						<span
							className='inline-block px-2 py-1 rounded-full text-xs font-medium'
							style={{
								backgroundColor: colors.ui.blue100,
								color: colors.brand.blue,
							}}>
							{formatDistance(undefined, location.walk_route_distance_m)}
						</span>
					</div>
				)}

				{/* Confidence */}
				{location.confidence && (
					<div className='mb-3'>
						<div className='flex items-center gap-2'>
							<span className='text-xs text-gray-500'>Confidence:</span>
							<div className='flex-1 bg-gray-200 rounded-full h-1.5'>
								<div
									className='h-1.5 rounded-full transition-all duration-300'
									style={{
										width: `${location.confidence * 100}%`,
										backgroundColor:
											location.confidence > 0.8
												? colors.utility.success
												: location.confidence > 0.6
												? colors.utility.warning
												: colors.utility.error,
									}}
								/>
							</div>
							<span className='text-xs font-medium'>
								{Math.round(location.confidence * 100)}%
							</span>
						</div>
					</div>
				)}

				{/* Action Buttons */}
				<div className='flex flex-col sm:flex-row gap-2 pt-2'>
					{actions.map((action, index) => (
						<button
							key={index}
							onClick={action.onClick}
							disabled={action.disabled}
							className={`${
								index === 0 ? 'flex-1' : ''
							} px-3 py-2 rounded-md text-sm font-medium transition-colors shadow-sm border ${
								action.variant === 'primary'
									? 'border-transparent text-white'
									: 'border-gray-200'
							}`}
							style={{
								backgroundColor:
									action.variant === 'primary'
										? colors.brand.blue
										: colors.ui.gray200,
								color:
									action.variant === 'primary'
										? 'white'
										: colors.neutral.textBlack,
							}}
							onMouseEnter={(e) => {
								if (action.variant === 'primary') {
									e.currentTarget.style.backgroundColor =
										colors.supporting.lightBlue;
								} else {
									e.currentTarget.style.backgroundColor = colors.ui.gray300;
								}
							}}
							onMouseLeave={(e) => {
								if (action.variant === 'primary') {
									e.currentTarget.style.backgroundColor = colors.brand.blue;
								} else {
									e.currentTarget.style.backgroundColor = colors.ui.gray200;
								}
							}}>
							{action.label}
						</button>
					))}
				</div>
			</div>
		</BaseCard>
	);
};

export default LocationHoverCard;
