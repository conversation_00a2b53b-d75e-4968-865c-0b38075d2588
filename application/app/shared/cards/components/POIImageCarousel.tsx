/** @format */

'use client';

import { MediaItem } from '@/app/shared/media/types';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FaChevronLeft, FaChevronRight, FaImage } from 'react-icons/fa';

interface POIImage {
	id: string;
	media_url: string;
	thumbnail_url?: string;
	caption?: string;
}

interface POIImageCarouselProps {
	poiId: string | number;
	poiType: string;
	className?: string;
	height?: string;
	showDots?: boolean;
	autoPlay?: boolean;
	autoPlayInterval?: number;
	// Pre-loaded media data to avoid individual API calls
	preloadedMedia?: MediaItem[];
	// Flag to disable API loading when using preloaded data
	disableApiLoading?: boolean;
}

const POIImageCarousel: React.FC<POIImageCarouselProps> = ({
	poiId,
	poiType,
	className = '',
	height = 'h-48',
	showDots = true,
	autoPlay = false,
	autoPlayInterval = 5000,
	preloadedMedia,
	disableApiLoading = false,
}) => {
	const [images, setImages] = useState<POIImage[]>([]);
	const [currentIndex, setCurrentIndex] = useState(0);
	const [loading, setLoading] = useState(true);
	const [hasMore, setHasMore] = useState(false);
	const [loadingMore, setLoadingMore] = useState(false);
	const [page, setPage] = useState(1);
	const touchStartX = useRef<number | null>(null);
	const touchEndX = useRef<number | null>(null);
	const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

	const INITIAL_LOAD = 5;
	const LOAD_MORE_BATCH = 5;

	// Load POI images
	const loadImages = useCallback(
		async (pageToLoad: number = 1, append: boolean = false) => {
			if (!append) setLoading(true);
			else setLoadingMore(true);

			try {
				const params = new URLSearchParams({
					poiId: poiId.toString(),
					poiType,
					limit: (pageToLoad === 1 ? INITIAL_LOAD : LOAD_MORE_BATCH).toString(),
					offset: ((pageToLoad - 1) * LOAD_MORE_BATCH).toString(),
				});

				const response = await fetch(`/api/pois/media?${params.toString()}`);
				const data = await response.json();

				if (data.success && data.media) {
					const newImages = data.media
						.filter(
							(item: MediaItem) =>
								(item.media_type || item.mediaType) === 'photo' &&
								(item.media_url || item.mediaUrl)
						)
						.map(
							(item: MediaItem): POIImage => ({
								id: item.id,
								media_url: item.media_url || item.mediaUrl || '',
								thumbnail_url: item.thumbnail_url || item.thumbnailUrl,
								caption: item.caption,
							})
						);

					if (append) {
						setImages((prev) => [...prev, ...newImages]);
					} else {
						setImages(newImages);
					}

					setHasMore(
						newImages.length ===
							(pageToLoad === 1 ? INITIAL_LOAD : LOAD_MORE_BATCH)
					);
					setPage(pageToLoad);
				}
			} catch (error) {
				console.error('Failed to load POI images:', error);
			} finally {
				setLoading(false);
				setLoadingMore(false);
			}
		},
		[poiId, poiType]
	);

	// Load more images when user swipes near the end
	const loadMoreImages = useCallback(() => {
		if (!loadingMore && hasMore && currentIndex >= images.length - 2) {
			loadImages(page + 1, true);
		}
	}, [loadingMore, hasMore, currentIndex, images.length, page, loadImages]);

	// Handle preloaded media
	useEffect(() => {
		if (preloadedMedia !== undefined) {
			if (preloadedMedia.length > 0) {
				const preloadedImages = preloadedMedia
					.filter(
						(item: MediaItem) =>
							(item.media_type || item.mediaType) === 'photo' &&
							(item.media_url || item.mediaUrl)
					)
					.map(
						(item: MediaItem): POIImage => ({
							id: item.id,
							media_url: item.media_url || item.mediaUrl || '',
							thumbnail_url: item.thumbnail_url || item.thumbnailUrl,
							caption: item.caption,
						})
					);

				setImages(preloadedImages);
				setLoading(false);
				setHasMore(false); // Don't load more when using preloaded data
			} else {
				// Empty preloaded media - stop loading and show empty state
				setImages([]);
				setLoading(false);
				setHasMore(false);
			}
		}
	}, [preloadedMedia]);

	// Initial load (only if not using preloaded media)
	useEffect(() => {
		if (!disableApiLoading && preloadedMedia === undefined) {
			loadImages(1, false);
		}
	}, [loadImages, disableApiLoading, preloadedMedia]);

	// Auto-play functionality
	useEffect(() => {
		if (autoPlay && images.length > 1) {
			autoPlayRef.current = setInterval(() => {
				setCurrentIndex((prev) => (prev + 1) % images.length);
			}, autoPlayInterval);

			return () => {
				if (autoPlayRef.current) {
					clearInterval(autoPlayRef.current);
				}
			};
		}
	}, [autoPlay, autoPlayInterval, images.length]);

	// Navigation functions
	const goToPrevious = () => {
		setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
	};

	const goToNext = () => {
		setCurrentIndex((prev) => {
			const nextIndex = (prev + 1) % images.length;
			// Trigger load more when approaching end
			if (nextIndex >= images.length - 2) {
				loadMoreImages();
			}
			return nextIndex;
		});
	};

	const goToSlide = (index: number) => {
		setCurrentIndex(index);
		if (index >= images.length - 2) {
			loadMoreImages();
		}
	};

	// Touch handlers for swipe
	const handleTouchStart = (e: React.TouchEvent) => {
		touchStartX.current = e.targetTouches[0].clientX;
	};

	const handleTouchMove = (e: React.TouchEvent) => {
		touchEndX.current = e.targetTouches[0].clientX;
	};

	const handleTouchEnd = () => {
		if (!touchStartX.current || !touchEndX.current) return;

		const distance = touchStartX.current - touchEndX.current;
		const isLeftSwipe = distance > 50;
		const isRightSwipe = distance < -50;

		if (isLeftSwipe && images.length > 1) {
			goToNext();
		}
		if (isRightSwipe && images.length > 1) {
			goToPrevious();
		}

		touchStartX.current = null;
		touchEndX.current = null;
	};

	// Loading state
	if (loading) {
		return (
			<div
				className={`${height} bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl flex items-center justify-center ${className}`}>
				<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div>
			</div>
		);
	}

	// No images state
	if (images.length === 0) {
		return (
			<div
				className={`${height} bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl flex items-center justify-center ${className}`}>
				<div className='text-center'>
					<FaImage className='w-8 h-8 text-gray-400 mx-auto mb-2' />
					<p className='text-sm text-gray-500'>No images available</p>
				</div>
			</div>
		);
	}

	return (
		<div
			className={`relative ${height} rounded-t-xl overflow-hidden group ${className}`}
			onTouchStart={handleTouchStart}
			onTouchMove={handleTouchMove}
			onTouchEnd={handleTouchEnd}>
			{/* Main image */}
			<div className='relative w-full h-full'>
				<img
					src={
						images[currentIndex]?.thumbnail_url ||
						images[currentIndex]?.media_url
					}
					alt={images[currentIndex]?.caption || `POI image ${currentIndex + 1}`}
					className='w-full h-full object-cover'
					loading='lazy'
				/>

				{/* Loading overlay for more images */}
				{loadingMore && (
					<div className='absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center'>
						<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white'></div>
					</div>
				)}
			</div>

			{/* Navigation arrows */}
			{images.length > 1 && (
				<>
					<button
						onClick={goToPrevious}
						className='absolute left-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full bg-black bg-opacity-50 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-opacity-70'>
						<FaChevronLeft className='w-3 h-3' />
					</button>
					<button
						onClick={goToNext}
						className='absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full bg-black bg-opacity-50 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-opacity-70'>
						<FaChevronRight className='w-3 h-3' />
					</button>
				</>
			)}

			{/* Dots indicator */}
			{showDots && images.length > 1 && (
				<div className='absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1'>
					{images.slice(0, Math.min(images.length, 10)).map((_, index) => (
						<button
							key={index}
							onClick={() => goToSlide(index)}
							className={`w-2 h-2 rounded-full transition-colors duration-200 ${
								index === currentIndex
									? 'bg-white'
									: 'bg-white bg-opacity-50 hover:bg-opacity-75'
							}`}
						/>
					))}
					{images.length > 10 && (
						<span className='text-white text-xs ml-1'>
							+{images.length - 10}
						</span>
					)}
				</div>
			)}

			{/* Image counter */}
			<div className='absolute top-2 right-2 px-2 py-1 rounded-full bg-black bg-opacity-50 text-white text-xs'>
				{currentIndex + 1} / {images.length}
				{hasMore ? '+' : ''}
			</div>
		</div>
	);
};

export default POIImageCarousel;
