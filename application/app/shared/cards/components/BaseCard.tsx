/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useEffect, useRef } from 'react';
import { CardConfig } from './types';

interface BaseCardProps {
	children: React.ReactNode;
	isVisible: boolean;
	onClose: () => void;
	position?: { x: number; y: number };
	config?: CardConfig;
	className?: string;
	style?: React.CSSProperties;
}

const BaseCard: React.FC<BaseCardProps> = ({
	children,
	isVisible,
	onClose,
	position,
	config = {},
	className = '',
	style = {},
}) => {
	const cardRef = useRef<HTMLDivElement>(null);

	const { maxWidth = '320px', animation = 'fade' } = config;

	// Handle click outside to close
	useEffect(() => {
		if (!isVisible) return;

		const handleClickOutside = (event: MouseEvent) => {
			if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
				onClose();
			}
		};

		// Add delay to prevent immediate close when card appears
		const timer = setTimeout(() => {
			document.addEventListener('mousedown', handleClickOutside);
		}, 100);

		return () => {
			clearTimeout(timer);
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [isVisible, onClose]);

	// Handle escape key
	useEffect(() => {
		if (!isVisible) return;

		const handleEscape = (event: KeyboardEvent) => {
			if (event.key === 'Escape') {
				onClose();
			}
		};

		document.addEventListener('keydown', handleEscape);
		return () => document.removeEventListener('keydown', handleEscape);
	}, [isVisible, onClose]);

	if (!isVisible) return null;

	const getAnimationClass = () => {
		switch (animation) {
			case 'slide':
				return 'animate-slide-up';
			case 'scale':
				return 'animate-scale-in';
			case 'fade':
			default:
				return 'animate-fade-in';
		}
	};

	const cardStyle: React.CSSProperties = {
		maxWidth,
		...style,
	};

	// If position is provided, render as floating card
	if (position) {
		return (
			<>
				{/* Backdrop */}
				<div
					className='fixed inset-0 z-[100]'
					onClick={onClose}
					style={{ backgroundColor: 'transparent' }}
				/>

				{/* Floating Card */}
				<div
					ref={cardRef}
					className={`fixed z-[101] pointer-events-auto ${getAnimationClass()} ${className}`}
					style={{
						left: `${position.x}px`,
						top: `${position.y}px`,
						...cardStyle,
					}}>
					<div
						className='rounded-xl shadow-2xl border backdrop-blur-md transition-all duration-300'
						style={{
							backgroundColor: `${colors.neutral.cloudWhite}F8`, // 97% opacity
							borderColor: colors.ui.blue200,
							boxShadow: `0 8px 32px ${colors.ui.blue200}60, 0 2px 8px ${colors.brand.navy}22`,
						}}>
						{children}
					</div>
				</div>
			</>
		);
	}

	// Render as modal or inline card
	return (
		<div
			ref={cardRef}
			className={`${getAnimationClass()} ${className}`}
			style={cardStyle}>
			<div
				className='rounded-xl shadow-lg border bg-white transition-all duration-300'
				style={{
					borderColor: colors.ui.blue200,
					boxShadow: `0 4px 16px ${colors.ui.blue200}40, 0 1px 4px ${colors.brand.navy}15`,
				}}>
				{children}
			</div>

			<style jsx>{`
				.animate-fade-in {
					animation: fadeInCard 0.25s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.animate-slide-up {
					animation: slideUpCard 0.3s ease-out;
				}

				.animate-scale-in {
					animation: scaleInCard 0.2s cubic-bezier(0.4, 0, 0.2, 1);
				}

				@keyframes fadeInCard {
					from {
						opacity: 0;
						transform: translateY(10px) scale(0.98);
					}
					to {
						opacity: 1;
						transform: translateY(0) scale(1);
					}
				}

				@keyframes slideUpCard {
					from {
						transform: translateY(100%);
						opacity: 0;
					}
					to {
						transform: translateY(0);
						opacity: 1;
					}
				}

				@keyframes scaleInCard {
					from {
						transform: scale(0.9);
						opacity: 0;
					}
					to {
						transform: scale(1);
						opacity: 1;
					}
				}

				@media (max-width: 600px) {
					.rounded-xl {
						border-radius: 16px !important;
					}
				}
			`}</style>
		</div>
	);
};

export default BaseCard;
