/**
 * Shared types for POI cards across the application
 *
 * @format
 */

// Base POI interface that all POI cards should support
// Note: All spatial IDs are BIGINT in DB but handled as numbers in JS (safe up to 9+ quadrillion)
export interface BasePOI {
	id: number; // BIGINT from spatial schema
	poi_type?: string;
	poi_id?: number; // BIGINT from spatial_schema.pois.id
	temp_id?: number; // BIGINT from spatial_schema.user_pois_temp.id
	approved_id?: number; // BIGINT from spatial_schema.user_pois_approved.id
	name: string;
	category: string;
	subcategory?: string;
	city?: string;
	district?: string;
	neighborhood?: string;
	country?: string;
	latitude: number;
	longitude: number;
	phone_number?: string;
	opening_hours?: string;
	description?: string;
	user_rating_avg?: number;
	user_rating_count?: number;
	distance_km?: number;
	is_favorite?: boolean;
}

// Location data for hover cards (used in chat)
export interface LocationData {
	id: string;
	name: string;
	lat: number;
	lng: number;
	confidence?: number;
	address?: string;
	walk_route_distance_m?: number;
}

// Card position for hover cards
export interface CardPosition {
	x: number;
	y: number;
}

// Common card props
export interface BaseCardProps {
	isVisible: boolean;
	onClose: () => void;
}

// Hover card specific props
export interface HoverCardProps extends BaseCardProps {
	location: LocationData;
	position: CardPosition;
	onCardHoverChange?: (hovering: boolean) => void;
}

// POI detail card props
export interface POICardProps extends BaseCardProps {
	poi: BasePOI;
	onNavigate?: (poi: BasePOI) => void;
	showActions?: boolean;
	variant?: 'hover' | 'modal' | 'inline';
}

// Card action types
export interface CardAction {
	label: string;
	icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
	onClick: () => void;
	variant?: 'primary' | 'secondary' | 'danger';
	disabled?: boolean;
}

// Card configuration
export interface CardConfig {
	showDistance?: boolean;
	showRating?: boolean;
	showActions?: boolean;
	showFavorite?: boolean;
	showPhone?: boolean;
	showHours?: boolean;
	maxWidth?: string;
	animation?: 'fade' | 'slide' | 'scale';
}
