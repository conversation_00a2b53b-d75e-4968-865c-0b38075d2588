/**
 * Cards feature components exports
 *
 * This module provides reusable card components that can be used across
 * different pages in the application. It includes:
 *
 * - LocationHoverCard: For hovering over location mentions in chat
 * - POICard: For displaying detailed POI information
 * - BaseCard: Base component for creating custom cards
 *
 * All components share consistent styling, animations, and behavior.
 */

// Components
export { default as BaseCard } from './BaseCard'
export { default as LocationHoverCard } from './LocationHoverCard'
export { default as POICard } from './POICard'

// Types
export type {
  BasePOI,
  LocationData,
  CardPosition,
  BaseCardProps,
  HoverCardProps,
  POICardProps,
  CardAction,
  CardConfig
} from './types'

// Utilities
export {
  formatDistance,
  formatRating,
  getLocationString,
  getCategoryIcon,
  generateMapsUrl,
  generateSearchUrl,
  copyCoordinates,
  calculateCardPosition,
  locationToPOI,
  poiToLocation,
  validatePOI,
  validateLocation
} from './utils'

// Re-export for convenience
export * from './types'
export * from './utils'
