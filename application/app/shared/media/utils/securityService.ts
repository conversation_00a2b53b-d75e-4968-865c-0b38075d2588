/**
 * Media Security Service
 * Advanced security utilities for file upload protection
 *
 * @format
 */

import crypto from 'crypto';

/**
 * Security scan result
 */
export interface SecurityScanResult {
	isSafe: boolean;
	threats: string[];
	warnings: string[];
	riskLevel: 'low' | 'medium' | 'high' | 'critical';
	recommendations: string[];
}

/**
 * Upload rate limit tracking
 */
interface RateLimitEntry {
	count: number;
	firstUpload: number;
	lastUpload: number;
}

/**
 * Advanced security service for media uploads
 */
export class MediaSecurityService {
	private static rateLimitMap = new Map<string, RateLimitEntry>();
	private static readonly RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds
	private static readonly MAX_UPLOADS_PER_HOUR = 50;
	private static readonly MAX_STORAGE_PER_USER = 100 * 1024 * 1024; // 100MB

	/**
	 * Comprehensive security scan of uploaded file
	 */
	static async performSecurityScan(file: File): Promise<SecurityScanResult> {
		const threats: string[] = [];
		const warnings: string[] = [];
		const recommendations: string[] = [];
		let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

		// 1. File name security check
		const nameThreats = this.scanFileName(file.name);
		threats.push(...nameThreats);

		// 2. File size check
		if (file.size > 50 * 1024 * 1024) {
			// 50MB
			threats.push('File size exceeds safe limits');
			riskLevel = 'high';
		}

		// 3. MIME type validation
		const mimeThreats = this.scanMimeType(file.type);
		threats.push(...mimeThreats);

		// 4. File signature validation
		try {
			const signatureValid = await this.validateFileSignature(file);
			if (!signatureValid) {
				threats.push('File signature does not match declared type');
				riskLevel = 'high';
			}
		} catch {
			warnings.push('Could not validate file signature');
		}

		// 5. Content analysis (basic)
		const contentThreats = await this.scanFileContent(file);
		threats.push(...contentThreats);

		// 6. Metadata analysis
		const metadataWarnings = await this.scanMetadata(file);
		warnings.push(...metadataWarnings);

		// Determine overall risk level
		if (threats.length > 0) {
			riskLevel = threats.some(
				(t) => t.includes('executable') || t.includes('script')
			)
				? 'critical'
				: 'high';
		} else if (warnings.length > 2) {
			riskLevel = 'medium';
		}

		// Generate recommendations
		if (riskLevel === 'critical' || riskLevel === 'high') {
			recommendations.push('Reject this file upload');
			recommendations.push("Scan the user's system for malware");
		} else if (riskLevel === 'medium') {
			recommendations.push('Process with additional validation');
			recommendations.push('Monitor user upload patterns');
		} else {
			recommendations.push('File appears safe to process');
		}

		return {
			isSafe: threats.length === 0,
			threats,
			warnings,
			riskLevel,
			recommendations,
		};
	}

	/**
	 * Scan file name for security threats
	 */
	private static scanFileName(fileName: string): string[] {
		const threats: string[] = [];

		// Check for executable extensions
		const executableExtensions = [
			'.exe',
			'.bat',
			'.cmd',
			'.com',
			'.scr',
			'.pif',
			'.vbs',
			'.js',
			'.jar',
			'.php',
			'.asp',
			'.jsp',
			'.py',
			'.pl',
			'.sh',
			'.ps1',
			'.msi',
			'.deb',
			'.rpm',
		];

		const lowerName = fileName.toLowerCase();
		for (const ext of executableExtensions) {
			if (lowerName.includes(ext)) {
				threats.push(`Contains executable extension: ${ext}`);
			}
		}

		// Check for double extensions
		const extensions = fileName.match(/\.[a-zA-Z0-9]+/g) || [];
		if (extensions.length > 1) {
			threats.push('Multiple file extensions detected');
		}

		// Check for script injection patterns
		const scriptPatterns = [
			/<script/i,
			/javascript:/i,
			/vbscript:/i,
			/onload=/i,
			/onerror=/i,
			/eval\(/i,
			/document\./i,
		];

		for (const pattern of scriptPatterns) {
			if (pattern.test(fileName)) {
				threats.push('Script injection pattern in filename');
			}
		}

		return threats;
	}

	/**
	 * Scan MIME type for threats
	 */
	private static scanMimeType(mimeType: string): string[] {
		const threats: string[] = [];

		// Dangerous MIME types
		const dangerousMimes = [
			'application/x-executable',
			'application/x-msdownload',
			'application/x-msdos-program',
			'application/x-winexe',
			'application/x-javascript',
			'text/javascript',
			'application/javascript',
			'text/html',
			'application/x-php',
			'application/x-httpd-php',
		];

		if (dangerousMimes.includes(mimeType.toLowerCase())) {
			threats.push(`Dangerous MIME type: ${mimeType}`);
		}

		// Check for generic/suspicious types
		if (mimeType === 'application/octet-stream') {
			threats.push('Generic binary type - could be executable');
		}

		return threats;
	}

	/**
	 * Validate file signature matches declared type
	 */
	private static async validateFileSignature(file: File): Promise<boolean> {
		const buffer = await file.slice(0, 32).arrayBuffer();
		const bytes = new Uint8Array(buffer);

		// File signatures for allowed types
		const signatures: { [key: string]: number[][] } = {
			'image/jpeg': [[0xff, 0xd8, 0xff]],
			'image/png': [[0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]],
			'image/gif': [
				[0x47, 0x49, 0x46, 0x38, 0x37, 0x61],
				[0x47, 0x49, 0x46, 0x38, 0x39, 0x61],
			],
			'image/webp': [[0x52, 0x49, 0x46, 0x46]], // RIFF header
			'video/mp4': [
				[0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70],
				[0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70],
			],
		};

		const expectedSignatures = signatures[file.type];
		if (!expectedSignatures) {
			return false; // Unknown type
		}

		return expectedSignatures.some((signature) =>
			signature.every((byte, index) => bytes[index] === byte)
		);
	}

	/**
	 * Scan file content for threats
	 */
	private static async scanFileContent(file: File): Promise<string[]> {
		const threats: string[] = [];

		try {
			// Read first 1KB of file content as text
			const buffer = await file.slice(0, 1024).arrayBuffer();
			const text = new TextDecoder('utf-8', { fatal: false }).decode(buffer);

			// Look for script patterns
			const scriptPatterns = [
				/<script[\s\S]*?>[\s\S]*?<\/script>/gi,
				/javascript:/gi,
				/vbscript:/gi,
				/data:text\/html/gi,
				/eval\s*\(/gi,
				/document\.write/gi,
				/window\.location/gi,
				/\.exe\s/gi,
				/cmd\.exe/gi,
				/powershell/gi,
			];

			for (const pattern of scriptPatterns) {
				if (pattern.test(text)) {
					threats.push('Suspicious script content detected');
					break;
				}
			}

			// Check for embedded executables
			const executableSignatures = [
				[0x4d, 0x5a], // PE executable
				[0x7f, 0x45, 0x4c, 0x46], // ELF executable
				[0xca, 0xfe, 0xba, 0xbe], // Mach-O executable
			];

			const bytes = new Uint8Array(buffer);
			for (const signature of executableSignatures) {
				if (signature.every((byte, index) => bytes[index] === byte)) {
					threats.push('Embedded executable detected');
					break;
				}
			}
		} catch {
			// Content scanning failed - not necessarily a threat
		}

		return threats;
	}

	/**
	 * Scan file metadata for suspicious content
	 */
	private static async scanMetadata(file: File): Promise<string[]> {
		const warnings: string[] = [];

		// Check file modification time
		if (file.lastModified) {
			const now = Date.now();
			const fileTime = file.lastModified;

			// File from future
			if (fileTime > now + 60000) {
				// 1 minute tolerance
				warnings.push('File has future timestamp');
			}

			// Very old file
			if (now - fileTime > 10 * 365 * 24 * 60 * 60 * 1000) {
				// 10 years
				warnings.push('File is very old');
			}
		}

		return warnings;
	}

	/**
	 * Check upload rate limits for user
	 */
	static checkRateLimit(userId: string): {
		allowed: boolean;
		remaining: number;
		resetTime: number;
		message?: string;
	} {
		const now = Date.now();
		const entry = this.rateLimitMap.get(userId);

		if (!entry) {
			// First upload
			this.rateLimitMap.set(userId, {
				count: 1,
				firstUpload: now,
				lastUpload: now,
			});
			return {
				allowed: true,
				remaining: this.MAX_UPLOADS_PER_HOUR - 1,
				resetTime: now + this.RATE_LIMIT_WINDOW,
			};
		}

		// Check if window has expired
		if (now - entry.firstUpload > this.RATE_LIMIT_WINDOW) {
			// Reset window
			this.rateLimitMap.set(userId, {
				count: 1,
				firstUpload: now,
				lastUpload: now,
			});
			return {
				allowed: true,
				remaining: this.MAX_UPLOADS_PER_HOUR - 1,
				resetTime: now + this.RATE_LIMIT_WINDOW,
			};
		}

		// Check if limit exceeded
		if (entry.count >= this.MAX_UPLOADS_PER_HOUR) {
			return {
				allowed: false,
				remaining: 0,
				resetTime: entry.firstUpload + this.RATE_LIMIT_WINDOW,
				message: 'Upload rate limit exceeded. Please try again later.',
			};
		}

		// Increment count
		entry.count++;
		entry.lastUpload = now;
		this.rateLimitMap.set(userId, entry);

		return {
			allowed: true,
			remaining: this.MAX_UPLOADS_PER_HOUR - entry.count,
			resetTime: entry.firstUpload + this.RATE_LIMIT_WINDOW,
		};
	}

	/**
	 * Generate secure hash for file content
	 */
	static async generateFileHash(file: File): Promise<string> {
		const buffer = await file.arrayBuffer();
		const hash = crypto.createHash('sha256');
		hash.update(new Uint8Array(buffer));
		return hash.digest('hex');
	}

	/**
	 * Clean up old rate limit entries
	 */
	static cleanupRateLimits(): void {
		const now = Date.now();
		for (const [userId, entry] of this.rateLimitMap.entries()) {
			if (now - entry.firstUpload > this.RATE_LIMIT_WINDOW) {
				this.rateLimitMap.delete(userId);
			}
		}
	}

	/**
	 * Quarantine suspicious file
	 */
	static async quarantineFile(
		file: File,
		reason: string
	): Promise<{
		success: boolean;
		quarantineId: string;
		error?: string;
	}> {
		try {
			const quarantineId = crypto.randomUUID();

			// In a real implementation, you would:
			// 1. Move file to quarantine directory
			// 2. Log the incident
			// 3. Notify administrators
			// 4. Store metadata about the quarantined file

			console.warn(
				`File quarantined: ${file.name}, Reason: ${reason}, ID: ${quarantineId}`
			);

			return {
				success: true,
				quarantineId,
			};
		} catch (error) {
			return {
				success: false,
				quarantineId: '',
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}
}
