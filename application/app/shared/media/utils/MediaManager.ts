/**
 * Media Management Library
 * Main export file for all media-related utilities
 *
 * @format
 */

import { logger } from '@/lib/logger';
import path from 'path';
import { FileValidator } from './fileValidation';
import { ImageProcessor } from './imageProcessing';
import { DirectoryManager, FileOperations } from './mediaUtils';

/**
 * High-level media management service
 */
export class MediaManager {
	/**
	 * Initialize media directories for a new user
	 */
	static async initializeUserDirectories(userId: string): Promise<{
		success: boolean;
		directories: {
			profileDir: string;
			postsDir: string;
		};
		error?: string;
	}> {
		try {
			const directories = await DirectoryManager.createUserDirectories(userId);
			return {
				success: true,
				directories,
			};
		} catch (error) {
			return {
				success: false,
				directories: { profileDir: '', postsDir: '' },
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Process and save profile picture
	 */
	static async saveProfilePicture(
		userId: string,
		file: File
	): Promise<{
		success: boolean;
		profilePictureUrl?: string;
		thumbnails?: string[];
		error?: string;
		warnings?: string[];
	}> {
		try {
			// Basic file validation
			if (!file || file.size === 0) {
				return {
					success: false,
					error: 'Invalid file provided',
				};
			}

			// Check file size (max 10MB)
			if (file.size > 10 * 1024 * 1024) {
				return {
					success: false,
					error: 'File size too large (max 10MB)',
				};
			}

			// Check file type
			const allowedTypes = [
				'image/jpeg',
				'image/png',
				'image/webp',
				'image/gif',
			];
			if (!allowedTypes.includes(file.type)) {
				return {
					success: false,
					error:
						'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.',
				};
			}

			// Create a secure filename first to avoid validation issues with original filename
			const secureFileName = `temp_${Date.now()}_${Math.random()
				.toString(36)
				.substring(2, 15)}${path.extname(file.name).toLowerCase()}`;

			// Create a new File object with secure name for validation
			const secureFile = new File([file], secureFileName, { type: file.type });

			// Validate file with secure name
			const validation = FileValidator.validateProfilePicture(secureFile);
			if (!validation.isValid) {
				return {
					success: false,
					error: validation.errors.join(', '),
				};
			}

			// Import utilities directly to avoid scoping issues
			const {
				MediaPathHelper: LocalPathHelper,
				DirectoryManager: LocalDirManager,
				FileOperations: LocalFileOps,
			} = await import('./mediaUtils');

			// Create user directories
			const { profileDir } = await LocalDirManager.createUserDirectories(
				userId
			);

			// Generate filename
			const fileName = LocalPathHelper.generateFileName(file.name, 'profile');

			// Save original file
			const { filePath } = await LocalFileOps.saveFile(
				file,
				profileDir,
				fileName
			);

			// Process and optimize image
			const processedResult = await ImageProcessor.processProfilePicture(
				filePath,
				profileDir,
				`processed_${fileName}`
			);

			if (!processedResult.success) {
				// Clean up original file
				await FileOperations.deleteFile(filePath);
				return {
					success: false,
					error: processedResult.error,
				};
			}

			// Generate thumbnails
			const thumbnailsDir = LocalPathHelper.getThumbnailsDir(profileDir);
			await LocalDirManager.ensureDirectory(thumbnailsDir);

			const thumbnailResults = await ImageProcessor.generateProfileThumbnails(
				processedResult.outputPath,
				thumbnailsDir,
				fileName
			);

			// Clean up original unprocessed file
			await FileOperations.deleteFile(filePath);

			return {
				success: true,
				profilePictureUrl: processedResult.publicUrl,
				thumbnails: thumbnailResults
					.filter((result) => result.success)
					.map((result) => result.publicUrl),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Process and save post media
	 */
	static async savePostMedia(
		userId: string,
		poiId: string,
		file: File
	): Promise<{
		success: boolean;
		mediaUrl?: string;
		thumbnails?: string[];
		mediaType?: 'image' | 'video';
		error?: string;
		warnings?: string[];
	}> {
		try {
			// Basic file validation
			if (!file || file.size === 0) {
				return {
					success: false,
					error: 'Invalid file provided',
				};
			}

			// Check file size (max 20MB)
			if (file.size > 20 * 1024 * 1024) {
				return {
					success: false,
					error: 'File size too large (max 20MB)',
				};
			}

			// Check file type
			const allowedTypes = [
				'image/jpeg',
				'image/png',
				'image/webp',
				'image/gif',
				'video/mp4',
				'video/webm',
			];
			if (!allowedTypes.includes(file.type)) {
				return {
					success: false,
					error:
						'Invalid file type. Only JPEG, PNG, WebP, GIF, MP4, and WebM are allowed.',
				};
			}

			// Create a secure filename first to avoid validation issues with original filename
			const secureFileName = `temp_${Date.now()}_${Math.random()
				.toString(36)
				.substring(2, 15)}${path.extname(file.name).toLowerCase()}`;

			// Create a new File object with secure name for validation
			const secureFile = new File([file], secureFileName, { type: file.type });

			// Validate file with secure name
			const validation = FileValidator.validatePostMedia(secureFile);
			if (!validation.isValid) {
				return {
					success: false,
					error: validation.errors.join(', '),
				};
			}

			// Import utilities directly to avoid scoping issues
			const {
				MediaPathHelper: LocalPathHelper,
				DirectoryManager: LocalDirManager,
				FileOperations: LocalFileOps,
			} = await import('./mediaUtils');

			// Create POI-specific directory
			const poiPostsDir = await LocalDirManager.createUserPOIPostsDir(
				userId,
				poiId
			);

			// Generate filename
			const fileName = LocalPathHelper.generateFileName(file.name, 'post');

			// Save original file
			const { filePath, publicUrl } = await LocalFileOps.saveFile(
				file,
				poiPostsDir,
				fileName
			);

			const mediaType = LocalPathHelper.getMediaType(fileName);

			// Process images
			if (mediaType === 'image') {
				// Process and optimize image
				const processedResult = await ImageProcessor.processPostImage(
					filePath,
					poiPostsDir,
					`processed_${fileName}`
				);

				if (processedResult.success) {
					// Generate thumbnails
					const thumbnailsDir = LocalPathHelper.getThumbnailsDir(poiPostsDir);
					const thumbnailResults = await ImageProcessor.generatePostThumbnails(
						processedResult.outputPath,
						thumbnailsDir,
						fileName
					);

					// Clean up original unprocessed file
					await FileOperations.deleteFile(filePath);

					return {
						success: true,
						mediaUrl: processedResult.publicUrl,
						thumbnails: thumbnailResults
							.filter((result) => result.success)
							.map((result) => result.publicUrl),
						mediaType: 'image',
					};
				} else {
					// If processing fails, use original file
					return {
						success: true,
						mediaUrl: publicUrl,
						mediaType: 'image',
					};
				}
			} else if (mediaType === 'video') {
				// For videos, just save the original file
				// TODO: Implement video thumbnail generation
				return {
					success: true,
					mediaUrl: publicUrl,
					mediaType: 'video',
				};
			} else {
				return {
					success: false,
					error: 'Unsupported media type',
				};
			}
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Delete profile picture and all related files
	 */
	static async deleteProfilePicture(userId: string): Promise<{
		success: boolean;
		error?: string;
		deletedFiles?: string[];
		cleanedUpDirectories?: string[];
	}> {
		try {
			const {
				MediaPathHelper: LocalPathHelper,
				DirectoryManager: LocalDirManager,
			} = await import('./mediaUtils');

			const profileDir = LocalPathHelper.getUserProfileDir(userId);
			const thumbnailsDir = LocalPathHelper.getThumbnailsDir(profileDir);

			const deletedFiles: string[] = [];

			// Get all files in profile directory
			try {
				const profileFiles = await LocalDirManager.getDirectoryFiles(
					profileDir
				);
				for (const file of profileFiles) {
					await FileOperations.deleteFile(file);
					deletedFiles.push(file);
				}
			} catch {
				// Directory might not exist, that's okay
			}

			// Get all files in thumbnails directory
			try {
				const thumbnailFiles = await LocalDirManager.getDirectoryFiles(
					thumbnailsDir
				);
				for (const file of thumbnailFiles) {
					await FileOperations.deleteFile(file);
					deletedFiles.push(file);
				}
			} catch {
				// Directory might not exist, that's okay
			}

			// Clean up empty directories
			try {
				await LocalDirManager.cleanupEmptyDirectories(thumbnailsDir);
				await LocalDirManager.cleanupEmptyDirectories(profileDir);
			} catch {
				// Ignore cleanup errors
			}

			// Check if user directory is now empty and clean it up
			const cleanupResult = await LocalDirManager.cleanupUserDirectoryIfEmpty(
				userId
			);

			return {
				success: true,
				deletedFiles,
				cleanedUpDirectories: cleanupResult.removedDirectories,
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Delete POI media and all related files (thumbnails, etc.)
	 */
	static async deletePOIMedia(
		userId: string,
		poiId: string,
		mediaUrl: string,
		thumbnailUrl?: string
	): Promise<{
		success: boolean;
		error?: string;
		deletedFiles?: string[];
		cleanedUpDirectories?: string[];
	}> {
		try {
			const { MediaPathHelper: LocalPathHelper } = await import('./mediaUtils');

			const deletedFiles: string[] = [];

			// Convert public URLs to file paths
			const mediaFilePath = this.publicUrlToFilePath(mediaUrl);
			const thumbnailFilePath = thumbnailUrl
				? this.publicUrlToFilePath(thumbnailUrl)
				: null;

			// Delete main media file
			if (mediaFilePath) {
				try {
					await FileOperations.deleteFile(mediaFilePath);
					deletedFiles.push(mediaFilePath);
				} catch (error) {
					logger.warn('Could not delete media file', { mediaFilePath, error });
				}
			}

			// Delete thumbnail file
			if (thumbnailFilePath) {
				try {
					await FileOperations.deleteFile(thumbnailFilePath);
					deletedFiles.push(thumbnailFilePath);
				} catch (error) {
					logger.warn('Could not delete thumbnail file', {
						thumbnailFilePath,
						error,
					});
				}
			}

			// Clean up empty directories
			try {
				const poiPostsDir = LocalPathHelper.getUserPostsDir(userId, poiId);
				const thumbnailsDir = LocalPathHelper.getThumbnailsDir(poiPostsDir);
				await DirectoryManager.cleanupEmptyDirectories(thumbnailsDir);
				await DirectoryManager.cleanupEmptyDirectories(poiPostsDir);
			} catch {
				// Ignore cleanup errors
			}

			// Check if user directory is now empty and clean it up
			const cleanupResult = await DirectoryManager.cleanupUserDirectoryIfEmpty(
				userId
			);

			return {
				success: true,
				deletedFiles,
				cleanedUpDirectories: cleanupResult.removedDirectories,
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Delete specific media file and cleanup
	 */
	static async deleteMedia(filePath: string): Promise<{
		success: boolean;
		error?: string;
	}> {
		try {
			await FileOperations.deleteFile(filePath);
			return { success: true };
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}

	/**
	 * Convert public URL to file path
	 */
	private static publicUrlToFilePath(publicUrl: string): string | null {
		try {
			// Remove leading slash and convert to absolute path
			const relativePath = publicUrl.startsWith('/')
				? publicUrl.slice(1)
				: publicUrl;
			return path.join(process.cwd(), 'public', relativePath);
		} catch {
			return null;
		}
	}

	/**
	 * Get user's storage usage
	 */
	static async getUserStorageUsage(userId: string): Promise<{
		success: boolean;
		usage?: {
			totalBytes: number;
			profileBytes: number;
			postsBytes: number;
			formattedTotal: string;
		};
		error?: string;
	}> {
		try {
			// Import utilities directly to avoid scoping issues
			const {
				MediaPathHelper: LocalPathHelper,
				DirectoryManager: LocalDirManager,
			} = await import('./mediaUtils');

			const profileDir = LocalPathHelper.getUserProfileDir(userId);
			const postsDir = LocalPathHelper.getUserPostsDir(userId);

			const profileBytes = await LocalDirManager.getDirectorySize(profileDir);
			const postsBytes = await LocalDirManager.getDirectorySize(postsDir);
			const totalBytes = profileBytes + postsBytes;

			return {
				success: true,
				usage: {
					totalBytes,
					profileBytes,
					postsBytes,
					formattedTotal: FileValidator.formatFileSize(totalBytes),
				},
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	}
}

export default MediaManager;
