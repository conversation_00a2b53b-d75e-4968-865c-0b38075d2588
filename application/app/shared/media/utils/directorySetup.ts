/**
 * Media Directory Setup and Management
 * Handles initial setup, permissions, and maintenance of media directory structure
 *
 * @format
 */

import fs from 'fs';
import path from 'path';
import { DirectoryManager, MediaPathHelper } from './mediaUtils';

/**
 * Directory setup result
 */
export interface DirectorySetupResult {
	success: boolean;
	createdDirectories: string[];
	errors: string[];
	warnings: string[];
}

/**
 * Directory health check result
 */
export interface DirectoryHealthCheck {
	isHealthy: boolean;
	issues: string[];
	recommendations: string[];
	totalSize: number;
	directoryCount: number;
	fileCount: number;
}

/**
 * Media directory setup and management service
 */
export class MediaDirectorySetup {
	/**
	 * Initialize the complete media directory structure
	 */
	static async initializeMediaStructure(): Promise<DirectorySetupResult> {
		const createdDirectories: string[] = [];
		const errors: string[] = [];
		const warnings: string[] = [];

		try {
			// Create base media directory
			const mediaRoot = MediaPathHelper.getMediaRoot();
			await DirectoryManager.ensureDirectory(mediaRoot);
			createdDirectories.push(mediaRoot);

			// Create example user directories (for testing)
			const exampleUserId = 'example-user';
			const userDirs = await DirectoryManager.createUserDirectories(
				exampleUserId
			);
			createdDirectories.push(userDirs.profileDir, userDirs.postsDir);

			// Create example POI directory
			const examplePoiId = 'example-poi';
			const poiDir = await DirectoryManager.createPOIProfileDir(examplePoiId);
			createdDirectories.push(poiDir);

			// Set up .gitignore for media directory
			await this.setupGitIgnore(mediaRoot);

			// Create README file
			await this.createReadmeFile(mediaRoot);

			// Verify permissions
			const permissionCheck = await this.verifyPermissions(mediaRoot);
			if (!permissionCheck.success) {
				warnings.push(...permissionCheck.warnings);
			}

			return {
				success: true,
				createdDirectories,
				errors,
				warnings,
			};
		} catch (error) {
			errors.push(error instanceof Error ? error.message : 'Unknown error');
			return {
				success: false,
				createdDirectories,
				errors,
				warnings,
			};
		}
	}

	/**
	 * Set up .gitignore for media directory
	 */
	private static async setupGitIgnore(mediaRoot: string): Promise<void> {
		const gitignorePath = path.join(mediaRoot, '.gitignore');
		const gitignoreContent = `# Ignore all user-generated media files
user-*/
poi-*/

# Keep example directories for structure
!user-example/
!poi-example/

# Keep documentation
!README.md
!.gitkeep
`;

		await fs.promises.writeFile(gitignorePath, gitignoreContent, 'utf8');
	}

	/**
	 * Create README file for media directory
	 */
	private static async createReadmeFile(mediaRoot: string): Promise<void> {
		const readmePath = path.join(mediaRoot, 'README.md');
		const readmeContent = `# Media Storage Directory

This directory contains user-generated media files organized in a structured hierarchy.

## Directory Structure

\`\`\`
media/
├── user-{user-id}/
│   ├── profile/
│   │   ├── profile-picture.jpg
│   │   └── thumbnails/
│   │       ├── profile-thumb-small.jpg
│   │       └── profile-thumb-medium.jpg
│   └── posts/
│       └── poi-{poi-id}/
│           ├── post-media.jpg
│           └── thumbnails/
│               ├── post-thumb-small.jpg
│               └── post-thumb-medium.jpg
└── poi-{poi-id}/
    └── profile/
        └── poi-profile.jpg
\`\`\`

## File Naming Convention

- Profile pictures: \`profile-{timestamp}-{uuid}.{ext}\`
- Post media: \`post-{timestamp}-{uuid}.{ext}\`
- Thumbnails: \`{original-name}-thumb-{size}.{ext}\`

## Security Features

- File type validation
- Size limits enforcement
- Security scanning
- Rate limiting
- Automatic cleanup of empty directories

## Storage Limits

- Profile pictures: 5MB max
- Post media: 20MB max
- Total per user: 100MB max

## Supported Formats

### Images
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)
- GIF (.gif)

### Videos
- MP4 (.mp4)
- WebM (.webm)
- MOV (.mov)
- AVI (.avi)

## Maintenance

The system automatically:
- Cleans up empty directories
- Validates file integrity
- Monitors storage usage
- Enforces security policies

## Future Migration

This local storage system is designed for easy migration to AWS S3 CDN.
All file operations are abstracted through the MediaManager service.
`;

		await fs.promises.writeFile(readmePath, readmeContent, 'utf8');
	}

	/**
	 * Verify directory permissions
	 */
	private static async verifyPermissions(mediaRoot: string): Promise<{
		success: boolean;
		warnings: string[];
	}> {
		const warnings: string[] = [];

		try {
			// Test write permissions
			const testFile = path.join(mediaRoot, '.permission-test');
			await fs.promises.writeFile(testFile, 'test', 'utf8');
			await fs.promises.unlink(testFile);

			// Test read permissions
			await fs.promises.readdir(mediaRoot);

			return { success: true, warnings };
		} catch {
			warnings.push('Directory permissions may be insufficient');
			return { success: false, warnings };
		}
	}

	/**
	 * Perform health check on media directory structure
	 */
	static async performHealthCheck(): Promise<DirectoryHealthCheck> {
		const issues: string[] = [];
		const recommendations: string[] = [];
		let totalSize = 0;
		let directoryCount = 0;
		let fileCount = 0;

		try {
			const mediaRoot = MediaPathHelper.getMediaRoot();

			// Check if media root exists
			if (!(await this.directoryExists(mediaRoot))) {
				issues.push('Media root directory does not exist');
				recommendations.push(
					'Run MediaDirectorySetup.initializeMediaStructure()'
				);
				return {
					isHealthy: false,
					issues,
					recommendations,
					totalSize: 0,
					directoryCount: 0,
					fileCount: 0,
				};
			}

			// Calculate directory statistics
			const stats = await this.calculateDirectoryStats(mediaRoot);
			totalSize = stats.totalSize;
			directoryCount = stats.directoryCount;
			fileCount = stats.fileCount;

			// Check for orphaned files
			const orphanedFiles = await this.findOrphanedFiles();
			if (orphanedFiles.length > 0) {
				issues.push(`Found ${orphanedFiles.length} orphaned files`);
				recommendations.push('Clean up orphaned files');
			}

			// Check for empty directories
			const emptyDirs = await this.findEmptyDirectories(mediaRoot);
			if (emptyDirs.length > 0) {
				issues.push(`Found ${emptyDirs.length} empty directories`);
				recommendations.push('Clean up empty directories');
			}

			// Check storage usage
			const maxStorage = 1024 * 1024 * 1024; // 1GB total limit
			if (totalSize > maxStorage) {
				issues.push('Total storage usage exceeds recommended limits');
				recommendations.push('Implement storage cleanup policies');
			}

			// Check permissions
			const permissionCheck = await this.verifyPermissions(mediaRoot);
			if (!permissionCheck.success) {
				issues.push('Directory permission issues detected');
				recommendations.push('Fix directory permissions');
			}

			return {
				isHealthy: issues.length === 0,
				issues,
				recommendations,
				totalSize,
				directoryCount,
				fileCount,
			};
		} catch (error) {
			issues.push(
				'Health check failed: ' +
					(error instanceof Error ? error.message : 'Unknown error')
			);
			return {
				isHealthy: false,
				issues,
				recommendations,
				totalSize,
				directoryCount,
				fileCount,
			};
		}
	}

	/**
	 * Clean up orphaned files and empty directories
	 */
	static async performCleanup(): Promise<{
		success: boolean;
		cleaned: {
			orphanedFiles: number;
			emptyDirectories: number;
			freedSpace: number;
		};
		errors: string[];
	}> {
		const errors: string[] = [];
		let orphanedFiles = 0;
		let emptyDirectories = 0;
		let freedSpace = 0;

		try {
			const mediaRoot = MediaPathHelper.getMediaRoot();

			// Find and remove orphaned files
			const orphaned = await this.findOrphanedFiles();
			for (const filePath of orphaned) {
				try {
					const stats = await fs.promises.stat(filePath);
					await fs.promises.unlink(filePath);
					orphanedFiles++;
					freedSpace += stats.size;
				} catch {
					errors.push(`Failed to remove orphaned file ${filePath}`);
				}
			}

			// Find and remove empty directories
			const emptyDirs = await this.findEmptyDirectories(mediaRoot);
			for (const dirPath of emptyDirs) {
				try {
					await fs.promises.rmdir(dirPath);
					emptyDirectories++;
				} catch {
					errors.push(`Failed to remove empty directory ${dirPath}`);
				}
			}

			return {
				success: errors.length === 0,
				cleaned: {
					orphanedFiles,
					emptyDirectories,
					freedSpace,
				},
				errors,
			};
		} catch (error) {
			errors.push(error instanceof Error ? error.message : 'Unknown error');
			return {
				success: false,
				cleaned: {
					orphanedFiles: 0,
					emptyDirectories: 0,
					freedSpace: 0,
				},
				errors,
			};
		}
	}

	/**
	 * Helper methods
	 */
	private static async directoryExists(dirPath: string): Promise<boolean> {
		try {
			const stats = await fs.promises.stat(dirPath);
			return stats.isDirectory();
		} catch {
			return false;
		}
	}

	private static async calculateDirectoryStats(dirPath: string): Promise<{
		totalSize: number;
		directoryCount: number;
		fileCount: number;
	}> {
		let totalSize = 0;
		let directoryCount = 0;
		let fileCount = 0;

		const items = await fs.promises.readdir(dirPath);

		for (const item of items) {
			const itemPath = path.join(dirPath, item);
			const stats = await fs.promises.stat(itemPath);

			if (stats.isDirectory()) {
				directoryCount++;
				const subStats = await this.calculateDirectoryStats(itemPath);
				totalSize += subStats.totalSize;
				directoryCount += subStats.directoryCount;
				fileCount += subStats.fileCount;
			} else {
				fileCount++;
				totalSize += stats.size;
			}
		}

		return { totalSize, directoryCount, fileCount };
	}

	private static async findOrphanedFiles(): Promise<string[]> {
		// This would implement logic to find files that don't belong to any user or POI
		// For now, return empty array
		return [];
	}

	private static async findEmptyDirectories(
		dirPath: string
	): Promise<string[]> {
		const emptyDirs: string[] = [];
		const items = await fs.promises.readdir(dirPath);

		if (items.length === 0) {
			emptyDirs.push(dirPath);
		} else {
			for (const item of items) {
				const itemPath = path.join(dirPath, item);
				const stats = await fs.promises.stat(itemPath);

				if (stats.isDirectory()) {
					const subEmptyDirs = await this.findEmptyDirectories(itemPath);
					emptyDirs.push(...subEmptyDirs);
				}
			}
		}

		return emptyDirs;
	}
}
