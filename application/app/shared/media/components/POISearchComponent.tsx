/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	POISearchService,
	type POISearchFilters,
} from '@/lib/poi/poiSearchService'; // FIXED: import only service and filters
import React, { useCallback, useState } from 'react';
import { <PERSON>Check, <PERSON>Loader, FiMapPin, FiSearch, FiX } from 'react-icons/fi';
import { POISearchResult } from '../types'; // FIXED: use local definition for POISearchResult

/**
 * POI search component props
 */
interface POISearchComponentProps {
	onPOISelected: (poi: POISearchResult) => void;
	onPOIValidated: (poi: POISearchResult | null) => void;
	placeholder?: string;
	required?: boolean;
	initialValue?: string;
	className?: string;
}

/**
 * POI search and validation component
 */
export const POISearchComponent: React.FC<POISearchComponentProps> = ({
	onPOISelected,
	onPOIValidated,
	placeholder = 'Search for a POI by name, ID, or location...',
	required = true,
	initialValue = '',
	className = '',
}) => {
	const [searchQuery, setSearchQuery] = useState(initialValue);
	const [searchResults, setSearchResults] = useState<POISearchResult[]>([]);
	const [selectedPOI, setSelectedPOI] = useState<POISearchResult | null>(null);
	const [isSearching, setIsSearching] = useState(false);
	const [isValidating, setIsValidating] = useState(false);
	const [showResults, setShowResults] = useState(false);
	const [validationStatus, setValidationStatus] = useState<
		'none' | 'valid' | 'invalid'
	>('none');
	const [validationMessage, setValidationMessage] = useState('');
	const [suggestions, setSuggestions] = useState<POISearchResult[]>([]);

	/**
	 * Debounced search function
	 */
	const debouncedSearch = useCallback(
		debounce(async (...args: unknown[]) => {
			const query = typeof args[0] === 'string' ? args[0] : '';
			if (query.length < 2) {
				setSearchResults([]);
				setShowResults(false);
				return;
			}
			setIsSearching(true);
			try {
				// Try searching by name first
				const nameResult = await POISearchService.searchByName(query, 10);

				if (nameResult.success && nameResult.pois.length > 0) {
					setSearchResults(
						nameResult.pois.map((poi) =>
							mapToLocalPOI(poi as unknown as Record<string, unknown>)
						)
					); // Map service type to local type
					setShowResults(true);
				} else {
					// If no results by name, try broader search
					const filters: POISearchFilters = {
						name: query,
						limit: 10,
					};

					const broadResult = await POISearchService.searchPOIs(filters);
					setSearchResults(
						broadResult.pois.map((poi) =>
							mapToLocalPOI(poi as unknown as Record<string, unknown>)
						)
					); // Map service type to local type
					setShowResults(broadResult.pois.length > 0);
				}
			} catch (error) {
				console.error('Search error:', error);
				setSearchResults([]);
				setShowResults(false);
			} finally {
				setIsSearching(false);
			}
		}, 300),
		[]
	);

	/**
	 * Handle search input change
	 */
	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		setSearchQuery(value);
		setValidationStatus('none');
		setValidationMessage('');
		setSuggestions([]);

		if (selectedPOI) {
			setSelectedPOI(null);
			onPOISelected({} as POISearchResult);
			onPOIValidated(null);
		}

		debouncedSearch(value);
	};

	/**
	 * Handle POI selection from search results
	 */
	const handlePOISelect = (poi: POISearchResult) => {
		setSelectedPOI(poi);
		setSearchQuery(`${poi.name} (${poi.city || 'Unknown City'})`);
		setShowResults(false);
		setValidationStatus('valid');
		setValidationMessage(`✓ ${poi.name} confirmed`);

		onPOISelected(poi);
		onPOIValidated(poi);
	};

	/**
	 * Validate POI by ID
	 */
	const validatePOIById = async (poiId: string) => {
		setIsValidating(true);
		setValidationStatus('none');
		setValidationMessage('');
		setSuggestions([]);

		try {
			const result = await POISearchService.validatePOIForUpload(poiId);

			if (result.isValid && result.poi) {
				const localPOI = mapToLocalPOI(
					result.poi as unknown as Record<string, unknown>
				); // FIXED: map to local type
				setSelectedPOI(localPOI); // FIXED: map to local type
				setValidationStatus('valid');
				setValidationMessage(`✓ ${localPOI.name} confirmed`);
				onPOISelected(localPOI); // FIXED: map to local type
				onPOIValidated(localPOI); // FIXED: map to local type
			} else {
				setValidationStatus('invalid');
				setValidationMessage(result.error || 'POI not found');

				if (result.suggestions && result.suggestions.length > 0) {
					setSuggestions(
						result.suggestions.map((poi) =>
							mapToLocalPOI(poi as unknown as Record<string, unknown>)
						)
					); // FIXED: map to local type
				}

				onPOIValidated(null);
			}
		} catch {
			setValidationStatus('invalid');
			setValidationMessage('Validation failed');
			onPOIValidated(null);
		} finally {
			setIsValidating(false);
		}
	};

	/**
	 * Handle manual POI ID validation
	 */
	const handleValidateClick = () => {
		if (searchQuery.trim()) {
			validatePOIById(searchQuery.trim());
		}
	};

	/**
	 * Handle key press events
	 */
	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			if (searchResults.length > 0 && showResults) {
				handlePOISelect(searchResults[0]);
			} else if (searchQuery.trim()) {
				validatePOIById(searchQuery.trim());
			}
		} else if (e.key === 'Escape') {
			setShowResults(false);
		}
	};

	return (
		<div className={`relative ${className}`}>
			{/* Search Input */}
			<div className='relative'>
				<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
					<FiSearch
						className='h-5 w-5'
						style={{ color: colors.neutral.slateGray }}
					/>
				</div>

				<input
					type='text'
					value={searchQuery}
					onChange={handleSearchChange}
					onKeyDown={handleKeyPress}
					placeholder={placeholder}
					required={required}
					className={`
            w-full pl-10 pr-12 py-3 border rounded-lg
            focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            ${validationStatus === 'valid' ? 'border-green-500' : ''}
            ${validationStatus === 'invalid' ? 'border-red-500' : ''}
          `}
					style={{
						borderColor:
							validationStatus === 'none'
								? colors.neutral.lightMistGray // FIXED: replaced lightGray with lightMistGray
								: undefined,
					}}
				/>

				{/* Loading/Validation Icons */}
				<div className='absolute inset-y-0 right-0 pr-3 flex items-center'>
					{(isSearching || isValidating) && (
						<FiLoader
							className='h-5 w-5 animate-spin'
							style={{ color: colors.brand.blue }}
						/>
					)}
					{validationStatus === 'valid' && (
						<FiCheck className='h-5 w-5 text-green-500' />
					)}
					{validationStatus === 'invalid' && (
						<FiX className='h-5 w-5 text-red-500' />
					)}
				</div>
			</div>

			{/* Validation Message */}
			{validationMessage && (
				<div
					className={`mt-2 text-sm ${
						validationStatus === 'valid' ? 'text-green-600' : 'text-red-600'
					}`}>
					{validationMessage}
				</div>
			)}

			{/* Manual Validation Button */}
			{searchQuery && !selectedPOI && !isSearching && (
				<button
					onClick={handleValidateClick}
					disabled={isValidating}
					className='mt-2 px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50'>
					{isValidating ? 'Validating...' : 'Validate POI ID'}
				</button>
			)}

			{/* Search Results Dropdown */}
			{showResults && searchResults.length > 0 && (
				<div className='absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto'>
					{searchResults.map((poi, index) => (
						<div
							key={`${poi.poi_type || 'unknown'}-${
								poi.poi_id || poi.id || index
							}`}
							onClick={() => handlePOISelect(poi)}
							className='px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0'>
							<div className='flex items-start justify-between'>
								<div className='flex-1'>
									<div className='font-medium text-gray-900'>{poi.name}</div>
									<div className='text-sm text-gray-500 flex items-center mt-1'>
										<FiMapPin className='h-4 w-4 mr-1' />
										{[poi.neighborhood, poi.district, poi.city]
											.filter(Boolean)
											.join(', ')}
									</div>
									{poi.category && (
										<div className='text-xs text-gray-400 mt-1'>
											{poi.category}
											{poi.subcategory ? ` • ${poi.subcategory}` : ''}
										</div>
									)}
								</div>
								<div className='text-xs text-gray-400 ml-2'>
									ID: {poi.poi_id}
								</div>
							</div>
						</div>
					))}
				</div>
			)}

			{/* Suggestions */}
			{suggestions.length > 0 && (
				<div className='mt-3'>
					<div className='text-sm font-medium text-gray-700 mb-2'>
						Did you mean:
					</div>
					<div className='space-y-2'>
						{suggestions.map((poi, index) => (
							<div
								key={`suggestion-${poi.poi_type || 'unknown'}-${
									poi.poi_id || poi.id || index
								}`}
								onClick={() => handlePOISelect(poi)}
								className='p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer'>
								<div className='font-medium text-gray-900'>{poi.name}</div>
								<div className='text-sm text-gray-500 flex items-center mt-1'>
									<FiMapPin className='h-4 w-4 mr-1' />
									{[poi.neighborhood, poi.district, poi.city]
										.filter(Boolean)
										.join(', ')}
								</div>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Selected POI Display */}
			{selectedPOI && (
				<div className='mt-3 p-4 bg-green-50 border border-green-200 rounded-lg'>
					<div className='flex items-start justify-between'>
						<div>
							<div className='font-medium text-green-900'>
								{selectedPOI.name}
							</div>
							<div className='text-sm text-green-700 flex items-center mt-1'>
								<FiMapPin className='h-4 w-4 mr-1' />
								{[
									selectedPOI.neighborhood,
									selectedPOI.district,
									selectedPOI.city,
								]
									.filter(Boolean)
									.join(', ')}
							</div>
							{selectedPOI.category && (
								<div className='text-xs text-green-600 mt-1'>
									{selectedPOI.category}
									{selectedPOI.subcategory
										? ` • ${selectedPOI.subcategory}`
										: ''}
								</div>
							)}
						</div>
						<button
							onClick={() => {
								setSelectedPOI(null);
								setSearchQuery('');
								setValidationStatus('none');
								setValidationMessage('');
								onPOISelected({} as POISearchResult);
								onPOIValidated(null);
							}}
							className='text-green-600 hover:text-green-800'>
							<FiX className='h-5 w-5' />
						</button>
					</div>
				</div>
			)}
		</div>
	);
};

/**
 * Debounce utility function
 */
function debounce<T extends (...args: unknown[]) => unknown>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout | null = null;

	return (...args: Parameters<T>) => {
		if (timeout) {
			clearTimeout(timeout);
		}

		timeout = setTimeout(() => {
			func(...args);
		}, wait);
	};
}

// Helper to map external POISearchResult to local type
function mapToLocalPOI(poi: Record<string, unknown>): POISearchResult {
	return {
		id: String(poi.poi_id ?? poi.id ?? ''),
		name: String(poi.name ?? ''),
		type: String(poi.poi_type ?? poi.type ?? ''),
		city: typeof poi.city === 'string' ? poi.city : undefined,
		district: typeof poi.district === 'string' ? poi.district : undefined,
		neighborhood:
			typeof poi.neighborhood === 'string' ? poi.neighborhood : undefined,
		address: typeof poi.address === 'string' ? poi.address : undefined,
		poi_id: poi.poi_id != null ? String(poi.poi_id) : undefined,
		poi_type: typeof poi.poi_type === 'string' ? poi.poi_type : undefined,
		category: typeof poi.category === 'string' ? poi.category : undefined,
		subcategory:
			typeof poi.subcategory === 'string' ? poi.subcategory : undefined,
	};
}
