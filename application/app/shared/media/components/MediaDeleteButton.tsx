/** @format */

'use client';

import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { FiTrash2 } from 'react-icons/fi';

interface MediaDeleteButtonProps {
	mediaId: string;
	onDeleteSuccess?: () => void;
	onDeleteError?: (error: string) => void;
	className?: string;
	size?: 'small' | 'medium' | 'large';
	variant?: 'icon' | 'button';
	confirmMessage?: string;
}

export const MediaDeleteButton: React.FC<MediaDeleteButtonProps> = ({
	mediaId,
	onDeleteSuccess,
	onDeleteError,
	className = '',
	size = 'medium',
	variant = 'icon',
	confirmMessage = 'Are you sure you want to delete this media? This action cannot be undone.',
}) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const [showConfirm, setShowConfirm] = useState(false);
	const [mounted, setMounted] = useState(false);

	// Ensure component is mounted before rendering portal
	React.useEffect(() => {
		setMounted(true);
	}, []);

	const sizeClasses = {
		small: 'w-6 h-6 p-1',
		medium: 'w-8 h-8 p-1.5',
		large: 'w-10 h-10 p-2',
	};

	const iconSizes = {
		small: 'w-3 h-3',
		medium: 'w-4 h-4',
		large: 'w-5 h-5',
	};

	const handleDelete = async () => {
		if (!mediaId) return;

		setIsDeleting(true);
		try {
			const response = await fetch('/api/media/delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ mediaId }),
			});

			const result = await response.json();

			if (result.success) {
				onDeleteSuccess?.();
				setShowConfirm(false);
			} else {
				throw new Error(result.error || 'Delete failed');
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Delete failed';
			onDeleteError?.(errorMessage);
		} finally {
			setIsDeleting(false);
		}
	};

	// Modal component that renders in a portal
	const ConfirmationModal = () => {
		if (!mounted || !showConfirm) return null;

		const modalContent = (
			<div
				className='fixed inset-0 bg-gradient-to-br from-black/40 via-black/60 to-black/80 backdrop-blur-sm flex items-center justify-center z-[9999] p-4'
				onClick={() => setShowConfirm(false)}>
				<div
					className='bg-gradient-to-br from-white via-white to-gray-50 rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl border border-gray-100 transform transition-all duration-300 scale-100 hover:scale-[1.02]'
					onClick={(e) => e.stopPropagation()}>
					<div className='text-center'>
						<div className='w-16 h-16 bg-gradient-to-br from-red-100 via-red-50 to-pink-50 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg'>
							<FiTrash2 className='w-8 h-8 text-red-600' />
						</div>
						<h3 className='text-xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-2'>
							Delete Media
						</h3>
						<p className='text-gray-600 mb-8 leading-relaxed'>
							{confirmMessage}
						</p>
						<div className='flex gap-3'>
							<button
								onClick={() => setShowConfirm(false)}
								className='flex-1 px-6 py-3 text-gray-700 bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 hover:from-gray-100 hover:via-gray-200 hover:to-gray-100 rounded-xl font-medium transition-all duration-200 border border-gray-200 hover:border-gray-300'
								disabled={isDeleting}>
								Cancel
							</button>
							<button
								onClick={handleDelete}
								disabled={isDeleting}
								className='flex-1 px-6 py-3 bg-gradient-to-r from-red-500 via-red-600 to-red-500 hover:from-red-600 hover:via-red-700 hover:to-red-600 text-white rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg hover:shadow-xl'>
								{isDeleting ? (
									<>
										<div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
										Deleting...
									</>
								) : (
									<>
										<FiTrash2 className='w-4 h-4' />
										Delete
									</>
								)}
							</button>
						</div>
					</div>
				</div>
			</div>
		);

		// Use portal if available, otherwise render directly
		if (typeof document !== 'undefined') {
			return createPortal(modalContent, document.body);
		}
		return modalContent;
	};

	if (variant === 'button') {
		return (
			<>
				<button
					onClick={() => setShowConfirm(true)}
					disabled={isDeleting}
					className={`inline-flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-red-500 via-red-600 to-red-500 hover:from-red-600 hover:via-red-700 hover:to-red-600 text-white rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg ${className}`}>
					<FiTrash2 className={iconSizes[size]} />
					<span>Delete</span>
				</button>

				<ConfirmationModal />
			</>
		);
	}

	// Icon variant
	return (
		<>
			<button
				onClick={() => setShowConfirm(true)}
				disabled={isDeleting}
				className={`${sizeClasses[size]} bg-gradient-to-br from-red-500 via-red-600 to-red-500 hover:from-red-600 hover:via-red-700 hover:to-red-600 text-white rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-md hover:shadow-lg hover:scale-105 ${className}`}
				title='Delete media'>
				{isDeleting ? (
					<div className='animate-spin rounded-full h-3 w-3 border-b-2 border-white'></div>
				) : (
					<FiTrash2 className={iconSizes[size]} />
				)}
			</button>

			<ConfirmationModal />
		</>
	);
};

export default MediaDeleteButton;
