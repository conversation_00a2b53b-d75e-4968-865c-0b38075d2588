/** @format */

'use client';

import React, { useCallback, useRef, useState } from 'react';
import {
	<PERSON><PERSON>lertCircle,
	<PERSON>Check,
	FiImage,
	FiLoader,
	FiUpload,
	FiVideo,
	FiX,
} from 'react-icons/fi';
import { POISearchResult } from '../types'; // FIXED: use local definition for POISearchResult
import { POISearchComponent } from './POISearchComponent';

// Import UploadResult from shared types
import { type UploadResult } from '../types';

/**
 * Upload file interface
 */
interface UploadFile {
	id: string;
	file: File;
	preview?: string;
	status: 'pending' | 'uploading' | 'success' | 'error';
	progress: number;
	error?: string;
	result?: UploadResult;
}

/**
 * Media upload component props
 */
interface MediaUploadComponentProps {
	uploadType: 'profile' | 'poi_media';
	onUploadComplete?: (results: UploadResult[]) => void;
	onUploadError?: (error: string) => void;
	maxFiles?: number;
	acceptedTypes?: string[];
	maxFileSize?: number; // in bytes
	className?: string;
	showPOISearch?: boolean;
	preSelectedPOI?: {
		poi_id: string | number;
		poi_type: string;
		name: string;
	};
}

/**
 * Media upload component with drag-and-drop functionality
 */
export const MediaUploadComponent: React.FC<MediaUploadComponentProps> = ({
	uploadType,
	onUploadComplete,
	onUploadError,
	maxFiles = uploadType === 'profile' ? 1 : 10,
	acceptedTypes = [
		'image/jpeg',
		'image/png',
		'image/webp',
		'image/gif',
		'video/mp4',
		'video/webm',
	],
	maxFileSize = uploadType === 'profile' ? 5 * 1024 * 1024 : 20 * 1024 * 1024, // 5MB for profile, 20MB for posts
	className = '',
	showPOISearch = uploadType === 'poi_media',
	preSelectedPOI,
}) => {
	const [files, setFiles] = useState<UploadFile[]>([]);
	const [isDragOver, setIsDragOver] = useState(false);
	const [selectedPOI, setSelectedPOI] = useState<POISearchResult | null>(
		preSelectedPOI
			? mapToLocalPOI({
					id: String(preSelectedPOI.poi_id),
					name: preSelectedPOI.name,
					type: String(preSelectedPOI.poi_type),
					poi_id: String(preSelectedPOI.poi_id),
					poi_type: String(preSelectedPOI.poi_type),
			  })
			: null
	);
	const [isUploading, setIsUploading] = useState(false);
	const [globalError, setGlobalError] = useState<string>('');
	const fileInputRef = useRef<HTMLInputElement>(null);

	/**
	 * Generate file preview URL
	 */
	const generatePreview = useCallback((file: File): Promise<string> => {
		return new Promise((resolve) => {
			if (file.type.startsWith('image/')) {
				const reader = new FileReader();
				reader.onload = (e) => resolve(e.target?.result as string);
				reader.readAsDataURL(file);
			} else {
				resolve(''); // No preview for videos
			}
		});
	}, []);

	/**
	 * Validate file
	 */
	const validateFile = useCallback(
		(file: File): string | null => {
			// Check file type
			if (!acceptedTypes.includes(file.type)) {
				return `File type ${file.type} is not supported`;
			}

			// Check file size
			if (file.size > maxFileSize) {
				const maxSizeMB = (maxFileSize / (1024 * 1024)).toFixed(1);
				return `File size exceeds ${maxSizeMB}MB limit`;
			}

			// Check file name
			if (file.name.length > 255) {
				return 'File name is too long';
			}

			return null;
		},
		[acceptedTypes, maxFileSize]
	);

	/**
	 * Add files to upload queue
	 */
	const addFiles = useCallback(
		async (newFiles: FileList | File[]) => {
			const fileArray = Array.from(newFiles);

			// Check max files limit
			if (files.length + fileArray.length > maxFiles) {
				setGlobalError(`Maximum ${maxFiles} files allowed`);
				return;
			}

			const uploadFiles: UploadFile[] = [];

			for (const file of fileArray) {
				const validationError = validateFile(file);

				if (validationError) {
					setGlobalError(validationError);
					continue;
				}

				const preview = await generatePreview(file);

				uploadFiles.push({
					id: `${Date.now()}-${Math.random()}`,
					file,
					preview,
					status: 'pending',
					progress: 0,
				});
			}

			setFiles((prev) => [...prev, ...uploadFiles]);
			setGlobalError('');
		},
		[files.length, maxFiles, validateFile, generatePreview]
	);

	/**
	 * Remove file from queue
	 */
	const removeFile = useCallback((fileId: string) => {
		setFiles((prev) => prev.filter((f) => f.id !== fileId));
	}, []);

	/**
	 * Handle drag events
	 */
	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(false);
	}, []);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			setIsDragOver(false);

			const droppedFiles = e.dataTransfer.files;
			if (droppedFiles.length > 0) {
				addFiles(droppedFiles);
			}
		},
		[addFiles]
	);

	/**
	 * Handle file input change
	 */
	const handleFileInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const selectedFiles = e.target.files;
			if (selectedFiles && selectedFiles.length > 0) {
				addFiles(selectedFiles);
			}
			// Reset input value to allow selecting the same file again
			e.target.value = '';
		},
		[addFiles]
	);

	/**
	 * Upload single file
	 */
	const uploadFile = useCallback(
		async (uploadFile: UploadFile): Promise<UploadResult> => {
			const formData = new FormData();
			formData.append('file', uploadFile.file);
			formData.append('uploadType', uploadType);

			if (uploadType === 'poi_media' && selectedPOI) {
				// Use poi_id if available, otherwise fall back to id
				const poiId = selectedPOI.poi_id || selectedPOI.id;
				const poiType = selectedPOI.poi_type || selectedPOI.type || 'official';

				if (poiId) formData.append('poiId', poiId);
				if (poiType) formData.append('poiType', poiType);
			}

			// Update file status
			setFiles((prev) =>
				prev.map((f) =>
					f.id === uploadFile.id
						? { ...f, status: 'uploading' as const, progress: 0 }
						: f
				)
			);

			try {
				const response = await fetch('/api/media/upload', {
					method: 'POST',
					body: formData,
				});

				const result = await response.json();

				if (result.success) {
					setFiles((prev) =>
						prev.map((f) =>
							f.id === uploadFile.id
								? { ...f, status: 'success' as const, progress: 100, result }
								: f
						)
					);
					return result;
				} else {
					throw new Error(result.error || 'Upload failed');
				}
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : 'Upload failed';
				setFiles((prev) =>
					prev.map((f) =>
						f.id === uploadFile.id
							? { ...f, status: 'error' as const, error: errorMessage }
							: f
					)
				);
				throw error;
			}
		},
		[uploadType, selectedPOI]
	);

	/**
	 * Upload all files
	 */
	const handleUpload = useCallback(async () => {
		if (files.length === 0) {
			setGlobalError('No files selected');
			return;
		}

		if (uploadType === 'poi_media' && !selectedPOI) {
			setGlobalError('Please select a POI before uploading');
			return;
		}

		setIsUploading(true);
		setGlobalError('');

		try {
			const results = [];
			const pendingFiles = files.filter((f) => f.status === 'pending');

			for (const file of pendingFiles) {
				try {
					const result = await uploadFile(file);
					results.push(result);
				} catch (error) {
					console.error('Upload error for file:', file.file.name, error);
				}
			}

			const successfulUploads = results.filter((r) => r.success);

			if (successfulUploads.length > 0) {
				onUploadComplete?.(successfulUploads);
			}

			if (successfulUploads.length < pendingFiles.length) {
				const failedCount = pendingFiles.length - successfulUploads.length;
				setGlobalError(`${failedCount} file(s) failed to upload`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Upload failed';
			setGlobalError(errorMessage);
			onUploadError?.(errorMessage);
		} finally {
			setIsUploading(false);
		}
	}, [
		files,
		uploadType,
		selectedPOI,
		uploadFile,
		onUploadComplete,
		onUploadError,
	]);

	/**
	 * Clear all files
	 */
	const clearFiles = useCallback(() => {
		setFiles([]);
		setGlobalError('');
	}, []);

	/**
	 * Get file type icon
	 */
	const getFileIcon = (file: File) => {
		if (file.type.startsWith('image/')) {
			return <FiImage className='h-8 w-8' />;
		} else if (file.type.startsWith('video/')) {
			return <FiVideo className='h-8 w-8' />;
		}
		return <FiUpload className='h-8 w-8' />;
	};

	/**
	 * Format file size
	 */
	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	};

	// Helper to map any POI object to local POISearchResult type
	function mapToLocalPOI(poi: Record<string, unknown>): POISearchResult {
		return {
			id: String(poi.poi_id ?? poi.id ?? ''),
			name: String(poi.name ?? ''),
			type: String(poi.poi_type ?? poi.type ?? ''),
			city: typeof poi.city === 'string' ? poi.city : undefined,
			district: typeof poi.district === 'string' ? poi.district : undefined,
			neighborhood:
				typeof poi.neighborhood === 'string' ? poi.neighborhood : undefined,
			address: typeof poi.address === 'string' ? poi.address : undefined,
			poi_id: poi.poi_id != null ? String(poi.poi_id) : undefined,
			poi_type: typeof poi.poi_type === 'string' ? poi.poi_type : undefined,
			category: typeof poi.category === 'string' ? poi.category : undefined,
			subcategory:
				typeof poi.subcategory === 'string' ? poi.subcategory : undefined,
		};
	}

	return (
		<div className={`space-y-6 ${className}`}>
			{/* POI Search (for poi_media uploads) */}
			{showPOISearch && !preSelectedPOI && (
				<div>
					<label className='block text-sm font-medium text-gray-700 mb-2'>
						Select POI *
					</label>
					<POISearchComponent
						onPOISelected={(poi) => setSelectedPOI(poi)} // POISearchComponent already returns the correct type
						onPOIValidated={(poi) => setSelectedPOI(poi)} // POISearchComponent already returns the correct type
						placeholder='Search for a POI to upload media...'
						required={true}
					/>
				</div>
			)}

			{/* Pre-selected POI Display */}
			{preSelectedPOI && (
				<div className='mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
					<div className='flex items-center space-x-2'>
						<span className='text-sm font-medium text-blue-800'>
							Uploading to:
						</span>
						<span className='text-sm text-blue-700'>{preSelectedPOI.name}</span>
					</div>
				</div>
			)}

			{/* Upload Area */}
			<div
				className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${files.length > 0 ? 'border-green-500 bg-green-50' : ''}
        `}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				onDrop={handleDrop}>
				<input
					ref={fileInputRef}
					type='file'
					multiple={maxFiles > 1}
					accept={acceptedTypes.join(',')}
					onChange={handleFileInputChange}
					className='hidden'
				/>

				<div className='space-y-4'>
					<FiUpload className='h-12 w-12 mx-auto text-gray-400' />

					<div>
						<p className='text-lg font-medium text-gray-900'>
							{isDragOver ? 'Drop files here' : 'Drag and drop files here'}
						</p>
						<p className='text-sm text-gray-500'>
							or{' '}
							<button
								type='button'
								onClick={() => fileInputRef.current?.click()}
								className='text-blue-600 hover:text-blue-500 font-medium'>
								browse files
							</button>
						</p>
					</div>

					<div className='text-xs text-gray-400'>
						<p>
							Max {maxFiles} file(s), up to {formatFileSize(maxFileSize)} each
						</p>
						<p>
							Supported:{' '}
							{acceptedTypes.map((type) => type.split('/')[1]).join(', ')}
						</p>
					</div>
				</div>
			</div>

			{/* Global Error */}
			{globalError && (
				<div className='flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg'>
					<FiAlertCircle className='h-5 w-5' />
					<span className='text-sm'>{globalError}</span>
				</div>
			)}

			{/* File List */}
			{files.length > 0 && (
				<div className='space-y-4'>
					<div className='flex items-center justify-between'>
						<h3 className='text-lg font-medium text-gray-900'>
							Files ({files.length})
						</h3>
						<button
							onClick={clearFiles}
							className='text-sm text-gray-500 hover:text-gray-700'>
							Clear all
						</button>
					</div>

					<div className='space-y-3'>
						{files.map((uploadFile) => (
							<div
								key={uploadFile.id}
								className='flex items-center space-x-4 p-4 border border-gray-200 rounded-lg'>
								{/* File Preview/Icon */}
								<div className='flex-shrink-0'>
									{uploadFile.preview ? (
										<img
											src={uploadFile.preview}
											alt={uploadFile.file.name}
											className='h-12 w-12 object-cover rounded'
										/>
									) : (
										<div className='h-12 w-12 flex items-center justify-center bg-gray-100 rounded'>
											{getFileIcon(uploadFile.file)}
										</div>
									)}
								</div>

								{/* File Info */}
								<div className='flex-1 min-w-0'>
									<p className='text-sm font-medium text-gray-900 truncate'>
										{uploadFile.file.name}
									</p>
									<p className='text-sm text-gray-500'>
										{formatFileSize(uploadFile.file.size)}
									</p>

									{/* Progress Bar */}
									{uploadFile.status === 'uploading' && (
										<div className='mt-2'>
											<div className='bg-gray-200 rounded-full h-2'>
												<div
													className='bg-blue-600 h-2 rounded-full transition-all duration-300'
													style={{ width: `${uploadFile.progress}%` }}
												/>
											</div>
										</div>
									)}

									{/* Error Message */}
									{uploadFile.status === 'error' && uploadFile.error && (
										<p className='text-sm text-red-600 mt-1'>
											{uploadFile.error}
										</p>
									)}
								</div>

								{/* Status Icon */}
								<div className='flex-shrink-0'>
									{uploadFile.status === 'pending' && (
										<div className='h-6 w-6 rounded-full bg-gray-200' />
									)}
									{uploadFile.status === 'uploading' && (
										<FiLoader className='h-6 w-6 text-blue-600 animate-spin' />
									)}
									{uploadFile.status === 'success' && (
										<FiCheck className='h-6 w-6 text-green-600' />
									)}
									{uploadFile.status === 'error' && (
										<FiAlertCircle className='h-6 w-6 text-red-600' />
									)}
								</div>

								{/* Remove Button */}
								<button
									onClick={() => removeFile(uploadFile.id)}
									disabled={uploadFile.status === 'uploading'}
									className='flex-shrink-0 text-gray-400 hover:text-gray-600 disabled:opacity-50'>
									<FiX className='h-5 w-5' />
								</button>
							</div>
						))}
					</div>

					{/* Upload Button */}
					<div className='flex justify-end space-x-3'>
						<button
							onClick={clearFiles}
							disabled={isUploading}
							className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50'>
							Clear
						</button>
						<button
							onClick={handleUpload}
							disabled={
								isUploading ||
								files.length === 0 ||
								(uploadType === 'poi_media' && !selectedPOI)
							}
							className='px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed'>
							{isUploading ? (
								<>
									<FiLoader className='inline h-4 w-4 mr-2 animate-spin' />
									Uploading...
								</>
							) : (
								`Upload ${files.length} file${files.length !== 1 ? 's' : ''}`
							)}
						</button>
					</div>
				</div>
			)}
		</div>
	);
};
