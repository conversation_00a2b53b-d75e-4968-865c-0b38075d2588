/**
 * Media Types
 *
 * Shared TypeScript types and interfaces for media functionality
 *
 * @format
 */

// Media upload types
export interface MediaUploadOptions {
	uploadType: 'profile_picture' | 'poi_media' | 'cover_photo';
	poiId?: string;
	poiType?: string;
	caption?: string;
	maxFileSize?: number;
	allowedTypes?: string[];
}

export interface MediaUploadResult {
	success: boolean;
	mediaUrl?: string;
	thumbnailUrl?: string;
	message?: string;
	error?: string;
}

// Comprehensive upload result interface supporting multiple API formats
export interface UploadResult {
	success: boolean;
	uploadType?: 'profile' | 'poi_media' | 'cover_photo';
	mediaUrl?: string;
	thumbnails?: string[];
	mediaType?: string;
	warnings?: string[];
	media?: {
		id: string;
		poi_id: string;
		user_id: string;
		media_type: 'photo' | 'video';
		media_url: string;
		thumbnail_url?: string;
		caption?: string;
		metadata?: Record<string, unknown>;
		is_verified: boolean;
		created_at: string;
	};
	poi?: {
		id: string;
		name: string;
		location: string;
	};
	error?: string;
	// Support for legacy formats
	secure_url?: string;
	public_id?: string;
}

// Media item types - unified interface supporting multiple API formats
export interface MediaItem {
	id: string;
	userId?: string;
	user_id?: string;
	// Support both camelCase and snake_case for API compatibility
	mediaUrl?: string;
	media_url?: string;
	thumbnailUrl?: string;
	thumbnail_url?: string;
	mediaType?: string;
	media_type?: 'photo' | 'video' | string;
	caption?: string;
	poiId?: string;
	poi_id?: string;
	poiType?: string;
	poi_type?: string;
	createdAt?: string;
	created_at?: string;
	updatedAt?: string;
	updated_at?: string;
	uploadedByUsername?: string;
	uploaded_by_username?: string;
	uploadedByName?: string;
	uploaded_by_name?: string;
	likeCount?: number;
	like_count?: number;
	favoriteCount?: number;
	favorite_count?: number;
	// Additional properties for different contexts
	file_path?: string;
	file_type?: string;
	poi_name?: string;
	poi_city?: string;
	poi_district?: string;
	poi_category?: string;
	poi_subcategory?: string;
	is_verified?: boolean;
	metadata?: Record<string, unknown>;
}

// POI search types
export interface POISearchResult {
	id: string;
	name: string;
	type: string;
	city?: string;
	district?: string;
	neighborhood?: string;
	address?: string;
	poi_id?: string; // FIXED: optional for upload logic compatibility
	poi_type?: string; // FIXED: optional for upload logic compatibility
	category?: string; // FIXED: for POISearchComponent compatibility
	subcategory?: string; // FIXED: for POISearchComponent compatibility
}

// FIXED: Add BasePOI type for shared POI usage
export interface BasePOI {
	id: string | number;
	name: string;
	type?: string;
	city?: string;
	district?: string;
	neighborhood?: string;
	address?: string;
	category?: string;
	subcategory?: string;
	latitude?: number;
	longitude?: number;
	[key: string]: unknown;
}

// Media validation types
export interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}
