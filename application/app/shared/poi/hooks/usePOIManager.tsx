/** @format */

'use client';

import { useCallback, useEffect, useState } from 'react';

export interface POI {
	poi_type: 'official' | 'user_temp' | 'user_approved';
	poi_id?: number; // BIGINT from spatial_schema.pois.id
	temp_id?: number; // BIGINT from spatial_schema.user_pois_temp.id
	approved_id?: number; // BIGINT from spatial_schema.user_pois_approved.id
	name: string;
	category: string;
	subcategory?: string;
	latitude: number;
	longitude: number;
	city?: string;
	district?: string;
	country?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	distance_km?: number;
	neighborhood?: string;
}

export interface POIFilters {
	categories: string[];
	subcategories?: string[];
	bounds?: string;
	radius?: number;
	limit?: number;
}

interface UsePOIManagerProps {
	userId?: string;
	userLocation?: { latitude: number; longitude: number } | null;
	autoLoad?: boolean;
}

export const usePOIManager = ({
	userId,
	userLocation,
	autoLoad = false,
}: UsePOIManagerProps = {}) => {
	const [pois, setPois] = useState<POI[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [filters, setFilters] = useState<POIFilters>({
		categories: [],
		subcategories: [],
	});
	const [favorites, setFavorites] = useState<POI[]>([]);

	// Load POIs based on current filters
	const loadPOIs = useCallback(
		async (customFilters?: Partial<POIFilters>) => {
			setLoading(true);
			setError(null);

			try {
				const currentFilters = { ...filters, ...customFilters };
				const params = new URLSearchParams();

				if (currentFilters.categories.length > 0) {
					params.append('categories', currentFilters.categories.join(','));
				}

				if (
					currentFilters.subcategories &&
					currentFilters.subcategories.length > 0
				) {
					params.append(
						'subcategories',
						currentFilters.subcategories.join(',')
					);
				}

				if (currentFilters.bounds) {
					params.append('bounds', currentFilters.bounds);
				}

				if (currentFilters.limit) {
					params.append('limit', currentFilters.limit.toString());
				}

				if (userId) {
					params.append('userId', userId);
				}

				const response = await fetch(`/api/pois/filter?${params.toString()}`);
				const data = await response.json();

				if (data.success) {
					setPois(data.pois);
				} else {
					setError(data.error || 'Failed to load POIs');
				}
			} catch (err) {
				setError('Network error while loading POIs');
				console.error('Error loading POIs:', err);
			} finally {
				setLoading(false);
			}
		},
		[filters, userId]
	);

	// Load nearby POIs based on user location
	const loadNearbyPOIs = useCallback(
		async (
			location?: { latitude: number; longitude: number },
			radius: number = 1000,
			customFilters?: Partial<POIFilters>
		) => {
			const targetLocation = location || userLocation;
			if (!targetLocation) {
				setError('Location is required for nearby search');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const currentFilters = { ...filters, ...customFilters };
				const params = new URLSearchParams({
					lat: targetLocation.latitude.toString(),
					lng: targetLocation.longitude.toString(),
					radius: radius.toString(),
				});

				if (currentFilters.categories.length > 0) {
					params.append('categories', currentFilters.categories.join(','));
				}

				if (
					currentFilters.subcategories &&
					currentFilters.subcategories.length > 0
				) {
					params.append(
						'subcategories',
						currentFilters.subcategories.join(',')
					);
				}

				if (currentFilters.limit) {
					params.append('limit', currentFilters.limit.toString());
				}

				if (userId) {
					params.append('userId', userId);
				}

				const response = await fetch(`/api/pois/nearby?${params.toString()}`);
				const data = await response.json();

				if (data.success) {
					setPois(data.pois);
				} else {
					setError(data.error || 'Failed to load nearby POIs');
				}
			} catch (err) {
				setError('Network error while loading nearby POIs');
				console.error('Error loading nearby POIs:', err);
			} finally {
				setLoading(false);
			}
		},
		[filters, userLocation, userId]
	);

	// Load user's favorite POIs
	const loadFavorites = useCallback(async () => {
		if (!userId) return;

		try {
			const response = await fetch(`/api/pois/favorite?userId=${userId}`);
			const data = await response.json();

			if (data.success) {
				setFavorites(data.favorites);
			}
		} catch (err) {
			console.error('Error loading favorites:', err);
		}
	}, [userId]);

	// Toggle favorite status of a POI
	const toggleFavorite = useCallback(
		async (poi: POI) => {
			if (!userId) return;

			try {
				const response = await fetch('/api/pois/interactions', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						userId,
						poiId: poi.poi_id,
						userPoiTempId: poi.temp_id,
						userPoiApprovedId: poi.approved_id,
						poiType: poi.poi_type,
						interactionType: 'favorite',
						action: poi.is_favorite ? 'remove' : 'add',
					}),
				});

				const data = await response.json();

				if (data.success) {
					// Update the POI in the current list
					setPois((currentPois) =>
						currentPois.map((p) =>
							p.poi_id === poi.poi_id &&
							p.temp_id === poi.temp_id &&
							p.approved_id === poi.approved_id
								? { ...p, is_favorite: !p.is_favorite }
								: p
						)
					);

					// Reload favorites
					loadFavorites();
				} else {
					setError(data.error || 'Failed to toggle favorite');
				}
			} catch (err) {
				setError('Network error while toggling favorite');
				console.error('Error toggling favorite:', err);
			}
		},
		[userId, loadFavorites]
	);

	// Submit a new POI
	const submitPOI = useCallback(
		async (poiData: {
			name: string;
			category: string;
			subcategory?: string;
			latitude: number;
			longitude: number;
			city?: string;
			district?: string;
			phone_number?: string;
			opening_hours?: string;
			description?: string;
			submission_notes?: string;
			submission_reason?: string;
			original_poi_id?: number;
		}) => {
			if (!userId) {
				setError('User ID is required to submit POIs');
				return false;
			}

			// Only require original_poi_id for submissions that are updates/reports of existing POIs
			const submissionReason = poiData.submission_reason || 'new_poi';
			if (submissionReason !== 'new_poi' && !poiData.original_poi_id) {
				setError('original_poi_id is required for POI updates and reports');
				return false;
			}
			try {
				// Prepare submission data - exclude original_poi_id for new POI submissions
				const submissionData = { userId, ...poiData };
				if (submissionReason === 'new_poi') {
					delete submissionData.original_poi_id;
				}

				const response = await fetch('/api/pois/submit', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(submissionData),
				});
				const data = await response.json();
				if (data.success) {
					loadPOIs();
					return true;
				} else {
					setError(data.error || 'Failed to submit POI');
					return false;
				}
			} catch (err) {
				setError('Network error while submitting POI');
				console.error('Error submitting POI:', err);
				return false;
			}
		},
		[userId]
	); // Removed loadPOIs from dependencies

	// Update filters and optionally reload
	const updateFilters = useCallback(
		(newFilters: Partial<POIFilters>, reload = false) => {
			setFilters((current) => ({ ...current, ...newFilters }));
			if (reload) {
				loadPOIs(newFilters);
			}
		},
		[]
	); // Removed loadPOIs from dependencies

	// Clear all filters
	const clearFilters = useCallback((reload = false) => {
		setFilters({ categories: [], subcategories: [] });
		if (reload) {
			loadPOIs({ categories: [], subcategories: [] });
		}
	}, []); // Removed loadPOIs from dependencies

	// Auto-load POIs when component mounts or dependencies change
	useEffect(() => {
		if (autoLoad && userLocation) {
			loadNearbyPOIs();
		}
	}, [autoLoad, userLocation]); // Removed loadNearbyPOIs from dependencies

	// Load favorites when userId changes
	useEffect(() => {
		if (userId) {
			loadFavorites();
		}
	}, [userId]); // Removed loadFavorites from dependencies

	return {
		// Data
		pois,
		favorites,
		filters,

		// State
		loading,
		error,

		// Actions
		loadPOIs,
		loadNearbyPOIs,
		loadFavorites,
		toggleFavorite,
		submitPOI,
		updateFilters,
		clearFilters,

		// Utilities
		clearError: () => setError(null),
		hasPOIs: pois.length > 0,
		hasFilters:
			filters.categories.length > 0 ||
			(filters.subcategories && filters.subcategories.length > 0),
		filteredCount: pois.length,
	};
};
