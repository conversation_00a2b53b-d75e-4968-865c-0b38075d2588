/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import {
	FaClock,
	FaHeart,
	FaInfoCircle,
	FaMapMarkerAlt,
	FaPhone,
	FaRegHeart,
} from 'react-icons/fa';

interface POI {
	poi_type: 'official' | 'user_temp' | 'user_approved';
	poi_id?: number;
	temp_id?: number;
	approved_id?: number;
	name: string;
	category: string;
	subcategory?: string;
	latitude: number;
	longitude: number;
	city?: string;
	district?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	distance_km?: number;
	neighborhood?: string;
}

interface POIMarkersProps {
	pois: POI[];
	onFavoriteToggle?: (poi: POI) => void;
	userId?: string;
}

const POIMarkers: React.FC<POIMarkersProps> = ({
	pois,
	onFavoriteToggle,
	userId,
}) => {
	const [selectedPOI, setSelectedPOI] = useState<POI | null>(null);
	const [favoriteLoading, setFavoriteLoading] = useState<string | null>(null);

	// Generate markers data for the globe
	const getMarkersData = () => {
		return pois.map((poi, index) => ({
			id: `${poi.poi_type}_${
				poi.poi_id || poi.temp_id || poi.approved_id
			}_${index}`,
			lat: poi.latitude,
			lng: poi.longitude,
			size: 0.1,
			color: getMarkerColor(poi),
			poi: poi,
		}));
	};

	const getMarkerColor = (poi: POI) => {
		// Color coding based on POI type and favorite status
		if (poi.is_favorite) {
			return '#ff4444'; // Red for favorites
		}

		switch (poi.poi_type) {
			case 'official':
				return '#3b82f6'; // Blue for official POIs
			case 'user_approved':
				return '#10b981'; // Green for approved user POIs
			case 'user_temp':
				return '#f59e0b'; // Orange for temp user POIs
			default:
				return '#6b7280'; // Gray fallback
		}
	};

	const getCategoryIcon = (category: string, subcategory?: string) => {
		// First try to get icon by subcategory for more specific icons
		const subcategoryIcons: { [key: string]: string } = {
			restaurant: '🍽️',
			cafe: '☕',
			bar: '🍺',
			pub: '🍻',
			fast_food: '🍔',
			shop: '🛍️',
			supermarket: '🛒',
			convenience: '🏪',
			bank: '🏦',
			atm: '💳',
			pharmacy: '💊',
			hospital: '🏥',
			clinic: '🩺',
			dentist: '🦷',
			school: '🏫',
			university: '🎓',
			library: '📚',
			hotel: '🏨',
			hostel: '🏠',
			fuel: '⛽',
			parking: '🅿️',
			park: '🌳',
			playground: '🎮',
			sports_centre: '⚽',
			cinema: '🎬',
			theatre: '🎭',
		};

		// Category-level icons as fallback
		const categoryIcons: { [key: string]: string } = {
			'Food & Drink': '🍽️',
			Shopping: '🛍️',
			Entertainment: '🎬',
			'Health & Medical': '🏥',
			Education: '🎓',
			Transportation: '🚗',
			Services: '🏦',
			Tourism: '🗺️',
			'Sports & Recreation': '⚽',
			Other: '📍',
		};

		// Try subcategory first, then category, then default
		if (subcategory && subcategoryIcons[subcategory]) {
			return subcategoryIcons[subcategory];
		}
		return categoryIcons[category] || '📍';
	};

	const handleFavoriteToggle = async (poi: POI) => {
		if (!userId || !onFavoriteToggle) return;

		const poiKey = `${poi.poi_type}_${
			poi.poi_id || poi.temp_id || poi.approved_id
		}`;
		setFavoriteLoading(poiKey);

		try {
			const response = await fetch('/api/pois/interactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					poiId: poi.poi_id,
					userPoiTempId: poi.temp_id,
					userPoiApprovedId: poi.approved_id,
					poiType: poi.poi_type,
					interactionType: 'favorite',
					action: poi.is_favorite ? 'remove' : 'add',
				}),
			});

			const data = await response.json();

			if (data.success) {
				onFavoriteToggle(poi);
			} else {
				console.error('Failed to toggle favorite:', data.error);
			}
		} catch (error) {
			console.error('Error toggling favorite:', error);
		} finally {
			setFavoriteLoading(null);
		}
	};

	const formatDistance = (distanceKm?: number) => {
		if (!distanceKm) return '';
		if (distanceKm < 1) {
			return `${Math.round(distanceKm * 1000)}m away`;
		}
		return `${distanceKm.toFixed(1)}km away`;
	};

	const formatOpeningHours = (hours?: string) => {
		if (!hours) return 'Hours not available';
		// Simple formatting - could be enhanced
		return hours.length > 50 ? hours.substring(0, 50) + '...' : hours;
	};

	// POI Detail Modal
	const POIDetailModal = ({ poi }: { poi: POI }) => (
		<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'>
			<div
				className='bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden'
				style={{ border: `1px solid ${colors.ui.gray200}` }}>
				{/* Header */}
				<div
					className='flex items-start justify-between p-6 border-b'
					style={{ borderColor: colors.ui.gray200 }}>
					<div className='flex-1'>
						<div className='flex items-center gap-3 mb-2'>
							<span className='text-2xl'>
								{getCategoryIcon(poi.category, poi.subcategory)}
							</span>
							<div>
								<h3
									className='text-xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									{poi.name}
								</h3>
								<div className='flex items-center gap-2'>
									<span
										className='text-sm px-2 py-1 rounded-full capitalize'
										style={{
											backgroundColor: `${getMarkerColor(poi)}20`,
											color: getMarkerColor(poi),
										}}>
										{poi.subcategory
											? poi.subcategory.replace(/_/g, ' ')
											: poi.category}
									</span>
									{poi.poi_type !== 'official' && (
										<span
											className='text-xs px-2 py-1 rounded-full'
											style={{
												backgroundColor: colors.ui.gray200,
												color: colors.neutral.slateGray,
											}}>
											User{' '}
											{poi.poi_type === 'user_approved'
												? 'Approved'
												: 'Pending'}
										</span>
									)}
								</div>
							</div>
						</div>
					</div>
					<div className='flex items-center gap-2'>
						{userId && (
							<button
								onClick={() => handleFavoriteToggle(poi)}
								disabled={
									favoriteLoading ===
									`${poi.poi_type}_${
										poi.poi_id || poi.temp_id || poi.approved_id
									}`
								}
								className='p-2 rounded-lg transition-colors'
								style={{
									color: poi.is_favorite
										? colors.utility.error
										: colors.neutral.slateGray,
								}}>
								{favoriteLoading ===
								`${poi.poi_type}_${
									poi.poi_id || poi.temp_id || poi.approved_id
								}` ? (
									<div className='w-5 h-5 animate-spin border-2 border-current border-t-transparent rounded-full' />
								) : poi.is_favorite ? (
									<FaHeart className='w-5 h-5' />
								) : (
									<FaRegHeart className='w-5 h-5' />
								)}
							</button>
						)}
						<button
							onClick={() => setSelectedPOI(null)}
							className='p-2 rounded-lg hover:bg-gray-100 transition-colors'
							style={{ color: colors.neutral.slateGray }}>
							✕
						</button>
					</div>
				</div>

				{/* Content */}
				<div className='p-6 space-y-4'>
					{/* Location */}
					<div className='flex items-start gap-3'>
						<FaMapMarkerAlt
							className='w-4 h-4 mt-1'
							style={{ color: colors.brand.blue }}
						/>
						<div>
							<div
								className='font-medium'
								style={{ color: colors.neutral.textBlack }}>
								{poi.latitude.toFixed(6)}, {poi.longitude.toFixed(6)}
							</div>
							{(poi.city || poi.district) && (
								<div
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									{[poi.district, poi.city].filter(Boolean).join(', ')}
								</div>
							)}
							{poi.distance_km && (
								<div
									className='text-sm font-medium'
									style={{ color: colors.brand.blue }}>
									{formatDistance(poi.distance_km)}
								</div>
							)}
						</div>
					</div>

					{/* Phone */}
					{poi.phone_number && (
						<div className='flex items-center gap-3'>
							<FaPhone
								className='w-4 h-4'
								style={{ color: colors.brand.blue }}
							/>
							<a
								href={`tel:${poi.phone_number}`}
								className='font-medium hover:underline'
								style={{ color: colors.brand.blue }}>
								{poi.phone_number}
							</a>
						</div>
					)}

					{/* Opening Hours */}
					{poi.opening_hours && (
						<div className='flex items-start gap-3'>
							<FaClock
								className='w-4 h-4 mt-1'
								style={{ color: colors.brand.blue }}
							/>
							<div>
								<div
									className='font-medium'
									style={{ color: colors.neutral.textBlack }}>
									Opening Hours
								</div>
								<div
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									{formatOpeningHours(poi.opening_hours)}
								</div>
							</div>
						</div>
					)}

					{/* POI Type Info */}
					<div className='flex items-start gap-3'>
						<FaInfoCircle
							className='w-4 h-4 mt-1'
							style={{ color: colors.brand.blue }}
						/>
						<div>
							<div
								className='font-medium'
								style={{ color: colors.neutral.textBlack }}>
								Data Source
							</div>
							<div
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								{poi.poi_type === 'official' && 'Official OpenStreetMap data'}
								{poi.poi_type === 'user_approved' &&
									'User-contributed (verified)'}
								{poi.poi_type === 'user_temp' &&
									'User-contributed (pending review)'}
							</div>
						</div>
					</div>
				</div>

				{/* Actions */}
				<div
					className='flex gap-3 p-6 border-t bg-gray-50'
					style={{ borderColor: colors.ui.gray200 }}>
					<button
						onClick={() => {
							const url = `https://www.google.com/maps/dir/?api=1&destination=${poi.latitude},${poi.longitude}`;
							window.open(url, '_blank');
						}}
						className='flex-1 py-2 px-4 rounded-xl text-white font-medium transition-colors'
						style={{ backgroundColor: colors.brand.blue }}>
						Get Directions
					</button>
					<button
						onClick={() => {
							const url = `https://www.google.com/maps/search/?api=1&query=${poi.latitude},${poi.longitude}`;
							window.open(url, '_blank');
						}}
						className='py-2 px-4 rounded-xl border font-medium transition-colors'
						style={{
							borderColor: colors.ui.gray300,
							color: colors.neutral.textBlack,
						}}>
						View on Map
					</button>
				</div>
			</div>
		</div>
	);

	return (
		<>
			{/* POI Detail Modal */}
			{selectedPOI && <POIDetailModal poi={selectedPOI} />}

			{/* This component doesn't render visible markers directly */}
			{/* The markers data is used by the parent Globe component */}
			<div style={{ display: 'none' }}>
				{/* Hidden component that provides marker data to parent */}
				{JSON.stringify(getMarkersData())}
			</div>
		</>
	);
};

export default POIMarkers;

// Export the markers data function for use by parent components
export const usePOIMarkers = (pois: POI[]) => {
	return pois.map((poi, index) => ({
		id: `${poi.poi_type}_${
			poi.poi_id || poi.temp_id || poi.approved_id
		}_${index}`,
		lat: poi.latitude,
		lng: poi.longitude,
		size: 0.1,
		color: poi.is_favorite
			? '#ff4444'
			: poi.poi_type === 'official'
			? '#3b82f6'
			: poi.poi_type === 'user_approved'
			? '#10b981'
			: '#f59e0b',
		poi: poi,
	}));
};
