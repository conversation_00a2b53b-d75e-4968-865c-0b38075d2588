/**
 * POI-related constants and field definitions
 *
 * @format
 */

// Fields that can be edited when submitting or updating POIs
export const POI_EDITABLE_FIELDS = [
	'name',
	'name_en',
	'name_tr',
	'name_uk',
	'name_de',
	'name_ru',
	'name_ar',
	'category',
	'subcategory',
	'cuisine',
	'full_address',
	'street',
	'neighborhood',
	'district',
	'city',
	'province',
	'country',
	'phone_number',
	'opening_hours',
	'description',
	'latitude',
	'longitude',
];

// POI categories and subcategories structure
export const POI_CATEGORIES_DATA = {
	'Food & Drink': {
		subcategories: [
			'Cafe',
			'Restaurant',
			'Bar',
			'Street Food',
			'Fine Dining',
			'Bakery',
			'Fast Food',
			'Food Truck',
			'Brewery',
			'Wine Bar',
			'Cocktail Bar',
			'Rooftop Bar',
		],
	},
	'Cultural & Creative Experiences': {
		subcategories: [
			'Museum Visit',
			'Art Gallery Walk',
			'Theater',
			'Concert',
			'Festival',
			'Workshop',
			'Art Studio',
			'Cultural Center',
			'Opera House',
			'Dance Performance',
			'Poetry Reading',
			'Book Reading',
		],
	},
	'Sports & Fitness': {
		subcategories: [
			'Gym',
			'Yoga Studio',
			'Swimming',
			'Rock Climbing',
			'Tennis',
			'Basketball',
			'Football',
			'Running Track',
			'Martial Arts',
			'Pilates',
			'CrossFit',
			'Boxing',
		],
	},
	Entertainment: {
		subcategories: [
			'Cinema',
			'Bowling',
			'Karaoke',
			'Gaming',
			'Comedy Club',
			'Dance Club',
			'Arcade',
			'Mini Golf',
			'Escape Room',
			'Board Game Cafe',
			'Pool Hall',
			'Laser Tag',
		],
	},
	'Shopping & Markets': {
		subcategories: [
			'Mall',
			'Boutique',
			'Market',
			'Vintage Store',
			'Bookstore',
			'Electronics',
			'Farmers Market',
			'Antique Shop',
			'Jewelry Store',
			'Clothing Store',
			'Shoe Store',
			'Gift Shop',
		],
	},
	'Outdoor & Nature': {
		subcategories: [
			'Park',
			'Beach',
			'Hiking Trail',
			'Garden',
			'Lake',
			'Forest',
			'Mountain',
			'Waterfall',
			'Botanical Garden',
			'Nature Reserve',
			'Camping',
			'Picnic Area',
		],
	},
	'Wellness & Beauty': {
		subcategories: [
			'Spa',
			'Massage',
			'Hair Salon',
			'Nail Salon',
			'Beauty Clinic',
			'Wellness Center',
			'Meditation Center',
			'Sauna',
			'Hot Springs',
			'Acupuncture',
			'Chiropractor',
			'Dermatologist',
		],
	},
	Transportation: {
		subcategories: [
			'Metro Station',
			'Bus Stop',
			'Airport',
			'Train Station',
			'Taxi Stand',
			'Car Rental',
			'Bike Rental',
			'Ferry Terminal',
			'Parking',
			'Gas Station',
			'Electric Charging',
			'Scooter Rental',
		],
	},
};

// Helper functions to get categories and subcategories
export const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

export const getPOISubcategories = (category?: string): string[] => {
	if (!category) {
		// Return all subcategories if no category specified
		return Object.values(POI_CATEGORIES_DATA).flatMap(
			(cat) => cat.subcategories
		);
	}

	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
	return categoryData ? categoryData.subcategories : [];
};

// Legacy exports for backward compatibility
export const POI_CATEGORIES = getPOICategories();
export const POI_SUBCATEGORY_OPTIONS = getPOISubcategories();

// Submission reasons for POI reports/updates
export const POI_SUBMISSION_REASONS = [
	{ value: 'new_poi', label: 'New POI' },
	{ value: 'missing_info', label: 'Missing Information' },
	{ value: 'incorrect_info', label: 'Incorrect Information' },
	{ value: 'closed_business', label: 'Business Closed' },
	{ value: 'moved_location', label: 'Location Changed' },
];
