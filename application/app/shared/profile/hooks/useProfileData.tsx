/** @format */

'use client';

import { useCallback, useEffect, useState } from 'react';

export interface UserProfile {
	id: string;
	user_id: string;
	name: string;
	age: number;
	avatar_url?: string;
	profile_completed: boolean;
	allow_data_usage: boolean;
	preferences: Record<string, unknown>;
	timezone: string;
	total_locations_visited: number;
	total_reviews_written: number;
	total_photos_uploaded: number;
	adventurer_level: number;
	days_active: number;
	last_activity_date?: string;
	created_at: string;
	updated_at: string;
}

interface UseProfileDataProps {
	userId?: string;
	autoLoad?: boolean;
}

export const useProfileData = ({
	userId,
	autoLoad = true,
}: UseProfileDataProps = {}) => {
	const [profile, setProfile] = useState<UserProfile | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Load profile data from API
	const loadProfile = useCallback(
		async (targetUserId?: string) => {
			const userIdToUse = targetUserId || userId;

			if (!userIdToUse) {
				setError('User ID is required');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch(`/api/profile?userId=${userIdToUse}`, {
					credentials: 'include',
				});
				const data = await response.json();

				if (response.ok) {
					setProfile(data.profile);
				} else {
					setError(data.error || 'Failed to load profile');
				}

				// Note: Interaction counts (likes, visits, etc.) are now handled by useUserInteractionStats
				// to avoid duplicate API calls. This hook now only fetches profile-specific data.
			} catch (err) {
				setError('Network error while loading profile');
				console.error('Error loading profile:', err);
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Update profile data
	const updateProfile = useCallback(
		async (updates: Partial<UserProfile>) => {
			if (!userId) {
				setError('User ID is required');
				return false;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/profile', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify({
						userId,
						...updates,
					}),
				});

				const data = await response.json();

				if (response.ok) {
					setProfile(data.profile);
					return true;
				} else {
					setError(data.error || 'Failed to update profile');
					return false;
				}
			} catch (err) {
				setError('Network error while updating profile');
				console.error('Error updating profile:', err);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Auto-load profile when component mounts or userId changes
	useEffect(() => {
		if (autoLoad && userId) {
			loadProfile();
		}
	}, [autoLoad, userId]); // Removed loadProfile from dependencies

	// Calculate derived stats
	const getProfileStats = useCallback(() => {
		if (!profile) {
			return {
				favoriteLocations: 0,
				helpfulReviews: 0,
				photosShared: 0,
				locationsAdded: 0,
				creditsEarnedTotal: 0,
				totalLikes: 0,
				totalVisits: 0,
			};
		}

		return {
			favoriteLocations: 0, // Will be loaded from favorites API
			helpfulReviews: profile.total_reviews_written || 0,
			photosShared: profile.total_photos_uploaded || 0,
			locationsAdded: profile.total_locations_visited || 0,
			creditsEarnedTotal: 0, // Will be loaded from credits API
			totalLikes: 0, // Now handled by useUserInteractionStats
			totalVisits: 0, // Now handled by useUserInteractionStats
		};
	}, [profile]);

	return {
		// Data
		profile,
		profileStats: getProfileStats(),

		// State
		loading,
		error,

		// Actions
		loadProfile,
		updateProfile,

		// Utilities
		clearError: () => setError(null),
		hasProfile: !!profile,
		isProfileComplete: profile?.profile_completed || false,
	};
};
