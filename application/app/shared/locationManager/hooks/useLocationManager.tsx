/** @format */

'use client';

import {
	canRequestLocation,
	setLocationRequestInProgress,
} from 'app/shared/locationManager/utils/locationUtils';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface LocationData {
	latitude: number;
	longitude: number;
	accuracy?: number;
	timestamp: number;
	source: 'auto' | 'manual';
	label?: string;
	liveLocationEnabled?: boolean;
}

export interface LocationSetupStatus {
	hasSetup: boolean;
	setupChoice: 'auto' | 'manual' | 'none' | null;
	setupTimestamp: number;
}

export interface UseLocationManagerReturn {
	location: LocationData | null;
	setManualLocation: (lat: number, lng: number, label?: string) => void;
	requestAutoLocation: () => Promise<void>;
	clearLocation: () => void;
	toggleLiveLocation: (enabled: boolean) => void;
	isLoading: boolean;
	error: string | null;
	// Setup status
	setupStatus: LocationSetupStatus;
	markSetupComplete: (choice: 'auto' | 'manual' | 'none') => void;
	needsLocationSetup: () => boolean;
	isInitialized: boolean;
	hasLoadedFromStorage: boolean;
	// For API requests
	getLiveLocationFlag: () => boolean | null;
}

const STORAGE_KEY = 'wizlop_user_location';
const SETUP_STATUS_KEY = 'wizlop_location_setup_status';

export function useLocationManager(): UseLocationManagerReturn {
	const [location, setLocation] = useState<LocationData | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [setupStatus, setSetupStatus] = useState<LocationSetupStatus>({
		hasSetup: false,
		setupChoice: null,
		setupTimestamp: 0,
	});
	const [isInitialized, setIsInitialized] = useState(false);
	const [hasLoadedFromStorage, setHasLoadedFromStorage] = useState(false);
	const [shouldSaveToStorage, setShouldSaveToStorage] = useState(false);
	// Global lock to prevent simultaneous location requests
	const [isRequestingLocation, setIsRequestingLocation] = useState(false);
	const [hasHydrated, setHasHydrated] = useState(false);
	const hasInitializedRef = useRef(false);

	// Hydration effect - runs once on client mount
	useEffect(() => {
		setHasHydrated(true);
	}, []);

	// Listen for custom location update events
	useEffect(() => {
		if (typeof window === 'undefined') return;

		const handleLocationUpdate = (e: CustomEvent) => {
			const newLocation = e.detail;
			if (
				newLocation &&
				typeof newLocation.latitude === 'number' &&
				typeof newLocation.longitude === 'number'
			) {
				setLocation(newLocation);
			}
		};

		window.addEventListener(
			'locationUpdated',
			handleLocationUpdate as EventListener
		);
		return () =>
			window.removeEventListener(
				'locationUpdated',
				handleLocationUpdate as EventListener
			);
	}, []);

	// Load saved location and setup status from localStorage on mount (client-side only)
	useEffect(() => {
		// Ensure we're on the client side and hydrated, and not already initialized
		if (
			typeof window === 'undefined' ||
			!hasHydrated ||
			hasInitializedRef.current
		)
			return;

		hasInitializedRef.current = true;

		try {
			// Load location
			const saved = localStorage.getItem(STORAGE_KEY);

			if (saved) {
				const parsedLocation = JSON.parse(saved);

				// Validate the saved location
				if (
					parsedLocation &&
					typeof parsedLocation.latitude === 'number' &&
					typeof parsedLocation.longitude === 'number'
				) {
					setLocation(parsedLocation);
				}
			}

			// Load setup status
			const savedSetup = localStorage.getItem(SETUP_STATUS_KEY);

			if (savedSetup) {
				const parsedSetup = JSON.parse(savedSetup);

				if (parsedSetup && typeof parsedSetup.hasSetup === 'boolean') {
					setSetupStatus(parsedSetup);
				}
			}
		} catch (error) {
			console.warn('Failed to load saved location or setup status:', error);
		} finally {
			// Mark as initialized and loaded from storage
			setIsInitialized(true);
			setHasLoadedFromStorage(true);
		}
	}, [hasHydrated]);

	// Save location to localStorage whenever it changes (client-side only)
	useEffect(() => {
		// Ensure we're on the client side and have finished loading from storage
		if (typeof window === 'undefined' || !hasLoadedFromStorage) return;

		if (location) {
			try {
				localStorage.setItem(STORAGE_KEY, JSON.stringify(location));

				// Dispatch custom event to notify other location manager instances
				window.dispatchEvent(
					new CustomEvent('locationUpdated', { detail: location })
				);
			} catch (error) {
				console.error('Failed to save location:', error);
			}
		} else {
			// Only remove from localStorage if we explicitly cleared the location after loading
			localStorage.removeItem(STORAGE_KEY);

			// Dispatch custom event to notify other location manager instances
			window.dispatchEvent(
				new CustomEvent('locationUpdated', { detail: null })
			);
		}
	}, [location, hasLoadedFromStorage]);

	// Save setup status to localStorage only when explicitly requested (client-side only)
	useEffect(() => {
		// Ensure we're on the client side
		if (typeof window === 'undefined') return;

		if (shouldSaveToStorage) {
			try {
				localStorage.setItem(SETUP_STATUS_KEY, JSON.stringify(setupStatus));
				setShouldSaveToStorage(false); // Reset the flag
			} catch (error) {
				console.warn('Failed to save setup status:', error);
				setShouldSaveToStorage(false); // Reset the flag even on error
			}
		}
	}, [setupStatus, shouldSaveToStorage]);

	const setManualLocation = useCallback(
		(lat: number, lng: number, label?: string) => {
			// Only create location on client side to avoid hydration issues
			if (typeof window === 'undefined') return;

			const newLocation: LocationData = {
				latitude: lat,
				longitude: lng,
				timestamp: Date.now(),
				source: 'manual',
				label: label || `${lat.toFixed(4)}, ${lng.toFixed(4)}`,
				liveLocationEnabled: false, // Manual locations don't use live updates
			};
			setLocation(newLocation);
			setError(null);
		},
		[]
	);

	const requestAutoLocation = useCallback(async (): Promise<void> => {
		// Only run on client side to avoid hydration issues
		if (typeof window === 'undefined') return;

		// Use global location request state to prevent conflicts
		if (!canRequestLocation()) {
			return;
		}

		if (!navigator.geolocation) {
			setError('Geolocation not supported by this browser');
			return;
		}

		// Check if we have a recent location (within 5 minutes)
		if (
			location &&
			location.source === 'auto' &&
			Date.now() - location.timestamp < 300000
		) {
			console.log('Using recent location, skipping request');
			return;
		}

		setLocationRequestInProgress(true);
		setIsRequestingLocation(true);
		setIsLoading(true);
		setError(null);

		const options: PositionOptions = {
			enableHighAccuracy: false,
			timeout: 8000, // Reduced timeout to prevent hanging
			maximumAge: 300000, // 5 minutes
		};

		try {
			const position = await new Promise<GeolocationPosition>(
				(resolve, reject) => {
					navigator.geolocation.getCurrentPosition(resolve, reject, options);
				}
			);

			const newLocation: LocationData = {
				latitude: position.coords.latitude,
				longitude: position.coords.longitude,
				accuracy: position.coords.accuracy,
				timestamp: Date.now(),
				source: 'auto',
				label: 'Current Location',
				liveLocationEnabled: location?.liveLocationEnabled ?? true,
			};

			setLocation(newLocation);
			setError(null);
			console.log('Location successfully updated:', newLocation);
		} catch (geoError: unknown) {
			let errorMessage = 'Failed to get location';
			let shouldLogAsError = true;

			const error = geoError as GeolocationPositionError;
			if (error.code === 1) {
				errorMessage = 'Location access denied by user';
				shouldLogAsError = false; // User choice, not an error
			} else if (error.code === 2) {
				errorMessage = 'Location currently unavailable';
				shouldLogAsError = false; // Common condition, not an error
				console.info(
					'Location unavailable - this is normal if GPS is off or signal is weak'
				);
			} else if (error.code === 3) {
				errorMessage = 'Location request timed out';
				console.warn('Location request timed out - try again later');
			} else {
				console.error('Unexpected geolocation error:', geoError);
			}

			if (shouldLogAsError) {
				console.warn('Auto location request failed:', geoError);
			}

			setError(errorMessage);
		} finally {
			setIsLoading(false);
			setIsRequestingLocation(false);
			setLocationRequestInProgress(false);
		}
	}, [location, isRequestingLocation]);

	// Note: Removed automatic location requests to prevent conflicts
	// Location will only be requested when explicitly called by user actions

	const clearLocation = useCallback(() => {
		setLocation(null);
		setError(null);
		// Only access localStorage on client side
		if (typeof window !== 'undefined') {
			localStorage.removeItem(STORAGE_KEY);
		}
	}, []);

	const markSetupComplete = useCallback(
		(choice: 'auto' | 'manual' | 'none') => {
			// Only run on client side to avoid hydration issues
			if (typeof window === 'undefined') return;

			const newSetupStatus: LocationSetupStatus = {
				hasSetup: true,
				setupChoice: choice,
				setupTimestamp: Date.now(),
			};
			setSetupStatus(newSetupStatus);
			setShouldSaveToStorage(true); // Trigger save to localStorage
		},
		[]
	);

	const needsLocationSetup = useCallback(() => {
		// Don't trigger setup until we've loaded from localStorage
		if (!isInitialized) {
			return false;
		}

		return !setupStatus.hasSetup;
	}, [setupStatus.hasSetup, isInitialized]);

	const toggleLiveLocation = useCallback(
		(enabled: boolean) => {
			// Only run on client side to avoid hydration issues
			if (typeof window === 'undefined') return;

			if (location && location.source === 'auto') {
				const updatedLocation: LocationData = {
					...location,
					liveLocationEnabled: enabled,
					timestamp: Date.now(),
				};
				setLocation(updatedLocation);
			}
		},
		[location]
	);

	const getLiveLocationFlag = useCallback((): boolean | null => {
		if (!location) return null;
		if (location.source === 'auto') return location.liveLocationEnabled ?? true;
		if (location.source === 'manual') return false;
		return null;
	}, [location]);

	return {
		location,
		setManualLocation,
		requestAutoLocation,
		clearLocation,
		toggleLiveLocation,
		isLoading,
		error,
		setupStatus,
		markSetupComplete,
		needsLocationSetup,
		isInitialized,
		hasLoadedFromStorage,
		getLiveLocationFlag,
	};
}
