// Global location request state to prevent conflicts across the entire app
let globalLocationRequestInProgress = false;
let lastLocationRequestTime = 0;

const MIN_REQUEST_INTERVAL = 5000; // 5 seconds minimum between requests

export const canRequestLocation = (): boolean => {
  const now = Date.now();
  
  // Check if a request is already in progress
  if (globalLocationRequestInProgress) {
    console.log('Global location request already in progress');
    return false;
  }
  
  // Check if enough time has passed since last request
  if (now - lastLocationRequestTime < MIN_REQUEST_INTERVAL) {
    console.log('Too soon since last location request');
    return false;
  }
  
  return true;
};

export const setLocationRequestInProgress = (inProgress: boolean): void => {
  globalLocationRequestInProgress = inProgress;
  if (inProgress) {
    lastLocationRequestTime = Date.now();
  }
};

export const isLocationRequestInProgress = (): boolean => {
  return globalLocationRequestInProgress;
};

// Location validation and calculation utilities
export const validateCoordinates = (lat: number, lng: number): boolean => {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  )
};

export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

export const formatDistance = (distanceKm: number): string => {
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m`;
  }
  return `${distanceKm.toFixed(1)}km`;
};
