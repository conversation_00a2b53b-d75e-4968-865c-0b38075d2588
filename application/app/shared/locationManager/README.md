<!-- @format -->

# 🌍 Wizlop Location Manager

A comprehensive location management system for the Wizlop application that handles user location setup, storage, and real-time synchronization across components.

## 🎯 Overview

The Location Manager provides a centralized, robust system for handling user location across the entire Wizlop application. It ensures consistent location state, seamless cross-page synchronization, and reliable location setup flows.

## 📁 Architecture

```
app/shared/locationManager/
├── components/
│   └── LocationSetup.tsx          # Shared location setup overlay
├── hooks/
│   ├── useLocationManager.tsx     # Core location state management
│   └── useLocationSetup.tsx       # Location setup flow management
├── utils/
│   └── locationUtils.ts           # Utility functions
├── index.ts                       # Main exports
└── README.md                      # This documentation
```

## 🔧 Core Components

### 1. LocationSetup Component (Shared Overlay)

**Location**: `app/shared/locationManager/components/LocationSetup.tsx`

**Purpose**: A reusable location setup overlay that can be used across different pages.

**Answer to your question**: The overlay setup is **SHARED** across all pages - it's a single component in the shared folder that adapts its behavior based on props.

**Key Features**:

- **Single Shared Component**: One component used by chat, globe, and welcome pages
- **Contextual Adaptation**: Changes icon, title, and subtitle based on `pageContext`
- **Flexible Display**: Can be modal overlay or full screen
- **Auto Location**: Requests browser geolocation with user permission
- **Manual Location**: Redirects to settings page for manual coordinate entry
- **Skip Option**: Allows users to continue without location

**Page-Specific Customizations**:

**Chat Page Usage**:

```tsx
<LocationSetup
	isOpen={showLocationSetup}
	onComplete={handleLocationSetupComplete}
	pageContext='chat' // 💬 icon, chat-specific text
	isModal={true} // Modal overlay
	title='Location Setup Required'
	subtitle='To provide better location-based responses...'
/>
```

**Globe Page Usage**:

```tsx
<LocationSetup
	isOpen={showLocationSetup}
	onComplete={handleLocationSetupComplete}
	pageContext='globe' // 🌍 icon, globe-specific text
	isModal={false} // Full screen
	title='Location Setup Required'
	subtitle='To explore the interactive globe...'
	setCurrentLocation={setCurrentLocation} // Globe-specific callback
/>
```

**Welcome Page Usage**:

```tsx
<LocationSetup
	isOpen={showLocationSetup}
	onComplete={handleLocationSetupComplete}
	pageContext='welcome' // 🌟 icon, welcome text
	showCloseButton={false} // No close button on welcome
	redirectAfterSetup='/chat'
/>
```

### 2. useLocationManager Hook

**Location**: `app/shared/locationManager/hooks/useLocationManager.tsx`

**Purpose**: Core location state management with localStorage persistence and real-time sync.

**Key Features**:

- **Persistent Storage**: Automatically saves/loads from localStorage
- **Real-time Sync**: Custom event system for cross-component updates
- **Hydration Safe**: Prevents SSR/client hydration mismatches
- **Auto Location**: Browser geolocation with error handling
- **Manual Location**: Programmatic location setting
- **Setup Tracking**: Tracks whether user has completed location setup

**API**:

```tsx
const {
	location, // Current location data or null
	setManualLocation, // (lat, lng, label?) => void
	requestAutoLocation, // () => Promise<void>
	clearLocation, // () => void
	isLoading, // Boolean: location request in progress
	error, // String: error message or null
	setupStatus, // Setup completion status
	markSetupComplete, // (choice) => void
	isInitialized, // Boolean: hook initialized
	hasLoadedFromStorage, // Boolean: localStorage load complete
} = useLocationManager();
```

### 3. useLocationSetup Hook

**Location**: `app/shared/locationManager/hooks/useLocationSetup.tsx`

**Purpose**: Manages the location setup flow and modal visibility.

**Key Features**:

- **Auto-trigger**: Automatically shows setup if needed
- **Session Tracking**: Prevents repeated setup prompts
- **Completion Handling**: Manages setup completion flow

## 🔄 Real-time Synchronization System

The location manager implements a sophisticated real-time synchronization system that ensures location data is immediately available across all pages and components.

### How It Works

1. **Event-Driven Updates**: When location is saved to localStorage, a custom `locationUpdated` event is dispatched
2. **Cross-Component Listening**: All `useLocationManager` instances listen for this event
3. **Immediate Synchronization**: Location state updates across all components in real-time

```typescript
// When location is saved
window.dispatchEvent(new CustomEvent('locationUpdated', { detail: location }));

// All instances listen and update
window.addEventListener('locationUpdated', handleLocationUpdate);
```

### Benefits

- **Instant Updates**: Location changes are reflected immediately across all pages
- **No Page Refresh**: Users see location updates without needing to refresh
- **Consistent State**: All components always have the latest location data

## 🔧 Advanced Features

### 1. Hydration-Safe Initialization

The system handles SSR/client hydration gracefully:

- **`hasHydrated`**: Ensures localStorage is only accessed on the client
- **`hasLoadedFromStorage`**: Prevents premature location processing
- **`hasInitializedRef`**: Prevents duplicate initialization in React StrictMode

### 2. Race Condition Prevention

Components wait for proper initialization before processing location updates:

```tsx
useEffect(() => {
	if (!hasLoadedFromStorage) return; // Wait for storage load

	if (userLocation) {
		// Process location update
	}
}, [userLocation, hasLoadedFromStorage]);
```

### 3. Setup State Persistence

Location setup state is shared across pages:

- **Setup completion**: Persisted to localStorage
- **Setup triggers**: Shared across page navigation
- **Clean reset**: Automatically cleared when setup is completed

### 4. Dual Update Mechanisms

Critical components like the Globe page have redundant update mechanisms:

- **Primary**: Location state changes trigger updates
- **Backup**: Setup completion forces location updates
- **Ensures reliability**: Location updates never fail due to timing issues

## 📱 Page Integration

### Globe Page

```tsx
const {
	location: userLocation,
	setupStatus,
	isInitialized,
	hasLoadedFromStorage,
	setManualLocation,
} = useLocationManager();

// Wait for storage load before processing location updates
useEffect(() => {
	if (userLocation) {
		// Update globe markers and focus
	} else if (hasLoadedFromStorage) {
		// Clear markers only after confirming no location in storage
	}
}, [userLocation, hasLoadedFromStorage]);
```

### Chat Page

```tsx
const {
  location: userLocation,
  error: locationError,
  isLoading: locationLoading,
  requestAutoLocation,
} = useLocationManager()

// Pass to TopBar and UserDropdown with dynamic key for re-rendering
<UserDropdown
  key={userLocation ? `${userLocation.latitude}-${userLocation.longitude}-${userLocation.timestamp}` : 'no-location'}
  userLocation={userLocation}
  locationError={locationError}
  locationLoading={locationLoading}
  requestAutoLocation={requestAutoLocation}
/>
```

## 🔑 localStorage Keys

- `wizlop_user_location`: Current location data
- `wizlop_location_setup_status`: Setup completion status
- `wizlop_location_setup_triggered`: Session setup trigger tracking

## 🚀 Usage Examples

### Basic Location Setup

```tsx
import {
	useLocationManager,
	useLocationSetup,
	LocationSetup,
} from 'app/shared/locationManager';

function MyComponent() {
	const { location } = useLocationManager();
	const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();

	return (
		<>
			<LocationSetup
				isOpen={showLocationSetup}
				onComplete={handleLocationSetupComplete}
				pageContext='mypage'
				isModal={true}
			/>
			{location && (
				<div>
					Location: {location.latitude}, {location.longitude}
				</div>
			)}
		</>
	);
}
```

### Manual Location Setting

```tsx
const { setManualLocation } = useLocationManager();

// Set location programmatically
setManualLocation(40.7128, -74.006, 'New York City');
```

## 🔮 Future Enhancements

1. **Location Validation**: Enhanced coordinate validation and bounds checking
2. **Offline Support**: Cache location data for offline scenarios
3. **Location History**: Track and manage location history
4. **Performance Optimization**: Further optimize event listeners and re-renders
5. **Testing Suite**: Comprehensive unit and integration tests
6. **Location Accuracy**: Enhanced accuracy detection and user feedback
7. **Geofencing**: Support for location-based triggers and notifications

## 🏗️ Integration Guide

### Adding Location to a New Page

1. **Import the hooks**:

```tsx
import {
	useLocationManager,
	useLocationSetup,
	LocationSetup,
} from 'app/shared/locationManager';
```

2. **Use the hooks**:

```tsx
const { location, isLoading, error } = useLocationManager();
const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();
```

3. **Add the setup component**:

```tsx
<LocationSetup
	isOpen={showLocationSetup}
	onComplete={handleLocationSetupComplete}
	pageContext='yourpage'
/>
```

### Best Practices

- **Always check `hasLoadedFromStorage`** before processing location updates in critical components
- **Use the shared LocationSetup component** instead of creating custom location setup flows
- **Handle loading and error states** appropriately in your UI
- **Respect user privacy** and provide clear explanations for location requests

## 🏛️ System Architecture

### Data Flow

```
User Action → LocationSetup → useLocationManager → localStorage + Event → All Components
```

1. **User initiates location setup** (auto or manual)
2. **LocationSetup component** handles the setup flow
3. **useLocationManager** processes and stores the location
4. **localStorage** persists the data
5. **Custom event** notifies all instances
6. **All components** receive the updated location immediately

### State Management

- **Local State**: Each component has its own location manager instance
- **Shared State**: Location data synchronized via localStorage and events
- **Initialization**: Hydration-safe loading with proper timing controls
- **Persistence**: Automatic localStorage management with error handling

### Error Handling

- **Graceful Degradation**: System continues to work even if localStorage fails
- **User Feedback**: Clear error messages for location request failures
- **Fallback Options**: Manual location entry when auto-location fails
- **Retry Logic**: Built-in retry mechanisms for failed location requests

---

**The Wizlop Location Manager provides a robust, scalable foundation for location-based features across the entire application. It handles the complexity of location management so your components can focus on delivering great user experiences.**
