/** @format */

'use client';

import { useCallback, useEffect, useState } from 'react';

interface UserCredits {
	id: string;
	user_id: string;
	credits_earned: number;
	credits_purchased: number;
	credits_used: number;
	subscription_type: 'none' | 'basic' | 'premium';
	subscription_expires_at?: string;
	created_at: string;
	updated_at: string;
}

interface UseCreditsDataProps {
	userId?: string;
	autoLoad?: boolean;
}

export const useCreditsData = ({
	userId,
	autoLoad = true,
}: UseCreditsDataProps = {}) => {
	const [credits, setCredits] = useState<UserCredits | null>(null);
	const [totalCredits, setTotalCredits] = useState(0);
	const [availableCredits, setAvailableCredits] = useState(0);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Load credits data from API
	const loadCredits = useCallback(
		async (targetUserId?: string) => {
			const userIdToUse = targetUserId || userId;

			if (!userIdToUse) {
				setError('User ID is required');
				return;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch(
					`/api/user/credits?userId=${userIdToUse}`,
					{
						credentials: 'include',
					}
				);
				const data = await response.json();

				if (data.success) {
					setCredits(data.credits);
					setTotalCredits(data.total_credits);
					setAvailableCredits(data.available_credits);
				} else {
					setError(data.error || 'Failed to load credits');
				}
			} catch (err) {
				setError('Network error while loading credits');
				console.error('Error loading credits:', err);
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Earn credits
	const earnCredits = useCallback(
		async (amount: number, reason?: string) => {
			if (!userId) {
				setError('User ID is required');
				return false;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/user/credits', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						userId,
						action: 'earn',
						amount,
						reason,
					}),
				});

				const data = await response.json();

				if (data.success) {
					setCredits(data.credits);
					setTotalCredits(data.total_credits);
					setAvailableCredits(data.available_credits);
					return true;
				} else {
					setError(data.error || 'Failed to earn credits');
					return false;
				}
			} catch (err) {
				setError('Network error while earning credits');
				console.error('Error earning credits:', err);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Purchase credits
	const purchaseCredits = useCallback(
		async (
			amount: number,
			subscriptionType?: 'basic' | 'premium',
			subscriptionExpiresAt?: string
		) => {
			if (!userId) {
				setError('User ID is required');
				return false;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/user/credits', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify({
						userId,
						action: 'purchase',
						amount,
						subscriptionType,
						subscriptionExpiresAt,
						reason: subscriptionType
							? `${subscriptionType} subscription`
							: 'credit purchase',
					}),
				});

				const data = await response.json();

				if (data.success) {
					setCredits(data.credits);
					setTotalCredits(data.total_credits);
					setAvailableCredits(data.available_credits);
					return true;
				} else {
					setError(data.error || 'Failed to purchase credits');
					return false;
				}
			} catch (err) {
				setError('Network error while purchasing credits');
				console.error('Error purchasing credits:', err);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Use credits
	const useCredits = useCallback(
		async (amount: number, reason?: string) => {
			if (!userId) {
				setError('User ID is required');
				return false;
			}

			if (amount > availableCredits) {
				setError('Insufficient credits');
				return false;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/user/credits', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						userId,
						action: 'use',
						amount,
						reason,
					}),
				});

				const data = await response.json();

				if (data.success) {
					setCredits(data.credits);
					setTotalCredits(data.total_credits);
					setAvailableCredits(data.available_credits);
					return true;
				} else {
					setError(data.error || 'Failed to use credits');
					return false;
				}
			} catch (err) {
				setError('Network error while using credits');
				console.error('Error using credits:', err);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[userId, availableCredits]
	);

	// Update subscription
	const updateSubscription = useCallback(
		async (
			subscriptionType: 'none' | 'basic' | 'premium',
			subscriptionExpiresAt?: string
		) => {
			if (!userId) {
				setError('User ID is required');
				return false;
			}

			setLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/user/credits', {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify({
						userId,
						subscriptionType,
						subscriptionExpiresAt,
					}),
				});

				const data = await response.json();

				if (data.success) {
					setCredits(data.credits);
					setTotalCredits(data.total_credits);
					setAvailableCredits(data.available_credits);
					return true;
				} else {
					setError(data.error || 'Failed to update subscription');
					return false;
				}
			} catch (err) {
				setError('Network error while updating subscription');
				console.error('Error updating subscription:', err);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[userId]
	);

	// Auto-load credits when component mounts or userId changes
	useEffect(() => {
		if (autoLoad && userId) {
			loadCredits();
		}
	}, [autoLoad, userId]); // Removed loadCredits from dependencies

	// Check if user has subscription
	const hasActiveSubscription = useCallback(() => {
		if (!credits || credits.subscription_type === 'none') return false;
		if (!credits.subscription_expires_at) return false;

		const expiryDate = new Date(credits.subscription_expires_at);
		return expiryDate > new Date();
	}, [credits]);

	// Get subscription status
	const getSubscriptionStatus = useCallback(() => {
		if (!credits) return { type: 'none', active: false, daysLeft: 0 };

		const isActive = hasActiveSubscription();
		let daysLeft = 0;

		if (isActive && credits.subscription_expires_at) {
			const expiryDate = new Date(credits.subscription_expires_at);
			const now = new Date();
			daysLeft = Math.ceil(
				(expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
			);
		}

		return {
			type: credits.subscription_type,
			active: isActive,
			daysLeft: Math.max(0, daysLeft),
		};
	}, [credits, hasActiveSubscription]);

	return {
		// Data
		credits,
		totalCredits,
		availableCredits,
		subscriptionStatus: getSubscriptionStatus(),

		// State
		loading,
		error,

		// Actions
		loadCredits,
		earnCredits,
		purchaseCredits,
		useCredits,
		updateSubscription,

		// Utilities
		clearError: () => setError(null),
		hasCredits: availableCredits > 0,
		hasActiveSubscription: hasActiveSubscription(),
		canAfford: (amount: number) => amount <= availableCredits,
	};
};
