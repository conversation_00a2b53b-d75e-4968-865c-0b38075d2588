/** @format */

'use client';

import { signOut, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

interface SessionManagerReturn {
	isSessionValid: boolean;
	isValidating: boolean;
	validateSession: () => Promise<boolean>;
	clearSession: () => Promise<void>;
	refreshSession: () => Promise<void>;
}

export function useSessionManager(): SessionManagerReturn {
	const { data: session, status, update } = useSession();
	const router = useRouter();
	const [isSessionValid, setIsSessionValid] = useState(true);
	const [isValidating, setIsValidating] = useState(false);

	// Validate session with server
	const validateSession = useCallback(async (): Promise<boolean> => {
		if (status === 'loading') return true;
		if (status === 'unauthenticated') return false;

		setIsValidating(true);
		try {
			const response = await fetch('/api/auth/validate-session', {
				method: 'GET',
				credentials: 'include',
			});

			const result = await response.json();
			const valid = response.ok && result.valid;

			setIsSessionValid(valid);

			if (!valid && session) {
				// Session is invalid but client thinks it's valid - clear it
				await clearSession();
			}

			return valid;
		} catch (error) {
			console.error('Session validation failed:', error);
			setIsSessionValid(false);
			return false;
		} finally {
			setIsValidating(false);
		}
	}, [status, session]);

	// Clear session completely
	const clearSession = useCallback(async (): Promise<void> => {
		try {
			// Clear any local storage items related to auth
			if (typeof window !== 'undefined') {
				localStorage.removeItem('wizlop_user_location');
				localStorage.removeItem('wizlop_location_setup_status');
				// Clear any other app-specific storage
				Object.keys(localStorage).forEach((key) => {
					if (key.startsWith('wizlop_') || key.startsWith('nextauth')) {
						localStorage.removeItem(key);
					}
				});

				// Clear session storage as well
				sessionStorage.clear();
			}

			// Sign out with NextAuth
			await signOut({
				redirect: false,
				callbackUrl: '/',
			});

			setIsSessionValid(false);

			// Force a hard refresh to clear any remaining state
			if (typeof window !== 'undefined') {
				window.location.href = '/';
			}
		} catch (error) {
			console.error('Error clearing session:', error);
			// Force refresh even if signOut fails
			if (typeof window !== 'undefined') {
				window.location.href = '/';
			}
		}
	}, []);

	// Refresh session
	const refreshSession = useCallback(async (): Promise<void> => {
		try {
			await update();
			await validateSession();
		} catch (error) {
			console.error('Error refreshing session:', error);
		}
	}, [update, validateSession]);

	// Validate session on mount and periodically
	useEffect(() => {
		if (status === 'authenticated') {
			validateSession();

			// Set up periodic validation (every 5 minutes)
			const interval = setInterval(validateSession, 5 * 60 * 1000);
			return () => clearInterval(interval);
		}
	}, [status, validateSession]);

	// Listen for storage events (when user logs out in another tab)
	useEffect(() => {
		const handleStorageChange = (e: StorageEvent) => {
			if (e.key === 'nextauth.message') {
				// NextAuth sends messages between tabs
				try {
					const message = JSON.parse(e.newValue || '{}');
					if (message.event === 'session' && message.data === null) {
						// Session was cleared in another tab
						setIsSessionValid(false);
						router.push('/');
					}
				} catch {
					// Ignore parsing errors
				}
			}
		};

		if (typeof window !== 'undefined') {
			window.addEventListener('storage', handleStorageChange);
			return () => window.removeEventListener('storage', handleStorageChange);
		}
	}, [router]);

	// Handle browser visibility change (validate when tab becomes visible)
	useEffect(() => {
		const handleVisibilityChange = () => {
			if (
				document.visibilityState === 'visible' &&
				status === 'authenticated'
			) {
				validateSession();
			}
		};

		if (typeof document !== 'undefined') {
			document.addEventListener('visibilitychange', handleVisibilityChange);
			return () =>
				document.removeEventListener(
					'visibilitychange',
					handleVisibilityChange
				);
		}
	}, [status, validateSession]);

	return {
		isSessionValid,
		isValidating,
		validateSession,
		clearSession,
		refreshSession,
	};
}

export default useSessionManager;
