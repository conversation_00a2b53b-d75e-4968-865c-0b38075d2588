/** @format */

'use client';

import { AppNavBar } from '@/app/shared/navigation';
import LoadingSpinner from '@/app/shared/system/components/LoadingSpinner';
import { useAuthGuard } from '@/app/shared/system/hooks/useAuthGuard';
import { getPageConfig } from '@/lib/navigation-config';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface LayoutWrapperProps {
	children: React.ReactNode;
}

const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {
	const pathname = usePathname();
	const [mounted, setMounted] = useState(false);

	const pageConfig = getPageConfig(pathname);

	// Configure auth guard based on page requirements
	const authConfig = pageConfig.requireAuth
		? {
				requireAuth: true,
				blockUnauthenticated: true,
				allowedRoles: pageConfig.allowedRoles,
				redirectTo: pathname === '/chat' ? '/' : '/auth',
		  }
		: { requireAuth: false };

	const { isAuthenticated, isAuthorized, isLoading } = useAuthGuard(authConfig);

	// Handle hydration
	useEffect(() => {
		setMounted(true);
	}, []);

	// Show loading during hydration or auth check
	if (!mounted || (pageConfig.requireAuth && isLoading)) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<LoadingSpinner text='Loading...' />
			</div>
		);
	}

	// If page requires auth but user is not authenticated/authorized, don't render content
	if (pageConfig.requireAuth && (!isAuthenticated || !isAuthorized)) {
		return null; // Auth guard will handle redirect
	}

	// Determine layout classes based on configuration
	const getLayoutClasses = () => {
		const baseClasses = [];

		switch (pageConfig.layoutType) {
			case 'fullscreen':
				baseClasses.push('h-screen', 'w-full', 'overflow-hidden');
				break;
			case 'standard':
				baseClasses.push('min-h-screen', 'flex', 'flex-col');
				break;
			case 'custom':
				baseClasses.push('min-h-screen', 'overflow-y-auto');
				break;
		}

		return baseClasses.join(' ');
	};

	const getContentClasses = () => {
		const classes = [];

		switch (pageConfig.layoutType) {
			case 'fullscreen':
				classes.push('h-full', 'w-full');
				if (pageConfig.showNavigation) {
					classes.push('pt-16'); // Account for fixed navigation
				}
				break;
			case 'standard':
				classes.push('flex-1');
				if (pageConfig.showNavigation) {
					classes.push('pt-16'); // Account for fixed navigation
				}
				if (pageConfig.contentPadding) {
					classes.push('px-4', 'py-6', 'md:px-8');
				}
				break;
			case 'custom':
				classes.push('w-full', 'min-h-0');
				if (pageConfig.showNavigation) {
					classes.push('pt-16'); // Account for fixed navigation
				}
				break;
		}

		return classes.join(' ');
	};

	return (
		<div className={getLayoutClasses()}>
			{/* Render navigation if configured */}
			{pageConfig.showNavigation && (
				<AppNavBar variant={pageConfig.navigationVariant} />
			)}

			{/* Main content area */}
			<main className={getContentClasses()}>{children}</main>
		</div>
	);
};

export default LayoutWrapper;
