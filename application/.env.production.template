# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file and set actual values for production deployment
# DO NOT commit actual production secrets to version control

# =============================================================================
# APPLICATION CORE
# =============================================================================
NODE_ENV=production
PORT=3000

# =============================================================================
# NEXTAUTH CONFIGURATION
# =============================================================================
# Set to your actual production domain
NEXTAUTH_URL=https://yourdomain.com
# Generate a NEW secure secret for production: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
NEXTAUTH_SECRET=REPLACE_WITH_PRODUCTION_SECRET

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# For AWS RDS or managed database
DB_HOST=your-rds-endpoint.region.rds.amazonaws.com
DB_PORT=5432
DB_NAME=wizlop_prod
DB_SCHEMA=backend_schema
DB_USER=wizlop_prod_user
DB_PASSWORD=REPLACE_WITH_SECURE_PASSWORD
DB_MAX_CONNECTIONS=50
DB_IDLE_TIMEOUT=30000
DATABASE_SSL=true

# Alternative database URL format
DATABASE_URL=postgresql://wizlop_prod_user:<EMAIL>:5432/wizlop_prod?sslmode=require

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================
# Production Google OAuth credentials
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# =============================================================================
# LLM ENGINE CONFIGURATION
# =============================================================================
# Production LLM engine endpoint
LLM_ENGINE_URL=https://your-llm-engine.yourdomain.com
LLM_ENGINE_TIMEOUT=120000
LLM_ENGINE_RETRIES=3
LLM_ENGINE_API_KEY=your-production-llm-api-key

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=86400000
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_CHAT=true
ENABLE_ANALYTICS=true
ENABLE_MAPS=true
MAINTENANCE_MODE=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=warn
LOG_CONSOLE=true
LOG_FILE=true
LOG_FILE_PATH=/var/log/wizlop/app.log

# =============================================================================
# CHAT CONFIGURATION
# =============================================================================
CHAT_MAX_MESSAGE_LENGTH=2000
CHAT_MAX_MESSAGES=100
CHAT_SESSION_TIMEOUT=3600000
CHAT_ENABLE_HISTORY=true

# =============================================================================
# PUBLIC ENVIRONMENT VARIABLES (accessible in browser)
# =============================================================================
NEXT_PUBLIC_MAINTENANCE_MODE=false
NEXT_PUBLIC_MAINTENANCE_MESSAGE=We're performing scheduled maintenance
NEXT_PUBLIC_MAINTENANCE_ETA=We'll be back shortly
