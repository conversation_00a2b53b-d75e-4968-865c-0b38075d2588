/** @format */

const path = require('path');

module.exports = {
	entry: './app/page.tsx',
	output: {
		filename: 'bundle.[contenthash].js',
		path: path.resolve(__dirname, '../../dist'),
		clean: true,
		publicPath: '/',
	},
	mode: 'production',
	devtool: 'source-map',
	resolve: {
		extensions: ['.tsx', '.ts', '.js', '.jsx'],
		alias: {
			'@': path.resolve(__dirname, '../../'),
		},
	},
	module: {
		rules: [
			{
				test: /\.tsx?$/,
				use: [
					{
						loader: 'ts-loader',
						options: {
							transpileOnly: false,
							configFile: path.resolve(__dirname, '../production/tsconfig.prod.json'),
							compilerOptions: {
								noEmit: false,
								jsx: 'react-jsx',
								skipLibCheck: true,
							},
						},
					},
				],
				exclude: /node_modules/,
			},
			{
				test: /\.css$/,
				use: ['style-loader', 'css-loader', 'postcss-loader'],
			},
			{
				test: /\.(png|svg|jpg|jpeg|gif)$/i,
				type: 'asset/resource',
			},
		],
	},
	optimization: {
		minimize: true,
		splitChunks: {
			chunks: 'all',
		},
	},
	performance: {
		hints: 'warning',
		maxEntrypointSize: 512000,
		maxAssetSize: 512000,
	},
};
