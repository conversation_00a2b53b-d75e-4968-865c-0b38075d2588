/** @format */

const path = require('path');

module.exports = {
	entry: './app/page.tsx',
	output: {
		filename: 'bundle.js',
		path: path.resolve(__dirname, '../../dist'),
		clean: true,
	},
	mode: 'development',
	devtool: 'eval-source-map',
	resolve: {
		extensions: ['.tsx', '.ts', '.js', '.jsx'],
		alias: {
			'@': path.resolve(__dirname, '../../'),
		},
	},
	module: {
		rules: [
			{
				test: /\.tsx?$/,
				use: [
					{
						loader: 'ts-loader',
						options: {
							transpileOnly: false,
							configFile: path.resolve(__dirname, '../development/tsconfig.dev.json'),
							compilerOptions: {
								noEmit: false,
								jsx: 'react-jsx',
								skipLibCheck: true,
							},
						},
					},
				],
				exclude: /node_modules/,
			},
			{
				test: /\.css$/,
				use: ['style-loader', 'css-loader', 'postcss-loader'],
			},
			{
				test: /\.(png|svg|jpg|jpeg|gif)$/i,
				type: 'asset/resource',
			},
		],
	},
	stats: {
		errorDetails: true,
		errors: true,
		warnings: true,
		moduleTrace: true,
		errorStack: true,
	},
	bail: false,
	performance: {
		hints: false,
	},
};
