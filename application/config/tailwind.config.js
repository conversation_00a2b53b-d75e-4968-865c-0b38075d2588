/** @format */

const path = require('path');

/** @type {import('tailwindcss').Config} */
module.exports = {
	content: [
		path.join(__dirname, '../app/**/*.{js,ts,jsx,tsx,mdx}'),
		path.join(__dirname, '../core/**/*.{js,ts,jsx,tsx,mdx}'),
		path.join(__dirname, '../pages/**/*.{js,ts,jsx,tsx,mdx}'),
		path.join(__dirname, '../components/**/*.{js,ts,jsx,tsx,mdx}'),
		path.join(__dirname, '../lib/**/*.{js,ts,jsx,tsx,mdx}'),
	],
	theme: {
		extend: {
			colors: {
				// Updated Wizlop Brand Colors - Based on new color palette
				wizlop: {
					// Brand colors mapped to numbered scale
					900: '#01034F', // brand.navy - darkest for text and strong elements
					800: '#1A1F5C', // supporting.softNavy
					700: '#33C2FF', // brand.blue - primary brand color

					// Supporting colors
					600: '#66D0FF', // supporting.lightBlue
					500: '#80ED99', // brand.green
					400: '#A3F7B5', // supporting.mintGreen

					// Light variations for backgrounds
					300: '#B3E5FF', // ui.blue200
					200: '#D7F7E1', // ui.green200
					100: '#E1F5FF', // ui.blue100
					75: '#EBFBF0', // ui.green100
					50: '#F0FAFF', // ui.blue50
					25: '#F5FDF8', // ui.green50
					10: '#F8F8FB', // ui.navy50
				},

				// Direct brand color mappings from colors.ts
				'wizlop-blue': '#33C2FF', // colors.brand.blue
				'wizlop-navy': '#01034F', // colors.brand.navy
				'wizlop-green': '#80ED99', // colors.brand.green
				'wizlop-light-blue': '#66D0FF', // colors.supporting.lightBlue
				'wizlop-soft-navy': '#1A1F5C', // colors.supporting.softNavy
				'wizlop-mint-green': '#A3F7B5', // colors.supporting.mintGreen
				'wizlop-text': '#1B1B1E', // colors.neutral.textBlack
				'wizlop-slate': '#5E6E7E', // colors.neutral.slateGray
				'wizlop-cloud': '#F5F7F8', // colors.neutral.cloudWhite
				'wizlop-mist': '#E6F2F0', // colors.neutral.lightMistGray

				// Updated primary scale using new brand colors
				primary: {
					50: '#F0FAFF', // ui.blue50
					100: '#E1F5FF', // ui.blue100
					200: '#B3E5FF', // ui.blue200
					300: '#66D0FF', // supporting.lightBlue
					400: '#33C2FF', // brand.blue
					500: '#01034F', // brand.navy
					600: '#1A1F5C', // supporting.softNavy
					700: '#80ED99', // brand.green
					800: '#A3F7B5', // supporting.mintGreen
					900: '#1B1B1E', // neutral.textBlack
				},

				// Keep accent colors for compatibility
				accent: {
					coral: '#FF6B6B',
					warm: '#FFE66D',
					cool: '#4ECDC4',
				},
			},
			fontFamily: {
				sans: [
					'-apple-system',
					'BlinkMacSystemFont',
					'Segoe UI',
					'Roboto',
					'Oxygen',
					'Ubuntu',
					'Cantarell',
					'Fira Sans',
					'Droid Sans',
					'Helvetica Neue',
					'sans-serif',
				],
			},
			animation: {
				'fade-in': 'fadeIn 0.5s ease-in-out',
				'slide-up': 'slideUp 0.6s ease-out',
				'bounce-gentle': 'bounceGentle 2s infinite',
			},
			animationDelay: {
				100: '0.1s',
				200: '0.2s',
				300: '0.3s',
				400: '0.4s',
				500: '0.5s',
				1000: '1s',
				1500: '1.5s',
			},
			keyframes: {
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				slideUp: {
					'0%': { transform: 'translateY(20px)', opacity: '0' },
					'100%': { transform: 'translateY(0)', opacity: '1' },
				},
				bounceGentle: {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-10px)' },
				},
			},
		},
	},
	plugins: [],
};
