# Production Environment Variables Template
# Set these in your production environment

# Database Configuration
DATABASE_URL="*********************************************/wizlop_prod"

# NextAuth Configuration
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secure-production-secret-key"

# OAuth Providers (Production)
GOOGLE_CLIENT_ID="your-production-google-client-id"
GOOGLE_CLIENT_SECRET="your-production-google-client-secret"

GITHUB_CLIENT_ID="your-production-github-client-id"
GITHUB_CLIENT_SECRET="your-production-github-client-secret"

# API Keys (Production)
OPENAI_API_KEY="your-production-openai-api-key"

# Production Settings
NODE_ENV="production"
DEBUG="false"
LOG_LEVEL="error"

# Feature Flags
ENABLE_ANALYTICS="true"
ENABLE_ERROR_REPORTING="true"

# Security
ALLOWED_ORIGINS="https://your-domain.com"
RATE_LIMIT_MAX="100"
