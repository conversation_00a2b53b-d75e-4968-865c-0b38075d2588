# Development Environment Variables Template
# Copy this file to .env.local and fill in your values

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/wizlop_dev"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-development-secret-key-here"

# OAuth Providers (Development)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# API Keys (Development)
OPENAI_API_KEY="your-openai-api-key"

# Development Settings
NODE_ENV="development"
DEBUG="true"
LOG_LEVEL="debug"

# Feature Flags
ENABLE_ANALYTICS="false"
ENABLE_ERROR_REPORTING="false"
