# Type Organization System

## 📋 Overview

This document outlines our TypeScript type organization strategy for the Wizlop application.

## 🎯 Core Principle

**"Co-location with Shared Promotion"**

- Start with types **local** to where they're used
- **Promote to shared** when multiple domains need them
- **Only create what you need** - no premature abstraction

## 📁 Directory Structure

```
application/
├── app/
│   ├── auth/types/           # Auth-specific types
│   │   └── next-auth.d.ts    # NextAuth module augmentation
│   ├── chat/types/           # Chat-specific types (when needed)
│   ├── pois/types/           # POI-specific types (when needed)
│   └── shared/types/         # Cross-domain shared types (when needed)
│       └── common.d.ts       # Types used by 2+ domains
└── types/                    # Global types only (when needed)
    └── global.d.ts           # window, process, etc.
```

## 🔄 Migration Rules

### When to Keep Types Local
- ✅ Used by **one domain only**
- ✅ Domain-specific interfaces
- ✅ Module augmentations (like NextAuth)

### When to Promote to Shared
- ✅ Used by **2+ domains**
- ✅ Common data structures
- ✅ API response types

### When to Create Global
- ✅ Window extensions
- ✅ Process environment
- ✅ Third-party library augmentations

## 📝 Examples

### ✅ Good: Domain-Specific
```typescript
// app/auth/types/next-auth.d.ts
declare module "next-auth" {
  interface User {
    role: 'user' | 'agent' | 'superuser'
  }
}
```

### ✅ Good: Shared When Needed
```typescript
// app/shared/types/common.d.ts
export interface ApiResponse<T> {
  success: boolean
  data: T
  error?: string
}
```

### ✅ Good: Global When Needed
```typescript
// types/global.d.ts
declare global {
  interface Window {
    gtag: Function
  }
}
```

## 🚀 Implementation Steps

1. **Start Local**: Create types in domain folders
2. **Monitor Usage**: Track when types are imported across domains
3. **Promote When Shared**: Move to `app/shared/types/` when used by 2+ domains
4. **Create Global**: Only when truly global scope is needed

## 🔧 TypeScript Configuration

Our `tsconfig.json` automatically discovers types with:
```json
{
  "include": ["**/*.ts", "**/*.tsx"]
}
```

No manual path configuration needed! 🎉

## 📚 Benefits

- ✅ **Clear Ownership**: Each domain owns its types
- ✅ **Easy Discovery**: Types are where you expect them
- ✅ **Scalable**: Grows organically with the codebase
- ✅ **Maintainable**: Changes stay within domain boundaries
- ✅ **No Over-Engineering**: Create only what you need

---

*Last Updated: 2025-01-02*
