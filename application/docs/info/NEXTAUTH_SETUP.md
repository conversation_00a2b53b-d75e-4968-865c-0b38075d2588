<!-- @format -->

# NextAuth Configuration Reference

## Required Environment Variables

```env
# NextAuth Core (Required)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-change-this-in-production

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Generate Secure Secret

```bash
# Generate a secure secret for production
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## OAuth Provider Setup

### Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 credentials
3. Add authorized redirect URI: `http://localhost:3000/api/auth/callback/google`
4. Copy Client ID and Secret to environment variables

### GitHub OAuth (Optional)

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Create OAuth App with callback: `http://localhost:3000/api/auth/callback/github`
3. Copy Client ID and Secret to environment variables

## Production Setup

### Domain Configuration

When deploying to production:

1. Update `NEXTAUTH_URL` to your domain
2. Generate new `NEXTAUTH_SECRET` for production
3. Update OAuth provider redirect URIs

### Security Notes

- Never commit secrets to version control
- Use different secrets for each environment
- Enable HTTPS in production
