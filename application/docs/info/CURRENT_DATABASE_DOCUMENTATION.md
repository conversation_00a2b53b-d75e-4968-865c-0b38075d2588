<!-- @format -->

# Current Database Documentation

## Overview

This document describes the actual current state of the Wizlop database as of December 2024. All information is based on direct database examination.

## Database Architecture

### Schemas

- **`backend_schema`**: User management, authentication, and personal data (17 tables)
- **`spatial_schema`**: POI data, spatial information, and location features (10 tables)
- **`public`**: System tables and PostGIS extensions (1 table)

**Total**: 3 schemas, 28 tables

## 1. SPATIAL_SCHEMA (10 tables)

### 1.1 `spatial_schema.pois` - Main POI Table

**Purpose**: Core table containing all Points of Interest data
**Records**: 46,591 POIs

**Columns**:

```sql
-- Basic Information
id SERIAL PRIMARY KEY
name TEXT
name_en TEXT
name_tr TEXT
amenity VARCHAR(100)
category VARCHAR(50)
subcategory VARCHAR(50)
cuisine VARCHAR(100)

-- Location Information
city VARCHAR(100)
district VARCHAR(100)
street TEXT
address TEXT
latitude DOUBLE PRECISION
longitude DOUBLE PRECISION
geom GEOMETRY(POINT, 4326)

-- Contact Information
phone_number VARCHAR(50)
opening_hours TEXT
description TEXT

-- Timestamps
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

-- Democratic Categorization
democratic_categories JSONB DEFAULT '[]'
community_tags JSONB DEFAULT '[]'
legacy_categories JSONB DEFAULT '{}'

-- User Interaction Metrics
user_rating_avg DOUBLE PRECISION
user_rating_count INTEGER DEFAULT 0
view_count INTEGER DEFAULT 0
favorite_count INTEGER DEFAULT 0
visit_count INTEGER DEFAULT 0
share_count INTEGER DEFAULT 0
like_count INTEGER DEFAULT 0
review_count INTEGER DEFAULT 0

-- Location Intelligence
popularity_score INTEGER DEFAULT 0
is_top_location BOOLEAN DEFAULT FALSE
is_hidden_gem BOOLEAN DEFAULT FALSE
trending_score INTEGER DEFAULT 0

-- Practical Info
price_range VARCHAR(10)
reservation_required BOOLEAN DEFAULT FALSE
accessibility_rating INTEGER

-- Status & Meta
status VARCHAR(20) DEFAULT 'active'
verification_level INTEGER DEFAULT 1
promoted_at TIMESTAMP
last_verified_at TIMESTAMP
```

**Current Data Status**:

- **Total POIs**: 46,591
- **Top Locations**: 62 (is_top_location = true)
- **Hidden Gems**: 469 (is_hidden_gem = true)
- **With Ratings**: 0 (user_rating_avg is NULL for all)
- **Popularity Scores**: All default to 0

**Indexes** (20 indexes):

- Primary key, spatial index (GIST), amenity, category, district, name
- Performance indexes: popularity_score, trending_score, view_count, favorite_count, like_count
- JSONB indexes: democratic_categories, community_tags, legacy_categories
- Composite indexes: amenity+popularity, rating+popularity, status+verification

### 1.2 `spatial_schema.user_pois_temp` - Temporary User POIs

**Purpose**: Store user-submitted POIs pending admin review

**Columns**:

```sql
id SERIAL PRIMARY KEY
submitted_by_user_id TEXT NOT NULL
name, name_en, name_tr TEXT
amenity, category, subcategory, cuisine VARCHAR(100)
city, district VARCHAR(100)
address, phone, website TEXT
latitude DECIMAL(10,8), longitude DECIMAL(11,8)
geom GEOMETRY(POINT, 4326)
admin_review_status VARCHAR(20) DEFAULT 'pending'
admin_feedback TEXT
created_at, updated_at TIMESTAMP WITH TIME ZONE
```

**Indexes**: Primary key, spatial index, amenity, submitted_by, status

### 1.3 `spatial_schema.user_pois_approved` - Approved User POIs

**Purpose**: Store user-submitted POIs that passed admin review
**Structure**: Similar to user_pois_temp with approval metadata

### 1.4 `spatial_schema.admin_boundaries` - Administrative Boundaries

**Purpose**: Store district and neighborhood boundaries

**Columns**:

```sql
id SERIAL PRIMARY KEY
name VARCHAR(255)
admin_level INTEGER
latitude, longitude DECIMAL(10,8)
geometry_json JSONB
geom GEOMETRY(POLYGON, 4326)
```

**Indexes**: Primary key, spatial index, name, admin_level, place_type

### 1.5 `spatial_schema.master_categories` - Democratic Categories

**Purpose**: Master list of community-voted categories

**Columns**:

```sql
id SERIAL PRIMARY KEY
category_name VARCHAR(100) UNIQUE
category_description TEXT
main_category VARCHAR(50)
is_active BOOLEAN DEFAULT TRUE
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

**Indexes**: Primary key, category_name, main_category, active status

### 1.6 `spatial_schema.poi_categories` - POI Category Aggregation

**Purpose**: Aggregated category votes for each POI

**Columns**:

```sql
id SERIAL PRIMARY KEY
poi_id INTEGER REFERENCES pois(id)
category_id INTEGER REFERENCES master_categories(id)
total_upvotes INTEGER DEFAULT 0
category_name VARCHAR(100)
last_updated TIMESTAMP
UNIQUE(poi_id, category_id)
```

**Indexes**: Primary key, poi_id, category_id, total_votes, category_name

### 1.7 `spatial_schema.poi_category_votes` - Category Voting

**Purpose**: Individual user votes for POI categories

**Columns**:

```sql
id SERIAL PRIMARY KEY
poi_id INTEGER REFERENCES pois(id)
category_id INTEGER REFERENCES master_categories(id)
user_id TEXT NOT NULL
vote_value INTEGER DEFAULT 1
voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
UNIQUE(poi_id, category_id, user_id)
```

**Indexes**: Primary key, poi_id+category_id+user_id, user_id, vote_value

### 1.8 `spatial_schema.poi_tags` - Descriptive Tags

**Purpose**: User-added descriptive tags for POIs

**Columns**:

```sql
id SERIAL PRIMARY KEY
poi_id INTEGER REFERENCES pois(id)
tag_text VARCHAR(100) NOT NULL
tag_type VARCHAR(20) DEFAULT 'descriptive'
added_by TEXT NOT NULL
added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

**Indexes**: Primary key, poi_id, tag_text, tag_type, added_by

### 1.9 `spatial_schema.community_reviews` - Public Reviews

**Purpose**: Community-written public reviews

**Columns**:

```sql
id SERIAL PRIMARY KEY
poi_id INTEGER REFERENCES pois(id)
user_id TEXT NOT NULL
rating INTEGER CHECK (rating >= 1 AND rating <= 5)
review_text TEXT
review_title VARCHAR(200)
visit_date DATE
photos JSONB DEFAULT '[]'
tags JSONB DEFAULT '[]'
helpful_votes INTEGER DEFAULT 0
is_verified BOOLEAN DEFAULT FALSE
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

**Indexes**: Primary key, poi_id, user_id, rating, created_at, rating+date

### 1.10 `spatial_schema.verification_tasks` - Community Tasks

**Purpose**: Community verification tasks for POI data

**Columns**:

```sql
id SERIAL PRIMARY KEY
task_type VARCHAR(30) NOT NULL
poi_id INTEGER REFERENCES pois(id)
title VARCHAR(200) NOT NULL
description, instructions TEXT
current_data JSONB
reported_issue TEXT
evidence_required JSONB DEFAULT '[]'
status VARCHAR(20) DEFAULT 'open'
priority INTEGER DEFAULT 2
assigned_to TEXT
assigned_at TIMESTAMP
reported_by TEXT
credit_reward INTEGER DEFAULT 10
difficulty_level INTEGER DEFAULT 1
verification_required INTEGER DEFAULT 2
created_at, completed_at, expires_at TIMESTAMP
```

**Indexes**: Primary key, task_type, poi_id, status, priority, assigned_to, expires_at

## 2. BACKEND_SCHEMA (17 tables)

### 2.1 `backend_schema.user_profiles` - User Profiles

**Purpose**: Extended user profile information

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
username VARCHAR(50) UNIQUE
name VARCHAR(255) NOT NULL
age INTEGER NOT NULL CHECK (age >= 13 AND age <= 120)
avatar_url TEXT
profile_completed BOOLEAN DEFAULT TRUE
allow_data_usage BOOLEAN DEFAULT FALSE
preferences JSONB DEFAULT '{}'
timezone VARCHAR(50) DEFAULT 'UTC'
total_locations_visited INTEGER DEFAULT 0
total_reviews_written INTEGER DEFAULT 0
total_photos_uploaded INTEGER DEFAULT 0
adventurer_level INTEGER DEFAULT 1
days_active INTEGER DEFAULT 0
last_activity_date DATE
password_hash VARCHAR(255)
created_at, updated_at TIMESTAMP WITH TIME ZONE
```

**Indexes**: Primary key, user_id, username (unique)
**Triggers**: update_updated_at_column

### 2.2 `backend_schema.user_favorites` - User Favorites

**Purpose**: Store user's favorite POIs

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
poi_id INTEGER
user_poi_temp_id INTEGER
user_poi_approved_id INTEGER
poi_type VARCHAR(20) NOT NULL CHECK (poi_type IN ('official', 'user_temp', 'user_approved'))
favorite_type VARCHAR(20) DEFAULT 'location'
notes TEXT
interaction_metadata JSONB DEFAULT '{}'
last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id, poi_id, user_id+poi_id
**Triggers**: sync_backend_interactions_to_spatial
**Constraints**: Check that only one POI type is set

### 2.3 `backend_schema.user_location_reviews` - User Reviews

**Purpose**: Store user reviews for POIs

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
poi_id INTEGER
user_poi_temp_id INTEGER
user_poi_approved_id INTEGER
poi_type VARCHAR(20) NOT NULL
rating INTEGER CHECK (rating >= 1 AND rating <= 5)
review_text TEXT
photos JSONB DEFAULT '[]'
visit_date DATE
is_verified BOOLEAN DEFAULT FALSE
helpful_votes INTEGER DEFAULT 0
created_at, updated_at TIMESTAMP WITH TIME ZONE
```

**Indexes**: Primary key, user_id, poi_id, rating, created_at, user_id+poi_id
**Triggers**: sync_reviews_to_spatial, update_updated_at_column

### 2.4 `backend_schema.user_location_visits` - User Visits

**Purpose**: Track user visits to POIs

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
poi_id INTEGER
user_poi_temp_id INTEGER
user_poi_approved_id INTEGER
poi_type VARCHAR(20) NOT NULL
visit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
visit_duration_minutes INTEGER
visit_type VARCHAR(20) CHECK (visit_type IN ('planned', 'spontaneous', 'recommended'))
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id, poi_id, visit_date, user_id+poi_id
**Triggers**: sync_visits_to_spatial

### 2.5 `backend_schema.user_credits` - User Credits System

**Purpose**: Manage user credits and gamification

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
credits_earned INTEGER DEFAULT 0
credits_purchased INTEGER DEFAULT 0
credits_used INTEGER DEFAULT 0
subscription_type VARCHAR(20) DEFAULT 'none'
subscription_expires_at TIMESTAMP WITH TIME ZONE
gamification_data JSONB DEFAULT '{}'
verification_tasks_completed INTEGER DEFAULT 0
accuracy_rating FLOAT DEFAULT 0.0
user_level INTEGER DEFAULT 1
total_points_earned INTEGER DEFAULT 0
created_at, updated_at TIMESTAMP WITH TIME ZONE
```

**Indexes**: Primary key, user_id
**Triggers**: update_updated_at_column

### 2.6 `backend_schema.user_location_settings` - User Settings

**Purpose**: Store user location preferences

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
search_radius INTEGER DEFAULT 1000 CHECK (search_radius >= 100 AND search_radius <= 10000)
num_candidates INTEGER DEFAULT 3 CHECK (num_candidates >= 1 AND num_candidates <= 20)
created_at, updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id
**Triggers**: update_updated_at_column

### 2.7 `backend_schema.user_location_photos` - User Photos

**Purpose**: Store user-uploaded photos for POIs

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
poi_id INTEGER
user_location_id UUID REFERENCES user_added_locations(id)
location_type VARCHAR(20) DEFAULT 'poi'
photo_url TEXT NOT NULL
caption TEXT
is_primary BOOLEAN DEFAULT FALSE
upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id, poi_id
**Constraints**: Check that only one location type is set

### 2.8 `backend_schema.user_added_locations` - User Added Locations

**Purpose**: Store user-submitted custom locations

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
name VARCHAR(255) NOT NULL
description TEXT
category VARCHAR(100)
latitude DECIMAL(10,8) NOT NULL
longitude DECIMAL(11,8) NOT NULL
address TEXT
phone_number VARCHAR(20)
website_url TEXT
is_verified BOOLEAN DEFAULT FALSE
verification_status VARCHAR(20) DEFAULT 'pending'
geom GEOMETRY(POINT, 4326)
created_at, updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id, verification_status, spatial index
**Triggers**: update_updated_at_column

### 2.9 `backend_schema.user_lists` - User Lists

**Purpose**: Store user-created lists of POIs

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
list_name VARCHAR(100) NOT NULL
description TEXT
list_type VARCHAR(20) DEFAULT 'personal'
poi_ids JSONB DEFAULT '[]'
tags JSONB DEFAULT '[]'
is_archived BOOLEAN DEFAULT FALSE
created_at, updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

**Indexes**: Primary key, user_id, list_type
**Triggers**: update_updated_at_column

### 2.10 `backend_schema.user_poi_submissions` - POI Submissions

**Purpose**: Track user POI submissions

**Columns**:

```sql
id UUID PRIMARY KEY DEFAULT uuid_generate_v4()
user_id TEXT NOT NULL REFERENCES nextauth_users(id)
poi_id UUID
name VARCHAR(255) NOT NULL
description TEXT
category VARCHAR(100)
latitude DECIMAL(10,8) NOT NULL
longitude DECIMAL(11,8) NOT NULL
address TEXT
phone VARCHAR(50)
website TEXT
status VARCHAR(20) DEFAULT 'pending'
submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
reviewed_at TIMESTAMP WITH TIME ZONE
reviewed_by TEXT REFERENCES nextauth_users(id)
review_notes TEXT
```

**Indexes**: Primary key, user_id, status

### 2.11-17. NextAuth Tables

**Purpose**: NextAuth.js authentication system

**Tables**:

- `nextauth_users` - User authentication data
- `nextauth_accounts` - OAuth provider accounts
- `nextauth_sessions` - User sessions
- `nextauth_verification_tokens` - Email verification tokens

**Additional Tables**:

- `roads` - Road network data
- `migration_log` - Database migration tracking

## 3. PUBLIC SCHEMA (1 table)

### 3.1 `public.spatial_ref_sys` - Spatial Reference Systems

**Purpose**: PostGIS spatial reference system definitions
**Content**: Standard PostGIS spatial reference data

## 4. Database Functions (23 functions in backend_schema)

### Core Functions:

- `add_poi_photo()` - Add photos to POIs
- `add_poi_tag()` - Add descriptive tags
- `vote_poi_category()` - Vote for POI categories
- `remove_poi_category_vote()` - Remove category votes
- `get_poi_profile()` - Get complete POI profile
- `get_poi_profile_summary()` - Get POI profile summary
- `get_poi_with_full_context()` - Get POI with user context
- `create_verification_task()` - Create community tasks
- `assign_verification_task()` - Assign tasks to users
- `update_popularity_scores()` - Update POI popularity
- `update_trending_scores()` - Update trending scores
- `refresh_materialized_views()` - Refresh performance views
- `set_primary_photo()` - Set primary photo for POI
- `update_poi_hours()` - Update POI opening hours

### Search Functions:

- `search_pois_by_community_tags()` - Search by community tags
- `search_pois_by_democratic_categories()` - Search by democratic categories

### Trigger Functions:

- `sync_backend_interactions_to_spatial()` - Sync user interactions
- `sync_reviews_to_spatial()` - Sync reviews
- `sync_visits_to_spatial()` - Sync visits
- `update_poi_rating_from_reviews()` - Update ratings
- `update_poi_community_tags()` - Update community tags
- `update_poi_democratic_categories()` - Update democratic categories
- `update_updated_at_column()` - Update timestamps

## 5. Database Indexes (120 total)

### Spatial Indexes:

- GIST indexes on geometry columns for spatial queries
- Spatial indexes on POI coordinates and boundaries

### Performance Indexes:

- Composite indexes for common query patterns
- JSONB GIN indexes for efficient JSON searches
- Descending indexes for ranking and popularity

### User Data Indexes:

- User ID indexes for fast user data retrieval
- Composite indexes for user-POI relationships

## 6. Current Data Status

### POI Data:

- **Total POIs**: 46,591
- **Top Locations**: 62 (0.13%)
- **Hidden Gems**: 469 (1.01%)
- **With Ratings**: 0 (0%)
- **Popularity Scores**: All default to 0

### User Data:

- User profiles, favorites, reviews, visits tracked
- Community features (tags, categories, verification tasks) available
- Gamification system (credits, levels) implemented

### Ranking System:

- **Database Structure**: ✅ Complete
- **Data Population**: ❌ Mostly default values
- **API Usage**: ❌ Not using real data (hardcoded values)
- **Scripts Available**: ✅ populate_poi_flags.sql exists

## 7. Key Issues Identified

1. **Ranking Data**: All POIs have default popularity scores (0)
2. **API Implementation**: Endpoints return hardcoded `false` for ranking flags
3. **Data Population**: Scripts exist but haven't been executed
4. **User Interactions**: No real user interaction data (all counters at 0)

## 8. Recommendations

1. **Execute Data Population**: Run `populate_poi_flags.sql`
2. **Update APIs**: Modify endpoints to use real database values
3. **Populate Sample Data**: Add realistic popularity scores and user interactions
4. **Enable Triggers**: Ensure all sync triggers are active
5. **Performance Optimization**: Monitor and optimize query performance

This documentation reflects the actual current state of the database as examined directly from the PostgreSQL instance.

## 9. Discussion & Planned Improvements

### 9.1 Accessibility Rating

**Current**: `accessibility_rating INTEGER`
**Decision**: Change to `is_wheelchair_accessible BOOLEAN`
**Reason**: Simpler and more practical for basic accessibility needs

### 9.2 Top Location & Hidden Gem System

**Current Issues**:

- `is_top_location` and `is_hidden_gem` columns exist but have no clear rules
- Current data (62 top locations, 469 hidden gems) is dummy data
- Hardcoded values don't reflect real popularity or quality

**Planned Changes**:

- **Remove**: `is_hidden_gem` column entirely
- **Remove**: `is_top_location` column
- **Add**: Dynamic function `get_top_locations()` that calculates top locations algorithmically
- **Algorithm**: Based on interactions, review counts (both internal and external), popularity scores
- **Result**: Real-time calculated list instead of hardcoded flags

### 9.3 External Ratings & Reviews

**Planned Additions**:

```sql
-- Add to spatial_schema.pois
online_rating_avg FLOAT DEFAULT NULL,        -- External average rating (1-5)
online_rating_count INTEGER DEFAULT 0,       -- Number of external reviews
online_rating_source VARCHAR(50),            -- Source: "google", "yelp", etc.
online_reviews JSONB DEFAULT '[]',           -- External review texts
last_online_update TIMESTAMP,                -- Last external data fetch
```

**Display Strategy**:

- Show internal and external ratings side-by-side
- Mark external reviews as "Internet" when displayed
- No sentiment analysis - display reviews as text strings
- No external APIs initially - manual data entry

### 9.4 Democratic Categorization

**Current System**: Community voting system with master categories
**Status**: Keep as-is for now
**How it works**:

- Users vote for categories (upvotes only)
- Aggregated votes determine POI categories
- Stored in `democratic_categories` JSONB field

### 9.5 Data Strategy

**Current Phase**: Work with existing 46,591 POIs
**Future**: Create comprehensive database with no missing data
**External APIs**: Not implemented yet
**Update Frequency**: Not critical for current development

### 9.6 Review System

**Current**: Text-based reviews in `user_location_reviews` and `community_reviews`
**Display**: Show both internal user reviews and external internet reviews
**Moderation**: No algorithmic filtering - display as-is

### 9.7 Implementation Priority

1. Remove `is_hidden_gem` column
2. Remove `is_top_location` column
3. Add external rating fields
4. Create `get_top_locations()` function
5. Update API endpoints to use dynamic calculations
6. Populate external rating data manually
7. Test and refine ranking algorithms
