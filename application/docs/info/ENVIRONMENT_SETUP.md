# Environment Configuration Guide

## 🎯 Quick Answer to Your Questions

### ✅ Do you need NEXTAUTH_URL and NEXTAUTH_SECRET?
**YES, absolutely!** Even with your own PostgreSQL database, these are required for:
- JWT token signing and encryption
- OAuth callback URLs (Google sign-in)
- Session security
- CSRF protection

### ✅ Configuration Architecture
You **DO have a centralized config system** (`lib/config.ts`) which is **PERFECT** for AWS ECS deployment!

## 📁 File Organization

### Development Environment
- **File**: `.env.local` (cleaned up, no duplicates)
- **Usage**: Local development only
- **Secrets**: Use placeholder values

### Production Template
- **File**: `.env.production.template`
- **Usage**: Copy and customize for production
- **Secrets**: Replace with actual secure values

### Centralized Configuration
- **File**: `lib/config.ts`
- **Features**: 
  - Type safety
  - Validation
  - Defaults
  - Environment detection
  - Perfect for containerized deployment

## 🔐 Secret Generation

### Generate Secure Secrets
```bash
# Run the secret generator
node scripts/generate-secrets.js

# Or manually generate NEXTAUTH_SECRET
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### Secret Requirements
- **NEXTAUTH_SECRET**: 32+ character random hex string
- **Different per environment**: Never reuse dev secrets in production
- **Cryptographically secure**: Use crypto.randomBytes(), not passwords

## 🌐 Domain Setup

### Development
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

### Production (when you get your domain)
```env
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-production-secret-here
```

### What Changes When You Get a Domain
1. Update `NEXTAUTH_URL` to your domain
2. Update Google OAuth settings in Google Cloud Console
3. Generate new `NEXTAUTH_SECRET` for production
4. Update `CORS_ORIGINS` to include your domain

## 🚀 AWS ECS Deployment

### ✅ Why Your Setup is Perfect for ECS

1. **Centralized Config**: `lib/config.ts` reads environment variables
2. **Type Safety**: Validates configuration at runtime
3. **Flexible**: Works with ECS task definition environment variables
4. **Secure**: Supports AWS Secrets Manager integration

### ECS Task Definition Structure
```json
{
  "environment": [
    {"name": "NODE_ENV", "value": "production"},
    {"name": "NEXTAUTH_URL", "value": "https://yourdomain.com"},
    {"name": "DB_HOST", "value": "your-rds-endpoint.amazonaws.com"}
  ],
  "secrets": [
    {"name": "NEXTAUTH_SECRET", "valueFrom": "arn:aws:secretsmanager:..."},
    {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:..."}
  ]
}
```

### Benefits of This Approach
- ✅ **No config file needed in container**
- ✅ **Secrets managed by AWS**
- ✅ **Environment variables fed from task definition**
- ✅ **Type-safe configuration**
- ✅ **Runtime validation**

## 📋 Deployment Checklist

### Before Deployment
- [ ] Generate production secrets using `scripts/generate-secrets.js`
- [ ] Create AWS Secrets Manager entries
- [ ] Update `.env.production.template` with your values
- [ ] Set up Google OAuth for production domain
- [ ] Configure RDS database

### During Deployment
- [ ] Build and push Docker image to ECR
- [ ] Create ECS task definition with environment variables
- [ ] Set up ALB with SSL certificate
- [ ] Configure DNS to point to load balancer

### After Deployment
- [ ] Run configuration validation
- [ ] Test authentication flows
- [ ] Verify database connectivity
- [ ] Test Google OAuth

## 🔧 Configuration Usage in Code

### Import and Use
```typescript
import { config, validateConfig, isProduction } from '@/lib/config'

// Validate on startup
validateConfig()

// Use throughout your app
const dbUrl = config.database.url
const isSecure = isProduction()
```

### Environment Detection
```typescript
import { isDevelopment, isProduction } from '@/lib/config'

if (isProduction()) {
  // Production-specific logic
}
```

## 🚨 Security Best Practices

1. **Never commit secrets** to version control
2. **Use different secrets** for each environment
3. **Rotate secrets regularly**
4. **Use AWS Secrets Manager** for production
5. **Validate configuration** on startup
6. **Use HTTPS** in production
7. **Enable database SSL** in production

## 📚 Additional Resources

- **AWS ECS Guide**: `deployment/aws-ecs-environment.md`
- **Production Template**: `.env.production.template`
- **Secret Generator**: `scripts/generate-secrets.js`
- **Config Validation**: `lib/config.ts`

Your setup is already production-ready! The centralized config system makes AWS ECS deployment straightforward and secure.
