# Wizlop Application

A production-ready Next.js application with dual build systems for development and production environments.

## 🚀 Quick Start

### Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Debug build issues
npm run build:all-errors
```

### Production
```bash
# Production build
npm run build

# Start production server
npm run start
```

## 📁 Project Structure

```
application/
├── config/                    # 🔧 All configuration files
│   ├── development/           # Dev-specific configs
│   ├── production/            # Prod-specific configs
│   ├── webpack/               # Webpack configurations
│   ├── eslint.config.js
│   ├── next.config.js
│   ├── postcss.config.js
│   └── tailwind.config.js
├── scripts/                   # 📜 Build scripts
│   ├── build-dev.sh
│   └── build-prod.sh
├── tools/                     # 🛠️ Development tools
│   ├── development/
│   └── production/
├── docs/                      # 📚 Documentation
│   └── BUILD_SYSTEMS.md
├── .env-templates/            # 🌍 Environment templates
├── app/                       # ⚛️ Next.js app directory
├── lib/                       # 📚 Shared libraries
└── public/                    # 🌐 Static assets
```

## 🔧 Build Systems

### 1. Next.js Build (Primary - Production)
- **Command**: `npm run build`
- **Purpose**: Production-ready React application
- **Output**: `.next/` directory

### 2. Webpack Build (Secondary - Development/Debug)
- **Command**: `npm run build:webpack`
- **Purpose**: Development debugging and error analysis
- **Output**: `dist/` directory

## 📋 Available Scripts

### Core Commands
```bash
npm run dev                 # Start development server
npm run build              # Next.js production build
npm run build:webpack      # Webpack development build
npm run build:all-errors   # Webpack build with detailed errors
npm run start              # Start production server
```

### Development Tools
```bash
npm run check-types:dev     # TypeScript checking (dev)
npm run check-types:prod    # TypeScript checking (strict)
npm run lint               # ESLint code quality
npm run lint:fix           # Auto-fix ESLint issues
```

### Cleanup
```bash
npm run clean              # Clean build artifacts
npm run clean:all          # Clean everything including node_modules
npm run setup:dev          # Setup development environment
npm run setup:prod         # Setup production environment
```

### Debug Tools
```bash
node tools/development/debug-build.js  # Debug build issues
./scripts/build-dev.sh                 # Development build script
./scripts/build-prod.sh                # Production build script
```

## 🌍 Environment Setup

### Development
1. Copy environment template:
   ```bash
   cp .env-templates/.env.development.template .env.local
   ```
2. Fill in your development values
3. Run: `npm run setup:dev`

### Production
1. Set environment variables from `.env-templates/.env.production.template`
2. Run: `npm run setup:prod`

## 🔍 Debugging Build Issues

1. **Use the Debug Tool**:
   ```bash
   node tools/development/debug-build.js
   ```

2. **Check TypeScript Errors**:
   ```bash
   npm run check-types:dev
   ```

3. **Check ESLint Issues**:
   ```bash
   npm run lint
   ```

4. **Detailed Webpack Errors**:
   ```bash
   npm run build:all-errors
   ```

## 📚 Documentation

- **[Build Systems Guide](docs/BUILD_SYSTEMS.md)** - Comprehensive build system documentation
- **[Environment Templates](.env-templates/)** - Environment configuration examples

## ✅ Features

- 🏗️ **Dual Build Systems**: Next.js for production, Webpack for debugging
- 🔧 **Organized Configuration**: All configs centralized in `config/`
- 🌍 **Environment Separation**: Dev/prod specific configurations
- 🛠️ **Development Tools**: Debug utilities and build scripts
- 🧹 **Clean Structure**: Organized project layout
- 📊 **Bundle Analysis**: Built-in bundle analysis tools
- 🔍 **Error Debugging**: Detailed error reporting and debugging tools

## 🚨 Troubleshooting

If you encounter build issues:

1. Run the debug tool: `node tools/development/debug-build.js`
2. Check the [Build Systems Documentation](docs/BUILD_SYSTEMS.md)
3. Ensure environment variables are set correctly
4. Clean and rebuild: `npm run clean && npm install`

## 🤝 Contributing

1. Use `npm run dev` for development
2. Run `npm run lint:fix` before committing
3. Test both build systems: `npm run build` and `npm run build:webpack`
4. Update documentation when adding new features
