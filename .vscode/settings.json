{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "always", "source.organizeImports": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "zencoder.enableRepoIndexing": true}