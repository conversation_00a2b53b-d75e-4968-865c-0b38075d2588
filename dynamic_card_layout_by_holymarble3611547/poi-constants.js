/**
 * POI-related constants and field definitions
 *
 * @format
 */

// Fields that can be edited when submitting or updating POIs
export const POI_EDITABLE_FIELDS = [
	'name',
	'name_en',
	'name_tr',
	'name_uk',
	'name_de',
	'name_ru',
	'name_ar',
	'category',
	'subcategory',
	'cuisine',
	'full_address',
	'street',
	'neighborhood',
	'district',
	'city',
	'province',
	'country',
	'phone_number',
	'opening_hours',
	'description',
	'latitude',
	'longitude',
];

// POI categories and subcategories structure
export const POI_CATEGORIES_DATA = {
	'Food & Drink': {
		subcategories: ['Cafe', 'Restaurant'],
	},
	'Cultural & Creative Experiences': {
		subcategories: ['Museum Visit', 'Art Gallery Walk'],
	},
	'Cultural & Creative Experiences2': {
		subcategories: ['Museum Visit2', 'Art Gallery Walk2'],
	},
};

// Helper functions to get categories and subcategories
export const getPOICategories = () => {
	return Object.keys(POI_CATEGORIES_DATA);
};

export const getPOISubcategoriesWithCategory = () => {
	const allSubcategories = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData = POI_CATEGORIES_DATA[category];
		for (const subcategory of categoryData.subcategories) {
			allSubcategories.push({ subcategory, category });
		}
	}
	return allSubcategories;
};

export const getPOISubcategories = (category) => {
	if (!category) {
		// Return all subcategories if no category specified
		return Object.values(POI_CATEGORIES_DATA).flatMap(
			(cat) => cat.subcategories
		);
	}

	const categoryData =
		POI_CATEGORIES_DATA[category];
	return categoryData ? categoryData.subcategories : [];
};

// Legacy exports for backward compatibility
export const POI_CATEGORIES = getPOICategories();
export const POI_SUBCATEGORY_OPTIONS = getPOISubcategories();

// Submission reasons for POI reports/updates
export const POI_SUBMISSION_REASONS = [
	{ value: 'new_poi', label: 'New POI' },
	{ value: 'missing_info', label: 'Missing Information' },
	{ value: 'incorrect_info', label: 'Incorrect Information' },
	{ value: 'closed_business', label: 'Business Closed' },
	{ value: 'moved_location', label: 'Location Changed' },
];

