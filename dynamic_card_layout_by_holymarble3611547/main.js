import { getPOICategories, getPOISubcategoriesWithCategory } from './poi-constants.js';

const BRAND_COLORS = ['#F94144', '#43AA8B', '#277DA1'];

// Generates a gradient from a base color
function getGradient(color) {
    const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    };
    
    const baseRgb = hexToRgb(color);
    if (!baseRgb) return 'linear-gradient(45deg, #ccc, #eee)';

    // Create a lighter shade for the gradient
    const lighterRgb = {
        r: Math.min(255, baseRgb.r + 40),
        g: Math.min(255, baseRgb.g + 40),
        b: Math.min(255, baseRgb.b + 40)
    };

    const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
    const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

    return `linear-gradient(45deg, ${startColor}, ${endColor})`;
}

// A simple shuffle function
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

function generateTreemap(items, x, y, width, height) {
    if (items.length === 0) {
        return [];
    }
    if (items.length === 1) {
        return [{ ...items[0], x, y, width, height }];
    }

    const totalWeight = items.reduce((sum, item) => sum + (item.weight || 1), 0);

    // Find a split point that divides the total weight.
    let cumulativeWeight = 0;
    let splitIndex = 0;
    for (let i = 0; i < items.length -1; i++) {
        cumulativeWeight += items[i].weight || 1;
        // We look for a split that is reasonably balanced to avoid tiny slivers.
        // The split will happen after the current item.
        if (cumulativeWeight >= totalWeight / 3 && cumulativeWeight <= (totalWeight * 2) / 3) {
            splitIndex = i + 1;
            break;
        }
    }

    // If no "good" split point is found, fall back to splitting near the middle.
    if (splitIndex === 0) {
        splitIndex = Math.ceil(items.length / 2);
    }
    
    const items1 = items.slice(0, splitIndex);
    const items2 = items.slice(splitIndex);

    // if one of the splits is empty (which can happen with the fallback), we just split in half
    if (items1.length === 0 || items2.length === 0) {
        const mid = Math.ceil(items.length / 2);
        const items1 = items.slice(0, mid);
        const items2 = items.slice(mid);
    }

    const weight1 = items1.reduce((sum, item) => sum + (item.weight || 1), 0);
    
    const ratio1 = weight1 / totalWeight;

    let rects = [];
    if (width > height) {
        // Split vertically
        const width1 = width * ratio1;
        const width2 = width - width1;
        rects = rects.concat(generateTreemap(items1, x, y, width1, height));
        rects = rects.concat(generateTreemap(items2, x + width1, y, width2, height));
    } else {
        // Split horizontally
        const height1 = height * ratio1;
        const height2 = height - height1;
        rects = rects.concat(generateTreemap(items1, x, y, width, height1));
        rects = rects.concat(generateTreemap(items2, x, y + height1, width, height2));
    }

    return rects;
}

function createCardElement(cardData, index) {
    const card = document.createElement('div');
    card.className = 'card';
    card.style.left = `${cardData.x}px`;
    card.style.top = `${cardData.y}px`;
    card.style.width = `${cardData.width}px`;
    card.style.height = `${cardData.height}px`;
    
    const color = BRAND_COLORS[index % BRAND_COLORS.length];
    card.style.background = getGradient(color);

    const title = document.createElement('h3');
    title.className = 'card-title';
    title.textContent = cardData.subcategory;
    card.appendChild(title);

    const subtitle = document.createElement('p');
    subtitle.className = 'card-subtitle';
    subtitle.textContent = cardData.category;
    card.appendChild(subtitle);
    
    return card;
}

function renderLayout(categoryFilter = 'All') {
    const container = document.getElementById('container');
    if (!container) return;

    container.innerHTML = ''; // Clear previous content

    let allSubcategories = getPOISubcategoriesWithCategory();

    if (categoryFilter !== 'All') {
        allSubcategories = allSubcategories.filter(sc => sc.category === categoryFilter);
    }

    // Deduplicate subcategories to ensure each card is unique.
    const uniqueSubcategories = [];
    const seenSubcategories = new Set();
    // Shuffle before deduplicating to vary which category is picked for a shared subcategory
    const shuffledAll = shuffleArray([...allSubcategories]);
    for (const item of shuffledAll) {
        if (!seenSubcategories.has(item.subcategory)) {
            uniqueSubcategories.push(item);
            seenSubcategories.add(item.subcategory);
        }
    }

    // Shuffle and pick a random number of subcategories (max 10)
    const shuffled = shuffleArray(uniqueSubcategories);
    const count = Math.max(1, Math.min(shuffled.length, Math.floor(Math.random() * 8) + 3)); // 3 to 10 cards, or fewer if not available
    let subcategories = shuffled.slice(0, count);

    if (subcategories.length === 0) return;

    // Assign random weights for more varied treemap sizes
    subcategories = subcategories.map(sc => ({
        ...sc,
        weight: Math.random() * 0.8 + 0.2 // weight between 0.2 and 1.0
    }));
    // Sort by weight to encourage more varied groupings
    subcategories.sort((a, b) => a.weight - b.weight);

    const { width, height } = container.getBoundingClientRect();
    
    const treemapLayout = generateTreemap(subcategories, 0, 0, width, height);

    treemapLayout.forEach((cardLayout, index) => {
        const cardElement = createCardElement(cardLayout, index);
        container.appendChild(cardElement);
    });
}

function renderFilters() {
    const filtersContainer = document.getElementById('filters');
    if (!filtersContainer) return;
    filtersContainer.innerHTML = '';

    const categories = ['All', ...getPOICategories()];
    const MAX_VISIBLE_FILTERS = 3; 

    const visibleCategories = categories.slice(0, MAX_VISIBLE_FILTERS + 1);
    const overflowCategories = categories.slice(MAX_VISIBLE_FILTERS + 1);

    visibleCategories.forEach(category => {
        const button = document.createElement('button');
        button.className = 'filter-btn';
        button.textContent = category;
        button.dataset.category = category;
        if (category === 'All') {
            button.classList.add('active');
        }
        button.addEventListener('click', handleFilterClick);
        filtersContainer.appendChild(button);
    });

    if (overflowCategories.length > 0) {
        const select = document.createElement('select');
        select.className = 'filter-select';

        const defaultOption = document.createElement('option');
        defaultOption.textContent = 'More...';
        defaultOption.disabled = true;
        defaultOption.selected = true;
        select.appendChild(defaultOption);

        overflowCategories.forEach(category => {
            const option = document.createElement('option');
            option.textContent = category;
            option.value = category;
            select.appendChild(option);
        });
        select.addEventListener('change', (e) => handleFilterClick(e, true));
        filtersContainer.appendChild(select);
    }
}

function handleFilterClick(event, isSelect = false) {
    const filtersContainer = document.getElementById('filters');
    const category = isSelect ? event.target.value : event.target.dataset.category;

    // Reset select if a button is clicked
    if (!isSelect) {
        const select = filtersContainer.querySelector('.filter-select');
        if (select) select.selectedIndex = 0;
    }
    
    // Update active class on buttons
    filtersContainer.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === category);
    });
    
    renderLayout(category);
}

function init() {
    renderFilters();
    renderLayout();

    const reshuffleBtn = document.getElementById('reshuffle-btn');
    if(reshuffleBtn) {
        reshuffleBtn.addEventListener('click', () => {
             const activeFilter = document.querySelector('.filter-btn.active')?.dataset.category || 'All';
             renderLayout(activeFilter);
        });
    }

    // Debounce resize handler
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            const activeFilter = document.querySelector('.filter-btn.active')?.dataset.category || 'All';
            renderLayout(activeFilter);
        }, 250);
    });
}

document.addEventListener('DOMContentLoaded', init);