body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
        Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f0f2f5;
    margin: 0;
}

#app {
    width: 100%;
    max-width: 1200px;
    margin: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

#controls {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

#filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn, .filter-select, #reshuffle-btn {
    padding: 8px 16px;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.filter-btn:hover, .filter-select:hover, #reshuffle-btn:hover {
    background-color: #e9e9e9;
    border-color: #999;
}

.filter-btn.active {
    background-color: #277DA1;
    color: white;
    border-color: #277DA1;
}

#reshuffle-btn {
    background-color: #43AA8B;
    color: white;
    border-color: #43AA8B;
    font-weight: bold;
}


#container {
    width: 1200px;
    height: 800px;
    border: 2px solid #ccc;
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
    background-color: #fff;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card {
    position: absolute;
    box-sizing: border-box;
    border: 2px solid #fff;
    overflow: hidden;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 15px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 6px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
}

.card:hover {
    transform: scale(1.02);
    z-index: 10;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: #fafafa;
}

.card-title {
    font-weight: bold;
    font-size: 1.5em;
    margin: 0;
}

.card-subtitle {
    font-size: 1em;
    opacity: 0.9;
    margin-top: 8px;
}

